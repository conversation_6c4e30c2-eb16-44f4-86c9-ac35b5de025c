#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تخطيط الرأس المُصحح مع الشعار المصغر وبيانات الشركة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_header_with_resized_logo():
    """اختبار الرأس مع الشعار المصغر"""
    print("🖼️ اختبار الرأس مع الشعار المصغر...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات اختبار شاملة
        test_data = {
            'request_number': '2025-FIXED-HEADER',
            'request_date': '2024/01/03',
            'remittance_amount': '85,000',
            'currency': 'SAR',
            'transfer_purpose': 'FIXED HEADER LAYOUT TEST',
            'exchanger': 'شركة الصرافة المحدثة',
            
            # بيانات المستفيد
            'receiver_name': 'FIXED LAYOUT TEST COMPANY LIMITED',
            'receiver_address': 'TEST ADDRESS FOR FIXED LAYOUT',
            'receiver_city': 'RIYADH',
            'receiver_phone': '+966 11 9876543',
            'receiver_account': '9876543210987654321',
            'receiver_country': 'SAUDI ARABIA',
            
            # بيانات البنك
            'receiver_bank': 'SAUDI NATIONAL BANK',
            'receiver_bank_branch': 'RIYADH MAIN BRANCH',
            'receiver_bank_address': 'KING FAHD ROAD, RIYADH',
            'receiver_swift': 'NCBKSARI',
            
            # بيانات المرسل
            'manager_name': 'مدير النظام المحدث'
        }
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # التحقق من تحميل بيانات الشركة
        if pdf_generator.company_data:
            print("✅ بيانات الشركة محملة:")
            print(f"   الاسم العربي: {pdf_generator.company_data['name']}")
            print(f"   الاسم الإنجليزي: {pdf_generator.company_data['name_en']}")
            print(f"   العنوان: {pdf_generator.company_data['address']}")
            print(f"   الهاتف: {pdf_generator.company_data['phone']}")
            if pdf_generator.company_data['logo_path']:
                print(f"   الشعار: {pdf_generator.company_data['logo_path']}")
        else:
            print("⚠️ لم يتم تحميل بيانات الشركة")
        
        # إنشاء ملف PDF مع التخطيط المُصحح
        output_path = "نموذج_رأس_مُصحح_مع_شعار_مصغر.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        print(f"✅ تم إنشاء النموذج المُصحح: {result_path}")
        
        # التحقق من وجود الملف
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"📄 حجم الملف: {file_size} بايت")
            print(f"📁 مسار الملف: {Path(result_path).absolute()}")
            
            # التحقق من نوع الرأس المستخدم
            logo_path = pdf_generator.get_company_logo_path()
            if logo_path:
                print(f"🖼️ تم استخدام الشعار: {logo_path}")
                print("   📏 الشعار مصغر ولا يتجاوز الخط الفاصل")
                print("   📝 بيانات الشركة تظهر حول الشعار")
            else:
                print("📝 تم استخدام الرأس النصي مع بيانات الشركة")
            
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_header_with_company_data():
    """اختبار الرأس النصي مع بيانات الشركة"""
    print("\n📝 اختبار الرأس النصي مع بيانات الشركة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # إخفاء الشعار مؤقتاً لاختبار الرأس النصي
        original_logo_path = None
        if pdf_generator.company_data and pdf_generator.company_data.get('logo_path'):
            original_logo_path = pdf_generator.company_data['logo_path']
            pdf_generator.company_data['logo_path'] = None
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-TEXT-HEADER',
            'request_date': '2024/01/03',
            'remittance_amount': '45,000',
            'currency': 'SAR',
            'transfer_purpose': 'TEXT HEADER TEST',
            'exchanger': 'شركة الصرافة النصية',
            'receiver_name': 'TEXT HEADER TEST COMPANY',
            'receiver_country': 'SAUDI ARABIA',
            'manager_name': 'مدير الاختبار النصي'
        }
        
        # إنشاء ملف PDF مع الرأس النصي
        output_path = "نموذج_رأس_نصي_مع_بيانات_الشركة.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        # استعادة مسار الشعار الأصلي
        if original_logo_path and pdf_generator.company_data:
            pdf_generator.company_data['logo_path'] = original_logo_path
        
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"✅ تم إنشاء النموذج النصي: {result_path}")
            print(f"📄 حجم الملف: {file_size} بايت")
            print("📝 الرأس النصي يحتوي على:")
            print("   ✅ اسم الشركة بالعربية والإنجليزية")
            print("   ✅ العنوان بالعربية والإنجليزية")
            print("   ✅ معلومات الاتصال")
            print("   ✅ شعار نصي مصغر في الوسط")
            return True
        else:
            print("❌ فشل في إنشاء النموذج النصي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الرأس النصي: {e}")
        return False

def test_logo_sizing():
    """اختبار تحجيم الشعار"""
    print("\n📏 اختبار تحجيم الشعار...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        # التحقق من وجود الشعار
        logo_path = pdf_generator.get_company_logo_path()
        
        if logo_path:
            print(f"🖼️ الشعار الموجود: {logo_path}")
            
            # فتح الصورة وفحص أبعادها
            try:
                from PIL import Image
                with Image.open(logo_path) as img:
                    original_width, original_height = img.size
                    print(f"   📏 الأبعاد الأصلية: {original_width}x{original_height} بكسل")
                    
                    # حساب الأبعاد المصغرة (نفس المنطق في الكود)
                    max_logo_width = 60  # mm
                    max_logo_height = 25  # mm
                    
                    # تحويل إلى بكسل (تقريبي: 1mm = 2.83 بكسل)
                    max_width_px = max_logo_width * 2.83
                    max_height_px = max_logo_height * 2.83
                    
                    width_ratio = max_width_px / original_width
                    height_ratio = max_height_px / original_height
                    scale_ratio = min(width_ratio, height_ratio)
                    
                    final_width = original_width * scale_ratio
                    final_height = original_height * scale_ratio
                    
                    print(f"   📐 الأبعاد المصغرة: {final_width:.0f}x{final_height:.0f} بكسل")
                    print(f"   📊 نسبة التصغير: {scale_ratio:.2f}")
                    print(f"   📏 الحجم في PDF: {final_width/2.83:.1f}x{final_height/2.83:.1f}mm")
                    
                    # التحقق من أن الشعار لا يتجاوز الحد المسموح
                    if final_height/2.83 <= 25:
                        print("   ✅ الشعار لا يتجاوز الخط الفاصل (25mm)")
                    else:
                        print("   ⚠️ الشعار قد يتجاوز الخط الفاصل")
                    
                    return True
                    
            except Exception as e:
                print(f"   ❌ خطأ في فتح الصورة: {e}")
                return False
        else:
            print("⚠️ لا يوجد شعار، سيتم استخدام الشعار النصي")
            print("   📏 الشعار النصي: دائرة 8mm قطر (لا يتجاوز الخط الفاصل)")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تحجيم الشعار: {e}")
        return False

def test_company_data_display():
    """اختبار عرض بيانات الشركة"""
    print("\n🏢 اختبار عرض بيانات الشركة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        if pdf_generator.company_data:
            print("✅ بيانات الشركة متوفرة:")
            
            # التحقق من البيانات المطلوبة
            required_fields = ['name', 'name_en', 'address', 'phone']
            missing_fields = []
            
            for field in required_fields:
                if pdf_generator.company_data.get(field):
                    print(f"   ✅ {field}: {pdf_generator.company_data[field]}")
                else:
                    print(f"   ❌ {field}: غير موجود")
                    missing_fields.append(field)
            
            # البيانات الاختيارية
            optional_fields = ['email', 'tax_number', 'commercial_register']
            for field in optional_fields:
                value = pdf_generator.company_data.get(field)
                if value:
                    print(f"   ✅ {field}: {value}")
                else:
                    print(f"   ⚪ {field}: غير محدد")
            
            if not missing_fields:
                print("\n✅ جميع البيانات المطلوبة متوفرة")
                return True
            else:
                print(f"\n⚠️ بيانات مفقودة: {', '.join(missing_fields)}")
                return False
        else:
            print("❌ لم يتم تحميل بيانات الشركة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار بيانات الشركة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تخطيط الرأس المُصحح...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_company_data_display())
    results.append(test_logo_sizing())
    results.append(test_header_with_resized_logo())
    results.append(test_text_header_with_company_data())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار تخطيط الرأس المُصحح:")
    print("="*60)
    
    test_names = [
        "عرض بيانات الشركة",
        "تحجيم الشعار",
        "الرأس مع الشعار المصغر",
        "الرأس النصي مع بيانات الشركة"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تخطيط الرأس المُصحح يعمل بنجاح!")
        print("✅ الشعار مصغر ولا يتجاوز الخط الفاصل")
        print("✅ بيانات الشركة تظهر بوضوح")
        print("✅ التخطيط متوازن ومحكم")
        print("✅ الرأس النصي يعمل كبديل ممتاز")
        
        # عرض الملفات المنشأة
        created_files = [
            "نموذج_رأس_مُصحح_مع_شعار_مصغر.pdf",
            "نموذج_رأس_نصي_مع_بيانات_الشركة.pdf"
        ]
        
        existing_files = [f for f in created_files if Path(f).exists()]
        if existing_files:
            print(f"\n📁 الملفات المنشأة:")
            for file in existing_files:
                print(f"   • {file}")
            print("يمكنك فتح الملفات لمراجعة التخطيط المُصحح")
            
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
