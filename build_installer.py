#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إنشاء ملف التثبيت التنفيذي لنظام ProShipment
ProShipment Executable Installer Builder
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import json
from datetime import datetime

class ProShipmentInstaller:
    """منشئ ملف التثبيت التنفيذي"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.installer_dir = self.project_root / "installer"
        self.version = "2.0.0"
        self.app_name = "ProShipment"
        
    def setup_directories(self):
        """إعداد المجلدات المطلوبة"""
        print("📁 إعداد المجلدات...")
        
        # إنشاء مجلدات البناء
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        self.installer_dir.mkdir(exist_ok=True)
        
        print("   ✅ تم إنشاء مجلدات البناء")
        
    def check_dependencies(self):
        """فحص التبعيات المطلوبة"""
        print("🔍 فحص التبعيات...")
        
        required_packages = [
            "pyinstaller",
            "PySide6",
            "SQLAlchemy",
            "reportlab",
            "openpyxl"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.lower().replace("-", "_"))
                print(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package} غير مثبت")
        
        if missing_packages:
            print(f"\n⚠️ يجب تثبيت الحزم التالية:")
            for package in missing_packages:
                print(f"   pip install {package}")
            return False
        
        return True
    
    def create_pyinstaller_spec(self):
        """إنشاء ملف PyInstaller spec"""
        print("📝 إنشاء ملف PyInstaller spec...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

block_cipher = None

# البيانات المطلوبة
added_files = [
    ('data', 'data'),
    ('config', 'config'),
    ('assets', 'assets'),
    ('src/resources', 'src/resources'),
    ('docs', 'docs'),
    ('LOGO_FOGEHI.png', '.'),
    ('LOGO_FOGEHI.jpg', '.'),
    ('README.md', '.'),
    ('CHANGELOG.md', '.'),
]

# المكتبات المخفية
hidden_imports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtPrintSupport',
    'PySide6.QtSvg',
    'SQLAlchemy',
    'sqlite3',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    'reportlab.platypus',
    'arabic_reshaper',
    'bidi',
    'openpyxl',
    'xlsxwriter',
    'requests',
    'beautifulsoup4',
    'PIL',
    'num2words',
    'qdarkstyle',
    'qtawesome',
    'qtstylish',
    'src.ui.main_window',
    'src.database.database_manager',
    'src.database.models',
    'src.ui.shipments',
    'src.ui.suppliers',
    'src.ui.remittances',
    'src.ui.dialogs',
    'src.ui.themes',
    'src.ui.responsive',
    'src.reports',
    'src.utils',
]

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='LOGO_FOGEHI.ico' if Path('LOGO_FOGEHI.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{self.app_name}',
)
'''
        
        spec_file = self.project_root / f"{self.app_name}.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"   ✅ تم إنشاء {spec_file}")
        return spec_file
    
    def create_icon(self):
        """إنشاء أيقونة التطبيق"""
        print("🎨 إنشاء أيقونة التطبيق...")
        
        try:
            from PIL import Image
            
            # البحث عن ملف الشعار
            logo_files = [
                self.project_root / "LOGO_FOGEHI.png",
                self.project_root / "LOGO_FOGEHI.jpg",
                self.project_root / "assets" / "header.png"
            ]
            
            logo_file = None
            for file in logo_files:
                if file.exists():
                    logo_file = file
                    break
            
            if logo_file:
                # تحويل إلى ICO
                img = Image.open(logo_file)
                img = img.resize((256, 256), Image.Resampling.LANCZOS)
                
                ico_file = self.project_root / "LOGO_FOGEHI.ico"
                img.save(ico_file, format='ICO')
                
                print(f"   ✅ تم إنشاء {ico_file}")
                return ico_file
            else:
                print("   ⚠️ لم يتم العثور على ملف الشعار")
                return None
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الأيقونة: {e}")
            return None
    
    def backup_data(self):
        """نسخ احتياطي للبيانات"""
        print("💾 إنشاء نسخة احتياطية للبيانات...")
        
        try:
            # نسخ قاعدة البيانات
            db_file = self.project_root / "data" / "proshipment.db"
            if db_file.exists():
                backup_file = self.project_root / "data" / f"proshipment_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                shutil.copy2(db_file, backup_file)
                print(f"   ✅ تم نسخ قاعدة البيانات إلى {backup_file}")
            
            # نسخ المرفقات
            attachments_dir = self.project_root / "attachments"
            if attachments_dir.exists():
                backup_attachments = self.project_root / "data" / "attachments_backup"
                if backup_attachments.exists():
                    shutil.rmtree(backup_attachments)
                shutil.copytree(attachments_dir, backup_attachments)
                print(f"   ✅ تم نسخ المرفقات إلى {backup_attachments}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في النسخ الاحتياطي: {e}")
            return False
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("🔨 بناء الملف التنفيذي...")
        
        try:
            # إنشاء ملف spec
            spec_file = self.create_pyinstaller_spec()
            
            # تشغيل PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                str(spec_file)
            ]
            
            print(f"   🔄 تشغيل: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ تم بناء الملف التنفيذي بنجاح")
                return True
            else:
                print(f"   ❌ فشل في البناء:")
                print(f"   خطأ: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في البناء: {e}")
            return False
    
    def create_installer_package(self):
        """إنشاء حزمة التثبيت"""
        print("📦 إنشاء حزمة التثبيت...")
        
        try:
            # مجلد التطبيق المبني
            app_dir = self.dist_dir / self.app_name
            
            if not app_dir.exists():
                print(f"   ❌ مجلد التطبيق غير موجود: {app_dir}")
                return False
            
            # إنشاء مجلد التثبيت
            installer_app_dir = self.installer_dir / self.app_name
            if installer_app_dir.exists():
                shutil.rmtree(installer_app_dir)
            
            # نسخ التطبيق
            shutil.copytree(app_dir, installer_app_dir)
            
            # إضافة ملفات إضافية
            additional_files = [
                ("README.md", "دليل_التشغيل.md"),
                ("CHANGELOG.md", "سجل_التغييرات.md"),
                ("requirements.txt", "متطلبات_النظام.txt")
            ]
            
            for src, dst in additional_files:
                src_file = self.project_root / src
                if src_file.exists():
                    shutil.copy2(src_file, installer_app_dir / dst)
            
            # إنشاء ملف معلومات التثبيت
            self.create_installer_info(installer_app_dir)
            
            # إنشاء ملف تشغيل
            self.create_run_script(installer_app_dir)
            
            # ضغط الحزمة
            archive_name = f"{self.app_name}-V{self.version}-{datetime.now().strftime('%Y-%m-%d')}"
            
            # ZIP
            zip_file = self.installer_dir / f"{archive_name}.zip"
            shutil.make_archive(str(zip_file.with_suffix('')), 'zip', self.installer_dir, self.app_name)
            
            # TAR.GZ
            tar_file = self.installer_dir / f"{archive_name}.tar.gz"
            shutil.make_archive(str(tar_file.with_suffix('').with_suffix('')), 'gztar', self.installer_dir, self.app_name)
            
            print(f"   ✅ تم إنشاء: {zip_file}")
            print(f"   ✅ تم إنشاء: {tar_file}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الحزمة: {e}")
            return False
    
    def create_installer_info(self, installer_dir):
        """إنشاء ملف معلومات التثبيت"""
        info = {
            "name": self.app_name,
            "version": self.version,
            "build_date": datetime.now().isoformat(),
            "description": "نظام إدارة الشحنات والحوالات المتكامل",
            "requirements": {
                "os": "Windows 10/11",
                "ram": "4 GB",
                "storage": "2 GB",
                "display": "1024x768 أو أعلى"
            },
            "features": [
                "إدارة الشحنات والحاويات",
                "نظام الحوالات المتقدم",
                "إدارة الموردين والأصناف",
                "تقارير PDF احترافية",
                "دعم قواعد البيانات المتعددة",
                "واجهة مستخدم متجاوبة",
                "نظام المرفقات المتقدم"
            ],
            "installation": {
                "steps": [
                    "فك ضغط الملف",
                    "تشغيل ProShipment.exe",
                    "اتباع معالج الإعداد الأولي"
                ]
            }
        }
        
        info_file = installer_dir / "معلومات_التثبيت.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)
    
    def create_run_script(self, installer_dir):
        """إنشاء ملف تشغيل"""
        # ملف تشغيل Windows
        bat_content = f'''@echo off
chcp 65001 > nul
title {self.app_name} V{self.version}
echo.
echo ===================================
echo   {self.app_name} V{self.version}
echo   نظام إدارة الشحنات والحوالات
echo ===================================
echo.
echo جاري تشغيل التطبيق...
echo.
start "" "{self.app_name}.exe"
'''
        
        bat_file = installer_dir / "تشغيل_التطبيق.bat"
        with open(bat_file, 'w', encoding='utf-8') as f:
            f.write(bat_content)
    
    def create_documentation(self):
        """إنشاء الوثائق"""
        print("📚 إنشاء الوثائق...")
        
        try:
            docs_dir = self.installer_dir / self.app_name / "الوثائق"
            docs_dir.mkdir(exist_ok=True)
            
            # نسخ الوثائق الموجودة
            source_docs = self.project_root / "docs"
            if source_docs.exists():
                for doc_file in source_docs.glob("*.md"):
                    shutil.copy2(doc_file, docs_dir)
            
            print("   ✅ تم نسخ الوثائق")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الوثائق: {e}")
            return False
    
    def generate_checksums(self):
        """إنشاء checksums للملفات"""
        print("🔐 إنشاء checksums...")
        
        try:
            import hashlib
            
            checksums = {}
            
            for file in self.installer_dir.glob("*.zip"):
                with open(file, 'rb') as f:
                    content = f.read()
                    checksums[file.name] = {
                        "md5": hashlib.md5(content).hexdigest(),
                        "sha256": hashlib.sha256(content).hexdigest()
                    }
            
            for file in self.installer_dir.glob("*.tar.gz"):
                with open(file, 'rb') as f:
                    content = f.read()
                    checksums[file.name] = {
                        "md5": hashlib.md5(content).hexdigest(),
                        "sha256": hashlib.sha256(content).hexdigest()
                    }
            
            checksums_file = self.installer_dir / "checksums.json"
            with open(checksums_file, 'w', encoding='utf-8') as f:
                json.dump(checksums, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ تم إنشاء {checksums_file}")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء checksums: {e}")
            return False

    def create_release_notes(self):
        """إنشاء ملاحظات الإصدار"""
        print("📋 إنشاء ملاحظات الإصدار...")

        release_notes = f"""# ProShipment V{self.version} - ملاحظات الإصدار

## 📅 تاريخ الإصدار
{datetime.now().strftime('%Y-%m-%d')}

## 🎯 نظرة عامة
نظام إدارة الشحنات والحوالات المتكامل - الإصدار {self.version}

## ✨ الميزات الجديدة
- 🚢 نظام إدارة الشحنات المتقدم
- 💰 نظام الحوالات الشامل
- 📦 إدارة الموردين والأصناف
- 📄 تقارير PDF احترافية
- 🎨 واجهة مستخدم متجاوبة
- 📎 نظام المرفقات المتطور
- 🌐 دعم قواعد البيانات المتعددة
- 🔒 نظام أمان متقدم

## 🔧 متطلبات النظام
- **نظام التشغيل:** Windows 10/11
- **الذاكرة:** 4 GB RAM أو أكثر
- **التخزين:** 2 GB مساحة فارغة
- **الشاشة:** 1024x768 أو أعلى
- **اللغة:** دعم العربية والإنجليزية

## 📦 محتويات الحزمة
- `ProShipment.exe` - الملف التنفيذي الرئيسي
- `data/` - مجلد البيانات وقاعدة البيانات
- `config/` - ملفات الإعدادات
- `الوثائق/` - دليل المستخدم والوثائق
- `تشغيل_التطبيق.bat` - ملف تشغيل سريع

## 🚀 طريقة التثبيت
1. فك ضغط الملف المحمل
2. تشغيل `ProShipment.exe`
3. اتباع معالج الإعداد الأولي
4. بدء استخدام التطبيق

## 📚 الوثائق
- دليل المستخدم: `الوثائق/دليل_المستخدم.md`
- دليل التثبيت: `الوثائق/دليل_التثبيت.md`
- الأسئلة الشائعة: `الوثائق/الأسئلة_الشائعة.md`

## 🔄 النسخ الاحتياطي
يتم الاحتفاظ بالبيانات الموجودة تلقائياً في:
- `data/proshipment.db` - قاعدة البيانات الرئيسية
- `attachments/` - المرفقات والمستندات
- `config/` - إعدادات التطبيق

## 🆘 الدعم الفني
للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.proshipment.com
- الهاتف: +967-1-234567

## ⚠️ ملاحظات مهمة
- يُنصح بإنشاء نسخة احتياطية قبل التحديث
- التأكد من إغلاق جميع نوافذ التطبيق قبل التحديث
- مراجعة دليل المستخدم للميزات الجديدة

---
© 2024 ProShipment. جميع الحقوق محفوظة.
"""

        notes_file = self.installer_dir / "RELEASE_NOTES_V{}.md".format(self.version)
        with open(notes_file, 'w', encoding='utf-8') as f:
            f.write(release_notes)

        print(f"   ✅ تم إنشاء {notes_file}")
        return True

    def run_build(self):
        """تشغيل عملية البناء الكاملة"""
        print("🚀 بدء عملية بناء ملف التثبيت التنفيذي")
        print("="*60)

        steps = [
            ("إعداد المجلدات", self.setup_directories),
            ("فحص التبعيات", self.check_dependencies),
            ("إنشاء الأيقونة", self.create_icon),
            ("نسخ احتياطي للبيانات", self.backup_data),
            ("بناء الملف التنفيذي", self.build_executable),
            ("إنشاء حزمة التثبيت", self.create_installer_package),
            ("إنشاء الوثائق", self.create_documentation),
            ("إنشاء checksums", self.generate_checksums),
            ("إنشاء ملاحظات الإصدار", self.create_release_notes)
        ]

        failed_steps = []

        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            try:
                if not step_func():
                    failed_steps.append(step_name)
                    print(f"   ❌ فشل في: {step_name}")
                else:
                    print(f"   ✅ نجح: {step_name}")
            except Exception as e:
                failed_steps.append(step_name)
                print(f"   ❌ خطأ في {step_name}: {e}")

        # النتائج النهائية
        print("\n" + "="*60)
        print("📊 نتائج عملية البناء")
        print("="*60)

        if not failed_steps:
            print("🎉 تم إنشاء ملف التثبيت بنجاح!")
            print(f"📦 الملفات متاحة في: {self.installer_dir}")

            # عرض الملفات المنشأة
            print("\n📁 الملفات المنشأة:")
            for file in self.installer_dir.glob("*"):
                if file.is_file():
                    size = file.stat().st_size / (1024*1024)  # MB
                    print(f"   📄 {file.name} ({size:.1f} MB)")

            print(f"\n🚀 لتشغيل التطبيق:")
            print(f"   1. فك ضغط أحد الملفات المضغوطة")
            print(f"   2. تشغيل ProShipment.exe")
            print(f"   3. أو تشغيل تشغيل_التطبيق.bat")

        else:
            print(f"⚠️ فشل في {len(failed_steps)} خطوات:")
            for step in failed_steps:
                print(f"   ❌ {step}")
            print("\n🔧 يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")

        return len(failed_steps) == 0

def main():
    """الدالة الرئيسية"""
    print("🏗️ أداة إنشاء ملف التثبيت التنفيذي - ProShipment V2.0.0")
    print("="*60)

    installer = ProShipmentInstaller()
    success = installer.run_build()

    if success:
        print("\n🎊 تم إنشاء ملف التثبيت التنفيذي بنجاح!")
        print("📋 يمكن الآن توزيع التطبيق على أي جهاز ويندوز")
        print("💾 البيانات الموجودة محفوظة ومتضمنة في الحزمة")
    else:
        print("\n❌ فشل في إنشاء ملف التثبيت")
        print("🔧 يرجى مراجعة الأخطاء وإعادة المحاولة")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
