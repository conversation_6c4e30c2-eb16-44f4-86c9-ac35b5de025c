#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نموذج طباعة طلب الحوالة
Test Remittance Print Template
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_print_template_structure():
    """اختبار هيكل نموذج الطباعة"""
    
    print("🔍 اختبار هيكل نموذج الطباعة...")
    print("=" * 60)
    
    try:
        # قراءة ملف نموذج الطباعة
        template_path = "src/ui/remittances/remittance_print_template.py"
        with open(template_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص العناصر الأساسية
        essential_elements = [
            ("class RemittancePrintTemplate", "فئة نموذج الطباعة"),
            ("create_header", "دالة إنشاء الرأس"),
            ("create_date_info", "دالة معلومات التاريخ"),
            ("create_parties_info", "دالة معلومات الأطراف"),
            ("create_main_content", "دالة المحتوى الرئيسي"),
            ("create_sender_info", "دالة معلومات المرسل"),
            ("create_purpose_info", "دالة الغرض من التحويل"),
            ("create_signature_section", "دالة قسم التوقيع"),
            ("populate_data", "دالة تعبئة البيانات"),
            ("print_preview", "دالة معاينة الطباعة"),
            ("print_document", "دالة الطباعة"),
            ("print_content", "دالة طباعة المحتوى")
        ]
        
        print("   📋 فحص العناصر الأساسية:")
        all_found = True
        
        for element, description in essential_elements:
            if element in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        # فحص النصوص العربية المطلوبة
        arabic_texts = [
            "شركة الفقيهي للتجارة والتموينات المحدودة",
            "تحية طيبة وبعد",
            "وذلك للحصول على العنوان التالي",
            "اسم المستفيد والعنوان ورقم الحساب",
            "اسم البنك المستفيد والعنوان والسويفت كود",
            "اسم الشركة المرسلة والعنوان",
            "الغرض من التحويل",
            "مرفق لكم صورة التحويل من البنك كمرجع",
            "ونشكركم مقدماً على حسن تعاونكم",
            "المدير العام",
            "نشأت رشاد قاسم الدبعي"
        ]
        
        print("\n   📝 فحص النصوص العربية:")
        for text in arabic_texts:
            if text in code:
                print(f"      ✅ '{text[:30]}...'")
            else:
                print(f"      ❌ '{text[:30]}...' - مفقود")
                all_found = False
        
        # فحص معلومات الشركة
        company_info = [
            "ALFOGEHI FOR TRADING AND CATERING LTD,CO",
            "ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD",
            "TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES",
            "NASHA'AT RASHAD QASIM ALDUBAEE",
            "+967 1 616109",
            "+967 1 615909",
            "+967 *********",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        print("\n   🏢 فحص معلومات الشركة:")
        for info in company_info:
            if info in code:
                print(f"      ✅ {info}")
            else:
                print(f"      ❌ {info} - مفقود")
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص نموذج الطباعة: {e}")
        return False

def test_print_integration():
    """اختبار تكامل الطباعة مع شاشة طلب الحوالة"""
    
    print("\n🔗 اختبار تكامل الطباعة...")
    print("=" * 60)
    
    try:
        # قراءة ملف شاشة طلب الحوالة
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص وجود زر الطباعة
        print("   📋 فحص زر الطباعة:")
        if "🖨️ طباعة النموذج" in code:
            print("      ✅ زر الطباعة موجود")
        else:
            print("      ❌ زر الطباعة مفقود")
            return False
        
        # فحص دالة الطباعة
        print("\n   📋 فحص دالة الطباعة:")
        if "def print_request_form" in code:
            print("      ✅ دالة print_request_form موجودة")
        else:
            print("      ❌ دالة print_request_form مفقودة")
            return False
        
        # فحص استيراد نموذج الطباعة
        if "from .remittance_print_template import RemittancePrintTemplate" in code:
            print("      ✅ استيراد نموذج الطباعة موجود")
        else:
            print("      ❌ استيراد نموذج الطباعة مفقود")
            return False
        
        # فحص ربط الزر بالدالة
        if "print_btn.clicked.connect(self.print_request_form)" in code:
            print("      ✅ ربط زر الطباعة بالدالة موجود")
        else:
            print("      ❌ ربط زر الطباعة بالدالة مفقود")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص التكامل: {e}")
        return False

def display_template_analysis():
    """عرض تحليل النموذج"""
    
    print("\n" + "=" * 80)
    print("📋 تحليل النموذج المرفق وتصميم نموذج الطباعة")
    print("=" * 80)
    
    print("\n🔍 تحليل النموذج الأصلي:")
    print("   📄 نوع المستند: خطاب رسمي لطلب تحويل مالي")
    print("   🏢 الشركة: شركة الفقيهي للتجارة والتموينات المحدودة")
    print("   📍 الموقع: صنعاء، اليمن")
    print("   🎯 الغرض: طلب تحويل مالي للصرافة")
    
    print("\n📋 العناصر المحددة:")
    print("   1. رأس الشركة (عربي وإنجليزي)")
    print("   2. الشعار في المنتصف")
    print("   3. رقم المستند والتاريخ")
    print("   4. معلومات الأطراف (الأخوة، للصرافة، المحترمون)")
    print("   5. تحية رسمية")
    print("   6. طلب التحويل")
    print("   7. معلومات المستفيد")
    print("   8. معلومات البنك")
    print("   9. معلومات المرسل")
    print("   10. الغرض من التحويل")
    print("   11. التوقيع والختم")
    
    print("\n🎨 التصميم المطبق:")
    print("   ✅ تخطيط مطابق للنموذج الأصلي")
    print("   ✅ نصوص عربية وإنجليزية")
    print("   ✅ معلومات الشركة كاملة")
    print("   ✅ تنسيق احترافي للطباعة")
    print("   ✅ دعم معاينة الطباعة")
    print("   ✅ تكامل مع شاشة طلب الحوالة")
    
    print("\n🖨️ ميزات الطباعة:")
    print("   ✅ معاينة قبل الطباعة")
    print("   ✅ طباعة مباشرة")
    print("   ✅ تنسيق A4")
    print("   ✅ جودة عالية")
    print("   ✅ تعبئة تلقائية للبيانات")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. فتح شاشة طلب الحوالة")
    print("   2. تعبئة البيانات المطلوبة")
    print("   3. الضغط على زر '🖨️ طباعة النموذج'")
    print("   4. اختيار معاينة أو طباعة مباشرة")
    print("   5. الحصول على نموذج مطابق للأصل")

if __name__ == "__main__":
    print("🚀 بدء اختبار نموذج طباعة طلب الحوالة...")
    print("=" * 80)
    
    # اختبار هيكل نموذج الطباعة
    template_success = test_print_template_structure()
    
    # اختبار تكامل الطباعة
    integration_success = test_print_integration()
    
    # عرض تحليل النموذج
    display_template_analysis()
    
    # النتيجة النهائية
    if template_success and integration_success:
        print("\n🏆 تم تصميم نموذج الطباعة بنجاح!")
        print("✅ هيكل النموذج مكتمل")
        print("✅ التكامل مع الشاشة ناجح")
        print("✅ جميع العناصر موجودة")
        print("✅ النصوص العربية والإنجليزية صحيحة")
        print("✅ معلومات الشركة كاملة")
        
        print("\n🎉 نموذج الطباعة جاهز للاستخدام!")
        print("💡 يمكنك الآن طباعة طلبات الحوالة بتنسيق احترافي")
        
    else:
        print("\n❌ هناك مشاكل في نموذج الطباعة")
        if not template_success:
            print("   - مشكلة في هيكل النموذج")
        if not integration_success:
            print("   - مشكلة في التكامل مع الشاشة")
    
    print("=" * 80)
