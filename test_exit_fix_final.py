#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧪 اختبار سريع للإصلاحات...")

try:
    # اختبار استيراد النافذة
    from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
    print("✅ تم استيراد نافذة طلب الحوالة")
    
    # اختبار وجود الدوال الجديدة
    window_class = RemittanceRequestWindow
    
    if hasattr(window_class, 'setup_form_change_tracking'):
        print("✅ دالة setup_form_change_tracking موجودة")
    else:
        print("❌ دالة setup_form_change_tracking غير موجودة")
    
    if hasattr(window_class, 'on_form_field_changed'):
        print("✅ دالة on_form_field_changed موجودة")
    else:
        print("❌ دالة on_form_field_changed غير موجودة")
    
    if hasattr(window_class, 'reset_form_modification_flag'):
        print("✅ دالة reset_form_modification_flag موجودة")
    else:
        print("❌ دالة reset_form_modification_flag غير موجودة")
    
    print("\n🎉 جميع الإصلاحات موجودة في الكود!")
    print("📋 ملخص الإصلاحات:")
    print("   🔧 تحسين دالة has_unsaved_changes()")
    print("   📊 إضافة متغير form_modified")
    print("   🎯 التحقق من التبويب الحالي")
    print("   📎 تحسين دمج نافذة المرفقات")
    print("   🔄 إعادة تعيين علامة التعديل")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
