#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات قاعدة بيانات Oracle
Oracle Database Configuration
"""

import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

class OracleDatabaseManager:
    """مدير قاعدة بيانات Oracle"""
    
    def __init__(self, connection_config: dict = None):
        """تهيئة مدير قاعدة بيانات Oracle"""
        
        # إعدادات الاتصال الافتراضية
        if connection_config is None:
            connection_config = {
                'host': 'localhost',
                'port': 1521,
                'service_name': 'XE',  # أو SID
                'username': 'proshipment',
                'password': 'your_password'
            }
        
        self.config = connection_config
        
        # إنشاء connection string لـ Oracle
        self.connection_string = self._build_connection_string()
        
        # إنشاء engine
        self.engine = create_engine(
            self.connection_string,
            echo=False,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,  # للتحقق من صحة الاتصال
            pool_recycle=3600    # إعادة تدوير الاتصالات كل ساعة
        )
        
        self.SessionLocal = sessionmaker(
            autocommit=False, 
            autoflush=False, 
            bind=self.engine
        )
    
    def _build_connection_string(self):
        """بناء connection string لـ Oracle"""
        
        # الطريقة الأولى - استخدام cx_Oracle
        # oracle+cx_oracle://username:password@host:port/?service_name=service_name
        
        # الطريقة الثانية - استخدام oracledb (الأحدث)
        # oracle+oracledb://username:password@host:port/?service_name=service_name
        
        connection_string = (
            f"oracle+cx_oracle://{self.config['username']}:"
            f"{self.config['password']}@"
            f"{self.config['host']}:{self.config['port']}/"
            f"?service_name={self.config['service_name']}"
        )
        
        return connection_string
    
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("SELECT 1 FROM DUAL"))
                row = result.fetchone()
                if row and row[0] == 1:
                    print("✅ تم الاتصال بقاعدة بيانات Oracle بنجاح")
                    return True
                else:
                    print("❌ فشل في اختبار الاتصال")
                    return False
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            # استيراد النماذج
            from src.database.models import Base
            
            # إنشاء جميع الجداول
            Base.metadata.create_all(bind=self.engine)
            
            # إدراج البيانات الأساسية
            self._insert_default_data()
            
            print("تم تهيئة قاعدة بيانات Oracle بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات"""
        return self.SessionLocal()
    
    def close_all_sessions(self):
        """إغلاق جميع الجلسات النشطة"""
        try:
            self.engine.dispose()
        except Exception as e:
            print(f"خطأ في إغلاق الجلسات: {e}")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        session = self.get_session()
        try:
            from src.database.models import SystemSettings, Currency, FiscalYear, Company
            from datetime import datetime
            
            # التحقق من وجود بيانات
            if session.query(SystemSettings).count() > 0:
                return
            
            # إعدادات النظام الافتراضية
            default_settings = [
                SystemSettings(
                    key="app_name",
                    value="نظام إدارة الشحنات",
                    description="اسم التطبيق",
                    category="general"
                ),
                SystemSettings(
                    key="database_type",
                    value="Oracle",
                    description="نوع قاعدة البيانات",
                    category="system"
                ),
                SystemSettings(
                    key="default_currency",
                    value="SAR",
                    description="العملة الافتراضية",
                    category="financial"
                )
            ]
            
            # العملات الافتراضية
            default_currencies = [
                Currency(
                    code="SAR",
                    name="ريال سعودي",
                    name_en="Saudi Riyal",
                    symbol="ر.س",
                    exchange_rate=1.0,
                    is_base=True
                ),
                Currency(
                    code="USD",
                    name="دولار أمريكي",
                    name_en="US Dollar",
                    symbol="$",
                    exchange_rate=3.75
                )
            ]
            
            # السنة المالية الحالية
            current_year = datetime.now().year
            fiscal_year = FiscalYear(
                year=current_year,
                start_date=datetime(current_year, 1, 1),
                end_date=datetime(current_year, 12, 31),
                is_current=True
            )
            
            # بيانات الشركة الافتراضية
            default_company = Company(
                name="شركة الشحنات المتقدمة",
                name_en="Advanced Shipping Company",
                address="الرياض، المملكة العربية السعودية",
                phone="+966-11-1234567",
                email="<EMAIL>"
            )
            
            # إضافة البيانات
            session.add_all(default_settings)
            session.add_all(default_currencies)
            session.add(fiscal_year)
            session.add(default_company)
            
            session.commit()
            print("تم إدراج البيانات الافتراضية في Oracle بنجاح")
            
        except Exception as e:
            session.rollback()
            print(f"خطأ في إدراج البيانات الافتراضية: {e}")
        finally:
            session.close()
    
    def backup_database(self, backup_path: str = None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_path is None:
                backup_dir = Path("backups")
                backup_dir.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_dir / f"oracle_backup_{timestamp}.sql"
            
            # استخدام Oracle Data Pump أو expdp
            # هذا مثال مبسط - في الواقع ستحتاج لاستخدام أدوات Oracle
            print(f"لإنشاء نسخة احتياطية من Oracle، استخدم:")
            print(f"expdp {self.config['username']}/{self.config['password']}@{self.config['host']}:{self.config['port']}/{self.config['service_name']} directory=backup_dir dumpfile={backup_path.name}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False

# مثال على الاستخدام
def create_oracle_manager():
    """إنشاء مدير قاعدة بيانات Oracle"""
    
    # إعدادات الاتصال
    oracle_config = {
        'host': 'localhost',        # عنوان الخادم
        'port': 1521,              # منفذ Oracle (افتراضي 1521)
        'service_name': 'XE',      # اسم الخدمة أو SID
        'username': 'proshipment', # اسم المستخدم
        'password': 'your_password' # كلمة المرور
    }
    
    # إنشاء مدير قاعدة البيانات
    oracle_manager = OracleDatabaseManager(oracle_config)
    
    # اختبار الاتصال
    if oracle_manager.test_connection():
        print("✅ جاهز للاستخدام مع Oracle")
        return oracle_manager
    else:
        print("❌ فشل في الاتصال بـ Oracle")
        return None

# دالة لتحويل التطبيق الحالي لاستخدام Oracle
def migrate_to_oracle():
    """تحويل التطبيق لاستخدام Oracle"""
    
    print("🔄 بدء عملية التحويل إلى Oracle...")
    
    # 1. إنشاء مدير Oracle
    oracle_manager = create_oracle_manager()
    if not oracle_manager:
        return False
    
    # 2. تهيئة قاعدة البيانات
    if not oracle_manager.initialize_database():
        return False
    
    # 3. نقل البيانات من SQLite (اختياري)
    # يمكن إضافة كود لنقل البيانات الموجودة
    
    print("✅ تم التحويل إلى Oracle بنجاح")
    return True

if __name__ == "__main__":
    # اختبار الاتصال
    oracle_manager = create_oracle_manager()
    
    if oracle_manager:
        # تهيئة قاعدة البيانات
        oracle_manager.initialize_database()
        
        # إغلاق الاتصالات
        oracle_manager.close_all_sessions()
