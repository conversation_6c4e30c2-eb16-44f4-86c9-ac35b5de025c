#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لتحسينات محاذاة التوقيع
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_signature_code_structure():
    """اختبار هيكل الكود للتوقيع"""
    print("📝 اختبار هيكل كود التوقيع...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي"),
            ("src/ui/remittances/remittance_pdf_generator.py", "مولد PDF")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن التحسينات الأساسية
            improvements_found = 0

            # للنماذج العادية
            if "manager_section" in content:
                print(f"      ✅ يحتوي على قسم المدير المنفصل")
                improvements_found += 1

            if "QVBoxLayout" in content and "المدير العام" in content:
                print(f"      ✅ يحتوي على تخطيط عمودي")
                improvements_found += 1

            if "border-radius" in content or "padding" in content:
                print(f"      ✅ يحتوي على تصميم محسن")
                improvements_found += 1

            # لمولد PDF
            if "signature_x" in content and "signature_y" in content:
                print(f"      ✅ يحتوي على إحداثيات محسنة (PDF)")
                improvements_found += 1

            if "rect(" in content and "المدير العام" in content:
                print(f"      ✅ يحتوي على إطار وعنوان (PDF)")
                improvements_found += 1

            if "drawCentredText" in content:
                print(f"      ✅ يحتوي على نص محاذي (PDF)")
                improvements_found += 1
            
            if improvements_found >= 2:
                print(f"      ✅ التحسينات مطبقة ({improvements_found} تحسينات)")
            else:
                print(f"      ❌ تحسينات قليلة ({improvements_found} تحسينات)")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل الكود: {e}")
        return False

def test_manager_title_structure():
    """اختبار هيكل عنوان المدير العام"""
    print("\n👔 اختبار هيكل عنوان المدير العام...")
    
    try:
        # فحص النموذج الأساسي
        with open("src/ui/remittances/remittance_print_template.py", 'r', encoding='utf-8') as f:
            basic_content = f.read()
        
        # البحث عن التحسينات المطلوبة
        if 'manager_label = QLabel("المدير العام")' in basic_content:
            print("   ✅ عنوان المدير العام موجود")
            
            if "setAlignment(Qt.AlignCenter)" in basic_content:
                print("   ✅ المحاذاة في الوسط")
            else:
                print("   ❌ المحاذاة غير صحيحة")
                return False
                
            if "manager_layout.addWidget(manager_label)" in basic_content:
                print("   ✅ العنوان في التخطيط العمودي")
            else:
                print("   ❌ العنوان ليس في التخطيط العمودي")
                return False
        else:
            print("   ❌ عنوان المدير العام غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عنوان المدير: {e}")
        return False

def test_signature_positioning():
    """اختبار موضع التوقيع"""
    print("\n✍️ اختبار موضع التوقيع...")
    
    try:
        # فحص النموذج المبسط
        with open("src/ui/remittances/simple_print_template.py", 'r', encoding='utf-8') as f:
            simple_content = f.read()
        
        # البحث عن التوقيع
        if 'signature_space = QLabel("نشأت رشاد قاسم الدبعي")' in simple_content:
            print("   ✅ اسم المدير في التوقيع موجود")
            
            if "manager_layout.addWidget(signature_space)" in simple_content:
                print("   ✅ التوقيع في التخطيط العمودي")
            else:
                print("   ❌ التوقيع ليس في التخطيط العمودي")
                return False
                
            if "border:" in simple_content and "padding:" in simple_content:
                print("   ✅ التوقيع له تصميم محسن")
            else:
                print("   ❌ التوقيع بدون تصميم محسن")
                return False
        else:
            print("   ❌ اسم المدير في التوقيع غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موضع التوقيع: {e}")
        return False

def test_pdf_improvements():
    """اختبار تحسينات PDF"""
    print("\n📄 اختبار تحسينات PDF...")
    
    try:
        with open("src/ui/remittances/remittance_pdf_generator.py", 'r', encoding='utf-8') as f:
            pdf_content = f.read()
        
        # البحث عن التحسينات
        improvements_found = 0
        
        if "signature_x = self.page_width - self.margin - 80*mm" in pdf_content:
            print("   ✅ إحداثيات التوقيع محسنة")
            improvements_found += 1
        
        if "c.rect(signature_x, signature_y, signature_width, signature_height" in pdf_content:
            print("   ✅ إطار التوقيع موجود")
            improvements_found += 1
        
        if "drawCentredText" in pdf_content:
            print("   ✅ النص محاذي في الوسط")
            improvements_found += 1
        
        if 'manager_title = self.reshape_arabic_text("المدير العام")' in pdf_content:
            print("   ✅ عنوان المدير العام في الأعلى")
            improvements_found += 1
        
        if improvements_found >= 3:
            print(f"   ✅ تحسينات PDF مطبقة ({improvements_found}/4)")
            return True
        else:
            print(f"   ❌ تحسينات PDF قليلة ({improvements_found}/4)")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        return False

def test_professional_template():
    """اختبار النموذج الاحترافي"""
    print("\n🌟 اختبار النموذج الاحترافي...")
    
    try:
        with open("src/ui/remittances/professional_print_template.py", 'r', encoding='utf-8') as f:
            pro_content = f.read()
        
        # البحث عن التحسينات المتقدمة
        advanced_features = 0
        
        if "qlineargradient" in pro_content:
            print("   ✅ تدرج لوني احترافي")
            advanced_features += 1
        
        if "border-radius: 15px" in pro_content:
            print("   ✅ زوايا مدورة متقدمة")
            advanced_features += 1
        
        if "min-width: 280px" in pro_content:
            print("   ✅ عرض محدد للتوقيع")
            advanced_features += 1
        
        if "rgba(255, 255, 255, 0.95)" in pro_content:
            print("   ✅ شفافية احترافية")
            advanced_features += 1
        
        if advanced_features >= 3:
            print(f"   ✅ النموذج الاحترافي محسن ({advanced_features}/4)")
            return True
        else:
            print(f"   ❌ النموذج الاحترافي يحتاج تحسين ({advanced_features}/4)")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج الاحترافي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات محاذاة التوقيع والمدير العام...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_signature_code_structure())
    results.append(test_manager_title_structure())
    results.append(test_signature_positioning())
    results.append(test_pdf_improvements())
    results.append(test_professional_template())
    
    # عرض النتائج النهائية
    print("\n" + "="*80)
    print("🎯 ملخص اختبار تحسينات محاذاة التوقيع:")
    print("="*80)
    
    test_names = [
        "هيكل كود التوقيع",
        "هيكل عنوان المدير العام",
        "موضع التوقيع",
        "تحسينات PDF",
        "النموذج الاحترافي"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع تحسينات محاذاة التوقيع تعمل بنجاح!")
        print("✅ المدير العام في الأعلى والتوقيع في الأسفل")
        print("✅ محاذاة احترافية في جميع النماذج")
        print("✅ تصميم مرئي محسن مع إطارات وألوان")
        print("✅ تحسينات PDF مع إطار التوقيع")
        print("✅ النموذج الاحترافي بتصميم متقدم")
        
        print("\n🌟 التحسينات المطبقة:")
        print("   📝 هيكل عمودي: المدير العام أعلى + التوقيع أسفل")
        print("   🎨 تصميم احترافي: إطارات، ألوان، تدرجات")
        print("   📐 محاذاة مثالية: وسط الصفحة")
        print("   📄 PDF محسن: إطار التوقيع مع تقسيم واضح")
        print("   🌟 نموذج احترافي: تصميم متقدم مع شفافية")
        
    elif successful_tests >= len(results) * 0.8:
        print("\n✅ معظم تحسينات محاذاة التوقيع تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحسينات فشلت. يرجى مراجعة:")
        print("- هيكل قسم التوقيع")
        print("- موضع المدير العام")
        print("- التصميم المرئي")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
