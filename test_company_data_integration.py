#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تكامل بيانات الشركة من قاعدة البيانات مع مولد PDF
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_company_data_loading():
    """اختبار تحميل بيانات الشركة من قاعدة البيانات"""
    print("🏢 اختبار تحميل بيانات الشركة...")
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import Company
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # التحقق من وجود بيانات الشركة
        session = db_manager.get_session()
        try:
            companies = session.query(Company).filter(Company.is_active == True).all()
            
            if companies:
                print(f"✅ تم العثور على {len(companies)} شركة نشطة:")
                for company in companies:
                    print(f"   • {company.name} ({company.name_en})")
                    print(f"     العنوان: {company.address}")
                    print(f"     الهاتف: {company.phone}")
                    print(f"     البريد: {company.email}")
                    if company.logo_path:
                        print(f"     الشعار: {company.logo_path}")
                    print()
                return True
            else:
                print("⚠️ لا توجد شركات نشطة في قاعدة البيانات")
                return False
                
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات الشركة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_generator_with_db():
    """اختبار مولد PDF مع بيانات الشركة من قاعدة البيانات"""
    print("\n📄 اختبار مولد PDF مع بيانات الشركة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # التحقق من تحميل بيانات الشركة
        if pdf_generator.company_data:
            print("✅ تم تحميل بيانات الشركة في مولد PDF:")
            for key, value in pdf_generator.company_data.items():
                if value:
                    print(f"   {key}: {value}")
        else:
            print("❌ لم يتم تحميل بيانات الشركة")
            return False
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-DB-TEST',
            'request_date': '2024/01/03',
            'remittance_amount': '50,000',
            'currency': 'SAR',
            'transfer_purpose': 'BUSINESS TRANSACTION',
            'exchanger': 'شركة الصرافة التجريبية',
            
            # بيانات المستفيد
            'receiver_name': 'TEST TRADING COMPANY LIMITED',
            'receiver_address': 'TEST ADDRESS, BUSINESS DISTRICT',
            'receiver_city': 'RIYADH',
            'receiver_phone': '+966 11 1234567',
            'receiver_account': '1234567890123456789',
            'receiver_country': 'SAUDI ARABIA',
            
            # بيانات البنك
            'receiver_bank': 'SAUDI NATIONAL BANK',
            'receiver_bank_branch': 'RIYADH MAIN BRANCH',
            'receiver_bank_address': 'KING FAHD ROAD, RIYADH',
            'receiver_swift': 'NCBKSARI',
            
            # بيانات المرسل (ستأتي من قاعدة البيانات)
            'manager_name': 'مدير النظام'
        }
        
        # إنشاء ملف PDF
        output_path = "نموذج_بيانات_الشركة_من_قاعدة_البيانات.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        print(f"✅ تم إنشاء النموذج: {result_path}")
        
        # التحقق من وجود الملف
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"📄 حجم الملف: {file_size} بايت")
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مولد PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logo_integration():
    """اختبار تكامل الشعار من قاعدة البيانات"""
    print("\n🖼️ اختبار تكامل الشعار...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        # اختبار دالة الحصول على مسار الشعار
        logo_path = pdf_generator.get_company_logo_path()
        
        if logo_path:
            print(f"✅ تم العثور على شعار: {logo_path}")
            
            # التحقق من وجود الملف
            if os.path.exists(logo_path):
                file_size = os.path.getsize(logo_path)
                print(f"   📄 حجم الملف: {file_size} بايت")
                
                # محاولة فتح الصورة
                try:
                    from PIL import Image
                    with Image.open(logo_path) as img:
                        width, height = img.size
                        print(f"   📏 الأبعاد: {width}x{height}")
                        print(f"   🎨 النمط: {img.mode}")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في فتح الصورة: {e}")
                    
                return True
            else:
                print(f"❌ الملف غير موجود: {logo_path}")
                return False
        else:
            print("⚠️ لم يتم العثور على شعار، سيتم استخدام الشعار النصي")
            return True  # ليس خطأ، فقط لا يوجد شعار
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الشعار: {e}")
        return False

def test_company_initials():
    """اختبار دالة الحصول على الأحرف الأولى"""
    print("\n🔤 اختبار الأحرف الأولى للشركة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        # اختبار دالة الأحرف الأولى
        initials = pdf_generator.get_company_initials()
        
        print(f"✅ الأحرف الأولى للشركة: {initials}")
        
        if pdf_generator.company_data:
            company_name = pdf_generator.company_data['name_en']
            print(f"   من اسم الشركة: {company_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأحرف الأولى: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔗 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            # اختبار بسيط للاتصال
            result = session.execute("SELECT 1").fetchone()
            if result:
                print("✅ الاتصال بقاعدة البيانات يعمل")
                return True
            else:
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
                
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تكامل بيانات الشركة مع مولد PDF...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_database_connection())
    results.append(test_company_data_loading())
    results.append(test_pdf_generator_with_db())
    results.append(test_logo_integration())
    results.append(test_company_initials())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار تكامل بيانات الشركة:")
    print("="*60)
    
    test_names = [
        "الاتصال بقاعدة البيانات",
        "تحميل بيانات الشركة",
        "مولد PDF مع بيانات الشركة",
        "تكامل الشعار",
        "الأحرف الأولى للشركة"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تكامل بيانات الشركة يعمل بنجاح!")
        print("✅ بيانات الشركة تُحمل من قاعدة البيانات")
        print("✅ مولد PDF يستخدم البيانات الصحيحة")
        print("✅ الشعار يتكامل مع النظام")
        print("✅ جميع المكونات تعمل بتناغم")
        
        # عرض الملف المنشأ
        output_file = "نموذج_بيانات_الشركة_من_قاعدة_البيانات.pdf"
        if Path(output_file).exists():
            print(f"\n📁 الملف المنشأ: {output_file}")
            print("يمكنك فتح الملف لمراجعة بيانات الشركة من قاعدة البيانات")
            
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم الاختبارات نجحت!")
        print("تكامل بيانات الشركة يعمل مع بعض القيود")
    else:
        print("\n⚠️ عدة اختبارات فشلت. يرجى مراجعة:")
        print("- تهيئة قاعدة البيانات")
        print("- إضافة بيانات الشركة")
        print("- مسارات الاستيراد")
    
    return successful_tests >= len(results) * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
