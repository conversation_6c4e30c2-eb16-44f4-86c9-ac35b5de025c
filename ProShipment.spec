# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

block_cipher = None

# البيانات المطلوبة
added_files = [
    ('data', 'data'),
    ('config', 'config'),
    ('assets', 'assets'),
    ('src/resources', 'src/resources'),
    ('docs', 'docs'),
    ('LOGO_FOGEHI.png', '.'),
    ('LOGO_FOGEHI.jpg', '.'),
    ('README.md', '.'),
    ('CHANGELOG.md', '.'),
]

# المكتبات المخفية
hidden_imports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtPrintSupport',
    'PySide6.QtSvg',
    'SQLAlchemy',
    'sqlite3',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    'reportlab.platypus',
    'arabic_reshaper',
    'bidi',
    'openpyxl',
    'xlsxwriter',
    'requests',
    'beautifulsoup4',
    'PIL',
    'num2words',
    'qdarkstyle',
    'qtawesome',
    'qtstylish',
    'src.ui.main_window',
    'src.database.database_manager',
    'src.database.models',
    'src.ui.shipments',
    'src.ui.suppliers',
    'src.ui.remittances',
    'src.ui.dialogs',
    'src.ui.themes',
    'src.ui.responsive',
    'src.reports',
    'src.utils',
]

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ProShipment',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='LOGO_FOGEHI.ico' if Path('LOGO_FOGEHI.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ProShipment',
)
