#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للتحديثات الجديدة في النظام
"""

import sys
import sqlite3
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_updates():
    """اختبار تحديثات قاعدة البيانات"""
    print("🗄️ اختبار تحديثات قاعدة البيانات...")
    
    try:
        db_path = Path("data/proshipment.db")
        if not db_path.exists():
            print("❌ قاعدة البيانات غير موجودة")
            return False
            
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # إضافة العمود الجديد إذا لم يكن موجوداً
        try:
            cursor.execute("ALTER TABLE remittance_requests ADD COLUMN receiver_bank_country TEXT")
            print("✅ تم إضافة عمود receiver_bank_country")
        except sqlite3.OperationalError:
            print("✅ عمود receiver_bank_country موجود بالفعل")
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(remittance_requests)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'receiver_bank_country' in columns:
            print("✅ عمود receiver_bank_country متوفر في الجدول")
        else:
            print("❌ عمود receiver_bank_country غير موجود")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_pdf_without_borders():
    """اختبار مولد PDF بدون حدود"""
    print("\n📄 اختبار مولد PDF بدون حدود...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-NO-BORDERS',
            'request_date': '2024/01/03',
            'remittance_amount': '100,000',
            'currency': 'SAR',
            'transfer_purpose': 'NO BORDERS TEST',
            'exchanger': 'شركة الصرافة بدون حدود',
            
            # بيانات المستقبل مع الحقول الجديدة
            'receiver_name': 'NO BORDERS TEST COMPANY LIMITED',
            'receiver_address': 'TEST ADDRESS WITHOUT BORDERS',
            'receiver_city': 'RIYADH',
            'receiver_phone': '+966 11 7777777',
            'receiver_account': '7777777777777777777',
            'receiver_country': 'SAUDI ARABIA',
            'receiver_bank_country': 'UNITED ARAB EMIRATES',
            
            # بيانات البنك
            'receiver_bank': 'EMIRATES NBD BANK',
            'receiver_bank_branch': 'DUBAI MAIN BRANCH',
            'receiver_bank_address': 'SHEIKH ZAYED ROAD, DUBAI',
            'receiver_swift': 'EBILAEAD',
            
            'manager_name': 'مدير بدون حدود'
        }
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # إنشاء ملف PDF بدون حدود
        output_path = "نموذج_بدون_حدود_مع_تحديثات.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"✅ تم إنشاء النموذج بدون حدود: {result_path}")
            print(f"📄 حجم الملف: {file_size} بايت")
            print("✅ تم إزالة حدود الصفحة")
            print("✅ تم تحديث رأس النموذج مع بيانات الشركة الجديدة")
            return True
        else:
            print("❌ فشل في إنشاء النموذج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_company_data_updates():
    """اختبار تحديثات بيانات الشركة"""
    print("\n🏢 اختبار تحديثات بيانات الشركة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        if pdf_generator.company_data:
            print("✅ بيانات الشركة محملة:")
            
            # التحقق من الحقول الجديدة
            new_fields = ['address_en', 'fax']
            for field in new_fields:
                if field in pdf_generator.company_data:
                    value = pdf_generator.company_data[field]
                    if value:
                        print(f"   ✅ {field}: {value}")
                    else:
                        print(f"   ⚪ {field}: غير محدد (متوفر)")
                else:
                    print(f"   ❌ {field}: غير موجود")
            
            # التحقق من الحقول الأساسية
            basic_fields = ['name', 'name_en', 'address', 'phone', 'email']
            all_basic_present = True
            for field in basic_fields:
                if field not in pdf_generator.company_data or not pdf_generator.company_data[field]:
                    all_basic_present = False
                    break
            
            if all_basic_present:
                print("✅ جميع الحقول الأساسية متوفرة")
                return True
            else:
                print("⚠️ بعض الحقول الأساسية مفقودة")
                return False
        else:
            print("❌ لم يتم تحميل بيانات الشركة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار بيانات الشركة: {e}")
        return False

def test_font_size_adjustments():
    """اختبار تعديلات أحجام الخطوط"""
    print("\n🔤 اختبار تعديلات أحجام الخطوط...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-FONT-TEST',
            'request_date': '2024/01/03',
            'remittance_amount': '75,000',
            'currency': 'SAR',
            'transfer_purpose': 'FONT SIZE TEST',
            'exchanger': 'شركة الصرافة للخطوط',
            'receiver_name': 'FONT TEST COMPANY',
            'receiver_country': 'SAUDI ARABIA',
            'manager_name': 'مدير الخطوط'
        }
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # إنشاء ملف PDF مع الخطوط المحدثة
        output_path = "نموذج_خطوط_محدثة.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"✅ تم إنشاء النموذج مع الخطوط المحدثة: {result_path}")
            print(f"📄 حجم الملف: {file_size} بايت")
            print("✅ اسم الشركة العربي: 12pt")
            print("✅ اسم الشركة الإنجليزي: 10pt (مصغر لتجنب التداخل)")
            return True
        else:
            print("❌ فشل في إنشاء النموذج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الخطوط: {e}")
        return False

def test_new_fields_integration():
    """اختبار تكامل الحقول الجديدة"""
    print("\n🆕 اختبار تكامل الحقول الجديدة...")
    
    try:
        # بيانات اختبار مع الحقول الجديدة
        test_data = {
            'request_number': '2025-NEW-FIELDS',
            'request_date': '2024/01/03',
            'remittance_amount': '150,000',
            'currency': 'USD',
            'transfer_purpose': 'NEW FIELDS INTEGRATION TEST',
            'exchanger': 'شركة الصرافة للحقول الجديدة',
            
            # الحقول الجديدة
            'receiver_name': 'NEW FIELDS TEST COMPANY LIMITED',
            'receiver_country': 'UNITED STATES',  # حقل نصي الآن
            'receiver_bank_country': 'UNITED KINGDOM',  # حقل جديد
            'receiver_address': 'NEW FIELDS TEST ADDRESS',
            'receiver_bank': 'HSBC BANK',
            'receiver_swift': 'HBUKGB4B',
            
            'manager_name': 'مدير الحقول الجديدة'
        }
        
        print("✅ بيانات الاختبار تحتوي على:")
        print(f"   البلد (نصي): {test_data['receiver_country']}")
        print(f"   بلد البنك (جديد): {test_data['receiver_bank_country']}")
        
        # محاكاة حفظ البيانات
        required_fields = [
            'receiver_country', 'receiver_bank_country', 
            'receiver_name', 'receiver_address'
        ]
        
        all_fields_present = all(field in test_data for field in required_fields)
        
        if all_fields_present:
            print("✅ جميع الحقول الجديدة متوفرة في البيانات")
            print("✅ حقل البلد تم تحويله إلى نصي")
            print("✅ حقل بلد البنك تم إضافته")
            return True
        else:
            print("❌ بعض الحقول الجديدة مفقودة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقول الجديدة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار الشامل للتحديثات الجديدة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_database_updates())
    results.append(test_new_fields_integration())
    results.append(test_company_data_updates())
    results.append(test_font_size_adjustments())
    results.append(test_pdf_without_borders())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص الاختبار الشامل للتحديثات:")
    print("="*60)
    
    test_names = [
        "تحديثات قاعدة البيانات",
        "تكامل الحقول الجديدة",
        "تحديثات بيانات الشركة",
        "تعديلات أحجام الخطوط",
        "PDF بدون حدود"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع التحديثات تعمل بنجاح!")
        print("✅ قاعدة البيانات محدثة مع الحقول الجديدة")
        print("✅ حقل البلد تم تحويله إلى نصي")
        print("✅ حقل بلد البنك تم إضافته")
        print("✅ بيانات الشركة محدثة مع الحقول الجديدة")
        print("✅ أحجام الخطوط محسنة لتجنب التداخل")
        print("✅ حدود الصفحة تم إزالتها من PDF")
        
        # عرض الملفات المنشأة
        created_files = [
            "نموذج_بدون_حدود_مع_تحديثات.pdf",
            "نموذج_خطوط_محدثة.pdf"
        ]
        
        existing_files = [f for f in created_files if Path(f).exists()]
        if existing_files:
            print(f"\n📁 الملفات المنشأة:")
            for file in existing_files:
                print(f"   • {file}")
            print("يمكنك فتح الملفات لمراجعة التحديثات")
            
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم التحديثات تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحديثات فشلت. يرجى مراجعة:")
        print("- تهيئة قاعدة البيانات")
        print("- إعدادات الشركة")
        print("- ملفات واجهة المستخدم")
    
    return successful_tests >= len(results) * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
