# دليل دمج Oracle مع ProShipment V2.0.0

## 📋 جدول المحتويات

1. [مقدمة](#مقدمة)
2. [متطلبات النظام](#متطلبات-النظام)
3. [تثبيت Oracle Instant Client](#تثبيت-oracle-instant-client)
4. [إعداد قاعدة البيانات](#إعداد-قاعدة-البيانات)
5. [تكوين التطبيق](#تكوين-التطبيق)
6. [نقل البيانات](#نقل-البيانات)
7. [استكشاف الأخطاء](#استكشاف-الأخطاء)
8. [الأمان والحماية](#الأمان-والحماية)
9. [الصيانة والمراقبة](#الصيانة-والمراقبة)
10. [الأسئلة الشائعة](#الأسئلة-الشائعة)

---

## 🎯 مقدمة

يدعم ProShipment V2.0.0 الآن قواعد بيانات Oracle بالإضافة إلى SQLite، مما يوفر:

- **أداء عالي** للبيانات الكبيرة
- **دعم عدة مستخدمين** متزامنين
- **ميزات أمان متقدمة**
- **موثوقية مؤسسية**
- **قابلية التوسع**

### الميزات الجديدة:
- ✅ دعم Oracle Database 11g, 12c, 18c, 19c, 21c
- ✅ دعم Oracle Cloud Infrastructure (OCI)
- ✅ نقل البيانات التلقائي من SQLite
- ✅ إعدادات متقدمة للأداء والأمان
- ✅ واجهة مستخدم محدثة لإدارة قواعد البيانات

---

## 🖥️ متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **الذاكرة**: 4 GB RAM
- **المساحة**: 2 GB مساحة فارغة
- **Python**: 3.8 أو أحدث
- **Oracle**: Oracle Database 11g أو أحدث

### المستحسن:
- **الذاكرة**: 8 GB RAM أو أكثر
- **المساحة**: 10 GB مساحة فارغة
- **الشبكة**: اتصال مستقر بخادم Oracle

### المكتبات المطلوبة:
```bash
pip install cx_Oracle>=8.3.0
pip install SQLAlchemy>=1.4.0
pip install PySide6>=6.0.0
```

---

## 📦 تثبيت Oracle Instant Client

### Windows:

1. **تحميل Oracle Instant Client**:
   - اذهب إلى [Oracle Instant Client Downloads](https://www.oracle.com/database/technologies/instant-client/downloads.html)
   - حمل "Basic Package" و "SQL*Plus Package"
   - اختر الإصدار المناسب (64-bit مستحسن)

2. **التثبيت**:
   ```cmd
   # فك الضغط إلى مجلد مثل:
   C:\oracle\instantclient_21_8
   
   # إضافة إلى PATH
   set PATH=%PATH%;C:\oracle\instantclient_21_8
   ```

3. **تعيين متغيرات البيئة**:
   ```cmd
   set ORACLE_HOME=C:\oracle\instantclient_21_8
   set TNS_ADMIN=C:\oracle\instantclient_21_8\network\admin
   ```

### Linux:

1. **تثبيت باستخدام Package Manager**:
   ```bash
   # Ubuntu/Debian
   sudo apt-get install oracle-instantclient-basic
   sudo apt-get install oracle-instantclient-sqlplus
   
   # CentOS/RHEL
   sudo yum install oracle-instantclient-basic
   sudo yum install oracle-instantclient-sqlplus
   ```

2. **تعيين متغيرات البيئة**:
   ```bash
   export ORACLE_HOME=/usr/lib/oracle/21/client64
   export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH
   export PATH=$ORACLE_HOME/bin:$PATH
   ```

### macOS:

1. **تثبيت باستخدام Homebrew**:
   ```bash
   brew tap InstantClientTap/instantclient
   brew install instantclient-basic
   brew install instantclient-sqlplus
   ```

2. **تعيين متغيرات البيئة**:
   ```bash
   export ORACLE_HOME=/usr/local/lib/instantclient_21_8
   export DYLD_LIBRARY_PATH=$ORACLE_HOME:$DYLD_LIBRARY_PATH
   ```

---

## 🗄️ إعداد قاعدة البيانات

### إنشاء مستخدم Oracle:

```sql
-- الاتصال كـ SYSDBA
sqlplus sys/password@localhost:1521/XE as sysdba

-- إنشاء مستخدم جديد
CREATE USER proshipment IDENTIFIED BY "secure_password_123";

-- منح الصلاحيات
GRANT CONNECT, RESOURCE TO proshipment;
GRANT CREATE SESSION TO proshipment;
GRANT CREATE TABLE TO proshipment;
GRANT CREATE SEQUENCE TO proshipment;
GRANT CREATE VIEW TO proshipment;
GRANT CREATE PROCEDURE TO proshipment;

-- منح مساحة تخزين
ALTER USER proshipment QUOTA UNLIMITED ON USERS;

-- تأكيد الإنشاء
SELECT username FROM dba_users WHERE username = 'PROSHIPMENT';
```

### إعداد Tablespace (اختياري):

```sql
-- إنشاء tablespace مخصص
CREATE TABLESPACE proshipment_data
DATAFILE 'C:\oracle\oradata\XE\proshipment_data01.dbf'
SIZE 100M
AUTOEXTEND ON
NEXT 10M
MAXSIZE UNLIMITED;

-- تعيين tablespace افتراضي للمستخدم
ALTER USER proshipment DEFAULT TABLESPACE proshipment_data;
```

---

## ⚙️ تكوين التطبيق

### 1. استخدام معالج الإعداد:

```bash
# تشغيل معالج الإعداد التفاعلي
python tools/oracle_setup_wizard.py
```

### 2. الإعداد اليدوي:

#### أ. نسخ ملف الإعدادات:
```bash
# للتطوير
cp config/database_development.json config/database.json

# للإنتاج
cp config/database_production.json config/database.json
```

#### ب. تعديل الإعدادات:
```json
{
  "type": "oracle",
  "oracle_config": {
    "host": "localhost",
    "port": 1521,
    "service_name": "XE",
    "username": "proshipment",
    "password": "secure_password_123",
    "connection_type": "service_name",
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": true,
    "use_ssl": false
  }
}
```

### 3. استخدام متغيرات البيئة:

#### Windows:
```cmd
# تشغيل سكريبت الإعداد
scripts\setup_oracle_env.bat

# أو تعيين يدوي
set DATABASE_TYPE=oracle
set ORACLE_HOST=localhost
set ORACLE_PORT=1521
set ORACLE_SERVICE_NAME=XE
set ORACLE_USERNAME=proshipment
set ORACLE_PASSWORD=secure_password_123
```

#### Linux/macOS:
```bash
# تشغيل سكريبت الإعداد
source scripts/setup_oracle_env.sh

# أو تعيين يدوي
export DATABASE_TYPE=oracle
export ORACLE_HOST=localhost
export ORACLE_PORT=1521
export ORACLE_SERVICE_NAME=XE
export ORACLE_USERNAME=proshipment
export ORACLE_PASSWORD=secure_password_123
```

---

## 🔄 نقل البيانات

### 1. نقل تلقائي:

```bash
# تشغيل أداة النقل التفاعلية
python tools/data_migration_tool.py

# اختر "1" للنقل التفاعلي الكامل
```

### 2. نقل يدوي:

```python
from tools.data_migration_tool import DataMigrationTool
from src.database.oracle_config import OracleConfig

# إعداد أداة النقل
migration_tool = DataMigrationTool("data/proshipment.db")

# إعداد Oracle
oracle_config = OracleConfig(
    host="localhost",
    port=1521,
    service_name="XE",
    username="proshipment",
    password="secure_password_123"
)

# تشغيل النقل
if migration_tool.setup_connections(oracle_config):
    migration_tool.migrate_all_data()
    migration_tool.verify_migration()
```

### 3. نقل جداول محددة:

```python
# نقل جدول واحد
migration_tool.migrate_table_data("shipments")

# نقل عدة جداول
tables = ["items", "suppliers", "shipments"]
for table in tables:
    migration_tool.migrate_table_data(table)
```

---

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال الشائعة:

#### 1. خطأ "TNS:listener does not currently know of service":
```bash
# التحقق من حالة Oracle
lsnrctl status

# إعادة تشغيل Listener
lsnrctl stop
lsnrctl start

# التحقق من tnsnames.ora
cat $TNS_ADMIN/tnsnames.ora
```

#### 2. خطأ "ORA-12154: TNS:could not resolve the connect identifier":
```bash
# التحقق من متغيرات البيئة
echo $ORACLE_HOME
echo $TNS_ADMIN

# التحقق من ملف tnsnames.ora
ls -la $TNS_ADMIN/tnsnames.ora
```

#### 3. خطأ "cx_Oracle.DatabaseError: DPI-1047":
```bash
# تثبيت Oracle Instant Client
# التأكد من إضافة المسار إلى PATH/LD_LIBRARY_PATH
```

### أدوات التشخيص:

#### 1. فحص البيئة:
```bash
python tools/environment_checker.py
```

#### 2. اختبار الاتصال:
```python
from src.database.universal_database_manager import create_oracle_manager

manager = create_oracle_manager(
    host="localhost",
    port=1521,
    service_name="XE",
    username="proshipment",
    password="password"
)

if manager.test_connection():
    print("✅ الاتصال نجح")
else:
    print("❌ فشل الاتصال")
```

#### 3. مراقبة السجلات:
```bash
# عرض سجلات التطبيق
tail -f logs/migration.log

# عرض سجلات Oracle
tail -f $ORACLE_HOME/network/log/listener.log
```

---

## 🔒 الأمان والحماية

### 1. إعدادات SSL:

```json
{
  "oracle_config": {
    "use_ssl": true,
    "wallet_location": "/path/to/oracle/wallet",
    "ssl_cert_path": "/path/to/ssl/certificate.pem"
  }
}
```

### 2. تشفير كلمات المرور:

```bash
# استخدام متغيرات البيئة بدلاً من الملفات
export ORACLE_PASSWORD="$(echo 'password' | base64)"
```

### 3. صلاحيات محدودة:

```sql
-- منح صلاحيات محددة فقط
GRANT SELECT, INSERT, UPDATE, DELETE ON proshipment.* TO app_user;
REVOKE ALL ON sensitive_table FROM app_user;
```

### 4. مراقبة الوصول:

```sql
-- تفعيل Audit
AUDIT SELECT, INSERT, UPDATE, DELETE ON proshipment.shipments;

-- عرض سجل Audit
SELECT * FROM dba_audit_trail WHERE owner = 'PROSHIPMENT';
```

---

## 🔧 الصيانة والمراقبة

### 1. النسخ الاحتياطي:

```bash
# نسخ احتياطي كامل
expdp proshipment/password@localhost:1521/XE \
  directory=backup_dir \
  dumpfile=proshipment_backup.dmp \
  logfile=backup.log

# نسخ احتياطي للجداول المحددة
expdp proshipment/password@localhost:1521/XE \
  tables=shipments,items,suppliers \
  directory=backup_dir \
  dumpfile=tables_backup.dmp
```

### 2. مراقبة الأداء:

```sql
-- مراقبة الجلسات النشطة
SELECT username, status, machine, program 
FROM v$session 
WHERE username = 'PROSHIPMENT';

-- مراقبة استخدام المساحة
SELECT tablespace_name, 
       round(bytes/1024/1024) as size_mb,
       round(maxbytes/1024/1024) as max_size_mb
FROM dba_data_files;

-- مراقبة الاستعلامات البطيئة
SELECT sql_text, elapsed_time, executions
FROM v$sql 
WHERE parsing_schema_name = 'PROSHIPMENT'
ORDER BY elapsed_time DESC;
```

### 3. تحسين الأداء:

```sql
-- إنشاء فهارس
CREATE INDEX idx_shipments_date ON shipments(shipment_date);
CREATE INDEX idx_items_code ON items(item_code);

-- تحليل الإحصائيات
EXEC DBMS_STATS.GATHER_SCHEMA_STATS('PROSHIPMENT');

-- تحسين الجداول
ALTER TABLE shipments ENABLE ROW MOVEMENT;
ALTER TABLE shipments SHRINK SPACE;
```

---

## ❓ الأسئلة الشائعة

### س: هل يمكنني استخدام SQLite و Oracle معاً؟
ج: نعم، يمكنك التبديل بين قواعد البيانات من خلال إعدادات التطبيق.

### س: ما هو الحد الأقصى لحجم البيانات؟
ج: مع Oracle، لا يوجد حد عملي لحجم البيانات.

### س: هل يدعم التطبيق Oracle Cloud؟
ج: نعم، يدعم Oracle Cloud Infrastructure (OCI) بالكامل.

### س: كيف أنقل البيانات من SQLite إلى Oracle؟
ج: استخدم أداة النقل المدمجة: `python tools/data_migration_tool.py`

### س: هل يمكنني استخدام Oracle Express Edition (XE)؟
ج: نعم، Oracle XE مدعوم بالكامل ومناسب للاستخدام الصغير والمتوسط.

### س: ماذا لو فشل النقل؟
ج: الأداة تحتفظ بنسخة من SQLite، ويمكنك العودة إليها في أي وقت.

---

## 📞 الدعم الفني

للحصول على المساعدة:

1. **راجع السجلات**: `logs/migration.log`
2. **شغل فحص البيئة**: `python tools/environment_checker.py`
3. **تحقق من الإعدادات**: `config/database.json`
4. **اتصل بالدعم الفني**: <EMAIL>

---

## 📝 ملاحظات الإصدار

### الإصدار 2.0.0:
- ✅ دعم Oracle Database
- ✅ أداة نقل البيانات
- ✅ واجهة إعدادات محدثة
- ✅ تحسينات الأداء
- ✅ ميزات أمان متقدمة

---

*آخر تحديث: ديسمبر 2024*
*ProShipment V2.0.0 - نظام إدارة الشحنات المتقدم*
