# 💸 نظام إدارة حوالات الموردين الشامل والمتقدم

## 🎯 نظرة عامة

تم تطوير نظام شامل ومتقدم لإدارة حوالات الموردين يدعم التحويل لعدة موردين في الحوالة الواحدة، مع انتظار تأكيد استلام الحوالة من البنك الخارجي قبل الترحيل لحسابات الموردين.

## ✨ الميزات الرئيسية

### 1. **إدارة شاملة للحوالات**
- ✅ إنشاء حوالات جديدة بأرقام تلقائية
- ✅ دعم العملات المتعددة مع أسعار الصرف
- ✅ معلومات البنوك المرسلة والمستقبلة
- ✅ تتبع حالة الحوالة خطوة بخطوة
- ✅ أكواد SWIFT ومعلومات مصرفية كاملة

### 2. **دعم الموردين المتعددين**
- ✅ إضافة عدة موردين لحوالة واحدة
- ✅ مبالغ منفصلة لكل مورد
- ✅ معلومات مصرفية مستقلة لكل مورد
- ✅ أرقام فواتير وأغراض منفصلة
- ✅ حالات منفصلة لكل مورد

### 3. **انتظار تأكيد البنك الخارجي**
- ✅ حالات واضحة لتتبع الحوالة
- ✅ عدم ترحيل قبل تأكيد الوصول
- ✅ تواريخ دقيقة لكل مرحلة
- ✅ تأكيد استلام من البنك الخارجي

### 4. **ترحيل تلقائي للحسابات**
- ✅ ترحيل المبالغ لحسابات الموردين بعد التأكيد
- ✅ تحديث الأرصدة والإحصائيات
- ✅ تتبع تواريخ الترحيل
- ✅ حماية من الترحيل المكرر

## 🗄️ هيكل قاعدة البيانات

### 1. **جدول الحوالات الرئيسي (supplier_remittances)**
```sql
- id: المعرف الفريد
- remittance_number: رقم الحوالة (فريد)
- remittance_date: تاريخ الحوالة
- total_amount: المبلغ الإجمالي
- currency_id: العملة
- exchange_rate: سعر الصرف
- sender_bank_name: اسم البنك المرسل
- receiver_bank_name: اسم البنك المستقبل
- swift_code: كود SWIFT
- status: الحالة (مرسلة، في الطريق، وصلت للبنك، مؤكدة، مرحلة، ملغاة)
- sent_date: تاريخ الإرسال
- received_date: تاريخ الوصول للبنك
- confirmed_date: تاريخ التأكيد
- posted_date: تاريخ الترحيل
- reference_number: الرقم المرجعي
- purpose: الغرض من الحوالة
- fees_amount: رسوم التحويل
```

### 2. **جدول تفاصيل الحوالات (supplier_remittance_items)**
```sql
- id: المعرف الفريد
- remittance_id: معرف الحوالة
- supplier_id: معرف المورد
- amount: المبلغ المحول للمورد
- currency_id: العملة
- supplier_bank_name: اسم بنك المورد
- supplier_account_number: رقم حساب المورد
- supplier_swift_code: كود SWIFT للمورد
- purpose: الغرض من التحويل
- invoice_numbers: أرقام الفواتير
- status: حالة التحويل للمورد
- supplier_received_date: تاريخ وصول المبلغ للمورد
- supplier_confirmed_date: تاريخ تأكيد المورد
- posted_to_supplier_date: تاريخ الترحيل لحساب المورد
```

### 3. **جدول حسابات الموردين (supplier_accounts)**
```sql
- id: المعرف الفريد
- supplier_id: معرف المورد
- currency_id: العملة
- opening_balance: الرصيد الافتتاحي
- current_balance: الرصيد الحالي
- credit_limit: حد الائتمان
- total_purchases: إجمالي المشتريات
- total_payments: إجمالي المدفوعات
- total_remittances: إجمالي الحوالات
- last_transaction_date: تاريخ آخر معاملة
- last_remittance_date: تاريخ آخر حوالة
```

## 🔄 سير العمل (Workflow)

### 1. **إنشاء حوالة جديدة**
```
الحالة: "مرسلة"
- إدخال معلومات الحوالة الأساسية
- تحديد البنوك المرسلة والمستقبلة
- إنشاء رقم حوالة تلقائي
```

### 2. **إضافة الموردين**
```
- إضافة عدة موردين للحوالة الواحدة
- تحديد مبلغ منفصل لكل مورد
- إدخال معلومات مصرفية لكل مورد
- تحديد أرقام الفواتير والأغراض
```

### 3. **إرسال الحوالة**
```
الحالة: "في الطريق"
- تحديث تاريخ الإرسال
- إرسال الحوالة للبنك الخارجي
```

### 4. **وصول للبنك الخارجي**
```
الحالة: "وصلت للبنك"
- تأكيد وصول الحوالة للبنك الخارجي
- تحديث تاريخ الوصول
```

### 5. **تأكيد الاستلام**
```
الحالة: "مؤكدة"
- تأكيد استلام الحوالة من البنك الخارجي
- تحديث تاريخ التأكيد
- تحديث حالة جميع الموردين إلى "مؤكد"
```

### 6. **ترحيل للحسابات**
```
الحالة: "مرحلة"
- ترحيل المبالغ لحسابات الموردين
- تحديث أرصدة الحسابات
- تحديث الإحصائيات
- تحديث تاريخ الترحيل
```

## 🖥️ واجهة المستخدم

### 1. **التبويبات الرئيسية**

#### **تبويب إدارة الحوالات**
- **قائمة الحوالات**: عرض جميع الحوالات مع المرشحات
- **تفاصيل الحوالة**: معلومات الحوالة والبنوك
- **الموردين في الحوالة**: قائمة الموردين ومبالغهم
- **أزرار العمليات**: حفظ، تأكيد، ترحيل

#### **تبويب حسابات الموردين**
- **جدول الحسابات**: عرض جميع حسابات الموردين
- **الأرصدة**: الرصيد الحالي والافتتاحي
- **الإحصائيات**: إجمالي المشتريات والحوالات
- **حدود الائتمان**: إدارة حدود الائتمان

#### **تبويب التقارير**
- **تقرير يومي**: حوالات اليوم الحالي
- **تقرير شهري**: ملخص الشهر الحالي
- **تقرير حسب المورد**: إحصائيات كل مورد

### 2. **نوافذ الحوار**

#### **نافذة حوالة جديدة**
- إدخال معلومات الحوالة الأساسية
- اختيار العملة والبنوك
- تحديد الغرض والملاحظات

#### **نافذة إضافة مورد**
- اختيار المورد من القائمة
- تحديد المبلغ والعملة
- إدخال معلومات البنك
- تحديد أرقام الفواتير

## 🎛️ العمليات المتاحة

### 1. **عمليات الحوالات**
- ✅ إنشاء حوالة جديدة
- ✅ تعديل معلومات الحوالة
- ✅ إضافة موردين للحوالة
- ✅ إزالة موردين من الحوالة
- ✅ تأكيد وصول الحوالة
- ✅ ترحيل الحوالة للحسابات
- ✅ إلغاء الحوالة

### 2. **عمليات الحسابات**
- ✅ عرض حسابات الموردين
- ✅ تتبع الأرصدة والمعاملات
- ✅ إدارة حدود الائتمان
- ✅ عرض تاريخ المعاملات

### 3. **عمليات التقارير**
- ✅ تقارير يومية وشهرية
- ✅ تقارير حسب المورد
- ✅ إحصائيات مالية شاملة
- ✅ تصدير البيانات

## 🔒 الأمان والتحكم

### 1. **صلاحيات المستخدمين**
- تتبع من قام بإنشاء الحوالة
- تتبع من قام بتأكيد الحوالة
- تتبع من قام بترحيل الحوالة
- حماية من التعديل غير المصرح

### 2. **التحقق من البيانات**
- التحقق من صحة المبالغ
- التحقق من وجود الموردين
- التحقق من صحة العملات
- منع الترحيل المكرر

### 3. **تتبع العمليات**
- تواريخ دقيقة لكل عملية
- سجل كامل للتغييرات
- حالات واضحة للحوالات
- إمكانية التراجع عن العمليات

## 📁 الملفات المضافة

### 1. **نماذج قاعدة البيانات**
- `src/database/models.py` - إضافة نماذج الحوالات والحسابات
- `add_supplier_remittances_tables.py` - ملف migration للجداول

### 2. **واجهة المستخدم**
- `src/ui/suppliers/supplier_remittances_window.py` - النافذة الرئيسية
- `src/ui/suppliers/suppliers_window.py` - إضافة زر الحوالات

### 3. **ملفات الاختبار والتوثيق**
- `test_supplier_remittances_system.py` - اختبار شامل للنظام
- `SUPPLIER_REMITTANCES_SYSTEM_DOCUMENTATION.md` - هذا الملف

## 🚀 كيفية الاستخدام

### 1. **الوصول للنظام**
```
1. افتح التطبيق الرئيسي
2. اذهب إلى "إدارة الموردين"
3. انقر على "💸 إدارة حوالات الموردين"
```

### 2. **إنشاء حوالة جديدة**
```
1. انقر على "حوالة جديدة"
2. أدخل معلومات الحوالة
3. احفظ الحوالة
4. أضف الموردين المطلوبين
```

### 3. **تتبع الحوالة**
```
1. اختر الحوالة من القائمة
2. تابع الحالة الحالية
3. أكد وصول الحوالة عند الاستلام
4. رحل الحوالة للحسابات
```

## 🎉 الخلاصة

تم تطوير نظام شامل ومتقدم لإدارة حوالات الموردين يوفر:

✅ **دعم الموردين المتعددين** في الحوالة الواحدة  
✅ **انتظار تأكيد البنك الخارجي** قبل الترحيل  
✅ **ترحيل تلقائي للحسابات** بعد التأكيد  
✅ **تتبع دقيق للحالات** والتواريخ  
✅ **واجهة مستخدم متقدمة** وسهلة الاستخدام  
✅ **تقارير شاملة** وإحصائيات مفصلة  
✅ **أمان وتحكم** في العمليات  

النظام الآن جاهز للاستخدام ويوفر إدارة احترافية وشاملة لحوالات الموردين! 🚀
