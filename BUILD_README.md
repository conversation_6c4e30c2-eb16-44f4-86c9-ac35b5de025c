# دليل بناء ملف التثبيت التنفيذي - ProShipment V2.0.0

## 🎯 الهدف
إنشاء ملف تثبيت تنفيذي (.exe) لنظام ProShipment ليصبح جاهزاً للعمل على أي جهاز ويندوز مع الاحتفاظ بجميع البيانات الموجودة.

## 🚀 البدء السريع

### الطريقة الأسهل (موصى بها):
```cmd
create_installer.bat
```

هذا الملف سيقوم بكل شيء تلقائياً:
- ✅ فحص متطلبات النظام
- ✅ تحضير المشروع
- ✅ تثبيت التبعيات
- ✅ بناء الملف التنفيذي
- ✅ إنشاء حزمة التوزيع

## 📋 الملفات المطلوبة

### ملفات البناء الأساسية:
- `build_installer.py` - أداة البناء الرئيسية
- `prepare_for_build.py` - تحضير المشروع
- `requirements_build.txt` - متطلبات البناء
- `create_installer.bat` - ملف التشغيل الشامل

### ملفات المشروع المطلوبة:
- `main.py` - نقطة البداية
- `requirements.txt` - متطلبات التشغيل
- `src/` - الكود المصدري
- `data/` - البيانات وقاعدة البيانات
- `config/` - ملفات الإعدادات

## 🔧 البناء اليدوي (للمطورين)

### 1. تحضير المشروع:
```cmd
python prepare_for_build.py
```

### 2. تثبيت متطلبات البناء:
```cmd
pip install -r requirements_build.txt
```

### 3. بناء الملف التنفيذي:
```cmd
python build_installer.py
```

## 📦 النتائج المتوقعة

بعد اكتمال البناء، ستجد في مجلد `installer/`:

### الملفات المضغوطة:
- `ProShipment-V2.0.0-YYYY-MM-DD.zip`
- `ProShipment-V2.0.0-YYYY-MM-DD.tar.gz`

### ملفات إضافية:
- `checksums.json` - للتحقق من سلامة الملفات
- `RELEASE_NOTES_V2.0.0.md` - ملاحظات الإصدار

### محتويات الحزمة:
```
ProShipment/
├── ProShipment.exe              # الملف التنفيذي
├── data/                        # البيانات
│   ├── proshipment.db          # قاعدة البيانات
│   └── attachments_backup/     # المرفقات
├── config/                      # الإعدادات
├── الوثائق/                    # الوثائق
├── تشغيل_التطبيق.bat           # ملف تشغيل
└── معلومات_التثبيت.json       # معلومات التثبيت
```

## 💾 الاحتفاظ بالبيانات

### البيانات المحفوظة تلقائياً:
- ✅ **قاعدة البيانات:** `data/proshipment.db`
- ✅ **المرفقات:** `attachments/` → `data/attachments_backup/`
- ✅ **الإعدادات:** `config/`
- ✅ **السجلات:** `logs/`

### النسخ الاحتياطية:
- يتم إنشاء نسخة احتياطية تلقائياً قبل البناء
- النسخ محفوظة مع طابع زمني

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "Python not found"
```cmd
# تثبيت Python من الموقع الرسمي
https://www.python.org/downloads/
# تأكد من تحديد "Add Python to PATH"
```

#### "PyInstaller failed"
```cmd
# تثبيت PyInstaller يدوياً
pip install pyinstaller

# أو استخدام إصدار محدد
pip install pyinstaller==5.13.0
```

#### "Permission denied"
```cmd
# تشغيل Command Prompt كمدير
# إغلاق برامج مكافحة الفيروسات مؤقتاً
```

#### "Out of memory"
```cmd
# إغلاق البرامج الأخرى
# زيادة الذاكرة الافتراضية
# استخدام جهاز بمواصفات أعلى
```

#### "Missing modules"
```cmd
# تثبيت جميع المتطلبات
pip install -r requirements.txt
pip install -r requirements_build.txt
```

## 📊 معلومات الأداء

### الأحجام المتوقعة:
- **الملف التنفيذي:** ~200-300 MB
- **الحزمة الكاملة:** ~400-500 MB
- **بعد الفك:** ~800 MB - 1 GB

### الأوقات المتوقعة:
- **تحضير المشروع:** 1-2 دقيقة
- **تثبيت التبعيات:** 3-5 دقائق
- **البناء:** 10-30 دقيقة (حسب الجهاز)
- **الضغط:** 2-5 دقائق

### متطلبات الجهاز للبناء:
- **نظام التشغيل:** Windows 10/11
- **المعالج:** Intel i5 أو AMD Ryzen 5 (أو أعلى)
- **الذاكرة:** 8 GB RAM (16 GB مفضل)
- **التخزين:** 10 GB مساحة فارغة
- **الإنترنت:** لتحميل التبعيات

## 🔒 الأمان والجودة

### فحوصات الجودة:
- ✅ فحص هيكل المشروع
- ✅ التحقق من وجود جميع الملفات
- ✅ اختبار الاستيراد
- ✅ إنشاء checksums

### الأمان:
- ✅ نسخ احتياطية تلقائية
- ✅ التحقق من سلامة البيانات
- ✅ حماية من فقدان البيانات

## 📤 التوزيع

### للمستخدم النهائي:
1. **إرسال الملف المضغوط**
2. **فك الضغط في أي مكان**
3. **تشغيل ProShipment.exe**

### متطلبات جهاز المستخدم:
- **نظام التشغيل:** Windows 10/11
- **الذاكرة:** 4 GB RAM
- **التخزين:** 2 GB مساحة فارغة
- **الشاشة:** 1024x768 أو أعلى
- **لا يحتاج Python أو برامج إضافية**

## 🔄 التحديثات المستقبلية

### لإنشاء إصدار جديد:
1. تحديث رقم الإصدار في `build_installer.py`
2. تحديث `CHANGELOG.md`
3. تشغيل عملية البناء مرة أخرى

### الاحتفاظ بالبيانات عند التحديث:
- البيانات محفوظة في مجلد `data/`
- يمكن نسخها من الإصدار القديم للجديد

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:
1. **مراجعة هذا الدليل**
2. **فحص ملفات السجلات**
3. **التأكد من متطلبات النظام**
4. **إعادة تشغيل الجهاز والمحاولة مرة أخرى**

### معلومات مفيدة للدعم:
- إصدار Windows
- إصدار Python
- رسائل الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## ✅ قائمة التحقق النهائية

قبل التوزيع، تأكد من:
- [ ] تم بناء الملف التنفيذي بنجاح
- [ ] تم اختبار التشغيل على جهاز نظيف
- [ ] جميع البيانات محفوظة ومتضمنة
- [ ] الوثائق محدثة ومتضمنة
- [ ] تم إنشاء checksums
- [ ] تم اختبار فك الضغط والتشغيل

## 🎉 الخلاصة

باتباع هذا الدليل، ستحصل على:
- ✅ ملف تثبيت تنفيذي جاهز للتوزيع
- ✅ احتفاظ كامل بجميع البيانات
- ✅ سهولة التثبيت على أي جهاز ويندوز
- ✅ عدم الحاجة لتثبيت Python أو تبعيات

---

**ملاحظة:** هذا الدليل محدث للإصدار V2.0.0. للإصدارات المستقبلية، قد تحتاج بعض التفاصيل للتحديث.
