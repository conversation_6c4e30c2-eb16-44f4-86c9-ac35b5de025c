#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ظهور تسميات الحقول في تبويب دفتر العناوين
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_address_book_labels_visibility():
    """اختبار ظهور تسميات الحقول في دفتر العناوين"""
    print("🏷️ اختبار ظهور تسميات الحقول في دفتر العناوين...")
    
    try:
        from PySide6.QtWidgets import QApplication, QLabel
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # الانتقال إلى تبويب دفتر العناوين
        window.tab_widget.setCurrentIndex(3)
        
        # البحث عن التسميات في النافذة
        labels_found = []
        expected_labels = [
            "👤 الاسم الكامل:",
            "🏦 رقم الحساب:",
            "🏛️ اسم البنك:",
            "🏢 فرع البنك:",
            "🌍 البلد:",
            "🏦 بلد البنك:",
            "💳 رمز السويفت:",
            "📍 العنوان:"
        ]
        
        # البحث عن جميع QLabel في النافذة
        all_labels = window.findChildren(QLabel)
        
        for label in all_labels:
            label_text = label.text()
            if label_text in expected_labels:
                labels_found.append(label_text)
                
                # التحقق من خصائص التسمية
                style = label.styleSheet()
                is_visible = label.isVisible()
                
                print(f"   ✅ تسمية موجودة: {label_text}")
                print(f"      مرئية: {'نعم' if is_visible else 'لا'}")
                
                if "font-weight: bold" in style:
                    print(f"      النمط: خط عريض ✅")
                else:
                    print(f"      النمط: خط عادي ⚠️")
                
                if "color: #2c3e50" in style:
                    print(f"      اللون: محدد ✅")
                else:
                    print(f"      اللون: افتراضي ⚠️")
        
        # التحقق من وجود جميع التسميات المطلوبة
        missing_labels = [label for label in expected_labels if label not in labels_found]
        
        if missing_labels:
            print(f"\n❌ التسميات التالية مفقودة:")
            for label in missing_labels:
                print(f"   • {label}")
            return False
        else:
            print(f"\n✅ تم العثور على جميع التسميات ({len(labels_found)}/{len(expected_labels)})")
        
        # اختبار التفاعل مع الحقول
        print("\n🔍 اختبار التفاعل مع الحقول...")
        
        # ملء حقل الاسم واختبار التركيز
        if hasattr(window, 'ab_receiver_name_input'):
            window.ab_receiver_name_input.setText("اختبار التسمية")
            window.ab_receiver_name_input.setFocus()
            
            if window.ab_receiver_name_input.text() == "اختبار التسمية":
                print("   ✅ حقل الاسم يعمل ويمكن التفاعل معه")
            else:
                print("   ❌ حقل الاسم لا يعمل")
                return False
        
        # اختبار حقل آخر
        if hasattr(window, 'ab_receiver_bank_input'):
            window.ab_receiver_bank_input.setText("بنك الاختبار")
            
            if window.ab_receiver_bank_input.text() == "بنك الاختبار":
                print("   ✅ حقل البنك يعمل ويمكن التفاعل معه")
            else:
                print("   ❌ حقل البنك لا يعمل")
                return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التسميات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_layout_structure():
    """اختبار هيكل تخطيط النموذج"""
    print("\n📐 اختبار هيكل تخطيط النموذج...")
    
    try:
        from PySide6.QtWidgets import QApplication, QVBoxLayout, QHBoxLayout
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # الانتقال إلى تبويب دفتر العناوين
        window.tab_widget.setCurrentIndex(3)
        
        # البحث عن التخطيطات
        v_layouts = window.findChildren(QVBoxLayout)
        h_layouts = window.findChildren(QHBoxLayout)
        
        print(f"   📊 عدد التخطيطات العمودية: {len(v_layouts)}")
        print(f"   📊 عدد التخطيطات الأفقية: {len(h_layouts)}")
        
        # التحقق من وجود تخطيطات كافية للنموذج
        if len(v_layouts) >= 5 and len(h_layouts) >= 4:
            print("   ✅ هيكل التخطيط يبدو صحيحاً")
        else:
            print("   ⚠️ هيكل التخطيط قد يحتاج مراجعة")
        
        # اختبار المسافات
        print("\n📏 اختبار المسافات والتباعد...")
        
        # التحقق من وجود مسافات مناسبة
        form_found = False
        for layout in v_layouts:
            spacing = layout.spacing()
            if spacing >= 10:
                form_found = True
                print(f"   ✅ تباعد مناسب: {spacing}px")
                break
        
        if not form_found:
            print("   ⚠️ قد تحتاج المسافات إلى تحسين")
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التخطيط: {e}")
        return False

def test_visual_appearance():
    """اختبار المظهر البصري"""
    print("\n🎨 اختبار المظهر البصري...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # عرض النافذة لفترة قصيرة للاختبار البصري
        window.show()
        window.tab_widget.setCurrentIndex(3)  # تبويب دفتر العناوين
        
        # معالجة الأحداث
        app.processEvents()
        
        print("   ✅ تم عرض النافذة بنجاح")
        print("   ✅ تم الانتقال إلى تبويب دفتر العناوين")
        
        # التحقق من حجم النافذة
        size = window.size()
        print(f"   📐 حجم النافذة: {size.width()}×{size.height()}")
        
        # إغلاق النافذة
        window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المظهر: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تسميات حقول دفتر العناوين...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_address_book_labels_visibility())
    results.append(test_form_layout_structure())
    results.append(test_visual_appearance())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار تسميات دفتر العناوين:")
    print("="*60)
    
    test_names = [
        "ظهور تسميات الحقول",
        "هيكل تخطيط النموذج",
        "المظهر البصري"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تسميات الحقول تظهر بوضوح!")
        print("✅ جميع التسميات مرئية ومنسقة")
        print("✅ الخطوط عريضة وواضحة")
        print("✅ الألوان محددة ومناسبة")
        print("✅ التخطيط منظم ومتناسق")
        print("✅ الحقول تعمل بشكل صحيح")
        
        print("\n🎨 خصائص التسميات:")
        print("   🔤 خط عريض (font-weight: bold)")
        print("   🎨 لون داكن (#2c3e50)")
        print("   📏 حجم خط 12px")
        print("   📐 مسافة 5px تحت كل تسمية")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم التسميات تظهر بشكل صحيح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة مشاكل في ظهور التسميات. يرجى مراجعة:")
        print("- تنسيق CSS للتسميات")
        print("- هيكل التخطيط")
        print("- خصائص الرؤية")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
