#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات نافذة طلب الحوالة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_branch_combo_fix():
    """اختبار إصلاح مشكلة branch_combo"""
    print("🏢 اختبار إصلاح مشكلة branch_combo...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من وجود حقل الفرع
        if hasattr(window, 'branch_combo'):
            print("✅ حقل الفرع (branch_combo) موجود")
        else:
            print("❌ حقل الفرع (branch_combo) غير موجود")
            return False
        
        # اختبار ملء حقل الفرع
        window.branch_combo.addItem("فرع اختبار", "test_branch")
        window.branch_combo.setCurrentIndex(0)
        
        if window.branch_combo.currentText():
            print("✅ حقل الفرع يعمل بشكل صحيح")
        else:
            print("❌ حقل الفرع لا يعمل")
            return False
        
        # اختبار جمع البيانات
        try:
            data = window.collect_new_request_data()
            
            if 'branch' in data:
                print("✅ جمع بيانات الفرع يعمل")
            else:
                print("❌ جمع بيانات الفرع لا يعمل")
                return False
                
            if 'branch_id' in data:
                print("✅ جمع معرف الفرع يعمل")
            else:
                print("❌ جمع معرف الفرع لا يعمل")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في جمع البيانات: {e}")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار branch_combo: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_address_book_form():
    """اختبار نموذج دفتر العناوين المبسط"""
    print("\n📇 اختبار نموذج دفتر العناوين المبسط...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من وجود حقول دفتر العناوين
        address_book_fields = [
            'ab_receiver_name_input',
            'ab_receiver_account_input',
            'ab_receiver_bank_input',
            'ab_receiver_bank_branch_input',
            'ab_receiver_swift_input',
            'ab_receiver_country_input',
            'ab_receiver_bank_country_input',
            'ab_receiver_address_input'
        ]
        
        missing_fields = []
        for field in address_book_fields:
            if not hasattr(window, field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ الحقول التالية مفقودة: {', '.join(missing_fields)}")
            return False
        else:
            print("✅ جميع حقول دفتر العناوين موجودة")
        
        # اختبار ملء الحقول
        test_data = {
            'name': 'شركة الاختبار المحدودة',
            'account': '****************',
            'bank': 'البنك الأهلي السعودي',
            'branch': 'فرع الرياض الرئيسي',
            'swift': 'NCBKSARI',
            'country': 'المملكة العربية السعودية',
            'bank_country': 'المملكة العربية السعودية',
            'address': 'الرياض، طريق الملك فهد'
        }
        
        # ملء الحقول
        window.ab_receiver_name_input.setText(test_data['name'])
        window.ab_receiver_account_input.setText(test_data['account'])
        window.ab_receiver_bank_input.setText(test_data['bank'])
        window.ab_receiver_bank_branch_input.setText(test_data['branch'])
        window.ab_receiver_swift_input.setText(test_data['swift'])
        window.ab_receiver_country_input.setText(test_data['country'])
        window.ab_receiver_bank_country_input.setText(test_data['bank_country'])
        window.ab_receiver_address_input.setText(test_data['address'])
        
        # التحقق من القيم
        if window.ab_receiver_name_input.text() == test_data['name']:
            print("✅ ملء حقول دفتر العناوين يعمل")
        else:
            print("❌ ملء حقول دفتر العناوين لا يعمل")
            return False
        
        # اختبار مسح الحقول
        window.clear_address_book_form()
        
        if not window.ab_receiver_name_input.text():
            print("✅ مسح حقول دفتر العناوين يعمل")
        else:
            print("❌ مسح حقول دفتر العناوين لا يعمل")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دفتر العناوين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tab_navigation():
    """اختبار التنقل بين التبويبات"""
    print("\n📋 اختبار التنقل بين التبويبات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من عدد التبويبات
        tab_count = window.tab_widget.count()
        print(f"📊 عدد التبويبات: {tab_count}")
        
        if tab_count >= 4:
            print("✅ جميع التبويبات موجودة")
        else:
            print("❌ بعض التبويبات مفقودة")
            return False
        
        # اختبار الانتقال إلى تبويب دفتر العناوين
        try:
            window.tab_widget.setCurrentIndex(3)  # تبويب دفتر العناوين
            current_tab = window.tab_widget.currentIndex()
            
            if current_tab == 3:
                print("✅ الانتقال إلى تبويب دفتر العناوين يعمل")
            else:
                print("❌ الانتقال إلى تبويب دفتر العناوين لا يعمل")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الانتقال للتبويب: {e}")
            return False
        
        # اختبار زر دفتر العناوين
        try:
            window.open_address_book_tab()
            current_tab = window.tab_widget.currentIndex()
            
            if current_tab == 3:
                print("✅ زر دفتر العناوين يعمل")
            else:
                print("❌ زر دفتر العناوين لا يعمل")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في زر دفتر العناوين: {e}")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنقل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات نافذة طلب الحوالة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_branch_combo_fix())
    results.append(test_address_book_form())
    results.append(test_tab_navigation())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار إصلاحات نافذة طلب الحوالة:")
    print("="*60)
    
    test_names = [
        "إصلاح مشكلة branch_combo",
        "نموذج دفتر العناوين المبسط",
        "التنقل بين التبويبات"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("✅ حقل الفرع (branch_combo) تم إضافته ويعمل")
        print("✅ نموذج دفتر العناوين مبسط وواضح")
        print("✅ التنقل بين التبويبات يعمل بسلاسة")
        print("✅ جمع البيانات يعمل بدون أخطاء")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   🏢 إضافة حقل الفرع في النموذج الأساسي")
        print("   📇 تبسيط تخطيط دفتر العناوين")
        print("   🔄 تحديث دوال جمع البيانات")
        print("   📊 تحسين التنقل بين التبويبات")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم الإصلاحات تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة إصلاحات فشلت. يرجى مراجعة:")
        print("- إعدادات النافذة")
        print("- تخطيط النماذج")
        print("- دوال جمع البيانات")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
