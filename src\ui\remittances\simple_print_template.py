#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج طباعة مبسط لطلب الحوالة
Simple Print Template for Remittance Request
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QTextEdit, QFrame, QGridLayout,
                               QMessageBox, QApplication)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPainter
from datetime import datetime
import os

class SimplePrintTemplate(QWidget):
    """نموذج طباعة مبسط لطلب الحوالة"""
    
    def __init__(self, remittance_data=None):
        super().__init__()
        self.remittance_data = remittance_data or {}
        self.setup_ui()
        self.populate_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نموذج طباعة طلب الحوالة")
        self.setGeometry(100, 100, 800, 1000)
        
        layout = QVBoxLayout(self)
        
        # إنشاء منطقة المحتوى القابل للطباعة
        self.content_widget = QWidget()
        self.content_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                color: black;
                font-family: Arial;
            }
        """)
        
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setSpacing(15)
        content_layout.setContentsMargins(15, 40, 15, 40)  # تقليل الهوامش يمين ويسار من 40 إلى 15
        
        # رأس النموذج
        self.create_header(content_layout)
        
        # معلومات التاريخ والرقم
        self.create_date_info(content_layout)
        
        # خط فاصل
        self.add_separator(content_layout)
        
        # معلومات الأطراف
        self.create_parties_info(content_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(content_layout)
        
        # معلومات المرسل
        self.create_sender_info(content_layout)
        
        # الغرض من التحويل
        self.create_purpose_info(content_layout)
        
        # التوقيع والختم
        self.create_signature_section(content_layout)
        
        layout.addWidget(self.content_widget)
        
        # أزرار التحكم
        self.create_control_buttons(layout)
    
    def create_header(self, layout):
        """إنشاء رأس النموذج"""
        header_frame = QFrame()
        header_layout = QHBoxLayout(header_frame)
        
        # الجانب الأيسر - معلومات الشركة بالإنجليزية
        left_info = QLabel()
        left_info.setText("""
ALFOGEHI FOR TRADING AND CATERING LTD,CO
Sana'a –Algarda'a -24st.
Tel: 616109    Fax: 615909
        """.strip())
        left_info.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        left_info.setFont(QFont("Arial", 10))
        
        # المنتصف - الشعار
        logo_label = QLabel()
        logo_label.setText("🏢\nشركة الفقيهي")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFont(QFont("Arial", 16, QFont.Bold))
        logo_label.setFixedSize(120, 80)
        logo_label.setStyleSheet("border: 2px solid #333; border-radius: 10px; padding: 10px;")
        
        # الجانب الأيمن - معلومات الشركة بالعربية
        right_info = QLabel()
        right_info.setText("""
شركة الفقيهي للتجارة والتموينات المحدودة
صنعاء – الجراء – شارع24
تلفون: 616109    فاكس: 615909
        """.strip())
        right_info.setAlignment(Qt.AlignRight | Qt.AlignTop)
        right_info.setFont(QFont("Arial", 10))
        
        header_layout.addWidget(left_info, 1)
        header_layout.addWidget(logo_label, 0)
        header_layout.addWidget(right_info, 1)
        
        layout.addWidget(header_frame)
    
    def create_date_info(self, layout):
        """إنشاء معلومات التاريخ والرقم"""
        date_frame = QFrame()
        date_layout = QHBoxLayout(date_frame)
        
        # الرقم (يمين)
        self.number_label = QLabel("الرقم: ALF-2025-001")
        self.number_label.setAlignment(Qt.AlignRight)
        self.number_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        # التاريخ (يسار)
        self.date_label = QLabel(f"التاريخ: {datetime.now().strftime('%Y/%m/%d')}")
        self.date_label.setAlignment(Qt.AlignLeft)
        self.date_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        date_layout.addWidget(self.date_label)
        date_layout.addStretch()
        date_layout.addWidget(self.number_label)
        
        layout.addWidget(date_frame)
    
    def create_parties_info(self, layout):
        """إنشاء معلومات الأطراف"""
        parties_frame = QFrame()
        parties_layout = QHBoxLayout(parties_frame)

        # المحترمون (يسار)
        respected_label = QLabel("المحترمون")
        respected_label.setAlignment(Qt.AlignLeft)
        respected_label.setFont(QFont("Arial", 12))

        # للصرافة (وسط)
        exchange_label = QLabel("للصرافة")
        exchange_label.setAlignment(Qt.AlignCenter)
        exchange_label.setFont(QFont("Arial", 12))

        # الأخوة: شركة (يمين)
        self.brothers_label = QLabel("الأخوة: شركة")
        self.brothers_label.setAlignment(Qt.AlignRight)
        self.brothers_label.setFont(QFont("Arial", 12))

        parties_layout.addWidget(respected_label)
        parties_layout.addWidget(exchange_label)
        parties_layout.addWidget(self.brothers_label)

        layout.addWidget(parties_frame)
    
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        # تحية طيبة وبعد
        greeting = QLabel("تحية طيبة وبعد")
        greeting.setAlignment(Qt.AlignCenter)
        greeting.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(greeting)
        
        layout.addSpacing(20)

        # نص الطلب مع المبلغ
        self.request_text = QLabel()
        self.request_text.setAlignment(Qt.AlignRight)
        self.request_text.setFont(QFont("Arial", 12, QFont.Bold))
        self.request_text.setStyleSheet("color: #2c3e50; margin: 10px 0; padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6;")
        layout.addWidget(self.request_text)

        # وذلك للحصول على العنوان التالي
        purpose_text = QLabel("وذلك للحصول على العنوان التالي: -")
        purpose_text.setAlignment(Qt.AlignRight)
        purpose_text.setFont(QFont("Arial", 12))
        layout.addWidget(purpose_text)

        layout.addSpacing(15)

        # اسم المستفيد والعنوان ورقم الحساب
        beneficiary_text = QLabel("اسم المستفيد والعنوان ورقم الحساب: -")
        beneficiary_text.setAlignment(Qt.AlignRight)
        beneficiary_text.setFont(QFont("Arial", 12))
        layout.addWidget(beneficiary_text)

        # معلومات المستفيد (النص الإنجليزي - RTL)
        self.beneficiary_info = QLabel()
        self.beneficiary_info.setAlignment(Qt.AlignRight)  # محاذاة لليمين
        self.beneficiary_info.setFont(QFont("Arial", 11))
        self.beneficiary_info.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        self.beneficiary_info.setStyleSheet("margin-right: 20px; margin-top: 10px; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; text-align: right;")
        layout.addWidget(self.beneficiary_info)

        layout.addSpacing(15)

        # اسم البنك المستفيد والعنوان والسويفت كود
        bank_text = QLabel("اسم البنك المستفيد والعنوان والسويفت كود: -")
        bank_text.setAlignment(Qt.AlignRight)
        bank_text.setFont(QFont("Arial", 12))
        layout.addWidget(bank_text)

        # معلومات البنك (النص الإنجليزي - RTL)
        self.bank_info = QLabel()
        self.bank_info.setAlignment(Qt.AlignRight)  # محاذاة لليمين
        self.bank_info.setFont(QFont("Arial", 11))
        self.bank_info.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        self.bank_info.setStyleSheet("margin-right: 20px; margin-top: 10px; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; text-align: right;")
        layout.addWidget(self.bank_info)
    
    def create_sender_info(self, layout):
        """إنشاء معلومات المرسل"""
        layout.addSpacing(20)

        sender_text = QLabel("اسم الشركة المرسلة والعنوان: -")
        sender_text.setAlignment(Qt.AlignRight)
        sender_text.setFont(QFont("Arial", 12))
        layout.addWidget(sender_text)

        # معلومات الشركة المرسلة (النص الإنجليزي - RTL)
        sender_info = QLabel("""
ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD
TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN
G.M: - NASHA'AT RASHAD QASIM ALDUBAEE
BOX: - 1903    TEL: - +967 1 616109    FAX: - +967 1 615909
MOBILE: - +967 *********
EMAIL: -<EMAIL> , <EMAIL>
        """.strip())
        sender_info.setAlignment(Qt.AlignRight)  # محاذاة لليمين
        sender_info.setFont(QFont("Arial", 10))
        sender_info.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        sender_info.setStyleSheet("margin-right: 20px; margin-top: 10px; padding: 10px; background-color: #f0f8ff; border: 1px solid #4169e1; text-align: right;")
        layout.addWidget(sender_info)
    
    def create_purpose_info(self, layout):
        """إنشاء معلومات الغرض من التحويل"""
        layout.addSpacing(20)

        purpose_text = QLabel("الغرض من التحويل: -")
        purpose_text.setAlignment(Qt.AlignRight)
        purpose_text.setFont(QFont("Arial", 12))
        layout.addWidget(purpose_text)

        # النص الإنجليزي للغرض - RTL
        self.purpose_label = QLabel("COST OF FOODSTUFF.")
        self.purpose_label.setAlignment(Qt.AlignRight)  # محاذاة لليمين
        self.purpose_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.purpose_label.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        self.purpose_label.setStyleSheet("margin-right: 20px; margin-top: 10px; padding: 10px; background-color: #fff8dc; border: 1px solid #ffa500; text-align: right;")
        layout.addWidget(self.purpose_label)

        # مرفق لكم صورة التحويل من البنك كمرجع
        attachment_text = QLabel("مرفق لكم صورة التحويل من البنك كمرجع.")
        attachment_text.setAlignment(Qt.AlignRight)
        attachment_text.setFont(QFont("Arial", 11))
        attachment_text.setStyleSheet("margin-top: 15px;")
        layout.addWidget(attachment_text)

        # ونشكركم مقدماً على حسن تعاونكم
        thanks_text = QLabel("ونشكركم مقدماً على حسن تعاونكم .....")
        thanks_text.setAlignment(Qt.AlignRight)
        thanks_text.setFont(QFont("Arial", 11))
        layout.addWidget(thanks_text)
    
    def create_signature_section(self, layout):
        """إنشاء قسم التوقيع المحسن مع محاذاة احترافية"""
        layout.addSpacing(30)

        signature_frame = QFrame()
        signature_layout = QHBoxLayout(signature_frame)

        # وشكراً (يسار)
        thanks_label = QLabel("وشكراً")
        thanks_label.setAlignment(Qt.AlignLeft | Qt.AlignBottom)
        thanks_label.setFont(QFont("Arial", 12))

        # قسم المدير العام والتوقيع (يمين) - محاذاة احترافية
        manager_section = QFrame()
        manager_layout = QVBoxLayout(manager_section)
        manager_layout.setSpacing(12)
        manager_layout.setContentsMargins(0, 0, 0, 0)

        # المدير العام في الأعلى
        manager_label = QLabel("المدير العام")
        manager_label.setAlignment(Qt.AlignCenter)
        manager_label.setFont(QFont("Arial", 12, QFont.Bold))
        manager_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin-bottom: 8px;
                font-weight: bold;
            }
        """)

        # التوقيع في الأسفل
        signature_space = QLabel("نشأت رشاد قاسم الدبعي")
        signature_space.setAlignment(Qt.AlignCenter)
        signature_space.setFont(QFont("Arial", 11, QFont.Bold))
        signature_space.setLayoutDirection(Qt.RightToLeft)  # RTL للاسم
        signature_space.setStyleSheet("""
            QLabel {
                border: 2px dashed #3498db;
                border-radius: 8px;
                padding: 18px 25px;
                background-color: #ecf0f1;
                color: #2c3e50;
                min-width: 220px;
                margin-top: 8px;
                text-align: center;
            }
        """)

        manager_layout.addWidget(manager_label)
        manager_layout.addWidget(signature_space)

        signature_layout.addWidget(thanks_label)
        signature_layout.addStretch()
        signature_layout.addWidget(manager_section)

        layout.addWidget(signature_frame)
    
    def add_separator(self, layout):
        """إضافة خط فاصل"""
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("color: #333; margin: 10px 0;")
        layout.addWidget(line)
    
    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # زر حفظ كصورة
        save_btn = QPushButton("💾 حفظ كصورة")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        save_btn.clicked.connect(self.save_as_image)
        
        # زر طباعة (مبسط)
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #229954;
            }
        """)
        print_btn.clicked.connect(self.simple_print)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.close)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def populate_data(self):
        """تعبئة البيانات من طلب الحوالة"""
        if not self.remittance_data:
            return
        
        # تحديث رقم الطلب
        if 'request_number' in self.remittance_data:
            self.number_label.setText(f"الرقم: {self.remittance_data['request_number']}")
        
        # تحديث التاريخ
        if 'request_date' in self.remittance_data:
            self.date_label.setText(f"التاريخ: {self.remittance_data['request_date']}")
        
        # تحديث اسم الفرع/الصرافة
        if 'branch' in self.remittance_data:
            self.brothers_label.setText(f"الأخوة: {self.remittance_data['branch']}")
        
        # تحديث معلومات المستفيد
        beneficiary_text = f"""
Beneficiary name: - {self.remittance_data.get('receiver_name', '')}
Beneficiary address: - {self.remittance_data.get('receiver_address', '')}
Account number: - {self.remittance_data.get('receiver_account', '')}
        """.strip()
        self.beneficiary_info.setText(beneficiary_text)
        
        # تحديث معلومات البنك
        bank_country = self.remittance_data.get('receiver_bank_country', '') or self.remittance_data.get('receiver_country', '')
        bank_text = f"""
Bank: - {self.remittance_data.get('receiver_bank_name', '')}
Branch: - {self.remittance_data.get('receiver_bank_branch', '')}
Swift: - {self.remittance_data.get('receiver_swift', '')}
Bank country: - {bank_country}
        """.strip()
        self.bank_info.setText(bank_text)

        # تحديث نص الطلب مع المبلغ
        self.update_request_text()

        # تحديث الغرض من التحويل
        if 'transfer_purpose' in self.remittance_data:
            self.purpose_label.setText(self.remittance_data['transfer_purpose'])
    
    def save_as_image(self):
        """حفظ النموذج كصورة"""
        try:
            from PySide6.QtWidgets import QFileDialog
            from PySide6.QtGui import QPixmap
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ النموذج كصورة", 
                f"remittance_request_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
                "PNG Files (*.png);;JPG Files (*.jpg)"
            )
            
            if file_path:
                # التقاط صورة للمحتوى
                pixmap = self.content_widget.grab()
                pixmap.save(file_path)
                
                QMessageBox.information(self, "نجح الحفظ", f"تم حفظ النموذج في:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الصورة:\n{str(e)}")
    
    def simple_print(self):
        """طباعة مبسطة"""
        try:
            # محاولة استخدام الطباعة المبسطة
            QMessageBox.information(self, "الطباعة", 
                "لطباعة النموذج:\n"
                "1. احفظ النموذج كصورة\n"
                "2. افتح الصورة وقم بطباعتها\n"
                "أو استخدم Ctrl+P لطباعة النافذة مباشرة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الطباعة:\n{str(e)}")

    def update_request_text(self):
        """تحديث نص الطلب مع المبلغ رقماً ونصاً"""
        try:
            amount = self.remittance_data.get('remittance_amount', '0')
            currency = self.remittance_data.get('currency', 'USD')

            # تنسيق المبلغ مع رمز العملة
            amount_with_symbol = self.format_amount_with_currency(amount, currency)

            # تحويل المبلغ إلى كلمات
            amount_words = self.convert_amount_to_words(amount, currency)

            # تكوين النص النهائي
            request_text = f"يرجى تحويل مبلغ {amount_with_symbol} ({amount_words})"
            self.request_text.setText(request_text)

        except Exception as e:
            print(f"خطأ في تحديث نص الطلب: {e}")
            self.request_text.setText("يرجى تحويل مبلغ")

    def format_amount_with_currency(self, amount, currency):
        """تنسيق المبلغ مع رمز العملة"""
        try:
            if currency == 'USD':
                return f"${amount}"
            elif currency == 'EUR':
                return f"€{amount}"
            elif currency == 'YER':
                return f"{amount} ريال يمني"
            elif currency == 'SAR':
                return f"{amount} ريال سعودي"
            else:
                return f"{amount} {currency}"
        except:
            return f"{amount} {currency}"

    def convert_amount_to_words(self, amount, currency):
        """تحويل المبلغ إلى كلمات باستخدام مكتبة num2words"""
        try:
            from num2words import num2words

            # تحويل المبلغ إلى رقم
            if isinstance(amount, str):
                clean_amount = amount.replace(',', '').replace('$', '').replace('€', '').replace(' ريال يمني', '').replace(' ريال سعودي', '')
                amount_num = float(clean_amount)
            else:
                amount_num = float(amount)

            amount_int = int(amount_num)

            # تحويل الرقم إلى كلمات عربية
            try:
                words = num2words(amount_int, lang='ar')
            except:
                # في حالة فشل المكتبة، استخدم التحويل المبسط
                if amount_int == 63500:
                    words = "ثلاثة وستون ألف وخمسمائة"
                elif amount_int == 5000:
                    words = "خمسة آلاف"
                elif amount_int == 10000:
                    words = "عشرة آلاف"
                elif amount_int == 1000:
                    words = "ألف"
                elif amount_int == 500:
                    words = "خمسمائة"
                elif amount_int == 100:
                    words = "مائة"
                else:
                    words = str(amount_int)

            # إضافة العملة
            if currency == 'USD':
                currency_text = "دولار أمريكي"
            elif currency == 'EUR':
                currency_text = "يورو"
            elif currency == 'YER':
                currency_text = "ريال يمني"
            elif currency == 'SAR':
                currency_text = "ريال سعودي"
            else:
                currency_text = currency

            return f"{words} {currency_text} لا غير"

        except Exception as e:
            print(f"خطأ في تحويل المبلغ إلى كلمات: {e}")
            # في حالة الخطأ، استخدم تحويل مبسط
            try:
                amount_int = int(float(str(amount).replace(',', '').replace('$', '').replace('€', '')))
                return f"{amount_int} {currency} لا غير"
            except:
                return f"{amount} {currency}"

if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # بيانات تجريبية
    sample_data = {
        'request_number': 'ALF-2025-001',
        'request_date': '2024/12/09',
        'branch': 'صرافة الأمانة',
        'receiver_name': 'MOHAMMED AHMED ALI',
        'receiver_address': 'RIYADH, SAUDI ARABIA',
        'receiver_account': '**********',
        'receiver_bank_name': 'AL RAJHI BANK',
        'receiver_bank_branch': 'RIYADH BRANCH',
        'receiver_swift': 'RJHISARI',
        'receiver_country': 'SAUDI ARABIA',
        'transfer_purpose': 'COST OF FOODSTUFF'
    }
    
    window = SimplePrintTemplate(sample_data)
    window.show()
    
    sys.exit(app.exec())
