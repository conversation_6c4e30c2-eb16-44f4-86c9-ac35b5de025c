#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار البيانات الافتراضية في شاشة طلب الحوالة
Default Data Test for Remittance Request
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_default_data_in_code():
    """اختبار وجود البيانات الافتراضية في الكود"""
    
    print("🔍 اختبار البيانات الافتراضية في الكود...")
    print("=" * 60)
    
    try:
        # قراءة ملف الكود
        with open("src/ui/remittances/remittance_request_window.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # البيانات الافتراضية المطلوبة
        default_data = {
            "الاسم الكامل": "ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD",
            "الجهة": "G.M: NASHA'AT RASHAD QASIM ALDUBAEE",
            "رقم الهاتف": "+967 1 616109",
            "رقم الفاكس": "+967 1 615909",
            "رقم الموبايل": "+967 *********",
            "ص.ب": "1903",
            "البريد الإلكتروني": "<EMAIL>, <EMAIL>",
            "العنوان": "TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN"
        }
        
        print("   📋 فحص وجود البيانات الافتراضية:")
        
        all_found = True
        for field_name, expected_value in default_data.items():
            if expected_value in code:
                print(f"   ✅ {field_name}: موجود")
            else:
                print(f"   ❌ {field_name}: غير موجود")
                all_found = False
        
        # فحص وجود setText في الكود
        setText_count = code.count('.setText("')
        print(f"\n   📊 عدد استخدامات setText: {setText_count}")
        
        if setText_count >= 8:  # 8 حقول للمرسل
            print("   ✅ عدد كافٍ من استخدامات setText")
        else:
            print("   ❌ عدد غير كافٍ من استخدامات setText")
            all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الكود: {e}")
        return False

def display_default_data_summary():
    """عرض ملخص البيانات الافتراضية"""
    
    print("\n" + "=" * 80)
    print("📋 البيانات الافتراضية المطبقة في قسم معلومات المرسل")
    print("=" * 80)
    
    default_fields = [
        ("👤 الاسم الكامل", "ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD"),
        ("🏛️ الجهة", "G.M: NASHA'AT RASHAD QASIM ALDUBAEE"),
        ("📱 رقم الهاتف", "+967 1 616109"),
        ("📠 رقم الفاكس", "+967 1 615909"),
        ("📲 رقم الموبايل", "+967 *********"),
        ("📮 ص.ب", "1903"),
        ("📧 البريد الإلكتروني", "<EMAIL>, <EMAIL>"),
        ("📍 العنوان", "TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN")
    ]
    
    for i, (field_name, field_value) in enumerate(default_fields, 1):
        print(f"\n{i:2d}. {field_name}:")
        print(f"    {field_value}")

def display_implementation_details():
    """عرض تفاصيل التطبيق"""
    
    print("\n" + "=" * 80)
    print("🔧 تفاصيل التطبيق")
    print("=" * 80)
    
    print("\n✅ ما تم تطبيقه:")
    print("   1. إضافة البيانات الافتراضية في دالة إنشاء القسم:")
    print("      - تم استخدام setText() لكل حقل")
    print("      - البيانات تظهر فور فتح النافذة")
    
    print("\n   2. تحديث دالة مسح النموذج:")
    print("      - عند الضغط على 'مسح النموذج'")
    print("      - يتم إعادة تعبئة البيانات الافتراضية")
    print("      - لا يتم مسح معلومات المرسل نهائياً")
    
    print("\n🎯 الفوائد:")
    print("   ✅ توفير الوقت في إدخال البيانات")
    print("   ✅ ضمان دقة المعلومات الأساسية")
    print("   ✅ تقليل الأخطاء في الكتابة")
    print("   ✅ تحسين تجربة المستخدم")
    print("   ✅ معلومات الشركة جاهزة دائماً")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. فتح شاشة طلب الحوالة")
    print("   2. ستجد معلومات المرسل مملوءة تلقائياً")
    print("   3. يمكن تعديل أي حقل حسب الحاجة")
    print("   4. عند الضغط على 'مسح النموذج':")
    print("      - تُمسح معلومات المستقبل والملاحظات")
    print("      - تُعاد تعبئة معلومات المرسل تلقائياً")

def display_company_info():
    """عرض معلومات الشركة"""
    
    print("\n" + "=" * 80)
    print("🏢 معلومات شركة الفقيهي")
    print("=" * 80)
    
    print("\n📋 البيانات الأساسية:")
    print("   🏢 اسم الشركة: ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD")
    print("   👤 المدير العام: NASHA'AT RASHAD QASIM ALDUBAEE")
    print("   🌍 الموقع: صنعاء، اليمن")
    
    print("\n📞 معلومات الاتصال:")
    print("   📱 الهاتف: +967 1 616109")
    print("   📠 الفاكس: +967 1 615909")
    print("   📲 الموبايل: +967 *********")
    print("   📮 ص.ب: 1903")
    
    print("\n📧 البريد الإلكتروني:")
    print("   📧 البريد الرئيسي: <EMAIL>")
    print("   📧 بريد الاستيراد: <EMAIL>")
    
    print("\n📍 العنوان:")
    print("   🏢 شارع تعز، مبنى الأيتام، بالقرب من متاجر البركة")
    print("   🌍 صنعاء، اليمن")

if __name__ == "__main__":
    print("🚀 بدء اختبار البيانات الافتراضية...")
    print("=" * 80)
    
    # اختبار وجود البيانات في الكود
    code_success = test_default_data_in_code()
    
    # عرض ملخص البيانات الافتراضية
    display_default_data_summary()
    
    # عرض تفاصيل التطبيق
    display_implementation_details()
    
    # عرض معلومات الشركة
    display_company_info()
    
    # النتيجة النهائية
    if code_success:
        print("\n🏆 تم تطبيق البيانات الافتراضية بنجاح!")
        print("✅ جميع الحقول تحتوي على البيانات المطلوبة")
        print("✅ البيانات ستظهر تلقائياً عند فتح النافذة")
        print("✅ دالة مسح النموذج محدثة")
        print("✅ تجربة مستخدم محسنة")
        
        print("\n🎉 شاشة طلب الحوالة جاهزة مع البيانات الافتراضية!")
        
    else:
        print("\n❌ هناك مشكلة في تطبيق البيانات الافتراضية")
    
    print("=" * 80)
