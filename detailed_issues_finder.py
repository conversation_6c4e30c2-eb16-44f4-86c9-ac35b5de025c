#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة البحث عن المشاكل المفصلة
Detailed Issues Finder
"""

import sys
import os
import ast
import sqlite3
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_database_integrity():
    """فحص سلامة قاعدة البيانات"""
    print("🗄️ فحص سلامة قاعدة البيانات...")
    
    issues = []
    db_path = project_root / "data" / "proshipment.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول والعلاقات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # فحص الفهارس
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
        indexes = [row[0] for row in cursor.fetchall()]
        
        print(f"   📊 {len(tables)} جدول، {len(indexes)} فهرس")
        
        # فحص الجداول المهمة للعلاقات
        important_relations = [
            ("shipments", "supplier_id", "suppliers", "id"),
            ("shipment_items", "shipment_id", "shipments", "id"),
            ("shipment_items", "item_id", "items", "id"),
            ("purchase_orders", "supplier_id", "suppliers", "id"),
            ("purchase_order_items", "purchase_order_id", "purchase_orders", "id")
        ]
        
        for table1, col1, table2, col2 in important_relations:
            if table1 in tables and table2 in tables:
                # فحص العلاقة
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table1} t1 
                    LEFT JOIN {table2} t2 ON t1.{col1} = t2.{col2}
                    WHERE t1.{col1} IS NOT NULL AND t2.{col2} IS NULL
                """)
                orphaned = cursor.fetchone()[0]
                
                if orphaned > 0:
                    issues.append(f"سجلات يتيمة في {table1}.{col1}: {orphaned}")
                    print(f"   ⚠️ سجلات يتيمة في {table1}.{col1}: {orphaned}")
                else:
                    print(f"   ✅ علاقة سليمة: {table1}.{col1} -> {table2}.{col2}")
        
        # فحص البيانات المكررة
        duplicate_checks = [
            ("suppliers", "code"),
            ("items", "code"),
            ("shipments", "shipment_number")
        ]
        
        for table, column in duplicate_checks:
            if table in tables:
                cursor.execute(f"""
                    SELECT {column}, COUNT(*) as count 
                    FROM {table} 
                    WHERE {column} IS NOT NULL 
                    GROUP BY {column} 
                    HAVING COUNT(*) > 1
                """)
                duplicates = cursor.fetchall()
                
                if duplicates:
                    for dup_value, count in duplicates:
                        issues.append(f"بيانات مكررة في {table}.{column}: '{dup_value}' ({count} مرات)")
                        print(f"   ⚠️ بيانات مكررة في {table}.{column}: '{dup_value}' ({count} مرات)")
                else:
                    print(f"   ✅ لا توجد بيانات مكررة في {table}.{column}")
        
        conn.close()
        
    except Exception as e:
        issues.append(f"خطأ في فحص قاعدة البيانات: {e}")
        print(f"   ❌ خطأ: {e}")
    
    return issues

def check_import_issues():
    """فحص مشاكل الاستيراد"""
    print("\n📦 فحص مشاكل الاستيراد...")
    
    issues = []
    src_path = project_root / "src"
    
    # فحص الاستيرادات الدائرية
    import_graph = {}
    
    for py_file in src_path.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل الاستيرادات
            try:
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            if alias.name.startswith('src.'):
                                imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module and node.module.startswith('src.'):
                            imports.append(node.module)
                
                relative_path = str(py_file.relative_to(project_root)).replace('\\', '/').replace('.py', '').replace('/', '.')
                import_graph[relative_path] = imports
                
            except SyntaxError as e:
                issues.append(f"خطأ نحوي في {py_file}: {e}")
                print(f"   ❌ خطأ نحوي في {py_file}: {e}")
                
        except Exception as e:
            issues.append(f"لا يمكن قراءة {py_file}: {e}")
    
    # فحص الاستيرادات المفقودة
    missing_imports = []
    for file_path, imports in import_graph.items():
        for imp in imports:
            # تحويل اسم الوحدة إلى مسار ملف
            module_path = project_root / imp.replace('.', '/') + '.py'
            if not module_path.exists():
                # فحص إذا كان مجلد
                module_dir = project_root / imp.replace('.', '/')
                if not (module_dir.exists() and (module_dir / '__init__.py').exists()):
                    missing_imports.append(f"استيراد مفقود في {file_path}: {imp}")
    
    for missing in missing_imports:
        issues.append(missing)
        print(f"   ⚠️ {missing}")
    
    if not missing_imports:
        print("   ✅ جميع الاستيرادات متاحة")
    
    return issues

def check_ui_issues():
    """فحص مشاكل واجهة المستخدم"""
    print("\n🖥️ فحص مشاكل واجهة المستخدم...")
    
    issues = []
    ui_path = project_root / "src" / "ui"
    
    # فحص النوافذ الرئيسية
    main_windows = [
        "main_window.py",
        "shipments/shipments_window.py",
        "suppliers/suppliers_window.py",
        "items/items_window.py",
        "settings/settings_window.py"
    ]
    
    for window_file in main_windows:
        window_path = ui_path / window_file
        if not window_path.exists():
            issues.append(f"نافذة مفقودة: {window_file}")
            print(f"   ❌ نافذة مفقودة: {window_file}")
        else:
            print(f"   ✅ نافذة موجودة: {window_file}")
    
    # فحص الحوارات
    dialogs_path = ui_path / "dialogs"
    if dialogs_path.exists():
        dialog_files = list(dialogs_path.glob("*.py"))
        print(f"   📋 {len(dialog_files)} حوار موجود")
        
        # فحص الحوارات المهمة
        important_dialogs = [
            "add_supplier_dialog.py",
            "add_item_dialog.py",
            "add_shipment_dialog.py"
        ]
        
        for dialog_file in important_dialogs:
            dialog_path = dialogs_path / dialog_file
            if not dialog_path.exists():
                issues.append(f"حوار مهم مفقود: {dialog_file}")
                print(f"   ⚠️ حوار مهم مفقود: {dialog_file}")
    else:
        issues.append("مجلد الحوارات مفقود")
        print("   ❌ مجلد الحوارات مفقود")
    
    return issues

def check_file_permissions():
    """فحص أذونات الملفات"""
    print("\n🔒 فحص أذونات الملفات...")
    
    issues = []
    
    # فحص ملف قاعدة البيانات
    db_path = project_root / "data" / "proshipment.db"
    if db_path.exists():
        try:
            # محاولة الكتابة
            with open(db_path, 'r+b') as f:
                pass
            print("   ✅ قاعدة البيانات قابلة للكتابة")
        except PermissionError:
            issues.append("قاعدة البيانات غير قابلة للكتابة")
            print("   ❌ قاعدة البيانات غير قابلة للكتابة")
    
    # فحص مجلد المرفقات
    attachments_path = project_root / "attachments"
    if attachments_path.exists():
        try:
            # محاولة إنشاء ملف تجريبي
            test_file = attachments_path / "test_write.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()  # حذف الملف التجريبي
            print("   ✅ مجلد المرفقات قابل للكتابة")
        except PermissionError:
            issues.append("مجلد المرفقات غير قابل للكتابة")
            print("   ❌ مجلد المرفقات غير قابل للكتابة")
    
    return issues

def check_configuration_issues():
    """فحص مشاكل التكوين"""
    print("\n⚙️ فحص مشاكل التكوين...")
    
    issues = []
    
    # فحص إعدادات النظام في قاعدة البيانات
    db_path = project_root / "data" / "proshipment.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص وجود إعدادات النظام
        cursor.execute("SELECT COUNT(*) FROM system_settings")
        settings_count = cursor.fetchone()[0]
        
        if settings_count == 0:
            issues.append("لا توجد إعدادات نظام")
            print("   ❌ لا توجد إعدادات نظام")
        else:
            print(f"   ✅ {settings_count} إعداد نظام موجود")
        
        # فحص بيانات الشركة
        cursor.execute("SELECT COUNT(*) FROM companies")
        companies_count = cursor.fetchone()[0]
        
        if companies_count == 0:
            issues.append("لا توجد بيانات شركة")
            print("   ❌ لا توجد بيانات شركة")
        else:
            print(f"   ✅ {companies_count} شركة موجودة")
        
        # فحص العملات
        cursor.execute("SELECT COUNT(*) FROM currencies")
        currencies_count = cursor.fetchone()[0]
        
        if currencies_count == 0:
            issues.append("لا توجد عملات")
            print("   ❌ لا توجد عملات")
        else:
            print(f"   ✅ {currencies_count} عملة موجودة")
        
        conn.close()
        
    except Exception as e:
        issues.append(f"خطأ في فحص التكوين: {e}")
        print(f"   ❌ خطأ: {e}")
    
    return issues

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص مفصل للمشاكل المحتملة...")
    print("="*60)
    
    all_issues = []
    
    # تشغيل جميع الفحوصات
    all_issues.extend(check_database_integrity())
    all_issues.extend(check_import_issues())
    all_issues.extend(check_ui_issues())
    all_issues.extend(check_file_permissions())
    all_issues.extend(check_configuration_issues())
    
    # عرض التقرير النهائي
    print("\n" + "="*60)
    print("📊 تقرير المشاكل المفصل")
    print("="*60)
    
    if not all_issues:
        print("\n🎉 لم يتم العثور على مشاكل!")
        print("✅ التطبيق جاهز للاستخدام")
        return True
    else:
        print(f"\n⚠️ تم العثور على {len(all_issues)} مشكلة:")
        
        for i, issue in enumerate(all_issues, 1):
            print(f"{i:2d}. {issue}")
        
        # تصنيف المشاكل
        critical_issues = [issue for issue in all_issues if any(word in issue.lower() for word in ['خطأ', 'مفقود', 'غير متاح'])]
        warning_issues = [issue for issue in all_issues if issue not in critical_issues]
        
        print(f"\n📊 التصنيف:")
        print(f"   🔴 مشاكل حرجة: {len(critical_issues)}")
        print(f"   🟡 تحذيرات: {len(warning_issues)}")
        
        if len(critical_issues) == 0:
            print("\n✅ لا توجد مشاكل حرجة - التطبيق قابل للاستخدام")
            return True
        else:
            print("\n⚠️ توجد مشاكل حرجة تحتاج إصلاح")
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
