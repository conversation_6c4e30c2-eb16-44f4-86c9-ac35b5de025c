#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص شاملة للتطبيق
Comprehensive Application Audit Tool
"""

import sys
import os
import ast
import sqlite3
import importlib.util
from pathlib import Path
from typing import List, Dict, Any
import traceback

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ApplicationAuditor:
    """مدقق شامل للتطبيق"""
    
    def __init__(self):
        self.project_root = project_root
        self.errors = []
        self.warnings = []
        self.info = []
        self.src_path = self.project_root / "src"
        self.db_path = self.project_root / "data" / "proshipment.db"
        
    def audit_application(self):
        """تدقيق شامل للتطبيق"""
        print("🔍 بدء الفحص الشامل للتطبيق...")
        print("="*80)
        
        # 1. فحص هيكلية المشروع
        self.audit_project_structure()
        
        # 2. فحص الاستيرادات والتبعيات
        self.audit_imports_and_dependencies()
        
        # 3. فحص قاعدة البيانات
        self.audit_database()
        
        # 4. فحص النماذج والعلاقات
        self.audit_models_and_relationships()
        
        # 5. فحص واجهة المستخدم
        self.audit_ui_components()
        
        # 6. فحص الأخطاء البرمجية
        self.audit_syntax_and_logic()
        
        # 7. فحص الأداء والذاكرة
        self.audit_performance_issues()
        
        # 8. فحص الأمان
        self.audit_security_issues()
        
        # عرض التقرير النهائي
        self.generate_final_report()
        
    def audit_project_structure(self):
        """فحص هيكلية المشروع"""
        print("\n📁 فحص هيكلية المشروع...")
        
        # الملفات والمجلدات المطلوبة
        required_files = [
            "main.py",
            "requirements.txt",
            "src/__init__.py",
            "src/database/__init__.py",
            "src/database/models.py",
            "src/database/database_manager.py",
            "src/ui/__init__.py",
            "src/ui/main_window.py",
            "src/utils/__init__.py"
        ]
        
        required_dirs = [
            "src",
            "src/database",
            "src/ui",
            "src/utils",
            "data"
        ]
        
        # فحص الملفات المطلوبة
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                self.errors.append(f"ملف مطلوب مفقود: {file_path}")
            else:
                self.info.append(f"✅ ملف موجود: {file_path}")
        
        # فحص المجلدات المطلوبة
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                self.errors.append(f"مجلد مطلوب مفقود: {dir_path}")
            else:
                self.info.append(f"✅ مجلد موجود: {dir_path}")
        
        # فحص ملفات __init__.py
        python_dirs = [
            "src",
            "src/database",
            "src/ui",
            "src/utils"
        ]
        
        for dir_path in python_dirs:
            init_file = self.project_root / dir_path / "__init__.py"
            if not init_file.exists():
                self.warnings.append(f"ملف __init__.py مفقود في: {dir_path}")
        
        print(f"   ✅ تم فحص {len(required_files)} ملف و {len(required_dirs)} مجلد")
    
    def audit_imports_and_dependencies(self):
        """فحص الاستيرادات والتبعيات"""
        print("\n📦 فحص الاستيرادات والتبعيات...")
        
        # قراءة requirements.txt
        req_file = self.project_root / "requirements.txt"
        if req_file.exists():
            with open(req_file, 'r', encoding='utf-8') as f:
                requirements = f.read().splitlines()
            
            # فحص التبعيات المطلوبة
            required_packages = [
                'PySide6',
                'SQLAlchemy',
                'reportlab',
                'requests',
                'beautifulsoup4',
                'openpyxl',
                'Pillow'
            ]
            
            for package in required_packages:
                found = any(package.lower() in req.lower() for req in requirements)
                if not found:
                    self.warnings.append(f"حزمة مطلوبة قد تكون مفقودة: {package}")
                else:
                    self.info.append(f"✅ حزمة موجودة: {package}")
        else:
            self.errors.append("ملف requirements.txt مفقود")
        
        # فحص الاستيرادات في الملفات
        python_files = list(self.src_path.rglob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # تحليل الاستيرادات
                try:
                    tree = ast.parse(content)
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Import):
                            for alias in node.names:
                                self.check_import_availability(alias.name, py_file)
                        elif isinstance(node, ast.ImportFrom):
                            if node.module:
                                self.check_import_availability(node.module, py_file)
                except SyntaxError as e:
                    self.errors.append(f"خطأ نحوي في {py_file}: {e}")
                    
            except Exception as e:
                self.warnings.append(f"لا يمكن قراءة الملف {py_file}: {e}")
        
        print(f"   ✅ تم فحص {len(python_files)} ملف Python")
    
    def check_import_availability(self, module_name, file_path):
        """فحص توفر الوحدة"""
        try:
            # تجاهل الاستيرادات النسبية والمحلية
            if module_name.startswith('.') or module_name.startswith('src'):
                return
            
            # محاولة استيراد الوحدة
            spec = importlib.util.find_spec(module_name)
            if spec is None:
                self.warnings.append(f"وحدة غير متاحة '{module_name}' في {file_path}")
        except Exception:
            pass
    
    def audit_database(self):
        """فحص قاعدة البيانات"""
        print("\n🗄️ فحص قاعدة البيانات...")
        
        if not self.db_path.exists():
            self.warnings.append("ملف قاعدة البيانات غير موجود")
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # الجداول المطلوبة
            required_tables = [
                'companies', 'branches', 'users', 'currencies',
                'fiscal_years', 'suppliers', 'items', 'shipments',
                'purchase_orders', 'system_settings'
            ]
            
            for table in required_tables:
                if table in tables:
                    self.info.append(f"✅ جدول موجود: {table}")
                    
                    # فحص عدد السجلات
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    self.info.append(f"   📊 عدد السجلات في {table}: {count}")
                else:
                    self.errors.append(f"جدول مطلوب مفقود: {table}")
            
            # فحص الفهارس
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            indexes = [row[0] for row in cursor.fetchall()]
            self.info.append(f"📈 عدد الفهارس: {len(indexes)}")
            
            conn.close()
            
        except Exception as e:
            self.errors.append(f"خطأ في فحص قاعدة البيانات: {e}")
        
        print(f"   ✅ تم فحص قاعدة البيانات")
    
    def audit_models_and_relationships(self):
        """فحص النماذج والعلاقات"""
        print("\n🔗 فحص النماذج والعلاقات...")
        
        try:
            # استيراد النماذج
            from src.database.models import Base
            
            # فحص جميع النماذج
            models = Base.registry._class_registry.values()
            
            for model in models:
                if hasattr(model, '__tablename__'):
                    table_name = model.__tablename__
                    self.info.append(f"✅ نموذج: {model.__name__} -> {table_name}")
                    
                    # فحص العلاقات
                    if hasattr(model, '__mapper__'):
                        relationships = model.__mapper__.relationships
                        for rel_name, rel in relationships.items():
                            self.info.append(f"   🔗 علاقة: {rel_name} -> {rel.mapper.class_.__name__}")
            
        except Exception as e:
            self.errors.append(f"خطأ في فحص النماذج: {e}")
        
        print(f"   ✅ تم فحص النماذج والعلاقات")
    
    def audit_ui_components(self):
        """فحص مكونات واجهة المستخدم"""
        print("\n🖥️ فحص مكونات واجهة المستخدم...")
        
        ui_path = self.src_path / "ui"
        ui_files = list(ui_path.rglob("*.py"))
        
        # فحص النوافذ الرئيسية
        main_windows = [
            "main_window.py",
            "shipments/shipments_window.py",
            "suppliers/suppliers_window.py",
            "items/items_window.py"
        ]
        
        for window_file in main_windows:
            window_path = ui_path / window_file
            if window_path.exists():
                self.info.append(f"✅ نافذة موجودة: {window_file}")
            else:
                self.errors.append(f"نافذة مطلوبة مفقودة: {window_file}")
        
        # فحص الحوارات
        dialogs_path = ui_path / "dialogs"
        if dialogs_path.exists():
            dialog_files = list(dialogs_path.glob("*.py"))
            self.info.append(f"📋 عدد الحوارات: {len(dialog_files)}")
        else:
            self.warnings.append("مجلد الحوارات مفقود")
        
        print(f"   ✅ تم فحص {len(ui_files)} ملف واجهة مستخدم")
    
    def audit_syntax_and_logic(self):
        """فحص الأخطاء النحوية والمنطقية"""
        print("\n🐛 فحص الأخطاء النحوية والمنطقية...")
        
        python_files = list(self.project_root.rglob("*.py"))
        syntax_errors = 0
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص النحو
                try:
                    ast.parse(content)
                except SyntaxError as e:
                    self.errors.append(f"خطأ نحوي في {py_file}:{e.lineno}: {e.msg}")
                    syntax_errors += 1
                
            except Exception as e:
                self.warnings.append(f"لا يمكن فحص الملف {py_file}: {e}")
        
        if syntax_errors == 0:
            self.info.append("✅ لا توجد أخطاء نحوية")
        else:
            self.errors.append(f"وجد {syntax_errors} خطأ نحوي")
        
        print(f"   ✅ تم فحص {len(python_files)} ملف Python")
    
    def audit_performance_issues(self):
        """فحص مشاكل الأداء"""
        print("\n⚡ فحص مشاكل الأداء...")
        
        # فحص حجم قاعدة البيانات
        if self.db_path.exists():
            db_size = self.db_path.stat().st_size / (1024 * 1024)  # MB
            if db_size > 100:
                self.warnings.append(f"حجم قاعدة البيانات كبير: {db_size:.2f} MB")
            else:
                self.info.append(f"✅ حجم قاعدة البيانات مناسب: {db_size:.2f} MB")
        
        # فحص عدد الملفات
        total_files = len(list(self.project_root.rglob("*")))
        if total_files > 1000:
            self.warnings.append(f"عدد الملفات كبير: {total_files}")
        else:
            self.info.append(f"✅ عدد الملفات مناسب: {total_files}")
        
        print(f"   ✅ تم فحص مؤشرات الأداء")
    
    def audit_security_issues(self):
        """فحص مشاكل الأمان"""
        print("\n🔒 فحص مشاكل الأمان...")
        
        # فحص كلمات المرور المكشوفة
        python_files = list(self.src_path.rglob("*.py"))
        
        security_keywords = ['password', 'secret', 'key', 'token']
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                for keyword in security_keywords:
                    if f'{keyword}=' in content or f'{keyword} =' in content:
                        self.warnings.append(f"كلمة مرور محتملة مكشوفة في {py_file}")
                        break
                        
            except Exception:
                pass
        
        # فحص أذونات الملفات
        if os.name != 'nt':  # Unix/Linux
            for py_file in python_files:
                stat = py_file.stat()
                if stat.st_mode & 0o077:  # قابل للقراءة/الكتابة للآخرين
                    self.warnings.append(f"أذونات ملف غير آمنة: {py_file}")
        
        print(f"   ✅ تم فحص الأمان")
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n" + "="*80)
        print("📊 التقرير النهائي للفحص الشامل")
        print("="*80)
        
        print(f"\n🔴 الأخطاء الحرجة: {len(self.errors)}")
        for error in self.errors:
            print(f"   ❌ {error}")
        
        print(f"\n🟡 التحذيرات: {len(self.warnings)}")
        for warning in self.warnings:
            print(f"   ⚠️ {warning}")
        
        print(f"\n🟢 المعلومات: {len(self.info)}")
        for info in self.info[:10]:  # أول 10 معلومات
            print(f"   ℹ️ {info}")
        
        if len(self.info) > 10:
            print(f"   ... و {len(self.info) - 10} معلومة أخرى")
        
        # تقييم الحالة العامة
        print(f"\n📈 تقييم الحالة العامة:")
        if len(self.errors) == 0:
            if len(self.warnings) == 0:
                print("   🟢 ممتاز: لا توجد أخطاء أو تحذيرات")
            elif len(self.warnings) <= 5:
                print("   🟡 جيد: بعض التحذيرات البسيطة")
            else:
                print("   🟠 متوسط: عدة تحذيرات تحتاج انتباه")
        else:
            if len(self.errors) <= 3:
                print("   🔴 يحتاج إصلاح: بعض الأخطاء الحرجة")
            else:
                print("   🚨 حرج: عدة أخطاء حرجة تحتاج إصلاح فوري")
        
        return len(self.errors) == 0

def main():
    """الدالة الرئيسية"""
    auditor = ApplicationAuditor()
    success = auditor.audit_application()
    
    if success:
        print("\n🎉 التطبيق في حالة جيدة!")
    else:
        print("\n⚠️ التطبيق يحتاج إصلاحات")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
