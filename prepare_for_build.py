#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تحضير المشروع لبناء ملف التثبيت
Project Preparation Tool for Build
"""

import os
import sys
import shutil
from pathlib import Path
import json

class ProjectPreparator:
    """محضر المشروع للبناء"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        
    def check_project_structure(self):
        """فحص هيكل المشروع"""
        print("🔍 فحص هيكل المشروع...")
        
        required_dirs = [
            "src",
            "src/ui",
            "src/database", 
            "src/utils",
            "data",
            "config",
            "docs"
        ]
        
        required_files = [
            "main.py",
            "requirements.txt",
            "README.md",
            "CHANGELOG.md"
        ]
        
        missing_items = []
        
        # فحص المجلدات
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                missing_items.append(f"مجلد: {dir_path}")
                print(f"   ❌ مجلد مفقود: {dir_path}")
            else:
                print(f"   ✅ {dir_path}")
        
        # فحص الملفات
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_items.append(f"ملف: {file_path}")
                print(f"   ❌ ملف مفقود: {file_path}")
            else:
                print(f"   ✅ {file_path}")
        
        return len(missing_items) == 0, missing_items
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        print("📁 التحقق من مجلد البيانات...")
        
        data_dir = self.project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        # إنشاء قاعدة بيانات فارغة إذا لم تكن موجودة
        db_file = data_dir / "proshipment.db"
        if not db_file.exists():
            print("   🔧 إنشاء قاعدة بيانات فارغة...")
            try:
                from src.database.database_manager import DatabaseManager
                db_manager = DatabaseManager(str(db_file))
                db_manager.create_tables()
                print("   ✅ تم إنشاء قاعدة البيانات")
            except Exception as e:
                print(f"   ⚠️ تعذر إنشاء قاعدة البيانات: {e}")
        else:
            print("   ✅ قاعدة البيانات موجودة")
        
        # إنشاء مجلد المرفقات
        attachments_dir = self.project_root / "attachments"
        attachments_dir.mkdir(exist_ok=True)
        
        # إنشاء مجلدات فرعية للمرفقات
        sub_dirs = ["shipments", "suppliers", "items", "remittances"]
        for sub_dir in sub_dirs:
            (attachments_dir / sub_dir).mkdir(exist_ok=True)
        
        print("   ✅ مجلدات المرفقات جاهزة")
        
        return True
    
    def ensure_config_files(self):
        """التأكد من وجود ملفات الإعدادات"""
        print("⚙️ التحقق من ملفات الإعدادات...")
        
        config_dir = self.project_root / "config"
        config_dir.mkdir(exist_ok=True)
        
        # إعدادات قاعدة البيانات الافتراضية
        default_db_config = {
            "type": "sqlite",
            "path": "data/proshipment.db",
            "backup_enabled": True,
            "backup_interval": 24,
            "max_backups": 7
        }
        
        db_config_file = config_dir / "database.json"
        if not db_config_file.exists():
            with open(db_config_file, 'w', encoding='utf-8') as f:
                json.dump(default_db_config, f, ensure_ascii=False, indent=2)
            print("   ✅ تم إنشاء إعدادات قاعدة البيانات")
        
        # إعدادات الثيم الافتراضية
        default_theme_config = {
            "theme": "light",
            "color_scheme": "blue",
            "font_size": 10,
            "font_scale": 1.0,
            "responsive_enabled": True,
            "auto_center": True
        }
        
        theme_config_file = config_dir / "theme_settings.json"
        if not theme_config_file.exists():
            with open(theme_config_file, 'w', encoding='utf-8') as f:
                json.dump(default_theme_config, f, ensure_ascii=False, indent=2)
            print("   ✅ تم إنشاء إعدادات الثيم")
        
        return True
    
    def create_missing_docs(self):
        """إنشاء الوثائق المفقودة"""
        print("📚 التحقق من الوثائق...")
        
        docs_dir = self.project_root / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # دليل المستخدم الأساسي
        user_guide_file = docs_dir / "user_guide.md"
        if not user_guide_file.exists():
            user_guide_content = """# دليل المستخدم - ProShipment V2.0.0

## مقدمة
نظام إدارة الشحنات والحوالات المتكامل

## البدء السريع
1. تشغيل التطبيق
2. إعداد بيانات الشركة
3. بدء إدخال البيانات

## الميزات الرئيسية
- إدارة الشحنات
- نظام الحوالات
- إدارة الموردين
- التقارير والطباعة

## الدعم الفني
للحصول على المساعدة، راجع الوثائق الأخرى أو تواصل مع الدعم الفني.
"""
            with open(user_guide_file, 'w', encoding='utf-8') as f:
                f.write(user_guide_content)
            print("   ✅ تم إنشاء دليل المستخدم")
        
        return True
    
    def optimize_for_build(self):
        """تحسين المشروع للبناء"""
        print("🚀 تحسين المشروع للبناء...")
        
        # إنشاء ملف __init__.py في المجلدات المطلوبة
        init_dirs = [
            "src",
            "src/ui",
            "src/ui/dialogs",
            "src/ui/shipments", 
            "src/ui/suppliers",
            "src/ui/remittances",
            "src/ui/themes",
            "src/ui/responsive",
            "src/database",
            "src/utils",
            "src/reports"
        ]
        
        for dir_path in init_dirs:
            full_dir = self.project_root / dir_path
            if full_dir.exists():
                init_file = full_dir / "__init__.py"
                if not init_file.exists():
                    init_file.touch()
                    print(f"   ✅ إنشاء __init__.py في {dir_path}")
        
        # تنظيف ملفات __pycache__
        self.clean_pycache()
        
        return True
    
    def clean_pycache(self):
        """تنظيف ملفات __pycache__"""
        print("🧹 تنظيف ملفات __pycache__...")
        
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        
        for pycache_dir in pycache_dirs:
            try:
                shutil.rmtree(pycache_dir)
                print(f"   🗑️ تم حذف {pycache_dir}")
            except Exception as e:
                print(f"   ⚠️ تعذر حذف {pycache_dir}: {e}")
        
        # حذف ملفات .pyc
        pyc_files = list(self.project_root.rglob("*.pyc"))
        for pyc_file in pyc_files:
            try:
                pyc_file.unlink()
                print(f"   🗑️ تم حذف {pyc_file}")
            except Exception as e:
                print(f"   ⚠️ تعذر حذف {pyc_file}: {e}")
        
        return True
    
    def create_build_info(self):
        """إنشاء معلومات البناء"""
        print("📋 إنشاء معلومات البناء...")
        
        build_info = {
            "project_name": "ProShipment",
            "version": "2.0.0",
            "build_date": "2024-12-11",
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "platform": sys.platform,
            "architecture": "x64" if sys.maxsize > 2**32 else "x86"
        }
        
        build_info_file = self.project_root / "build_info.json"
        with open(build_info_file, 'w', encoding='utf-8') as f:
            json.dump(build_info, f, ensure_ascii=False, indent=2)
        
        print("   ✅ تم إنشاء معلومات البناء")
        return True
    
    def run_preparation(self):
        """تشغيل عملية التحضير الكاملة"""
        print("🔧 تحضير المشروع لبناء ملف التثبيت")
        print("="*50)
        
        steps = [
            ("فحص هيكل المشروع", self.check_project_structure),
            ("التأكد من مجلد البيانات", self.ensure_data_directory),
            ("التأكد من ملفات الإعدادات", self.ensure_config_files),
            ("إنشاء الوثائق المفقودة", self.create_missing_docs),
            ("تحسين المشروع للبناء", self.optimize_for_build),
            ("إنشاء معلومات البناء", self.create_build_info)
        ]
        
        failed_steps = []
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            try:
                if step_name == "فحص هيكل المشروع":
                    success, missing = step_func()
                    if not success:
                        print(f"   ❌ عناصر مفقودة: {missing}")
                        failed_steps.append(step_name)
                    else:
                        print(f"   ✅ هيكل المشروع سليم")
                else:
                    if step_func():
                        print(f"   ✅ نجح: {step_name}")
                    else:
                        failed_steps.append(step_name)
                        print(f"   ❌ فشل: {step_name}")
            except Exception as e:
                failed_steps.append(step_name)
                print(f"   ❌ خطأ في {step_name}: {e}")
        
        # النتائج النهائية
        print("\n" + "="*50)
        print("📊 نتائج التحضير")
        print("="*50)
        
        if not failed_steps:
            print("🎉 تم تحضير المشروع بنجاح!")
            print("✅ المشروع جاهز لبناء ملف التثبيت")
            print("\n🚀 الخطوة التالية:")
            print("   تشغيل: python build_installer.py")
            print("   أو: build_exe.bat")
        else:
            print(f"⚠️ فشل في {len(failed_steps)} خطوات:")
            for step in failed_steps:
                print(f"   ❌ {step}")
            print("\n🔧 يرجى إصلاح المشاكل أعلاه قبل البناء")
        
        return len(failed_steps) == 0

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة تحضير المشروع لبناء ملف التثبيت")
    print("="*50)
    
    preparator = ProjectPreparator()
    success = preparator.run_preparation()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
