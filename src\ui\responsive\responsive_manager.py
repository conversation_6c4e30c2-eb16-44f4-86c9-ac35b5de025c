#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التصميم المتجاوب - ProShipment
Responsive Design Manager
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from enum import Enum

from PySide6.QtWidgets import (
    QApplication, QWidget, QMainWindow, QDialog,
    QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QTableWidget, QTreeWidget, QScrollArea, QSplitter,
    QGroupBox, QFrame, QTabWidget
)
from PySide6.QtCore import Qt, QSize, QRect, QTimer, Signal, QObject
from PySide6.QtGui import QFont, QFontMetrics, QScreen, QGuiApplication

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

class ScreenSize(Enum):
    """أحجام الشاشات المختلفة"""
    SMALL = "small"      # أقل من 1366x768
    MEDIUM = "medium"    # 1366x768 إلى 1920x1080
    LARGE = "large"      # 1920x1080 إلى 2560x1440
    XLARGE = "xlarge"    # أكبر من 2560x1440

class WindowType(Enum):
    """أنواع النوافذ"""
    MAIN_WINDOW = "main_window"
    DIALOG = "dialog"
    POPUP = "popup"
    FULLSCREEN = "fullscreen"

class ResponsiveManager(QObject):
    """مدير التصميم المتجاوب"""
    
    # إشارات
    screen_size_changed = Signal(ScreenSize)
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_screen_size = None
        self.current_theme = "light"
        self.scale_factor = 1.0
        self.font_scale = 1.0
        
        # إعدادات الأحجام لكل نوع شاشة
        self.size_configs = {
            ScreenSize.SMALL: {
                "window_min_size": (800, 600),
                "window_default_size": (1024, 768),
                "font_base_size": 9,
                "icon_size": 16,
                "button_height": 32,
                "input_height": 28,
                "spacing": 8,
                "margins": 10,
                "table_row_height": 35
            },
            ScreenSize.MEDIUM: {
                "window_min_size": (1000, 700),
                "window_default_size": (1200, 800),
                "font_base_size": 10,
                "icon_size": 20,
                "button_height": 36,
                "input_height": 32,
                "spacing": 10,
                "margins": 15,
                "table_row_height": 40
            },
            ScreenSize.LARGE: {
                "window_min_size": (1200, 800),
                "window_default_size": (1400, 900),
                "font_base_size": 11,
                "icon_size": 24,
                "button_height": 40,
                "input_height": 36,
                "spacing": 12,
                "margins": 20,
                "table_row_height": 45
            },
            ScreenSize.XLARGE: {
                "window_min_size": (1400, 900),
                "window_default_size": (1600, 1000),
                "font_base_size": 12,
                "icon_size": 28,
                "button_height": 44,
                "input_height": 40,
                "spacing": 15,
                "margins": 25,
                "table_row_height": 50
            }
        }
        
        # مراقبة تغيير حجم الشاشة
        self.setup_screen_monitoring()
        
    def setup_screen_monitoring(self):
        """إعداد مراقبة تغيير حجم الشاشة"""
        app = QApplication.instance()
        if app:
            app.screenAdded.connect(self.on_screen_changed)
            app.screenRemoved.connect(self.on_screen_changed)
            app.primaryScreenChanged.connect(self.on_screen_changed)
            
        # تحديث أولي
        self.update_screen_info()
        
    def on_screen_changed(self):
        """معالج تغيير الشاشة"""
        QTimer.singleShot(100, self.update_screen_info)
        
    def update_screen_info(self):
        """تحديث معلومات الشاشة"""
        screen = QGuiApplication.primaryScreen()
        if screen:
            geometry = screen.geometry()
            new_size = self.detect_screen_size(geometry.width(), geometry.height())
            
            if new_size != self.current_screen_size:
                old_size = self.current_screen_size
                self.current_screen_size = new_size
                self.update_scale_factors()
                
                print(f"📱 تغيير حجم الشاشة: {old_size} → {new_size}")
                print(f"📐 الدقة: {geometry.width()}x{geometry.height()}")
                print(f"📏 معامل التكبير: {self.scale_factor}")
                
                self.screen_size_changed.emit(new_size)
    
    def detect_screen_size(self, width: int, height: int) -> ScreenSize:
        """تحديد حجم الشاشة بناءً على الدقة"""
        total_pixels = width * height
        
        if total_pixels < 1366 * 768:
            return ScreenSize.SMALL
        elif total_pixels < 1920 * 1080:
            return ScreenSize.MEDIUM
        elif total_pixels < 2560 * 1440:
            return ScreenSize.LARGE
        else:
            return ScreenSize.XLARGE
    
    def update_scale_factors(self):
        """تحديث معاملات التكبير"""
        screen = QGuiApplication.primaryScreen()
        if screen:
            # معامل التكبير الأساسي من النظام
            self.scale_factor = screen.devicePixelRatio()
            
            # معامل تكبير الخط
            if self.current_screen_size == ScreenSize.SMALL:
                self.font_scale = 0.9
            elif self.current_screen_size == ScreenSize.MEDIUM:
                self.font_scale = 1.0
            elif self.current_screen_size == ScreenSize.LARGE:
                self.font_scale = 1.1
            else:  # XLARGE
                self.font_scale = 1.2
    
    def get_config(self, key: str) -> Any:
        """الحصول على إعداد للشاشة الحالية"""
        if self.current_screen_size and self.current_screen_size in self.size_configs:
            config = self.size_configs[self.current_screen_size]
            return config.get(key)
        return None
    
    def get_scaled_size(self, base_size: int) -> int:
        """الحصول على حجم مُكبر"""
        return int(base_size * self.scale_factor * self.font_scale)
    
    def get_font_size(self, size_type: str = "base") -> int:
        """الحصول على حجم الخط"""
        base_size = self.get_config("font_base_size") or 10
        
        multipliers = {
            "small": 0.8,
            "base": 1.0,
            "medium": 1.2,
            "large": 1.4,
            "xlarge": 1.6,
            "title": 1.8,
            "header": 2.0
        }
        
        multiplier = multipliers.get(size_type, 1.0)
        return self.get_scaled_size(int(base_size * multiplier))
    
    def apply_responsive_layout(self, widget: QWidget, window_type: WindowType = WindowType.DIALOG):
        """تطبيق التخطيط المتجاوب على widget"""
        if not widget:
            return
            
        # تطبيق الحجم المناسب
        self.apply_window_size(widget, window_type)
        
        # تطبيق الخطوط
        self.apply_responsive_fonts(widget)
        
        # تطبيق التباعد والهوامش
        self.apply_responsive_spacing(widget)
        
        # تطبيق أحجام العناصر
        self.apply_responsive_elements(widget)
    
    def apply_window_size(self, widget: QWidget, window_type: WindowType):
        """تطبيق حجم النافذة المناسب"""
        if window_type == WindowType.MAIN_WINDOW:
            # النافذة الرئيسية تأخذ معظم الشاشة
            screen = QGuiApplication.primaryScreen()
            if screen:
                screen_geometry = screen.availableGeometry()
                width = int(screen_geometry.width() * 0.9)
                height = int(screen_geometry.height() * 0.9)
                widget.resize(width, height)
                
                # توسيط النافذة
                x = (screen_geometry.width() - width) // 2
                y = (screen_geometry.height() - height) // 2
                widget.move(x, y)
        
        elif window_type == WindowType.DIALOG:
            # النوافذ الحوارية
            default_size = self.get_config("window_default_size")
            if default_size:
                widget.resize(*default_size)
                
            min_size = self.get_config("window_min_size")
            if min_size:
                widget.setMinimumSize(*min_size)
    
    def apply_responsive_fonts(self, widget: QWidget):
        """تطبيق الخطوط المتجاوبة"""
        # تطبيق على widget نفسه
        if hasattr(widget, 'font'):
            font = widget.font()
            font.setPointSize(self.get_font_size("base"))
            widget.setFont(font)
        
        # تطبيق على العناصر الفرعية
        for child in widget.findChildren(QWidget):
            self.apply_font_to_widget(child)
    
    def apply_font_to_widget(self, widget: QWidget):
        """تطبيق الخط على widget محدد"""
        font_sizes = {
            QLabel: "base",
            QPushButton: "base",
            QLineEdit: "base",
            QTextEdit: "base",
            QComboBox: "base"
        }
        
        widget_type = type(widget)
        if widget_type in font_sizes:
            font = widget.font()
            size_type = font_sizes[widget_type]
            
            # تحديد نوع الخط بناءً على النص أو الخصائص
            if isinstance(widget, QLabel):
                text = widget.text()
                if any(keyword in text for keyword in ["عنوان", "رئيسي", "Title"]):
                    size_type = "title"
                elif any(keyword in text for keyword in ["فرعي", "Header"]):
                    size_type = "header"
            
            font.setPointSize(self.get_font_size(size_type))
            widget.setFont(font)
    
    def apply_responsive_spacing(self, widget: QWidget):
        """تطبيق التباعد والهوامش المتجاوبة"""
        spacing = self.get_config("spacing") or 10
        margins = self.get_config("margins") or 15
        
        # تطبيق على التخطيطات
        for layout in widget.findChildren(QVBoxLayout):
            layout.setSpacing(spacing)
            layout.setContentsMargins(margins, margins, margins, margins)
            
        for layout in widget.findChildren(QHBoxLayout):
            layout.setSpacing(spacing)
            layout.setContentsMargins(margins, margins, margins, margins)
            
        for layout in widget.findChildren(QGridLayout):
            layout.setSpacing(spacing)
            layout.setContentsMargins(margins, margins, margins, margins)
            
        for layout in widget.findChildren(QFormLayout):
            layout.setSpacing(spacing)
            layout.setContentsMargins(margins, margins, margins, margins)
    
    def apply_responsive_elements(self, widget: QWidget):
        """تطبيق أحجام العناصر المتجاوبة"""
        button_height = self.get_config("button_height") or 36
        input_height = self.get_config("input_height") or 32
        table_row_height = self.get_config("table_row_height") or 40
        icon_size = self.get_config("icon_size") or 20
        
        # الأزرار
        for button in widget.findChildren(QPushButton):
            button.setMinimumHeight(button_height)
            if not button.maximumHeight() or button.maximumHeight() > button_height * 2:
                button.setMaximumHeight(button_height * 2)
        
        # حقول الإدخال
        for input_field in widget.findChildren(QLineEdit):
            input_field.setMinimumHeight(input_height)
            input_field.setMaximumHeight(input_height)
        
        for combo in widget.findChildren(QComboBox):
            combo.setMinimumHeight(input_height)
            combo.setMaximumHeight(input_height)
        
        # الجداول
        for table in widget.findChildren(QTableWidget):
            table.verticalHeader().setDefaultSectionSize(table_row_height)
            table.horizontalHeader().setMinimumHeight(table_row_height)
        
        # الأشجار
        for tree in widget.findChildren(QTreeWidget):
            tree.setIndentation(icon_size)
    
    def get_responsive_stylesheet(self) -> str:
        """الحصول على stylesheet متجاوب"""
        font_size = self.get_font_size("base")
        button_height = self.get_config("button_height") or 36
        input_height = self.get_config("input_height") or 32
        spacing = self.get_config("spacing") or 10
        
        return f"""
        /* الخطوط الأساسية */
        QWidget {{
            font-size: {font_size}px;
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
        }}
        
        /* الأزرار */
        QPushButton {{
            min-height: {button_height}px;
            max-height: {button_height * 2}px;
            padding: {spacing//2}px {spacing}px;
            border-radius: {spacing//2}px;
        }}
        
        /* حقول الإدخال */
        QLineEdit, QComboBox {{
            min-height: {input_height}px;
            max-height: {input_height}px;
            padding: {spacing//3}px {spacing//2}px;
            border-radius: {spacing//3}px;
        }}
        
        /* النصوص */
        QTextEdit {{
            padding: {spacing//2}px;
            border-radius: {spacing//2}px;
        }}
        
        /* المجموعات */
        QGroupBox {{
            font-weight: bold;
            padding-top: {spacing*2}px;
            margin-top: {spacing}px;
        }}
        
        /* التبويبات */
        QTabWidget::pane {{
            border: 1px solid #c0c0c0;
            border-radius: {spacing//2}px;
        }}
        
        QTabBar::tab {{
            padding: {spacing//2}px {spacing}px;
            margin-right: {spacing//4}px;
        }}
        
        /* الجداول */
        QTableWidget {{
            gridline-color: #e0e0e0;
            selection-background-color: #3498db;
        }}
        
        QHeaderView::section {{
            padding: {spacing//2}px;
            border: none;
            border-bottom: 1px solid #c0c0c0;
        }}
        """
    
    def center_window(self, widget: QWidget):
        """توسيط النافذة على الشاشة"""
        screen = QGuiApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = widget.frameGeometry()
            
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            widget.move(window_geometry.topLeft())
    
    def get_screen_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الشاشة"""
        screen = QGuiApplication.primaryScreen()
        if not screen:
            return {}
        
        geometry = screen.geometry()
        available = screen.availableGeometry()
        
        return {
            "size": self.current_screen_size.value if self.current_screen_size else "unknown",
            "resolution": f"{geometry.width()}x{geometry.height()}",
            "available_area": f"{available.width()}x{available.height()}",
            "scale_factor": self.scale_factor,
            "font_scale": self.font_scale,
            "dpi": screen.logicalDotsPerInch(),
            "physical_dpi": screen.physicalDotsPerInch()
        }

# إنشاء مثيل عام
responsive_manager = ResponsiveManager()
