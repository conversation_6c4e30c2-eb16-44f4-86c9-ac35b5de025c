#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة ربط الموردين بالعملات
Test Supplier Currencies Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from src.ui.suppliers.supplier_currencies_window import SupplierCurrenciesWindow
from src.utils.arabic_support import setup_arabic_support

def test_supplier_currencies_window():
    """اختبار نافذة ربط الموردين بالعملات"""
    
    print("🧪 اختبار نافذة ربط الموردين بالعملات")
    print("=" * 50)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إعداد دعم اللغة العربية
        setup_arabic_support(app)
        
        print("✅ تم إنشاء التطبيق وإعداد دعم العربية")
        
        # إنشاء النافذة
        window = SupplierCurrenciesWindow()
        print("✅ تم إنشاء نافذة ربط الموردين بالعملات")
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة")
        
        print("\n📋 معلومات النافذة:")
        print(f"   العنوان: {window.windowTitle()}")
        print(f"   الحجم: {window.size().width()} x {window.size().height()}")
        print(f"   الحد الأدنى للحجم: {window.minimumSize().width()} x {window.minimumSize().height()}")
        
        print("\n🎯 النافذة جاهزة للاستخدام!")
        print("   يمكنك الآن:")
        print("   • اختيار مورد من القائمة المنسدلة")
        print("   • عرض العملات المرتبطة بالمورد")
        print("   • إضافة ربط عملة جديدة")
        print("   • تعديل أو حذف الروابط الموجودة")
        print("   • تعيين عملة أساسية للمورد")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    
    print("\n🔍 اختبار تكامل قاعدة البيانات")
    print("-" * 30)
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import Supplier, Currency, SupplierCurrency
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # فحص الجداول
        suppliers_count = session.query(Supplier).count()
        currencies_count = session.query(Currency).count()
        links_count = session.query(SupplierCurrency).count()
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   الموردين: {suppliers_count}")
        print(f"   العملات: {currencies_count}")
        print(f"   الروابط: {links_count}")
        
        # فحص بعض البيانات التجريبية
        if suppliers_count > 0:
            sample_supplier = session.query(Supplier).first()
            print(f"   مورد تجريبي: {sample_supplier.name}")
            
        if currencies_count > 0:
            sample_currency = session.query(Currency).first()
            print(f"   عملة تجريبية: {sample_currency.name}")
            
        if links_count > 0:
            sample_link = session.query(SupplierCurrency).first()
            print(f"   ربط تجريبي: {sample_link.supplier.name} - {sample_link.currency.name}")
            
        session.close()
        print("✅ تكامل قاعدة البيانات يعمل بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ في تكامل قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 بدء اختبار نافذة ربط الموردين بالعملات")
    print("=" * 60)
    
    # اختبار تكامل قاعدة البيانات أولاً
    test_database_integration()
    
    # اختبار النافذة
    exit_code = test_supplier_currencies_window()
    
    print(f"\n🏁 انتهى الاختبار برمز الخروج: {exit_code}")
    sys.exit(exit_code)
