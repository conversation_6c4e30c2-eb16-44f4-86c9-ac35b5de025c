#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تجريبي لنافذة تفاصيل طلب الحوالة
Demo for Remittance Details Window
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_details_demo():
    """تشغيل العرض التجريبي لنافذة التفاصيل"""
    
    print("🚀 بدء العرض التجريبي لنافذة تفاصيل طلب الحوالة...")
    print("=" * 80)
    
    try:
        # إعداد البيئة
        import os
        os.environ['QT_QPA_PLATFORM'] = 'windows'  # للويندوز
        
        from PySide6.QtWidgets import QApplication, QMessageBox
        import sqlite3
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # البحث عن طلب موجود في قاعدة البيانات
        print("🔍 البحث عن طلبات موجودة...")
        
        db_path = Path("data/proshipment.db")
        if not db_path.exists():
            print("❌ ملف قاعدة البيانات غير موجود")
            return 1
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # جلب آخر طلب
        cursor.execute("""
            SELECT id, request_number, sender_name, receiver_name, 
                   remittance_amount, currency, status 
            FROM remittance_requests 
            ORDER BY id DESC LIMIT 1
        """)
        
        latest_request = cursor.fetchone()
        conn.close()
        
        if not latest_request:
            print("❌ لا توجد طلبات في قاعدة البيانات")
            print("💡 قم بإنشاء طلب حوالة أولاً من الشاشة الرئيسية")
            return 1
        
        request_id = latest_request[0]
        request_number = latest_request[1] or f"REQ-{request_id}"
        sender_name = latest_request[2] or "غير محدد"
        receiver_name = latest_request[3] or "غير محدد"
        amount = latest_request[4] or 0
        currency = latest_request[5] or "غير محدد"
        status = latest_request[6] or "غير محدد"
        
        print(f"✅ تم العثور على طلب للعرض:")
        print(f"   📄 المعرف: {request_id}")
        print(f"   📄 رقم الطلب: {request_number}")
        print(f"   👤 المرسل: {sender_name[:50]}...")
        print(f"   👥 المستقبل: {receiver_name}")
        print(f"   💰 المبلغ: {amount} {currency}")
        print(f"   📊 الحالة: {status}")
        
        print("\n🖼️ فتح نافذة التفاصيل...")
        
        # استيراد وإنشاء نافذة التفاصيل
        from src.ui.remittances.remittance_details_window import RemittanceDetailsWindow
        
        details_window = RemittanceDetailsWindow(request_id)
        details_window.show()
        
        # عرض رسالة توضيحية
        msg = QMessageBox()
        msg.setWindowTitle("نافذة تفاصيل طلب الحوالة")
        msg.setText(f"""
🎉 مرحباً بك في نافذة تفاصيل طلب الحوالة!

📋 الطلب المعروض:
• رقم الطلب: {request_number}
• المرسل: {sender_name[:30]}...
• المستقبل: {receiver_name}
• المبلغ: {amount} {currency}

✨ الميزات المتاحة:
• عرض شامل لجميع بيانات الطلب
• 4 أقسام منظمة (البيانات الأساسية، المرسل، المستقبل، التفاصيل الإضافية)
• أكثر من 25 حقل معلومات
• إمكانية الطباعة
• تحديث الحالة

🖨️ للطباعة:
انقر على زر "طباعة" لفتح نموذج الطباعة

🔄 لتحديث الحالة:
انقر على زر "تحديث الحالة" (قيد التطوير)

❌ للإغلاق:
انقر على زر "إغلاق" أو أغلق النافذة
        """.strip())
        msg.setIcon(QMessageBox.Information)
        msg.exec()
        
        print("✅ تم فتح نافذة التفاصيل بنجاح!")
        print("💡 يمكنك الآن استكشاف جميع تفاصيل الطلب")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PySide6: pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل العرض التجريبي: {e}")
        import traceback
        traceback.print_exc()
        return 1

def display_features():
    """عرض ميزات نافذة التفاصيل"""
    
    print("\n" + "=" * 80)
    print("✨ ميزات نافذة تفاصيل طلب الحوالة")
    print("=" * 80)
    
    print("\n📋 الأقسام الرئيسية:")
    print("   1. 📋 البيانات الأساسية:")
    print("      • رقم الطلب وتاريخ الإنشاء")
    print("      • الفرع والصراف")
    print("      • مبلغ الحوالة والعملة")
    print("      • الغرض من التحويل والحالة")
    
    print("\n   2. 👤 معلومات المرسل:")
    print("      • الاسم الكامل والجهة")
    print("      • أرقام الهاتف والفاكس والموبايل")
    print("      • صندوق البريد والبريد الإلكتروني")
    print("      • العنوان الكامل")
    
    print("\n   3. 👥 معلومات المستقبل:")
    print("      • اسم المستقبل ورقم الحساب")
    print("      • اسم البنك وفرع البنك")
    print("      • رمز السويفت والبلد")
    print("      • العنوان الكامل")
    
    print("\n   4. 📝 تفاصيل إضافية:")
    print("      • الملاحظات والتعليقات")
    print("      • تاريخ الإنشاء والتحديث")
    print("      • معلومات النظام")
    
    print("\n🎨 التصميم:")
    print("   ✅ واجهة احترافية ومنظمة")
    print("   ✅ ألوان مميزة لكل قسم")
    print("   ✅ أيقونات تعبيرية واضحة")
    print("   ✅ تخطيط متجاوب ومرن")
    print("   ✅ منطقة تمرير للبيانات الطويلة")
    
    print("\n🔧 الوظائف:")
    print("   ✅ تحميل تلقائي للبيانات من قاعدة البيانات")
    print("   ✅ عرض شامل لجميع الحقول")
    print("   ✅ تنسيق جميل للتواريخ والأرقام")
    print("   ✅ معالجة البيانات المفقودة")
    print("   ✅ دعم النصوص الطويلة")
    
    print("\n🖨️ الطباعة:")
    print("   ✅ زر طباعة مدمج")
    print("   ✅ ربط مع نموذج الطباعة")
    print("   ✅ تنسيق احترافي للطباعة")
    
    print("\n🔄 التحديث:")
    print("   ✅ زر تحديث الحالة")
    print("   ✅ إمكانية التطوير المستقبلي")
    print("   ✅ تكامل مع النظام")
    
    print("\n🎯 الاستخدام:")
    print("   1. من شاشة طلب الحوالة")
    print("   2. اذهب إلى تبويب 'قائمة الطلبات'")
    print("   3. حدد طلباً من الجدول")
    print("   4. اضغط على 'عرض التفاصيل'")
    print("   5. استكشف جميع أقسام النافذة")
    print("   6. استخدم أزرار الطباعة والتحديث")

if __name__ == "__main__":
    # عرض ميزات النافذة
    display_features()
    
    # تشغيل العرض التجريبي
    exit_code = run_details_demo()
    
    print("\n" + "=" * 80)
    if exit_code == 0:
        print("🎉 انتهى العرض التجريبي بنجاح!")
        print("✅ نافذة التفاصيل تعمل بشكل مثالي")
        print("✅ جميع الأقسام والحقول تعرض البيانات بشكل صحيح")
        print("✅ الطباعة والتحديث متاحان")
    else:
        print("❌ حدث خطأ في العرض التجريبي")
        print("💡 تأكد من وجود بيانات في قاعدة البيانات")
    print("=" * 80)
    
    sys.exit(exit_code)
