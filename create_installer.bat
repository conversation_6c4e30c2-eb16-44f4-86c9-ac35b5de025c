@echo off
chcp 65001 > nul
title ProShipment - إنشاء ملف التثبيت التنفيذي الشامل

echo.
echo ================================================
echo   ProShipment V2.0.0
echo   إنشاء ملف التثبيت التنفيذي الشامل
echo   مع الاحتفاظ بجميع البيانات الموجودة
echo ================================================
echo.

echo 📋 التحقق من متطلبات النظام...
echo.

REM التحقق من Python
echo 🐍 فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo.
    echo 📥 يرجى تثبيت Python 3.8 أو أحدث من:
    echo    https://www.python.org/downloads/
    echo.
    echo ⚠️ تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

python --version
echo ✅ Python متاح

REM التحقق من pip
echo.
echo 📦 فحص pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متاح
    echo 🔧 محاولة إصلاح pip...
    python -m ensurepip --upgrade
    if %errorlevel% neq 0 (
        echo ❌ فشل في إصلاح pip
        pause
        exit /b 1
    )
)

pip --version
echo ✅ pip متاح

echo.
echo ================================================
echo   المرحلة 1: تحضير المشروع
echo ================================================
echo.

echo 🔧 تحضير المشروع للبناء...
python prepare_for_build.py
if %errorlevel% neq 0 (
    echo ❌ فشل في تحضير المشروع
    echo 🔧 يرجى مراجعة الأخطاء أعلاه
    pause
    exit /b 1
)

echo ✅ تم تحضير المشروع بنجاح

echo.
echo ================================================
echo   المرحلة 2: تثبيت متطلبات البناء
echo ================================================
echo.

echo 📦 تحديث pip...
python -m pip install --upgrade pip

echo.
echo 📦 تثبيت متطلبات البناء...
pip install -r requirements_build.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات البناء
    echo.
    echo 🔧 محاولة تثبيت المتطلبات الأساسية فقط...
    pip install pyinstaller PySide6 SQLAlchemy reportlab openpyxl Pillow
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات الأساسية
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت متطلبات البناء

echo.
echo ================================================
echo   المرحلة 3: بناء ملف التثبيت التنفيذي
echo ================================================
echo.

echo 🔨 بدء عملية البناء...
echo ⏳ هذه العملية قد تستغرق 10-30 دقيقة حسب سرعة الجهاز...
echo.

python build_installer.py
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء ملف التثبيت
    echo.
    echo 🔧 نصائح لحل المشاكل:
    echo    1. تأكد من وجود مساحة كافية على القرص الصلب (5+ GB)
    echo    2. أغلق برامج مكافحة الفيروسات مؤقتاً
    echo    3. تشغيل Command Prompt كمدير
    echo    4. تأكد من عدم تشغيل التطبيق حالياً
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================
echo   🎉 تم إنشاء ملف التثبيت بنجاح!
echo ================================================
echo.

echo ✅ العملية اكتملت بنجاح!
echo.
echo 📁 الملفات المنشأة متاحة في مجلد: installer\
echo.

REM عرض الملفات المنشأة
if exist "installer\" (
    echo 📦 الملفات المتاحة:
    echo.
    for %%f in (installer\*.zip) do (
        echo    📄 %%~nxf
    )
    for %%f in (installer\*.tar.gz) do (
        echo    📄 %%~nxf
    )
    echo.
)

echo 🚀 للتوزيع على المستخدمين النهائيين:
echo    1. أرسل أحد الملفات المضغوطة للمستخدم
echo    2. المستخدم يفك الضغط في أي مكان
echo    3. المستخدم يشغل ProShipment.exe
echo.

echo 💾 البيانات الموجودة:
echo    ✅ قاعدة البيانات محفوظة ومتضمنة
echo    ✅ المرفقات محفوظة ومتضمنة  
echo    ✅ الإعدادات محفوظة ومتضمنة
echo.

echo 🎯 متطلبات جهاز المستخدم النهائي:
echo    - Windows 10/11
echo    - 4 GB RAM
echo    - 2 GB مساحة فارغة
echo    - لا يحتاج Python أو برامج إضافية
echo.

echo 📋 للمزيد من المعلومات، راجع:
echo    - INSTALLATION_GUIDE.md
echo    - installer\RELEASE_NOTES_V2.0.0.md
echo.

echo ================================================
echo   العملية اكتملت بنجاح! 🎊
echo ================================================

pause
