#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_improved_remittance_dialog():
    """اختبار نافذة إنشاء الحوالة المحسنة"""
    print("🔍 اختبار نافذة إنشاء الحوالة المحسنة...")
    print("=" * 60)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة
        dialog = NewRemittanceDialog()
        print("✅ تم إنشاء نافذة الحوالة المحسنة")
        
        # اختبار الحقول الجديدة
        print("\n🔄 اختبار الحقول الجديدة...")
        
        # التحقق من وجود الحقول الجديدة
        if hasattr(dialog, 'transfer_entity_combo'):
            print("   ✅ حقل جهة التحويل موجود")
        else:
            print("   ❌ حقل جهة التحويل مفقود")
            
        if hasattr(dialog, 'transfer_entity_name_combo'):
            print("   ✅ حقل اسم جهة التحويل موجود")
        else:
            print("   ❌ حقل اسم جهة التحويل مفقود")
            
        if hasattr(dialog, 'suppliers_table'):
            print("   ✅ جدول الموردين موجود")
        else:
            print("   ❌ جدول الموردين مفقود")
            
        # التحقق من الأزرار الجديدة
        print("\n🔄 اختبار الأزرار الجديدة...")
        
        if hasattr(dialog, 'save_draft_btn'):
            print("   ✅ زر حفظ كمسودة موجود")
        else:
            print("   ❌ زر حفظ كمسودة مفقود")
            
        if hasattr(dialog, 'confirm_btn'):
            print("   ✅ زر تأكيد الحوالة موجود")
        else:
            print("   ❌ زر تأكيد الحوالة مفقود")
            
        if hasattr(dialog, 'transfer_to_suppliers_btn'):
            print("   ✅ زر ترحيل للموردين موجود")
            if not dialog.transfer_to_suppliers_btn.isEnabled():
                print("   ✅ زر الترحيل معطل بشكل صحيح")
            else:
                print("   ⚠️ زر الترحيل يجب أن يكون معطل في البداية")
        else:
            print("   ❌ زر ترحيل للموردين مفقود")
            
        # اختبار الدوال الجديدة
        print("\n🔄 اختبار الدوال الجديدة...")
        
        if hasattr(dialog, 'update_transfer_entity_name'):
            print("   ✅ دالة تحديث جهة التحويل موجودة")
        else:
            print("   ❌ دالة تحديث جهة التحويل مفقودة")
            
        if hasattr(dialog, 'add_supplier_to_remittance'):
            print("   ✅ دالة إضافة مورد موجودة")
        else:
            print("   ❌ دالة إضافة مورد مفقودة")
            
        if hasattr(dialog, 'confirm_remittance'):
            print("   ✅ دالة تأكيد الحوالة موجودة")
        else:
            print("   ❌ دالة تأكيد الحوالة مفقودة")
            
        if hasattr(dialog, 'transfer_to_suppliers'):
            print("   ✅ دالة ترحيل للموردين موجودة")
        else:
            print("   ❌ دالة ترحيل للموردين مفقودة")
            
        print("\n📋 الميزات الجديدة المضافة:")
        print("   1. ✅ القسم الأول: معلومات الحوالة الأساسية")
        print("      - التاريخ")
        print("      - رقم الحوالة")
        print("      - جهة التحويل (بنك/صراف)")
        print("      - اسم جهة التحويل (مرتبط بإدارة البنوك)")
        print("      - الرقم المرجعي")
        print("      - العملة")
        print("      - المبلغ")
        print("      - حالة الحوالة")
        print("      - ملاحظات")
        print()
        print("   2. ✅ القسم الثاني: الموردين المحول لهم")
        print("      - جدول الموردين")
        print("      - إضافة/حذف موردين")
        print("      - عرض إجمالي المبلغ الموزع")
        print()
        print("   3. ✅ الأزرار الجديدة:")
        print("      - حفظ كمسودة")
        print("      - تأكيد الحوالة")
        print("      - ترحيل للموردين (يتم تفعيله بعد التأكيد)")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النافذة جاهزة للاستخدام مع جميع الميزات الجديدة.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📝 كيفية استخدام النافذة المحسنة:")
    print("=" * 40)
    print("1. فتح النافذة:")
    print("   - من القائمة الرئيسية: إدارة الحوالات → إنشاء حوالة جديدة")
    print()
    print("2. ملء البيانات الأساسية:")
    print("   - اختر جهة التحويل (بنك أو صراف)")
    print("   - اختر اسم الجهة (سيتم تحديث القائمة تلقائياً)")
    print("   - أدخل المبلغ والعملة")
    print("   - أدخل الرقم المرجعي")
    print()
    print("3. إضافة الموردين:")
    print("   - انقر 'إضافة مورد'")
    print("   - اختر المورد والمبلغ")
    print("   - تأكد من تطابق إجمالي المبلغ الموزع")
    print()
    print("4. حفظ وتأكيد:")
    print("   - احفظ كمسودة أولاً")
    print("   - راجع البيانات")
    print("   - اضغط 'تأكيد الحوالة'")
    print("   - اضغط 'ترحيل للموردين' لإتمام العملية")

if __name__ == "__main__":
    success = test_improved_remittance_dialog()
    
    if success:
        show_usage_instructions()
        print("\n🚀 النافذة المحسنة جاهزة للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
