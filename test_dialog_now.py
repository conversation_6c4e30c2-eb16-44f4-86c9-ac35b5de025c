#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 اختبار النافذة الآن...")

try:
    # اختبار الاستيراد
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    print("✅ تم إعداد المسار")
    
    from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
    print("✅ تم استيراد NewRemittanceDialog")
    
    from PySide6.QtWidgets import QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    print("✅ تم إنشاء QApplication")
    
    dialog = NewRemittanceDialog()
    print("✅ تم إنشاء النافذة بنجاح!")
    
    # اختبار الدوال
    dialog.setup_validators()
    print("✅ setup_validators تعمل")
    
    dialog.validate_form()
    print("✅ validate_form تعمل")
    
    dialog.collect_form_data()
    print("✅ collect_form_data تعمل")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("النافذة جاهزة للاستخدام بدون أي أخطاء.")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
