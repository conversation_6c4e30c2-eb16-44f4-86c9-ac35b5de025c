#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تفاصيل طلب الحوالة
Test Remittance Details Window
"""

import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_details_window_structure():
    """اختبار هيكل نافذة التفاصيل"""
    
    print("🔍 اختبار هيكل نافذة التفاصيل...")
    print("=" * 60)
    
    try:
        # قراءة ملف نافذة التفاصيل
        details_path = "src/ui/remittances/remittance_details_window.py"
        with open(details_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص العناصر الأساسية
        essential_elements = [
            ("class RemittanceDetailsWindow", "فئة نافذة التفاصيل"),
            ("create_header", "دالة إنشاء الرأس"),
            ("create_basic_info_section", "دالة البيانات الأساسية"),
            ("create_sender_section", "دالة معلومات المرسل"),
            ("create_receiver_section", "دالة معلومات المستقبل"),
            ("create_additional_info_section", "دالة التفاصيل الإضافية"),
            ("load_request_data", "دالة تحميل البيانات"),
            ("populate_data", "دالة تعبئة البيانات"),
            ("print_details", "دالة الطباعة"),
            ("update_status", "دالة تحديث الحالة")
        ]
        
        print("   📋 فحص العناصر الأساسية:")
        all_found = True
        
        for element, description in essential_elements:
            if element in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        # فحص الأقسام المطلوبة
        sections = [
            "📋 البيانات الأساسية",
            "👤 معلومات المرسل", 
            "👥 معلومات المستقبل",
            "📝 تفاصيل إضافية"
        ]
        
        print("\n   📝 فحص الأقسام:")
        for section in sections:
            if section in code:
                print(f"      ✅ {section}")
            else:
                print(f"      ❌ {section} - مفقود")
                all_found = False
        
        # فحص الحقول المطلوبة
        required_fields = [
            "رقم الطلب", "تاريخ الطلب", "الفرع", "الصراف",
            "مبلغ الحوالة", "العملة", "الغرض", "الحالة",
            "الاسم الكامل", "الجهة", "رقم الهاتف", "رقم الفاكس",
            "رقم الموبايل", "ص.ب", "البريد الإلكتروني", "العنوان",
            "اسم المستقبل", "رقم الحساب", "اسم البنك", "فرع البنك",
            "السويفت", "البلد", "الملاحظات"
        ]
        
        print("\n   📊 فحص الحقول المطلوبة:")
        missing_fields = []
        for field in required_fields:
            if field in code:
                print(f"      ✅ {field}")
            else:
                print(f"      ❌ {field} - مفقود")
                missing_fields.append(field)
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص نافذة التفاصيل: {e}")
        return False

def test_integration_with_main_window():
    """اختبار التكامل مع النافذة الرئيسية"""
    
    print("\n🔗 اختبار التكامل مع النافذة الرئيسية...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        main_path = "src/ui/remittances/remittance_request_window.py"
        with open(main_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص استيراد نافذة التفاصيل
        print("   📋 فحص التكامل:")
        
        if "from .remittance_details_window import RemittanceDetailsWindow" in code:
            print("      ✅ استيراد نافذة التفاصيل موجود")
        else:
            print("      ❌ استيراد نافذة التفاصيل مفقود")
            return False
        
        # فحص إنشاء نافذة التفاصيل
        if "RemittanceDetailsWindow(self.selected_request_id, self)" in code:
            print("      ✅ إنشاء نافذة التفاصيل موجود")
        else:
            print("      ❌ إنشاء نافذة التفاصيل مفقود")
            return False
        
        # فحص دالة اختيار الطلب
        if "def on_request_selected" in code:
            print("      ✅ دالة اختيار الطلب موجودة")
        else:
            print("      ❌ دالة اختيار الطلب مفقودة")
            return False
        
        # فحص تحديث selected_request_id
        if "self.selected_request_id = self.current_requests[row][0]" in code:
            print("      ✅ تحديث معرف الطلب المختار موجود")
        else:
            print("      ❌ تحديث معرف الطلب المختار مفقود")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص التكامل: {e}")
        return False

def test_database_compatibility():
    """اختبار توافق قاعدة البيانات"""
    
    print("\n🗄️ اختبار توافق قاعدة البيانات...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        if not db_path.exists():
            print("   ❌ ملف قاعدة البيانات غير موجود")
            return False
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='remittance_requests'")
        if not cursor.fetchone():
            print("   ❌ جدول remittance_requests غير موجود")
            conn.close()
            return False
        
        print("   ✅ جدول remittance_requests موجود")
        
        # فحص وجود بيانات للاختبار
        cursor.execute("SELECT COUNT(*) FROM remittance_requests")
        count = cursor.fetchone()[0]
        
        print(f"   📊 عدد الطلبات في قاعدة البيانات: {count}")
        
        if count > 0:
            # اختبار جلب طلب واحد
            cursor.execute("SELECT id, request_number, sender_name, receiver_name FROM remittance_requests LIMIT 1")
            sample = cursor.fetchone()
            
            if sample:
                print(f"   ✅ عينة من البيانات:")
                print(f"      - المعرف: {sample[0]}")
                print(f"      - رقم الطلب: {sample[1] or 'غير محدد'}")
                print(f"      - المرسل: {sample[2] or 'غير محدد'}")
                print(f"      - المستقبل: {sample[3] or 'غير محدد'}")
        else:
            print("   ⚠️ لا توجد بيانات للاختبار")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def create_test_data():
    """إنشاء بيانات تجريبية للاختبار"""
    
    print("\n🧪 إنشاء بيانات تجريبية...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # بيانات تجريبية
        test_data = {
            'request_number': f'TEST{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'request_date': '2024-12-09',
            'branch': 'الفرع الرئيسي',
            'exchanger': 'أحمد الصراف',
            'remittance_amount': 2500.0,
            'currency': 'ريال يمني',
            'transfer_purpose': 'اختبار نافذة التفاصيل',
            'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
            'sender_entity': 'G.M: NASHA\'AT RASHAD QASIM ALDUBAEE',
            'sender_phone': '+967 1 616109',
            'sender_fax': '+967 1 615909',
            'sender_mobile': '+967 *********',
            'sender_pobox': '1903',
            'sender_email': '<EMAIL>, <EMAIL>',
            'sender_address': 'TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',
            'receiver_name': 'سارة أحمد محمد',
            'receiver_account': '****************',
            'receiver_bank_name': 'بنك الإنماء',
            'receiver_bank_branch': 'فرع الدمام',
            'receiver_swift': 'INMASARI',
            'receiver_country': 'السعودية',
            'receiver_address': 'الدمام، السعودية',
            'notes': 'طلب تجريبي لاختبار نافذة التفاصيل',
            'status': 'معلق',
            'created_at': datetime.now().isoformat()
        }
        
        # إدراج البيانات التجريبية
        cursor.execute("""
            INSERT INTO remittance_requests (
                request_number, request_date, branch, exchanger, remittance_amount, 
                currency, transfer_purpose, sender_name, sender_entity, sender_phone, 
                sender_fax, sender_mobile, sender_pobox, sender_email, sender_address,
                receiver_name, receiver_account, receiver_bank_name, receiver_bank_branch, 
                receiver_swift, receiver_country, receiver_address, notes, status, created_at,
                amount, source_currency, target_currency
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_data['request_number'], test_data['request_date'], test_data['branch'],
            test_data['exchanger'], test_data['remittance_amount'], test_data['currency'],
            test_data['transfer_purpose'], test_data['sender_name'], test_data['sender_entity'],
            test_data['sender_phone'], test_data['sender_fax'], test_data['sender_mobile'],
            test_data['sender_pobox'], test_data['sender_email'], test_data['sender_address'],
            test_data['receiver_name'], test_data['receiver_account'], test_data['receiver_bank_name'],
            test_data['receiver_bank_branch'], test_data['receiver_swift'], test_data['receiver_country'],
            test_data['receiver_address'], test_data['notes'], test_data['status'], test_data['created_at'],
            # الحقول القديمة المطلوبة
            test_data['remittance_amount'], test_data['currency'], test_data['currency']
        ))
        
        test_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"   ✅ تم إنشاء بيانات تجريبية بمعرف: {test_id}")
        print(f"   📄 رقم الطلب: {test_data['request_number']}")
        print(f"   👤 المرسل: {test_data['sender_name'][:50]}...")
        print(f"   👥 المستقبل: {test_data['receiver_name']}")
        print(f"   💰 المبلغ: {test_data['remittance_amount']} {test_data['currency']}")
        
        return test_id
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return None

def display_fix_summary():
    """عرض ملخص الإصلاح"""
    
    print("\n" + "=" * 80)
    print("🔧 ملخص إصلاح مشكلة عرض التفاصيل")
    print("=" * 80)
    
    print("\n❌ المشكلة الأصلية:")
    print("   عند تحديد طلب من القائمة والضغط على 'عرض التفاصيل' لا يتم عرض الطلب")
    
    print("\n🔍 السبب:")
    print("   - دالة view_request_details كانت تعرض رسالة بسيطة فقط")
    print("   - لم تكن هناك نافذة مخصصة لعرض التفاصيل")
    print("   - عدم وجود واجهة شاملة لعرض جميع بيانات الطلب")
    
    print("\n✅ الحلول المطبقة:")
    print("   1. إنشاء نافذة تفاصيل شاملة:")
    print("      - RemittanceDetailsWindow فئة مخصصة")
    print("      - 4 أقسام منظمة للبيانات")
    print("      - 25+ حقل لعرض جميع التفاصيل")
    print("      - تصميم احترافي ومنظم")
    
    print("\n   2. تحديث النافذة الرئيسية:")
    print("      - استيراد نافذة التفاصيل")
    print("      - تحديث دالة view_request_details")
    print("      - ربط صحيح مع معرف الطلب المختار")
    
    print("\n   3. ميزات إضافية:")
    print("      - زر طباعة التفاصيل")
    print("      - زر تحديث الحالة")
    print("      - منطقة تمرير للبيانات الطويلة")
    print("      - تنسيق جميل وواضح")
    
    print("\n🎯 النتائج المحققة:")
    print("   ✅ عرض شامل لجميع بيانات الطلب")
    print("   ✅ واجهة مستخدم احترافية")
    print("   ✅ تنظيم منطقي للمعلومات")
    print("   ✅ إمكانية الطباعة والتحديث")
    print("   ✅ تكامل مثالي مع النظام")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. فتح شاشة طلب الحوالة")
    print("   2. الذهاب إلى تبويب 'قائمة الطلبات'")
    print("   3. تحديد طلب من الجدول")
    print("   4. الضغط على زر 'عرض التفاصيل'")
    print("   5. ستفتح نافذة شاملة بجميع التفاصيل")

if __name__ == "__main__":
    print("🚀 بدء اختبار نافذة تفاصيل طلب الحوالة...")
    print("=" * 80)
    
    # اختبار هيكل النافذة
    structure_success = test_details_window_structure()
    
    # اختبار التكامل
    integration_success = test_integration_with_main_window()
    
    # اختبار قاعدة البيانات
    db_success = test_database_compatibility()
    
    # إنشاء بيانات تجريبية
    test_id = create_test_data()
    
    # عرض ملخص الإصلاح
    display_fix_summary()
    
    # النتيجة النهائية
    if structure_success and integration_success and db_success:
        print("\n🏆 تم إصلاح مشكلة عرض التفاصيل بنجاح!")
        print("✅ نافذة التفاصيل مكتملة ومتكاملة")
        print("✅ التكامل مع النافذة الرئيسية ناجح")
        print("✅ قاعدة البيانات متوافقة")
        
        if test_id:
            print(f"✅ بيانات تجريبية جاهزة (معرف: {test_id})")
        
        print("\n🎉 يمكنك الآن عرض تفاصيل الطلبات بشكل شامل!")
        print("💡 جرب: حدد طلباً من القائمة واضغط 'عرض التفاصيل'")
        
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إلى حل")
        if not structure_success:
            print("   - مشكلة في هيكل نافذة التفاصيل")
        if not integration_success:
            print("   - مشكلة في التكامل مع النافذة الرئيسية")
        if not db_success:
            print("   - مشكلة في قاعدة البيانات")
    
    print("=" * 80)
