#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحديثات الجديدة في نافذة طلب الحوالة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt
    from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
    
    def test_print_removal():
        """اختبار حذف نموذج الطباعة"""
        print("🧪 اختبار 1: حذف نموذج الطباعة...")
        
        try:
            # إنشاء النافذة
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            window = RemittanceRequestWindow()
            
            # التحقق من عدم وجود دالة الطباعة
            if not hasattr(window, 'print_request_form'):
                print("✅ تم حذف دالة الطباعة بنجاح")
                return True
            else:
                print("❌ دالة الطباعة ما زالت موجودة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
    
    def test_pdf_libraries():
        """اختبار مكتبات PDF"""
        print("\n🧪 اختبار 2: مكتبات PDF للعربية...")
        
        libraries_status = {}
        
        # اختبار ReportLab
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from arabic_reshaper import arabic_reshaper
            from bidi.algorithm import get_display
            libraries_status['ReportLab'] = True
            print("✅ ReportLab متوفرة")
        except ImportError as e:
            libraries_status['ReportLab'] = False
            print(f"❌ ReportLab غير متوفرة: {e}")
        
        # اختبار FPDF2
        try:
            from fpdf import FPDF
            libraries_status['FPDF2'] = True
            print("✅ FPDF2 متوفرة")
        except ImportError as e:
            libraries_status['FPDF2'] = False
            print(f"❌ FPDF2 غير متوفرة: {e}")
        
        # اختبار WeasyPrint
        try:
            import weasyprint
            libraries_status['WeasyPrint'] = True
            print("✅ WeasyPrint متوفرة")
        except (ImportError, OSError) as e:
            libraries_status['WeasyPrint'] = False
            print(f"❌ WeasyPrint غير متوفرة: {e}")
        
        # اختبار مكتبات دعم العربية
        try:
            import arabic_reshaper
            import bidi
            libraries_status['Arabic Support'] = True
            print("✅ مكتبات دعم العربية متوفرة")
        except ImportError as e:
            libraries_status['Arabic Support'] = False
            print(f"❌ مكتبات دعم العربية غير متوفرة: {e}")
        
        successful_libs = sum(libraries_status.values())
        total_libs = len(libraries_status)
        
        print(f"📊 النتيجة: {successful_libs}/{total_libs} مكتبات متوفرة")
        return successful_libs >= 2  # نحتاج على الأقل مكتبتين للعمل
    
    def test_exit_button():
        """اختبار زر الخروج"""
        print("\n🧪 اختبار 3: زر الخروج...")
        
        try:
            # إنشاء النافذة
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            window = RemittanceRequestWindow()
            
            # التحقق من وجود دالة الخروج
            if hasattr(window, 'close_application'):
                print("✅ دالة الخروج موجودة")
                
                # التحقق من وجود دالة التحقق من التغييرات
                if hasattr(window, 'has_unsaved_changes'):
                    print("✅ دالة التحقق من التغييرات موجودة")
                    
                    # التحقق من وجود دالة حفظ البيانات
                    if hasattr(window, 'save_current_data'):
                        print("✅ دالة حفظ البيانات موجودة")
                        return True
                    else:
                        print("❌ دالة حفظ البيانات غير موجودة")
                        return False
                else:
                    print("❌ دالة التحقق من التغييرات غير موجودة")
                    return False
            else:
                print("❌ دالة الخروج غير موجودة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
    
    def test_toolbar():
        """اختبار شريط الأدوات"""
        print("\n🧪 اختبار 4: شريط الأدوات...")
        
        try:
            # إنشاء النافذة
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            window = RemittanceRequestWindow()
            
            # التحقق من وجود شريط الأدوات
            from PySide6.QtWidgets import QToolBar
            toolbars = window.findChildren(QToolBar)
            if toolbars:
                toolbar = toolbars[0]
                actions = toolbar.actions()
                
                # البحث عن زر الخروج
                exit_action_found = False
                for action in actions:
                    if "خروج" in action.text():
                        exit_action_found = True
                        break
                
                if exit_action_found:
                    print("✅ زر الخروج موجود في شريط الأدوات")
                    print(f"📊 عدد الأزرار في شريط الأدوات: {len([a for a in actions if not a.isSeparator()])}")
                    return True
                else:
                    print("❌ زر الخروج غير موجود في شريط الأدوات")
                    return False
            else:
                print("❌ شريط الأدوات غير موجود")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
    
    def main():
        """الدالة الرئيسية للاختبار"""
        print("🚀 بدء اختبار التحديثات الجديدة في نافذة طلب الحوالة...\n")
        
        results = []
        
        # تشغيل الاختبارات
        results.append(test_print_removal())
        results.append(test_pdf_libraries())
        results.append(test_exit_button())
        results.append(test_toolbar())
        
        # عرض النتائج
        print("\n" + "="*60)
        print("📊 ملخص نتائج الاختبار:")
        print("="*60)
        
        test_names = [
            "حذف نموذج الطباعة",
            "مكتبات PDF للعربية", 
            "زر الخروج",
            "شريط الأدوات"
        ]
        
        successful_tests = 0
        for i, (test_name, result) in enumerate(zip(test_names, results)):
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{i+1}. {test_name}: {status}")
            if result:
                successful_tests += 1
        
        print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
        
        if successful_tests == len(results):
            print("\n🎉 جميع التحديثات تعمل بنجاح!")
            print("✅ تم حذف نموذج الطباعة القديم")
            print("✅ تم تثبيت مكتبات PDF تدعم العربية")
            print("✅ تم إضافة زر خروج ذكي للنافذة")
        elif successful_tests >= len(results) * 0.75:
            print("\n✅ معظم التحديثات تعمل بنجاح!")
        else:
            print("\n⚠️ بعض التحديثات تحتاج إلى مراجعة")
        
        return successful_tests >= len(results) * 0.75
    
    if __name__ == "__main__":
        success = main()
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من تثبيت PySide6 وأن مسار المشروع صحيح")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
