#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات البنوك والصرافات
Test Banks and Exchanges Fixes
"""

import sqlite3
from pathlib import Path

def test_banks_exchanges_fixes():
    """اختبار إصلاحات البنوك والصرافات"""
    
    print("🧪 اختبار إصلاحات البنوك والصرافات")
    print("=" * 50)
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. اختبار إضافة بنك جديد
        print("\n🏦 اختبار إضافة بنك جديد...")
        
        test_bank_data = (
            'TEST001',  # code
            'بنك الاختبار',  # name
            'Test Bank',  # name_en
            'TESTSAR',  # swift_code
            'السعودية',  # country
            'الرياض',  # city
            'شارع الاختبار',  # address
            '011-1234567',  # phone
            '011-1234568',  # fax - هذا العمود كان مفقوداً
            '<EMAIL>',  # email
            'www.testbank.com',  # website
            'تجاري',  # bank_type
            1,  # is_active
            'بنك تجريبي للاختبار'  # notes
        )
        
        try:
            cursor.execute("""
                INSERT INTO banks (code, name, name_en, swift_code, country, city, address, phone, fax, email, website, bank_type, is_active, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, test_bank_data)
            
            bank_id = cursor.lastrowid
            print(f"✅ تم إضافة بنك جديد بنجاح (ID: {bank_id})")
            
            # التحقق من البيانات المضافة
            cursor.execute("SELECT name, fax FROM banks WHERE id = ?", (bank_id,))
            result = cursor.fetchone()
            if result:
                print(f"   الاسم: {result[0]}")
                print(f"   الفاكس: {result[1]}")
            
        except Exception as e:
            print(f"❌ فشل في إضافة البنك: {str(e)}")
            return False
        
        # 2. اختبار إضافة صرافة جديدة
        print("\n💱 اختبار إضافة صرافة جديدة...")
        
        test_exchange_data = (
            'صرافة الاختبار',  # name
            'Test Exchange',  # name_en - هذا العمود كان مفقوداً
            'TEST001',  # code - هذا العمود كان مفقوداً
            'صرافة',  # category
            'SR-EX-TEST',  # license_number
            '011-7654321',  # phone
            '**********',  # mobile
            '<EMAIL>',  # email
            'الرياض، شارع الاختبار',  # address
            'www.testexchange.com',  # website
            20.0,  # transfer_fee
            0.5,  # commission_rate
            'صرافة تجريبية للاختبار',  # notes
            1  # is_active
        )
        
        try:
            cursor.execute("""
                INSERT INTO exchanges (name, name_en, code, category, license_number, phone, mobile, email, address, website, transfer_fee, commission_rate, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, test_exchange_data)
            
            exchange_id = cursor.lastrowid
            print(f"✅ تم إضافة صرافة جديدة بنجاح (ID: {exchange_id})")
            
            # التحقق من البيانات المضافة
            cursor.execute("SELECT name, name_en, code FROM exchanges WHERE id = ?", (exchange_id,))
            result = cursor.fetchone()
            if result:
                print(f"   الاسم: {result[0]}")
                print(f"   الاسم الإنجليزي: {result[1]}")
                print(f"   الكود: {result[2]}")
            
        except Exception as e:
            print(f"❌ فشل في إضافة الصرافة: {str(e)}")
            return False
        
        # 3. اختبار عرض البيانات
        print("\n📊 اختبار عرض البيانات...")
        
        # عرض البنوك
        cursor.execute("SELECT COUNT(*) FROM banks")
        banks_count = cursor.fetchone()[0]
        print(f"📈 إجمالي البنوك: {banks_count}")
        
        cursor.execute("SELECT name, fax FROM banks WHERE fax IS NOT NULL LIMIT 3")
        banks_with_fax = cursor.fetchall()
        print("📋 بنوك لديها فاكس:")
        for bank in banks_with_fax:
            print(f"   • {bank[0]} - فاكس: {bank[1]}")
        
        # عرض الصرافات
        cursor.execute("SELECT COUNT(*) FROM exchanges")
        exchanges_count = cursor.fetchone()[0]
        print(f"📈 إجمالي الصرافات: {exchanges_count}")
        
        cursor.execute("SELECT name, name_en, code FROM exchanges WHERE name_en IS NOT NULL LIMIT 3")
        exchanges_with_en = cursor.fetchall()
        print("📋 صرافات لديها اسم إنجليزي:")
        for exchange in exchanges_with_en:
            print(f"   • {exchange[0]} ({exchange[1]}) - كود: {exchange[2]}")
        
        # 4. تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        cursor.execute("DELETE FROM banks WHERE code = 'TEST001'")
        cursor.execute("DELETE FROM exchanges WHERE code = 'TEST001'")
        print("✅ تم حذف البيانات التجريبية")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_column_existence():
    """اختبار وجود الأعمدة المطلوبة"""
    
    print("\n🔍 اختبار وجود الأعمدة المطلوبة...")
    
    db_path = Path("data/proshipment.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # فحص جدول البنوك
    cursor.execute("PRAGMA table_info(banks)")
    banks_columns = [row[1] for row in cursor.fetchall()]
    
    required_banks_columns = ['fax']
    missing_banks = [col for col in required_banks_columns if col not in banks_columns]
    
    if missing_banks:
        print(f"❌ أعمدة مفقودة في جدول البنوك: {missing_banks}")
    else:
        print("✅ جدول البنوك يحتوي على جميع الأعمدة المطلوبة")
    
    # فحص جدول الصرافات
    cursor.execute("PRAGMA table_info(exchanges)")
    exchanges_columns = [row[1] for row in cursor.fetchall()]
    
    required_exchanges_columns = ['name_en', 'code']
    missing_exchanges = [col for col in required_exchanges_columns if col not in exchanges_columns]
    
    if missing_exchanges:
        print(f"❌ أعمدة مفقودة في جدول الصرافات: {missing_exchanges}")
    else:
        print("✅ جدول الصرافات يحتوي على جميع الأعمدة المطلوبة")
    
    conn.close()
    
    return len(missing_banks) == 0 and len(missing_exchanges) == 0

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاحات البنوك والصرافات...")
    print("=" * 60)
    
    # اختبار وجود الأعمدة
    columns_ok = test_column_existence()
    
    if columns_ok:
        # اختبار العمليات
        operations_ok = test_banks_exchanges_fixes()
        
        if operations_ok:
            print("\n🏆 جميع الاختبارات نجحت!")
            print("✅ يمكنك الآن إضافة بنوك وصرافات جديدة بدون أخطاء")
            print("✅ جميع الحقول متاحة للاستخدام")
            print("✅ قاعدة البيانات مستقرة وجاهزة")
        else:
            print("\n❌ فشلت بعض اختبارات العمليات")
    else:
        print("\n❌ بعض الأعمدة المطلوبة مفقودة")
    
    print("=" * 60)
