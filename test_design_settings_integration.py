#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دمج نافذة إعدادات التصميم في التطبيق الرئيسي
Test Design Settings Integration in Main Application
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_window_menu():
    """اختبار وجود نافذة إعدادات التصميم في القائمة الرئيسية"""
    print("🧪 اختبار دمج إعدادات التصميم في النافذة الرئيسية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        print("   ✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود دالة فتح إعدادات التصميم
        if hasattr(main_window, 'open_design_settings'):
            print("   ✅ دالة open_design_settings موجودة")
        else:
            print("   ❌ دالة open_design_settings غير موجودة")
            return False
        
        # التحقق من وجود دالة معالج تطبيق الإعدادات
        if hasattr(main_window, 'on_design_settings_applied'):
            print("   ✅ دالة on_design_settings_applied موجودة")
        else:
            print("   ❌ دالة on_design_settings_applied غير موجودة")
            return False
        
        # التحقق من شريط القوائم
        menu_bar = main_window.menuBar()
        if menu_bar:
            print("   ✅ شريط القوائم موجود")
            
            # البحث عن قائمة العرض
            view_menu = None
            for action in menu_bar.actions():
                if "العرض" in action.text():
                    view_menu = action.menu()
                    break
            
            if view_menu:
                print("   ✅ قائمة العرض موجودة")
                
                # البحث عن إعدادات التصميم
                design_settings_found = False
                for action in view_menu.actions():
                    if "إعدادات التصميم" in action.text():
                        design_settings_found = True
                        print("   ✅ عنصر 'إعدادات التصميم' موجود في قائمة العرض")
                        break
                
                if not design_settings_found:
                    print("   ❌ عنصر 'إعدادات التصميم' غير موجود في قائمة العرض")
                    return False
            else:
                print("   ❌ قائمة العرض غير موجودة")
                return False
        else:
            print("   ❌ شريط القوائم غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النافذة الرئيسية: {e}")
        return False

def test_tree_menu_integration():
    """اختبار دمج إعدادات التصميم في القائمة الشجرية"""
    print("\n🧪 اختبار دمج إعدادات التصميم في القائمة الشجرية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # التحقق من وجود القائمة الشجرية
        if hasattr(main_window, 'menu_tree'):
            print("   ✅ القائمة الشجرية موجودة")
            
            # البحث عن عنصر "إعدادات التصميم"
            design_settings_found = False
            
            # البحث في جميع العناصر
            root = main_window.menu_tree.invisibleRootItem()
            for i in range(root.childCount()):
                parent_item = root.child(i)
                for j in range(parent_item.childCount()):
                    child_item = parent_item.child(j)
                    if child_item.text(0) == "الإعدادات":
                        # البحث في عناصر الإعدادات
                        for k in range(child_item.childCount()):
                            setting_item = child_item.child(k)
                            if setting_item.text(0) == "إعدادات التصميم":
                                design_settings_found = True
                                print("   ✅ عنصر 'إعدادات التصميم' موجود في القائمة الشجرية")
                                break
                    if design_settings_found:
                        break
                if design_settings_found:
                    break
            
            if not design_settings_found:
                print("   ❌ عنصر 'إعدادات التصميم' غير موجود في القائمة الشجرية")
                return False
        else:
            print("   ❌ القائمة الشجرية غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار القائمة الشجرية: {e}")
        return False

def test_design_settings_dialog_opening():
    """اختبار فتح نافذة إعدادات التصميم"""
    print("\n🧪 اختبار فتح نافذة إعدادات التصميم...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # محاولة فتح نافذة إعدادات التصميم
        main_window.open_design_settings()
        
        # التحقق من إنشاء النافذة
        if hasattr(main_window, 'design_settings_window') and main_window.design_settings_window:
            print("   ✅ تم إنشاء نافذة إعدادات التصميم")
            
            # التحقق من عنوان النافذة
            window_title = main_window.design_settings_window.windowTitle()
            if "إعدادات التصميم" in window_title:
                print(f"   ✅ عنوان النافذة صحيح: {window_title}")
            else:
                print(f"   ⚠️ عنوان النافذة: {window_title}")
            
            # التحقق من التبويبات
            if hasattr(main_window.design_settings_window, 'tab_widget'):
                tab_count = main_window.design_settings_window.tab_widget.count()
                print(f"   ✅ عدد التبويبات: {tab_count}")
                
                # عرض أسماء التبويبات
                for i in range(tab_count):
                    tab_name = main_window.design_settings_window.tab_widget.tabText(i)
                    print(f"      {i+1}. {tab_name}")
            
            # إغلاق النافذة
            main_window.design_settings_window.close()
            print("   ✅ تم إغلاق النافذة بنجاح")
            
            return True
        else:
            print("   ❌ فشل في إنشاء نافذة إعدادات التصميم")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في فتح نافذة إعدادات التصميم: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_menu_action_connection():
    """اختبار ربط إجراءات القائمة"""
    print("\n🧪 اختبار ربط إجراءات القائمة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # البحث عن إجراء إعدادات التصميم في قائمة العرض
        menu_bar = main_window.menuBar()
        design_action = None
        
        for action in menu_bar.actions():
            if "العرض" in action.text():
                view_menu = action.menu()
                for sub_action in view_menu.actions():
                    if "إعدادات التصميم" in sub_action.text():
                        design_action = sub_action
                        break
                break
        
        if design_action:
            print("   ✅ تم العثور على إجراء إعدادات التصميم")
            
            # التحقق من الربط
            if design_action.receivers(design_action.triggered) > 0:
                print("   ✅ الإجراء مربوط بدالة")
            else:
                print("   ❌ الإجراء غير مربوط بدالة")
                return False
            
            # التحقق من tooltip
            tooltip = design_action.toolTip()
            if tooltip:
                print(f"   ✅ tooltip موجود: {tooltip}")
            else:
                print("   ⚠️ tooltip غير موجود")
            
            return True
        else:
            print("   ❌ لم يتم العثور على إجراء إعدادات التصميم")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار ربط الإجراءات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار دمج نافذة إعدادات التصميم في التطبيق")
    print("="*60)
    
    tests = [
        ("قائمة النافذة الرئيسية", test_main_window_menu),
        ("القائمة الشجرية", test_tree_menu_integration),
        ("فتح نافذة إعدادات التصميم", test_design_settings_dialog_opening),
        ("ربط إجراءات القائمة", test_menu_action_connection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج اختبار دمج إعدادات التصميم")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.8:
        print("\n🎉 دمج إعدادات التصميم نجح بامتياز!")
        print("✅ يمكن الوصول إلى نافذة إعدادات التصميم من:")
        print("   📋 قائمة العرض في شريط القوائم")
        print("   🌳 القائمة الشجرية تحت الإعدادات")
        status = "ممتاز"
    elif success_rate >= 0.6:
        print("\n✅ دمج إعدادات التصميم نجح بشكل جيد")
        print("⚠️ بعض الميزات قد تحتاج مراجعة")
        status = "جيد"
    else:
        print("\n⚠️ دمج إعدادات التصميم يحتاج إصلاحات")
        print("🔧 راجع الأخطاء أعلاه")
        status = "يحتاج إصلاح"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    # إرشادات للمستخدم
    if success_rate >= 0.6:
        print(f"\n📋 كيفية الوصول إلى إعدادات التصميم:")
        print(f"   1️⃣ من شريط القوائم: العرض → ⚙️ إعدادات التصميم")
        print(f"   2️⃣ من القائمة الجانبية: الإعدادات → إعدادات التصميم")
        print(f"   3️⃣ اختصار لوحة المفاتيح: (سيتم إضافته لاحقاً)")
        
        print(f"\n🎨 الميزات المتاحة في إعدادات التصميم:")
        print(f"   🌈 تغيير مخطط الألوان")
        print(f"   🌙 التبديل بين الثيم الفاتح والمظلم")
        print(f"   📱 إعدادات التصميم المتجاوب")
        print(f"   🔤 تخصيص أحجام الخطوط")
        print(f"   🎨 ألوان مخصصة")
    
    return success_rate >= 0.6

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
