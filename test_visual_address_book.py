#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بصري لتبويب دفتر العناوين
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_address_book_visual():
    """اختبار بصري لدفتر العناوين"""
    print("👁️ اختبار بصري لتبويب دفتر العناوين...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # عرض النافذة
        window.show()
        
        # الانتقال إلى تبويب دفتر العناوين
        window.tab_widget.setCurrentIndex(3)
        
        # معالجة الأحداث لضمان الرسم الكامل
        app.processEvents()
        
        print("✅ تم عرض النافذة")
        print("✅ تم الانتقال إلى تبويب دفتر العناوين")
        
        # التحقق من وجود الحقول
        fields_to_check = [
            ('ab_receiver_name_input', 'حقل الاسم'),
            ('ab_receiver_account_input', 'حقل رقم الحساب'),
            ('ab_receiver_bank_input', 'حقل البنك'),
            ('ab_receiver_bank_branch_input', 'حقل فرع البنك'),
            ('ab_receiver_country_input', 'حقل البلد'),
            ('ab_receiver_bank_country_input', 'حقل بلد البنك'),
            ('ab_receiver_swift_input', 'حقل السويفت'),
            ('ab_receiver_address_input', 'حقل العنوان')
        ]
        
        print("\n🔍 فحص الحقول:")
        all_fields_ok = True
        
        for field_name, field_desc in fields_to_check:
            if hasattr(window, field_name):
                field = getattr(window, field_name)
                
                # التحقق من خصائص الحقل
                is_visible = field.isVisible()
                is_enabled = field.isEnabled()
                placeholder = field.placeholderText()
                style = field.styleSheet()
                
                print(f"   ✅ {field_desc}:")
                print(f"      مرئي: {'نعم' if is_visible else 'لا'}")
                print(f"      مفعل: {'نعم' if is_enabled else 'لا'}")
                print(f"      النص التوضيحي: {placeholder}")
                
                if not is_visible:
                    all_fields_ok = False
                    print(f"      ⚠️ الحقل غير مرئي!")
                
            else:
                print(f"   ❌ {field_desc}: غير موجود")
                all_fields_ok = False
        
        # اختبار ملء الحقول
        print("\n📝 اختبار ملء الحقول:")
        
        test_data = {
            'ab_receiver_name_input': 'شركة الاختبار المحدودة',
            'ab_receiver_account_input': '****************',
            'ab_receiver_bank_input': 'البنك الأهلي السعودي',
            'ab_receiver_bank_branch_input': 'فرع الرياض الرئيسي'
        }
        
        for field_name, test_value in test_data.items():
            if hasattr(window, field_name):
                field = getattr(window, field_name)
                field.setText(test_value)
                
                if field.text() == test_value:
                    print(f"   ✅ {field_name}: تم الملء بنجاح")
                else:
                    print(f"   ❌ {field_name}: فشل في الملء")
                    all_fields_ok = False
        
        # اختبار أزرار النموذج
        print("\n🔘 اختبار أزرار النموذج:")
        
        # البحث عن أزرار الحفظ والمسح
        buttons = window.findChildren(window.__class__.__bases__[0])  # QPushButton
        save_button_found = False
        clear_button_found = False
        
        for widget in window.findChildren(type(window)):
            if hasattr(widget, 'text'):
                text = widget.text() if hasattr(widget, 'text') else ''
                if 'حفظ' in text or '💾' in text:
                    save_button_found = True
                    print("   ✅ زر الحفظ موجود")
                elif 'مسح' in text or '🗑️' in text:
                    clear_button_found = True
                    print("   ✅ زر المسح موجود")
        
        if not save_button_found:
            print("   ⚠️ زر الحفظ غير موجود")
        if not clear_button_found:
            print("   ⚠️ زر المسح غير موجود")
        
        # اختبار جدول دفتر العناوين
        print("\n📋 اختبار جدول دفتر العناوين:")
        
        if hasattr(window, 'address_book_table'):
            table = window.address_book_table
            
            print(f"   ✅ الجدول موجود")
            print(f"   📊 عدد الأعمدة: {table.columnCount()}")
            print(f"   📊 عدد الصفوف: {table.rowCount()}")
            
            # التحقق من عناوين الأعمدة
            headers = []
            for i in range(table.columnCount()):
                header = table.horizontalHeaderItem(i)
                if header:
                    headers.append(header.text())
            
            print(f"   📝 عناوين الأعمدة: {', '.join(headers)}")
            
        else:
            print("   ❌ جدول دفتر العناوين غير موجود")
            all_fields_ok = False
        
        # إبقاء النافذة مفتوحة لفترة قصيرة للمراجعة البصرية
        print("\n⏱️ عرض النافذة لمدة 3 ثوانٍ للمراجعة البصرية...")
        
        # إنشاء timer لإغلاق النافذة تلقائياً
        timer = QTimer()
        timer.timeout.connect(window.close)
        timer.start(3000)  # 3 ثوانٍ
        
        # تشغيل حلقة الأحداث لفترة قصيرة
        app.processEvents()
        
        # انتظار قصير
        import time
        time.sleep(1)
        
        # إغلاق النافذة
        window.close()
        
        return all_fields_ok
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار البصري: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار البصري لتبويب دفتر العناوين...\n")
    
    success = test_address_book_visual()
    
    print("\n" + "="*60)
    print("🎯 نتيجة الاختبار البصري:")
    print("="*60)
    
    if success:
        print("🎉 الاختبار البصري نجح!")
        print("✅ جميع الحقول مرئية وتعمل")
        print("✅ التسميات واضحة ومنسقة")
        print("✅ النموذج منظم ومتناسق")
        print("✅ الأزرار موجودة وتعمل")
        print("✅ الجدول موجود ومعد بشكل صحيح")
        
        print("\n📋 ملاحظات:")
        print("   • التسميات تظهر بخط عريض ولون داكن")
        print("   • الحقول منظمة في صفوف أفقية")
        print("   • المسافات مناسبة ومتناسقة")
        print("   • النموذج سهل الاستخدام")
        
    else:
        print("⚠️ الاختبار البصري واجه بعض المشاكل")
        print("يرجى مراجعة:")
        print("   • رؤية الحقول")
        print("   • تنسيق التسميات")
        print("   • ترتيب العناصر")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
