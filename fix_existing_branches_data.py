#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بيانات الفروع الموجودة
Fix Existing Branches Data
"""

import sqlite3
from pathlib import Path

def fix_existing_branches_data():
    """إصلاح بيانات الفروع الموجودة"""
    
    print("🔧 إصلاح بيانات الفروع الموجودة...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. عرض الفروع الموجودة قبل الإصلاح
        print("📋 الفروع الموجودة قبل الإصلاح:")
        cursor.execute("""
            SELECT id, name, parent_type, parent_id, bank_id, exchange_id
            FROM branches
            WHERE is_active = 1
        """)
        branches_before = cursor.fetchall()
        
        for branch in branches_before:
            print(f"   ID: {branch[0]}, الاسم: {branch[1]}, النوع: {branch[2]}, parent_id: {branch[3]}, bank_id: {branch[4]}, exchange_id: {branch[5]}")
        
        # 2. إصلاح الفروع البنكية
        print("\n🏦 إصلاح الفروع البنكية...")
        cursor.execute("""
            UPDATE branches 
            SET bank_id = parent_id 
            WHERE parent_type = 'bank' AND bank_id IS NULL
        """)
        updated_banks = cursor.rowcount
        print(f"✅ تم تحديث {updated_banks} فرع بنكي")
        
        # 3. إصلاح فروع الصرافات
        print("\n💱 إصلاح فروع الصرافات...")
        cursor.execute("""
            UPDATE branches 
            SET exchange_id = parent_id 
            WHERE parent_type = 'exchange' AND exchange_id IS NULL
        """)
        updated_exchanges = cursor.rowcount
        print(f"✅ تم تحديث {updated_exchanges} فرع صرافة")
        
        # 4. عرض الفروع بعد الإصلاح
        print("\n📋 الفروع بعد الإصلاح:")
        cursor.execute("""
            SELECT b.id, b.name, b.parent_type, b.parent_id, b.bank_id, b.exchange_id,
                   banks.name as bank_name, exchanges.name as exchange_name
            FROM branches b
            LEFT JOIN banks ON b.bank_id = banks.id
            LEFT JOIN exchanges ON b.exchange_id = exchanges.id
            WHERE b.is_active = 1
        """)
        branches_after = cursor.fetchall()
        
        for branch in branches_after:
            parent_name = branch[6] if branch[6] else branch[7] if branch[7] else "غير محدد"
            print(f"   ID: {branch[0]}, الاسم: {branch[1]}, النوع: {branch[2]}, الجهة الأم: {parent_name}")
        
        # 5. اختبار إضافة فرع جديد
        print("\n🧪 اختبار إضافة فرع جديد...")
        
        # الحصول على بنك للاختبار
        cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 LIMIT 1")
        bank = cursor.fetchone()
        
        if bank:
            # إضافة فرع جديد
            cursor.execute("""
                INSERT INTO branches (
                    name, name_en, code, type, parent_type, parent_id,
                    bank_id, address, phone, manager_name, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                f'فرع اختبار {bank[1]}',
                f'Test Branch {bank[1]}',
                'TEST_NEW',
                'فرع اختبار',
                'bank',
                bank[0],
                bank[0],
                'عنوان اختبار',
                '011-1234567',
                'مدير الاختبار',
                1
            ))
            
            new_id = cursor.lastrowid
            print(f"✅ تم إضافة فرع جديد (ID: {new_id})")
            
            # التحقق من البيانات
            cursor.execute("""
                SELECT b.id, b.name, b.bank_id, banks.name as bank_name
                FROM branches b
                LEFT JOIN banks ON b.bank_id = banks.id
                WHERE b.id = ?
            """, (new_id,))
            
            result = cursor.fetchone()
            if result:
                print(f"   الاسم: {result[1]}")
                print(f"   bank_id: {result[2]}")
                print(f"   اسم البنك: {result[3]}")
            
            # حذف الفرع التجريبي
            cursor.execute("DELETE FROM branches WHERE id = ?", (new_id,))
            print("🗑️ تم حذف الفرع التجريبي")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح بيانات الفروع الموجودة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_branch_list_update():
    """اختبار تحديث قائمة الفروع"""
    
    print("\n🔄 اختبار تحديث قائمة الفروع...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # تشغيل نفس الاستعلام الذي تستخدمه نافذة إدارة البنوك
        cursor.execute("""
            SELECT b.id, b.name, b.name_en, b.code, b.type, b.parent_type, b.parent_id,
                   b.bank_id, b.exchange_id, b.address, b.phone, b.fax, b.email,
                   b.manager_name, b.manager_phone, b.notes, b.is_active, b.created_at,
                   banks.name as bank_name, exchanges.name as exchange_name
            FROM branches b
            LEFT JOIN banks ON b.bank_id = banks.id
            LEFT JOIN exchanges ON b.exchange_id = exchanges.id
            WHERE b.is_active = 1
            ORDER BY b.name
        """)
        
        branches_data = cursor.fetchall()
        
        print(f"📊 تم استرجاع {len(branches_data)} فرع من قاعدة البيانات")
        
        # تجميع الفروع حسب البنك/الصراف (نفس منطق النافذة)
        banks_branches = {}
        exchanges_branches = {}
        
        for branch in branches_data:
            if branch[7]:  # bank_id
                bank_name = branch[18] or f"بنك {branch[7]}"  # bank_name
                if bank_name not in banks_branches:
                    banks_branches[bank_name] = []
                banks_branches[bank_name].append(branch)
            elif branch[8]:  # exchange_id
                exchange_name = branch[19] or f"صراف {branch[8]}"  # exchange_name
                if exchange_name not in exchanges_branches:
                    exchanges_branches[exchange_name] = []
                exchanges_branches[exchange_name].append(branch)
        
        print("\n🏦 فروع البنوك:")
        for bank_name, branches in banks_branches.items():
            print(f"   {bank_name}:")
            for branch in branches:
                print(f"     • {branch[1]} (ID: {branch[0]})")
        
        print("\n💱 فروع الصرافات:")
        for exchange_name, branches in exchanges_branches.items():
            print(f"   {exchange_name}:")
            for branch in branches:
                print(f"     • {branch[1]} (ID: {branch[0]})")
        
        conn.close()
        
        print("\n✅ اختبار تحديث قائمة الفروع نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحديث القائمة: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح بيانات الفروع الموجودة...")
    print("=" * 60)
    
    # إصلاح البيانات
    fix_success = fix_existing_branches_data()
    
    if fix_success:
        # اختبار تحديث القائمة
        test_success = test_branch_list_update()
        
        if test_success:
            print("\n🏆 تم إصلاح البيانات واختبار تحديث القائمة بنجاح!")
            print("✅ الآن يمكن إضافة الفروع وتحديث القائمة بشكل صحيح")
        else:
            print("\n⚠️ تم إصلاح البيانات ولكن فشل اختبار التحديث")
    else:
        print("\n❌ فشل في إصلاح البيانات!")
    
    print("=" * 60)
