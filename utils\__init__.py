# -*- coding: utf-8 -*-
"""
أدوات مساعدة للنظام
System Utilities Package
"""

try:
    from .shipping_company_validator import ShippingCompanyValidator
except ImportError:
    # إنشاء فئة بديلة بسيطة في حالة فشل الاستيراد
    class ShippingCompanyValidator:
        def __init__(self):
            self.global_shipping_companies = {}
        
        def validate_company_name(self, name):
            return {"is_valid": True, "suggestions": [], "company_info": None}
        
        def get_company_suggestions(self, partial_name):
            return []

__all__ = ['ShippingCompanyValidator']
