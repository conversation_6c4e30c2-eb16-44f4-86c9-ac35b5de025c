#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الثيمات والألوان - ProShipment
Theme and Color Manager
"""

import sys
import json
from pathlib import Path
from typing import Dict, Any, Optional
from enum import Enum

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import Qt, QObject, Signal
from PySide6.QtGui import QPalette, QColor

# استيراد مكتبات الثيمات
try:
    import qdarkstyle
    QDARKSTYLE_AVAILABLE = True
except ImportError:
    QDARKSTYLE_AVAILABLE = False

try:
    import qtawesome as qta
    QTAWESOME_AVAILABLE = True
except ImportError:
    QTAWESOME_AVAILABLE = False

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

class ThemeType(Enum):
    """أنواع الثيمات"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"
    CUSTOM = "custom"

class ColorScheme(Enum):
    """مخططات الألوان"""
    BLUE = "blue"
    GREEN = "green"
    PURPLE = "purple"
    ORANGE = "orange"
    RED = "red"
    TEAL = "teal"

class ThemeManager(QObject):
    """مدير الثيمات والألوان"""
    
    # إشارات
    theme_changed = Signal(str)
    color_scheme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = ThemeType.LIGHT
        self.current_color_scheme = ColorScheme.BLUE
        self.custom_colors = {}
        
        # تحميل إعدادات الثيم
        self.load_theme_settings()
        
        # تعريف مخططات الألوان
        self.color_schemes = {
            ColorScheme.BLUE: {
                "primary": "#3498db",
                "secondary": "#2980b9",
                "success": "#27ae60",
                "warning": "#f39c12",
                "danger": "#e74c3c",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#f5f5f5",
                "text": "#212529",
                "text_secondary": "#6c757d"
            },
            ColorScheme.GREEN: {
                "primary": "#27ae60",
                "secondary": "#229954",
                "success": "#28a745",
                "warning": "#ffc107",
                "danger": "#dc3545",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#f0f8f0",
                "text": "#212529",
                "text_secondary": "#6c757d"
            },
            ColorScheme.PURPLE: {
                "primary": "#9b59b6",
                "secondary": "#8e44ad",
                "success": "#27ae60",
                "warning": "#f39c12",
                "danger": "#e74c3c",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#f8f0ff",
                "text": "#212529",
                "text_secondary": "#6c757d"
            },
            ColorScheme.ORANGE: {
                "primary": "#e67e22",
                "secondary": "#d35400",
                "success": "#27ae60",
                "warning": "#f39c12",
                "danger": "#e74c3c",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#fff8f0",
                "text": "#212529",
                "text_secondary": "#6c757d"
            },
            ColorScheme.RED: {
                "primary": "#e74c3c",
                "secondary": "#c0392b",
                "success": "#27ae60",
                "warning": "#f39c12",
                "danger": "#dc3545",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#fff0f0",
                "text": "#212529",
                "text_secondary": "#6c757d"
            },
            ColorScheme.TEAL: {
                "primary": "#1abc9c",
                "secondary": "#16a085",
                "success": "#27ae60",
                "warning": "#f39c12",
                "danger": "#e74c3c",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#f0ffff",
                "text": "#212529",
                "text_secondary": "#6c757d"
            }
        }
        
        # ألوان الثيم المظلم
        self.dark_colors = {
            "background": "#2b2b2b",
            "surface": "#3c3c3c",
            "text": "#ffffff",
            "text_secondary": "#cccccc",
            "border": "#555555",
            "hover": "#404040"
        }
    
    def load_theme_settings(self):
        """تحميل إعدادات الثيم من الملف"""
        try:
            settings_file = Path("config/theme_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                theme_name = settings.get("theme", "light")
                self.current_theme = ThemeType(theme_name)
                
                color_name = settings.get("color_scheme", "blue")
                self.current_color_scheme = ColorScheme(color_name)
                
                self.custom_colors = settings.get("custom_colors", {})
                
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الثيم: {e}")
            # استخدام الإعدادات الافتراضية
            self.current_theme = ThemeType.LIGHT
            self.current_color_scheme = ColorScheme.BLUE
    
    def save_theme_settings(self):
        """حفظ إعدادات الثيم في الملف"""
        try:
            settings_dir = Path("config")
            settings_dir.mkdir(exist_ok=True)
            
            settings = {
                "theme": self.current_theme.value,
                "color_scheme": self.current_color_scheme.value,
                "custom_colors": self.custom_colors
            }
            
            settings_file = settings_dir / "theme_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الثيم: {e}")
    
    def set_theme(self, theme: ThemeType):
        """تعيين الثيم"""
        if theme != self.current_theme:
            self.current_theme = theme
            self.apply_theme()
            self.save_theme_settings()
            self.theme_changed.emit(theme.value)
    
    def set_color_scheme(self, color_scheme: ColorScheme):
        """تعيين مخطط الألوان"""
        if color_scheme != self.current_color_scheme:
            self.current_color_scheme = color_scheme
            self.apply_theme()
            self.save_theme_settings()
            self.color_scheme_changed.emit(color_scheme.value)
    
    def apply_theme(self):
        """تطبيق الثيم على التطبيق"""
        app = QApplication.instance()
        if not app:
            return
        
        if self.current_theme == ThemeType.DARK and QDARKSTYLE_AVAILABLE:
            # تطبيق الثيم المظلم
            app.setStyleSheet(qdarkstyle.load_stylesheet_pyside6())
        else:
            # تطبيق الثيم المخصص
            stylesheet = self.generate_stylesheet()
            app.setStyleSheet(stylesheet)
    
    def generate_stylesheet(self) -> str:
        """إنشاء stylesheet مخصص"""
        colors = self.get_current_colors()
        
        return f"""
        /* الألوان الأساسية */
        QWidget {{
            background-color: {colors['background']};
            color: {colors['text']};
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
        }}
        
        /* النوافذ الرئيسية */
        QMainWindow {{
            background-color: {colors['background']};
        }}
        
        /* النوافذ الحوارية */
        QDialog {{
            background-color: {colors['background']};
        }}
        
        /* الأزرار */
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {colors['secondary']};
        }}
        
        QPushButton:pressed {{
            background-color: {self.darken_color(colors['secondary'], 0.1)};
        }}
        
        QPushButton:disabled {{
            background-color: #cccccc;
            color: #666666;
        }}
        
        /* أزرار النجاح */
        QPushButton[class="success"] {{
            background-color: {colors['success']};
        }}
        
        QPushButton[class="success"]:hover {{
            background-color: {self.darken_color(colors['success'], 0.1)};
        }}
        
        /* أزرار التحذير */
        QPushButton[class="warning"] {{
            background-color: {colors['warning']};
        }}
        
        QPushButton[class="warning"]:hover {{
            background-color: {self.darken_color(colors['warning'], 0.1)};
        }}
        
        /* أزرار الخطر */
        QPushButton[class="danger"] {{
            background-color: {colors['danger']};
        }}
        
        QPushButton[class="danger"]:hover {{
            background-color: {self.darken_color(colors['danger'], 0.1)};
        }}
        
        /* حقول الإدخال */
        QLineEdit {{
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 8px 12px;
            color: {colors['text']};
        }}
        
        QLineEdit:focus {{
            border-color: {colors['primary']};
        }}
        
        /* القوائم المنسدلة */
        QComboBox {{
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 8px 12px;
            color: {colors['text']};
        }}
        
        QComboBox:focus {{
            border-color: {colors['primary']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
        }}
        
        /* مناطق النص */
        QTextEdit {{
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 8px;
            color: {colors['text']};
        }}
        
        QTextEdit:focus {{
            border-color: {colors['primary']};
        }}
        
        /* التسميات */
        QLabel {{
            color: {colors['text']};
        }}
        
        /* المجموعات */
        QGroupBox {{
            font-weight: bold;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            color: {colors['text']};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {colors['primary']};
        }}
        
        /* التبويبات */
        QTabWidget::pane {{
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background-color: {colors['surface']};
        }}
        
        QTabBar::tab {{
            background-color: #f0f0f0;
            border: 1px solid #d0d0d0;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['primary']};
            color: white;
            border-bottom: none;
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['secondary']};
            color: white;
        }}
        
        /* الجداول */
        QTableWidget {{
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            gridline-color: #f0f0f0;
            selection-background-color: {colors['primary']};
            selection-color: white;
        }}
        
        QHeaderView::section {{
            background-color: {colors['surface']};
            color: {colors['text']};
            padding: 8px;
            border: none;
            border-bottom: 2px solid {colors['primary']};
            font-weight: bold;
        }}
        
        QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
        }}
        
        QTableWidget::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        /* أشرطة التمرير */
        QScrollBar:vertical {{
            background-color: #f0f0f0;
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['primary']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['secondary']};
        }}
        
        QScrollBar:horizontal {{
            background-color: #f0f0f0;
            height: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {colors['primary']};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['secondary']};
        }}
        
        /* أشرطة الحالة */
        QStatusBar {{
            background-color: {colors['surface']};
            color: {colors['text']};
            border-top: 1px solid #e0e0e0;
        }}
        
        /* أشرطة الأدوات */
        QToolBar {{
            background-color: {colors['surface']};
            border: none;
            spacing: 4px;
        }}
        
        QToolBar::separator {{
            background-color: #e0e0e0;
            width: 1px;
            margin: 4px;
        }}
        
        /* القوائم */
        QMenuBar {{
            background-color: {colors['surface']};
            color: {colors['text']};
        }}
        
        QMenuBar::item {{
            padding: 8px 12px;
            background-color: transparent;
        }}
        
        QMenuBar::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        QMenu {{
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }}
        
        QMenu::item {{
            padding: 8px 16px;
        }}
        
        QMenu::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        /* مربعات الاختيار */
        QCheckBox {{
            color: {colors['text']};
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            background-color: white;
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary']};
        }}
        
        /* أزرار الراديو */
        QRadioButton {{
            color: {colors['text']};
        }}
        
        QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid #e0e0e0;
            border-radius: 9px;
            background-color: white;
        }}
        
        QRadioButton::indicator:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary']};
        }}
        """
    
    def get_current_colors(self) -> Dict[str, str]:
        """الحصول على الألوان الحالية"""
        if self.current_theme == ThemeType.DARK:
            colors = self.color_schemes[self.current_color_scheme].copy()
            colors.update(self.dark_colors)
            return colors
        else:
            return self.color_schemes[self.current_color_scheme].copy()
    
    def get_color(self, color_name: str) -> str:
        """الحصول على لون محدد"""
        colors = self.get_current_colors()
        return colors.get(color_name, "#000000")
    
    def darken_color(self, color: str, factor: float) -> str:
        """تغميق لون بنسبة معينة"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.lstrip('#')
            
            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            
            # تطبيق التغميق
            r = max(0, int(r * (1 - factor)))
            g = max(0, int(g * (1 - factor)))
            b = max(0, int(b * (1 - factor)))
            
            # تحويل إلى hex
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except:
            return color
    
    def lighten_color(self, color: str, factor: float) -> str:
        """تفتيح لون بنسبة معينة"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.lstrip('#')
            
            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            
            # تطبيق التفتيح
            r = min(255, int(r + (255 - r) * factor))
            g = min(255, int(g + (255 - g) * factor))
            b = min(255, int(b + (255 - b) * factor))
            
            # تحويل إلى hex
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except:
            return color
    
    def get_icon(self, icon_name: str, color: Optional[str] = None) -> Optional[Any]:
        """الحصول على أيقونة مع اللون المحدد"""
        if not QTAWESOME_AVAILABLE:
            return None
        
        try:
            if color is None:
                color = self.get_color("text")
            
            return qta.icon(icon_name, color=color)
        except:
            return None
    
    def apply_button_style(self, button, style_class: str = "primary"):
        """تطبيق ستايل على زر"""
        colors = self.get_current_colors()
        
        style_map = {
            "primary": colors["primary"],
            "secondary": colors["secondary"],
            "success": colors["success"],
            "warning": colors["warning"],
            "danger": colors["danger"],
            "info": colors["info"]
        }
        
        bg_color = style_map.get(style_class, colors["primary"])
        hover_color = self.darken_color(bg_color, 0.1)
        
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(hover_color, 0.1)};
            }}
        """)

    def get_available_themes(self) -> Dict[str, str]:
        """الحصول على قائمة الثيمات المتاحة"""
        themes = {
            "light": "فاتح",
            "dark": "مظلم" if QDARKSTYLE_AVAILABLE else "مظلم (غير متاح)",
            "auto": "تلقائي"
        }
        return themes

    def get_available_color_schemes(self) -> Dict[str, str]:
        """الحصول على قائمة مخططات الألوان المتاحة"""
        return {
            "blue": "أزرق",
            "green": "أخضر",
            "purple": "بنفسجي",
            "orange": "برتقالي",
            "red": "أحمر",
            "teal": "تركوازي"
        }

# إنشاء مثيل عام
theme_manager = ThemeManager()
