#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للتطبيق
Final Comprehensive Application Test
"""

import sys
import os
import sqlite3
import traceback
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("🚀 اختبار بدء تشغيل التطبيق...")
    
    try:
        # اختبار استيراد المكونات الأساسية
        from PySide6.QtWidgets import QApplication
        from src.database.database_manager import DatabaseManager
        from src.database.models import Base
        
        print("   ✅ تم استيراد المكونات الأساسية بنجاح")
        
        # اختبار إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("   ✅ تم إنشاء تطبيق Qt بنجاح")
        
        # اختبار الاتصال بقاعدة البيانات
        db_manager = DatabaseManager()
        if db_manager.connect():
            print("   ✅ تم الاتصال بقاعدة البيانات بنجاح")
            db_manager.disconnect()
        else:
            print("   ❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في بدء التطبيق: {e}")
        traceback.print_exc()
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🗄️ اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if not db_manager.connect():
            print("   ❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        # اختبار قراءة البيانات
        session = db_manager.get_session()
        
        # اختبار جدول الشركات
        from src.database.models import Company
        companies = session.query(Company).all()
        print(f"   ✅ تم قراءة {len(companies)} شركة")
        
        # اختبار جدول الموردين
        from src.database.models import Supplier
        suppliers = session.query(Supplier).all()
        print(f"   ✅ تم قراءة {len(suppliers)} مورد")
        
        # اختبار جدول الأصناف
        from src.database.models import Item
        items = session.query(Item).all()
        print(f"   ✅ تم قراءة {len(items)} صنف")
        
        # اختبار جدول الشحنات
        from src.database.models import Shipment
        shipments = session.query(Shipment).all()
        print(f"   ✅ تم قراءة {len(shipments)} شحنة")
        
        session.close()
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في عمليات قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_ui_components():
    """اختبار مكونات واجهة المستخدم"""
    print("\n🖥️ اختبار مكونات واجهة المستخدم...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار النافذة الرئيسية
        from src.ui.main_window import MainWindow
        main_window = MainWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار نوافذ فرعية
        ui_tests = [
            ("src.ui.shipments.shipments_window", "ShipmentsWindow", "نافذة الشحنات"),
            ("src.ui.suppliers.suppliers_window", "SuppliersWindow", "نافذة الموردين"),
            ("src.ui.items.items_window", "ItemsWindow", "نافذة الأصناف")
        ]
        
        for module_name, class_name, description in ui_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                window_class = getattr(module, class_name)
                window = window_class()
                print(f"   ✅ تم إنشاء {description} بنجاح")
                window.close()
            except Exception as e:
                print(f"   ⚠️ مشكلة في {description}: {e}")
        
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في مكونات واجهة المستخدم: {e}")
        traceback.print_exc()
        return False

def test_pdf_generation():
    """اختبار إنشاء PDF"""
    print("\n📄 اختبار إنشاء PDF...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'TEST-FINAL-001',
            'request_date': '2024/12/09',
            'remittance_amount': '10000',
            'currency': 'USD',
            'receiver_name': 'AHMED MOHAMMED ALI',
            'receiver_bank_name': 'TEST BANK',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        generator = RemittancePDFGenerator()
        
        # مسار مؤقت للاختبار
        test_output = project_root / "temp" / "test_final.pdf"
        
        result = generator.generate_pdf(test_data, str(test_output))
        
        if result and test_output.exists():
            file_size = test_output.stat().st_size
            print(f"   ✅ تم إنشاء PDF بنجاح ({file_size} بايت)")
            
            # حذف الملف التجريبي
            test_output.unlink()
            
            return True
        else:
            print("   ❌ فشل في إنشاء PDF")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء PDF: {e}")
        traceback.print_exc()
        return False

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("\n📁 اختبار عمليات الملفات...")
    
    try:
        # اختبار الكتابة في مجلد المرفقات
        attachments_dir = project_root / "attachments"
        test_file = attachments_dir / "test_write.txt"
        
        test_file.write_text("اختبار الكتابة", encoding='utf-8')
        content = test_file.read_text(encoding='utf-8')
        
        if content == "اختبار الكتابة":
            print("   ✅ اختبار الكتابة في مجلد المرفقات نجح")
            test_file.unlink()
        else:
            print("   ❌ فشل في اختبار الكتابة")
            return False
        
        # اختبار مجلد التصدير
        exports_dir = project_root / "exports"
        test_export = exports_dir / "test_export.txt"
        
        test_export.write_text("اختبار التصدير", encoding='utf-8')
        print("   ✅ اختبار الكتابة في مجلد التصدير نجح")
        test_export.unlink()
        
        # اختبار مجلد السجلات
        logs_dir = project_root / "logs"
        test_log = logs_dir / "test_log.txt"
        
        test_log.write_text("اختبار السجل", encoding='utf-8')
        print("   ✅ اختبار الكتابة في مجلد السجلات نجح")
        test_log.unlink()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في عمليات الملفات: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """اختبار التكوين"""
    print("\n⚙️ اختبار التكوين...")
    
    try:
        # فحص ملفات التكوين الجديدة
        config_files = [
            "requirements.txt",
            ".gitignore",
            "performance_config.py",
            "security_config.py"
        ]
        
        for config_file in config_files:
            file_path = project_root / config_file
            if file_path.exists():
                print(f"   ✅ ملف التكوين موجود: {config_file}")
            else:
                print(f"   ❌ ملف التكوين مفقود: {config_file}")
                return False
        
        # اختبار قراءة إعدادات الأداء
        performance_config = project_root / "performance_config.py"
        content = performance_config.read_text(encoding='utf-8')
        
        if "DATABASE_POOL_SIZE" in content:
            print("   ✅ إعدادات الأداء صحيحة")
        else:
            print("   ❌ إعدادات الأداء غير صحيحة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكوين: {e}")
        traceback.print_exc()
        return False

def test_database_indexes():
    """اختبار فهارس قاعدة البيانات"""
    print("\n📈 اختبار فهارس قاعدة البيانات...")
    
    try:
        db_path = project_root / "data" / "proshipment.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الفهارس الجديدة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
        indexes = [row[0] for row in cursor.fetchall()]
        
        required_indexes = [
            "idx_suppliers_code",
            "idx_items_code",
            "idx_shipments_number",
            "idx_shipments_date",
            "idx_shipment_items_shipment",
            "idx_shipment_items_item"
        ]
        
        for index_name in required_indexes:
            if index_name in indexes:
                print(f"   ✅ فهرس موجود: {index_name}")
            else:
                print(f"   ❌ فهرس مفقود: {index_name}")
                conn.close()
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الفهارس: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء الاختبار النهائي الشامل للتطبيق...")
    print("="*70)
    
    tests = [
        ("بدء تشغيل التطبيق", test_application_startup),
        ("عمليات قاعدة البيانات", test_database_operations),
        ("مكونات واجهة المستخدم", test_ui_components),
        ("إنشاء PDF", test_pdf_generation),
        ("عمليات الملفات", test_file_operations),
        ("التكوين", test_configuration),
        ("فهارس قاعدة البيانات", test_database_indexes)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*70)
    print("📊 نتائج الاختبار النهائي الشامل")
    print("="*70)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate == 1.0:
        print("\n🎉 ممتاز! جميع الاختبارات نجحت")
        print("✅ التطبيق جاهز تماماً للاستخدام النهائي")
        print("🚀 يمكن تسليم التطبيق للمستخدم النهائي")
        status = "جاهز للإنتاج"
    elif success_rate >= 0.85:
        print("\n✅ جيد جداً! معظم الاختبارات نجحت")
        print("⚠️ بعض المشاكل البسيطة قد تحتاج مراجعة")
        status = "جاهز مع تحفظات"
    elif success_rate >= 0.7:
        print("\n🟡 متوسط. عدة اختبارات نجحت")
        print("🔧 يحتاج بعض الإصلاحات قبل الاستخدام")
        status = "يحتاج إصلاحات"
    else:
        print("\n🔴 ضعيف. عدة اختبارات فشلت")
        print("🚨 يحتاج إصلاحات جوهرية")
        status = "غير جاهز"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    return success_rate >= 0.85

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
