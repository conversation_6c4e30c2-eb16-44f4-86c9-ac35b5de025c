function(){return (function(){var h=this||self;
function aa(a){var b=typeof a;if("object"==b)if(a){if(a instanceof Array)return"array";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if("[object Window]"==c)return"object";if("[object Array]"==c||"number"==typeof a.length&&"undefined"!=typeof a.splice&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("splice"))return"array";if("[object Function]"==c||"undefined"!=typeof a.call&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("call"))return"function"}else return"null";else if("function"==
b&&"undefined"==typeof a.call)return"object";return b}function k(a){return"function"==aa(a)}function n(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function ba(a,b){function c(){}c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};var ca=window;function q(a,b){this.code=a;this.a=r[a]||u;this.message=b||"";a=this.a.replace(/((?:^|\s+)[a-z])/g,function(c){return c.toUpperCase().replace(/^[\s\xa0]+/g,"")});b=a.length-5;if(0>b||a.indexOf("Error",b)!=b)a+="Error";this.name=a;a=Error(this.message);a.name=this.name;this.stack=a.stack||""}ba(q,Error);var u="unknown error",r={15:"element not selectable",11:"element not visible"};r[31]=u;r[30]=u;r[24]="invalid cookie domain";r[29]="invalid element coordinates";r[12]="invalid element state";
r[32]="invalid selector";r[51]="invalid selector";r[52]="invalid selector";r[17]="javascript error";r[405]="unsupported operation";r[34]="move target out of bounds";r[27]="no such alert";r[7]="no such element";r[8]="no such frame";r[23]="no such window";r[28]="script timeout";r[33]="session not created";r[10]="stale element reference";r[21]="timeout";r[25]="unable to set cookie";r[26]="unexpected alert open";r[13]=u;r[9]="unknown command";var da;var ea=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},w=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)},fa=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f="string"===typeof a?a.split(""):a,g=0;g<c;g++)if(g in f){var l=f[g];b.call(void 0,l,g,a)&&(d[e++]=l)}return d},ha=Array.prototype.map?function(a,b){return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d},ia=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,
d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1},ja=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};
function ka(a,b){a:{for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}return 0>b?null:"string"===typeof a?a.charAt(b):a[b]}function la(a,b){a.sort(b||ma)}function ma(a,b){return a>b?1:a<b?-1:0};function na(a){var b=a.length-1;return 0<=b&&a.indexOf(" ",b)==b}var x=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
function oa(a,b){var c=0;a=x(String(a)).split(".");b=x(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==f[0].length&&0==g[0].length)break;c=pa(0==f[1].length?0:parseInt(f[1],10),0==g[1].length?0:parseInt(g[1],10))||pa(0==f[2].length,0==g[2].length)||pa(f[2],g[2]);f=f[3];g=g[3]}while(0==c)}return c}function pa(a,b){return a<b?-1:a>b?1:0};var y;a:{var qa=h.navigator;if(qa){var ra=qa.userAgent;if(ra){y=ra;break a}}y=""}function C(a){return-1!=y.indexOf(a)};function sa(){return C("Firefox")||C("FxiOS")}function ta(){return(C("Chrome")||C("CriOS"))&&!C("Edge")};function ua(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};function D(){return C("iPhone")&&!C("iPod")&&!C("iPad")};function va(a,b){var c=xa;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};var ya=C("Opera"),E=C("Trident")||C("MSIE"),za=C("Edge"),Aa=C("Gecko")&&!(-1!=y.toLowerCase().indexOf("webkit")&&!C("Edge"))&&!(C("Trident")||C("MSIE"))&&!C("Edge"),Ba=-1!=y.toLowerCase().indexOf("webkit")&&!C("Edge");function Ca(){var a=h.document;return a?a.documentMode:void 0}var Da;
a:{var Ea="",Fa=function(){var a=y;if(Aa)return/rv:([^\);]+)(\)|;)/.exec(a);if(za)return/Edge\/([\d\.]+)/.exec(a);if(E)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Ba)return/WebKit\/(\S+)/.exec(a);if(ya)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Fa&&(Ea=Fa?Fa[1]:"");if(E){var Ga=Ca();if(null!=Ga&&Ga>parseFloat(Ea)){Da=String(Ga);break a}}Da=Ea}var xa={};function Ha(a){return va(a,function(){return 0<=oa(Da,a)})}var F;F=h.document&&E?Ca():void 0;function G(a,b){this.x=void 0!==a?a:0;this.y=void 0!==b?b:0}G.prototype.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};G.prototype.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};G.prototype.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};function H(a,b){this.width=a;this.height=b}H.prototype.aspectRatio=function(){return this.width/this.height};H.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};H.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};H.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};function I(a){return a?new Ia(J(a)):da||(da=new Ia)}function Ja(a){for(;a&&1!=a.nodeType;)a=a.previousSibling;return a}function Ka(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a}function J(a){return 9==a.nodeType?a:a.ownerDocument||a.document}
function La(a,b){a&&(a=a.parentNode);for(var c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null}function Ia(a){this.a=a||h.document||document}
function L(a,b,c,d){a=d||a.a;var e=b&&"*"!=b?String(b).toUpperCase():"";if(a.querySelectorAll&&a.querySelector&&(e||c))c=a.querySelectorAll(e+(c?"."+c:""));else if(c&&a.getElementsByClassName)if(b=a.getElementsByClassName(c),e){a={};for(var f=d=0,g;g=b[f];f++)e==g.nodeName&&(a[d++]=g);a.length=d;c=a}else c=b;else if(b=a.getElementsByTagName(e||"*"),c){a={};for(f=d=0;g=b[f];f++){e=g.className;var l;if(l="function"==typeof e.split)l=0<=ea(e.split(/\s+/),c);l&&(a[d++]=g)}a.length=d;c=a}else c=b;return c}
;var Ma={l:function(a){return!(!a.querySelectorAll||!a.querySelector)},g:function(a,b){if(!a)throw new q(32,"No class name specified");a=x(a);if(-1!==a.indexOf(" "))throw new q(32,"Compound class names not permitted");if(Ma.l(b))try{return b.querySelector("."+a.replace(/\./g,"\\."))||null}catch(c){throw new q(32,"An invalid or illegal class name was specified");}a=L(I(b),"*",a,b);return a.length?a[0]:null},c:function(a,b){if(!a)throw new q(32,"No class name specified");a=x(a);if(-1!==a.indexOf(" "))throw new q(32,
"Compound class names not permitted");if(Ma.l(b))try{return b.querySelectorAll("."+a.replace(/\./g,"\\."))}catch(c){throw new q(32,"An invalid or illegal class name was specified");}return L(I(b),"*",a,b)}};var Na=sa(),Oa=D()||C("iPod"),Ra=C("iPad"),Sa=C("Android")&&!(ta()||sa()||C("Opera")||C("Silk")),Ta=ta(),Ua=C("Safari")&&!(ta()||C("Coast")||C("Opera")||C("Edge")||C("Edg/")||C("OPR")||sa()||C("Silk")||C("Android"))&&!(D()||C("iPad")||C("iPod"));function M(a){return(a=a.exec(y))?a[1]:""}(function(){if(Na)return M(/Firefox\/([0-9.]+)/);if(E||za||ya)return Da;if(Ta)return D()||C("iPad")||C("iPod")?M(/CriOS\/([0-9.]+)/):M(/Chrome\/([0-9.]+)/);if(Ua&&!(D()||C("iPad")||C("iPod")))return M(/Version\/([0-9.]+)/);if(Oa||Ra){var a=/Version\/(\S+).*Mobile\/(\S+)/.exec(y);if(a)return a[1]+"."+a[2]}else if(Sa)return(a=M(/Android\s+([0-9.]+)/))?a:M(/Version\/([0-9.]+)/);return""})();var Va=E&&!(8<=Number(F)),Wa=E&&!(9<=Number(F));var N={g:function(a,b){if(!k(b.querySelector)&&E&&(E?0<=oa(F,8):Ha(8))&&!n(b.querySelector))throw Error("CSS selection is not supported");if(!a)throw new q(32,"No selector specified");a=x(a);try{var c=b.querySelector(a)}catch(d){throw new q(32,"An invalid or illegal selector was specified");}return c&&1==c.nodeType?c:null},c:function(a,b){if(!k(b.querySelectorAll)&&E&&(E?0<=oa(F,8):Ha(8))&&!n(b.querySelector))throw Error("CSS selection is not supported");if(!a)throw new q(32,"No selector specified");
a=x(a);try{return b.querySelectorAll(a)}catch(c){throw new q(32,"An invalid or illegal selector was specified");}}};var Xa={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",
darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",
ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",
lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",
moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",
seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};var Ya="backgroundColor borderTopColor borderRightColor borderBottomColor borderLeftColor color outlineColor".split(" "),Za=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/,$a=/^#(?:[0-9a-f]{3}){1,2}$/i,ab=/^(?:rgba)?\((\d{1,3}),\s?(\d{1,3}),\s?(\d{1,3}),\s?(0|1|0\.\d*)\)$/i,bb=/^(?:rgb)?\((0|[1-9]\d{0,2}),\s?(0|[1-9]\d{0,2}),\s?(0|[1-9]\d{0,2})\)$/i;function O(a,b){b=b.toLowerCase();return"style"==b?cb(a.style.cssText):Va&&"value"==b&&P(a,"INPUT")?a.value:Wa&&!0===a[b]?String(a.getAttribute(b)):(a=a.getAttributeNode(b))&&a.specified?a.value:null}var db=/[;]+(?=(?:(?:[^"]*"){2})*[^"]*$)(?=(?:(?:[^']*'){2})*[^']*$)(?=(?:[^()]*\([^()]*\))*[^()]*$)/;
function cb(a){var b=[];w(a.split(db),function(c){var d=c.indexOf(":");0<d&&(c=[c.slice(0,d),c.slice(d+1)],2==c.length&&b.push(c[0].toLowerCase(),":",c[1],";"))});b=b.join("");return b=";"==b.charAt(b.length-1)?b:b+";"}function P(a,b){b&&"string"!==typeof b&&(b=b.toString());return a instanceof HTMLFormElement?!!a&&1==a.nodeType&&(!b||"FORM"==b):!!a&&1==a.nodeType&&(!b||a.tagName.toUpperCase()==b)};function eb(a,b,c,d){this.j=a;this.a=b;this.b=c;this.h=d}eb.prototype.ceil=function(){this.j=Math.ceil(this.j);this.a=Math.ceil(this.a);this.b=Math.ceil(this.b);this.h=Math.ceil(this.h);return this};eb.prototype.floor=function(){this.j=Math.floor(this.j);this.a=Math.floor(this.a);this.b=Math.floor(this.b);this.h=Math.floor(this.h);return this};eb.prototype.round=function(){this.j=Math.round(this.j);this.a=Math.round(this.a);this.b=Math.round(this.b);this.h=Math.round(this.h);return this};function Q(a,b,c,d){this.a=a;this.b=b;this.width=c;this.height=d}Q.prototype.ceil=function(){this.a=Math.ceil(this.a);this.b=Math.ceil(this.b);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};Q.prototype.floor=function(){this.a=Math.floor(this.a);this.b=Math.floor(this.b);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
Q.prototype.round=function(){this.a=Math.round(this.a);this.b=Math.round(this.b);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};var fb="function"===typeof ShadowRoot;function gb(a){for(a=a.parentNode;a&&1!=a.nodeType&&9!=a.nodeType&&11!=a.nodeType;)a=a.parentNode;return P(a)?a:null}
function R(a,b){b=ua(b);if("float"==b||"cssFloat"==b||"styleFloat"==b)b=Wa?"styleFloat":"cssFloat";a:{var c=b;var d=J(a);if(d.defaultView&&d.defaultView.getComputedStyle&&(d=d.defaultView.getComputedStyle(a,null))){c=d[c]||d.getPropertyValue(c)||"";break a}c=""}a=c||hb(a,b);if(null===a)a=null;else if(0<=ea(Ya,b)){b:{var e=a.match(ab);if(e&&(b=Number(e[1]),c=Number(e[2]),d=Number(e[3]),e=Number(e[4]),0<=b&&255>=b&&0<=c&&255>=c&&0<=d&&255>=d&&0<=e&&1>=e)){b=[b,c,d,e];break b}b=null}if(!b)b:{if(d=a.match(bb))if(b=
Number(d[1]),c=Number(d[2]),d=Number(d[3]),0<=b&&255>=b&&0<=c&&255>=c&&0<=d&&255>=d){b=[b,c,d,1];break b}b=null}if(!b)b:{b=a.toLowerCase();c=Xa[b.toLowerCase()];if(!c&&(c="#"==b.charAt(0)?b:"#"+b,4==c.length&&(c=c.replace(Za,"#$1$1$2$2$3$3")),!$a.test(c))){b=null;break b}b=[parseInt(c.substr(1,2),16),parseInt(c.substr(3,2),16),parseInt(c.substr(5,2),16),1]}a=b?"rgba("+b.join(", ")+")":a}return a}
function hb(a,b){var c=a.currentStyle||a.style,d=c[b];void 0===d&&k(c.getPropertyValue)&&(d=c.getPropertyValue(b));return"inherit"!=d?void 0!==d?d:null:(a=gb(a))?hb(a,b):null}
function ib(a,b,c){function d(g){var l=S(g);return 0<l.height&&0<l.width?!0:P(g,"PATH")&&(0<l.height||0<l.width)?(g=R(g,"stroke-width"),!!g&&0<parseInt(g,10)):"hidden"!=R(g,"overflow")&&ia(g.childNodes,function(z){return 3==z.nodeType||P(z)&&d(z)})}function e(g){return jb(g)==T&&ja(g.childNodes,function(l){return!P(l)||e(l)||!d(l)})}if(!P(a))throw Error("Argument to isShown must be of type Element");if(P(a,"BODY"))return!0;if(P(a,"OPTION")||P(a,"OPTGROUP"))return a=La(a,function(g){return P(g,"SELECT")}),
!!a&&ib(a,!0,c);var f=kb(a);if(f)return!!f.image&&0<f.rect.width&&0<f.rect.height&&ib(f.image,b,c);if(P(a,"INPUT")&&"hidden"==a.type.toLowerCase()||P(a,"NOSCRIPT"))return!1;f=R(a,"visibility");return"collapse"!=f&&"hidden"!=f&&c(a)&&(b||0!=lb(a))&&d(a)?!e(a):!1}
function mb(a){function b(c){if(P(c)&&"none"==R(c,"display"))return!1;var d;if((d=c.parentNode)&&d.shadowRoot&&void 0!==c.assignedSlot)d=c.assignedSlot?c.assignedSlot.parentNode:null;else if(c.getDestinationInsertionPoints){var e=c.getDestinationInsertionPoints();0<e.length&&(d=e[e.length-1])}if(fb&&d instanceof ShadowRoot){if(d.host.shadowRoot&&d.host.shadowRoot!==d)return!1;d=d.host}return!d||9!=d.nodeType&&11!=d.nodeType?d&&P(d,"DETAILS")&&!d.open&&!P(c,"SUMMARY")?!1:!!d&&b(d):!0}return ib(a,!1,
b)}var T="hidden";
function jb(a){function b(m){function p(wa){if(wa==g)return!0;var Pa=R(wa,"display");return 0==Pa.lastIndexOf("inline",0)||"contents"==Pa||"absolute"==Qa&&"static"==R(wa,"position")?!1:!0}var Qa=R(m,"position");if("fixed"==Qa)return A=!0,m==g?null:g;for(m=gb(m);m&&!p(m);)m=gb(m);return m}function c(m){var p=m;if("visible"==z)if(m==g&&l)p=l;else if(m==l)return{x:"visible",y:"visible"};p={x:R(p,"overflow-x"),y:R(p,"overflow-y")};m==g&&(p.x="visible"==p.x?"auto":p.x,p.y="visible"==p.y?"auto":p.y);return p}
function d(m){if(m==g){var p=(new Ia(f)).a;m=p.scrollingElement?p.scrollingElement:Ba||"CSS1Compat"!=p.compatMode?p.body||p.documentElement:p.documentElement;p=p.parentWindow||p.defaultView;m=E&&Ha("10")&&p.pageYOffset!=m.scrollTop?new G(m.scrollLeft,m.scrollTop):new G(p.pageXOffset||m.scrollLeft,p.pageYOffset||m.scrollTop)}else m=new G(m.scrollLeft,m.scrollTop);return m}var e=nb(a),f=J(a),g=f.documentElement,l=f.body,z=R(g,"overflow"),A;for(a=b(a);a;a=b(a)){var t=c(a);if("visible"!=t.x||"visible"!=
t.y){var v=S(a);if(0==v.width||0==v.height)return T;var B=e.a<v.a,K=e.b<v.b;if(B&&"hidden"==t.x||K&&"hidden"==t.y)return T;if(B&&"visible"!=t.x||K&&"visible"!=t.y){B=d(a);K=e.b<v.b-B.y;if(e.a<v.a-B.x&&"visible"!=t.x||K&&"visible"!=t.x)return T;e=jb(a);return e==T?T:"scroll"}B=e.h>=v.a+v.width;v=e.j>=v.b+v.height;if(B&&"hidden"==t.x||v&&"hidden"==t.y)return T;if(B&&"visible"!=t.x||v&&"visible"!=t.y){if(A&&(t=d(a),e.h>=g.scrollWidth-t.x||e.a>=g.scrollHeight-t.y))return T;e=jb(a);return e==T?T:"scroll"}}}return"none"}
function S(a){var b=kb(a);if(b)return b.rect;if(P(a,"HTML"))return a=J(a),a=((a?a.parentWindow||a.defaultView:window)||window).document,a="CSS1Compat"==a.compatMode?a.documentElement:a.body,a=new H(a.clientWidth,a.clientHeight),new Q(0,0,a.width,a.height);try{var c=a.getBoundingClientRect()}catch(d){return new Q(0,0,0,0)}b=new Q(c.left,c.top,c.right-c.left,c.bottom-c.top);E&&a.ownerDocument.body&&(a=J(a),b.a-=a.documentElement.clientLeft+a.body.clientLeft,b.b-=a.documentElement.clientTop+a.body.clientTop);
return b}function kb(a){var b=P(a,"MAP");if(!b&&!P(a,"AREA"))return null;var c=b?a:P(a.parentNode,"MAP")?a.parentNode:null,d=null,e=null;c&&c.name&&(d=N.g('*[usemap="#'+c.name+'"]',J(c)))&&(e=S(d),b||"default"==a.shape.toLowerCase()||(a=ob(a),b=Math.min(Math.max(a.a,0),e.width),c=Math.min(Math.max(a.b,0),e.height),e=new Q(b+e.a,c+e.b,Math.min(a.width,e.width-b),Math.min(a.height,e.height-c))));return{image:d,rect:e||new Q(0,0,0,0)}}
function ob(a){var b=a.shape.toLowerCase();a=a.coords.split(",");if("rect"==b&&4==a.length){b=a[0];var c=a[1];return new Q(b,c,a[2]-b,a[3]-c)}if("circle"==b&&3==a.length)return b=a[2],new Q(a[0]-b,a[1]-b,2*b,2*b);if("poly"==b&&2<a.length){b=a[0];c=a[1];for(var d=b,e=c,f=2;f+1<a.length;f+=2)b=Math.min(b,a[f]),d=Math.max(d,a[f]),c=Math.min(c,a[f+1]),e=Math.max(e,a[f+1]);return new Q(b,c,d-b,e-c)}return new Q(0,0,0,0)}function nb(a){a=S(a);return new eb(a.b,a.a+a.width,a.b+a.height,a.a)}
function pb(a){return a.replace(/^[^\S\xa0]+|[^\S\xa0]+$/g,"")}function qb(a){var b=[];fb?rb(a,b):sb(a,b);a=ha(b,pb);return pb(a.join("\n")).replace(/\xa0/g," ")}
function tb(a,b,c){if(P(a,"BR"))b.push("");else{var d=P(a,"TD"),e=R(a,"display"),f=!d&&!(0<=ea(ub,e)),g=void 0!==a.previousElementSibling?a.previousElementSibling:Ja(a.previousSibling);g=g?R(g,"display"):"";var l=R(a,"float")||R(a,"cssFloat")||R(a,"styleFloat");!f||"run-in"==g&&"none"==l||/^[\s\xa0]*$/.test(b[b.length-1]||"")||b.push("");var z=mb(a),A=null,t=null;z&&(A=R(a,"white-space"),t=R(a,"text-transform"));w(a.childNodes,function(v){c(v,b,z,A,t)});a=b[b.length-1]||"";!d&&"table-cell"!=e||!a||
na(a)||(b[b.length-1]+=" ");f&&"run-in"!=e&&!/^[\s\xa0]*$/.test(a)&&b.push("")}}function sb(a,b){tb(a,b,function(c,d,e,f,g){3==c.nodeType&&e?vb(c,d,f,g):P(c)&&sb(c,d)})}var ub="inline inline-block inline-table none table-cell table-column table-column-group".split(" ");
function vb(a,b,c,d){a=a.nodeValue.replace(/[\u200b\u200e\u200f]/g,"");a=a.replace(/(\r\n|\r|\n)/g,"\n");if("normal"==c||"nowrap"==c)a=a.replace(/\n/g," ");a="pre"==c||"pre-wrap"==c?a.replace(/[ \f\t\v\u2028\u2029]/g,"\u00a0"):a.replace(/[ \f\t\v\u2028\u2029]+/g," ");"capitalize"==d?a=a.replace(E?/(^|\s|\b)(\S)/g:/(^|\s|\b)(\S)/gu,function(e,f,g){return f+g.toUpperCase()}):"uppercase"==d?a=a.toUpperCase():"lowercase"==d&&(a=a.toLowerCase());c=b.pop()||"";na(c)&&0==a.lastIndexOf(" ",0)&&(a=a.substr(1));
b.push(c+a)}function lb(a){if(Wa){if("relative"==R(a,"position"))return 1;a=R(a,"filter");return(a=a.match(/^alpha\(opacity=(\d*)\)/)||a.match(/^progid:DXImageTransform.Microsoft.Alpha\(Opacity=(\d*)\)/))?Number(a[1])/100:1}return wb(a)}function wb(a){var b=1,c=R(a,"opacity");c&&(b=Number(c));(a=gb(a))&&(b*=wb(a));return b}
function xb(a,b,c,d,e){if(3==a.nodeType&&c)vb(a,b,d,e);else if(P(a))if(P(a,"CONTENT")||P(a,"SLOT")){for(var f=a;f.parentNode;)f=f.parentNode;f instanceof ShadowRoot?(f=P(a,"CONTENT")?a.getDistributedNodes():a.assignedNodes(),w(0<f.length?f:a.childNodes,function(g){xb(g,b,c,d,e)})):rb(a,b)}else if(P(a,"SHADOW")){for(f=a;f.parentNode;)f=f.parentNode;if(f instanceof ShadowRoot&&(a=f))for(a=a.olderShadowRoot;a;)w(a.childNodes,function(g){xb(g,b,c,d,e)}),a=a.olderShadowRoot}else rb(a,b)}
function rb(a,b){a.shadowRoot&&w(a.shadowRoot.childNodes,function(c){xb(c,b,!0,null,null)});tb(a,b,function(c,d,e,f,g){var l=null;1==c.nodeType?l=c:3==c.nodeType&&(l=c);null!=l&&(null!=l.assignedSlot||l.getDestinationInsertionPoints&&0<l.getDestinationInsertionPoints().length)||xb(c,d,e,f,g)})};var yb={l:function(a,b){return!(!a.querySelectorAll||!a.querySelector)&&!/^\d.*/.test(b)},g:function(a,b){var c=I(b),d="string"===typeof a?c.a.getElementById(a):a;return d?O(d,"id")==a&&b!=d&&Ka(b,d)?d:ka(L(c,"*"),function(e){return O(e,"id")==a&&b!=e&&Ka(b,e)}):null},c:function(a,b){if(!a)return[];if(yb.l(b,a))try{return b.querySelectorAll("#"+yb.F(a))}catch(c){return[]}b=L(I(b),"*",null,b);return fa(b,function(c){return O(c,"id")==a})},F:function(a){return a.replace(/([\s'"\\#.:;,!?+<>=~*^$|%&@`{}\-\/\[\]\(\)])/g,
"\\$1")}};var U={},zb={};U.v=function(a,b,c){try{var d=N.c("a",b)}catch(e){d=L(I(b),"A",null,b)}return ka(d,function(e){e=qb(e);e=e.replace(/^[\s]+|[\s]+$/g,"");return c&&-1!=e.indexOf(a)||e==a})};U.u=function(a,b,c){try{var d=N.c("a",b)}catch(e){d=L(I(b),"A",null,b)}return fa(d,function(e){e=qb(e);e=e.replace(/^[\s]+|[\s]+$/g,"");return c&&-1!=e.indexOf(a)||e==a})};U.g=function(a,b){return U.v(a,b,!1)};U.c=function(a,b){return U.u(a,b,!1)};zb.g=function(a,b){return U.v(a,b,!0)};
zb.c=function(a,b){return U.u(a,b,!0)};var V={i:function(a,b){return function(c){var d=V.f(a);d=S(d);c=S(c);return b.call(null,d,c)}},C:function(a){return V.i(a,function(b,c){return c.b+c.height<=b.b})},D:function(a){return V.i(a,function(b,c){return c.b>=b.b+b.height})},H:function(a){return V.i(a,function(b,c){return c.a+c.width<=b.a})},J:function(a){return V.i(a,function(b,c){return c.a>=b.a+b.width})},L:function(a){return V.i(a,function(b,c){return c.a<b.a+b.width&&c.a+c.width>b.a&&c.b+c.height<=b.b})},M:function(a){return V.i(a,function(b,
c){return c.a<b.a+b.width&&c.a+c.width>b.a&&c.b>=b.b+b.height})},N:function(a){return V.i(a,function(b,c){return c.b<b.b+b.height&&c.b+c.height>b.b&&c.a+c.width<=b.a})},O:function(a){return V.i(a,function(b,c){return c.b<b.b+b.height&&c.b+c.height>b.b&&c.a>=b.a+b.width})},I:function(a,b){var c;b?c=b:"number"==typeof a.distance&&(c=a.distance);c||(c=50);return function(d){var e=V.f(a);if(e===d)return!1;e=S(e);d=S(d);e=new Q(e.a-c,e.b-c,e.width+2*c,e.height+2*c);return e.a<=d.a+d.width&&d.a<=e.a+e.width&&
e.b<=d.b+d.height&&d.b<=e.b+e.height}},f:function(a){if(n(a)&&1==a.nodeType)return a;if(k(a))return V.f(a.call(null));if(n(a)){var b;a:{if(b=Ab(a)){var c=Bb[b];if(c&&k(c.g)){b=c.g(a[b],ca.document);break a}}throw new q(61,"Unsupported locator strategy: "+b);}if(!b)throw new q(7,"No element has been found by "+JSON.stringify(a));return b}throw new q(61,"Selector is of wrong type: "+JSON.stringify(a));}};
V.B={above:V.C,below:V.D,left:V.H,near:V.I,right:V.J,straightAbove:V.L,straightBelow:V.M,straightLeft:V.N,straightRight:V.O};V.A={above:V.f,below:V.f,left:V.f,near:V.f,right:V.f,straightAbove:V.f,straightBelow:V.f,straightLeft:V.f,straightRight:V.f};
V.G=function(a,b){var c=[];w(a,function(e){e&&ja(b,function(f){var g=f.kind,l=V.B[g];if(!l)throw new q(61,"Cannot find filter suitable for "+g);return l.apply(null,f.args)(e)},null)&&c.push(e)},null);a=b[b.length-1];var d=V.A[a?a.kind:"unknown"];return d?(a=d.apply(null,a.args))?V.K(a,c):c:c};
V.K=function(a,b){function c(f){f=S(f);return Math.sqrt(Math.pow(d-(f.a+Math.max(1,f.width)/2),2)+Math.pow(e-(f.b+Math.max(1,f.height)/2),2))}a=S(a);var d=a.a+Math.max(1,a.width)/2,e=a.b+Math.max(1,a.height)/2;la(b,function(f,g){return c(f)-c(g)});return b};V.g=function(a,b){a=V.c(a,b);return 0==a.length?null:a[0]};
V.c=function(a,b){if(!a.hasOwnProperty("root")||!a.hasOwnProperty("filters"))throw new q(61,"Locator not suitable for relative locators: "+JSON.stringify(a));var c=a.filters,d=aa(c);if("array"!=d&&("object"!=d||"number"!=typeof c.length))throw new q(61,"Targets should be an array: "+JSON.stringify(a));var e;P(a.root)?e=[a.root]:e=Cb(a.root,b);return 0==e.length?[]:V.G(e,a.filters)};var Db={g:function(a,b){if(""===a)throw new q(32,'Unable to locate an element with the tagName ""');return b.getElementsByTagName(a)[0]||null},c:function(a,b){if(""===a)throw new q(32,'Unable to locate an element with the tagName ""');return b.getElementsByTagName(a)}};var W={};W.m=function(){var a={P:"http://www.w3.org/2000/svg"};return function(b){return a[b]||null}}();
W.s=function(a,b,c){var d=J(a);if(!d.documentElement)return null;try{var e=d.createNSResolver?d.createNSResolver(d.documentElement):W.m;if(E&&!Ha(7))return d.evaluate.call(d,b,a,e,c,null);if(!E||9<=Number(F)){for(var f={},g=d.getElementsByTagName("*"),l=0;l<g.length;++l){var z=g[l],A=z.namespaceURI;if(A&&!f[A]){var t=z.lookupPrefix(A);if(!t){var v=A.match(".*/(\\w+)/?$");t=v?v[1]:"xhtml"}f[A]=t}}var B={},K;for(K in f)B[f[K]]=K;e=function(m){return B[m]||null}}try{return d.evaluate(b,a,e,c,null)}catch(m){if("TypeError"===
m.name)return e=d.createNSResolver?d.createNSResolver(d.documentElement):W.m,d.evaluate(b,a,e,c,null);throw m;}}catch(m){if(!Aa||"NS_ERROR_ILLEGAL_VALUE"!=m.name)throw new q(32,"Unable to locate an element with the xpath expression "+b+" because of the following error:\n"+m);}};W.o=function(a,b){if(!a||1!=a.nodeType)throw new q(32,'The result of the xpath expression "'+b+'" is: '+a+". It should be an element.");};
W.g=function(a,b){var c=function(){var d=W.s(b,a,9);return d?d.singleNodeValue||null:b.selectSingleNode?(d=J(b),d.setProperty&&d.setProperty("SelectionLanguage","XPath"),b.selectSingleNode(a)):null}();null===c||W.o(c,a);return c};
W.c=function(a,b){var c=function(){var d=W.s(b,a,7);if(d){for(var e=d.snapshotLength,f=[],g=0;g<e;++g)f.push(d.snapshotItem(g));return f}return b.selectNodes?(d=J(b),d.setProperty&&d.setProperty("SelectionLanguage","XPath"),b.selectNodes(a)):[]}();w(c,function(d){W.o(d,a)});return c};var Bb={className:Ma,"class name":Ma,css:N,"css selector":N,relative:V,id:yb,linkText:U,"link text":U,name:{g:function(a,b){b=L(I(b),"*",null,b);return ka(b,function(c){return O(c,"name")==a})},c:function(a,b){b=L(I(b),"*",null,b);return fa(b,function(c){return O(c,"name")==a})}},partialLinkText:zb,"partial link text":zb,tagName:Db,"tag name":Db,xpath:W};function Ab(a){for(var b in a)if(a.hasOwnProperty(b))return b;return null}
function Cb(a,b){var c=Ab(a);if(c){var d=Bb[c];if(d&&k(d.c))return d.c(a[c],b||ca.document)}throw new q(61,"Unsupported locator strategy: "+c);};var Eb=Cb,X=["se_exportedFunctionSymbol"],Y=h;X[0]in Y||"undefined"==typeof Y.execScript||Y.execScript("var "+X[0]);for(var Z;X.length&&(Z=X.shift());)X.length||void 0===Eb?Y[Z]&&Y[Z]!==Object.prototype[Z]?Y=Y[Z]:Y=Y[Z]={}:Y[Z]=Eb;; return this.se_exportedFunctionSymbol.apply(null,arguments);}).apply(window, arguments);}
