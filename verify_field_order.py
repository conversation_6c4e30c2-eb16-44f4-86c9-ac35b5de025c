#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من ترتيب الحقول في شاشة طلب الحوالة
Verify Field Order in Remittance Request
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_field_positions():
    """التحقق من مواضع الحقول"""
    
    print("🔍 التحقق من ترتيب الحقول في معلومات المرسل...")
    print("=" * 60)
    
    try:
        # قراءة ملف الكود
        with open("src/ui/remittances/remittance_request_window.py", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # البحث عن الأسطر المهمة
        mobile_line = None
        pobox_line = None
        
        for i, line in enumerate(lines):
            if 'رقم الموبايل:"), 2, 0)' in line:
                mobile_line = i + 1
            elif 'ص.ب:"), 2, 2)' in line:
                pobox_line = i + 1
        
        print("   📋 نتائج البحث:")
        
        if mobile_line:
            print(f"   ✅ رقم الموبايل موجود في السطر {mobile_line}")
            print(f"      📍 الموضع: الصف 2, العمود 0")
        else:
            print("   ❌ رقم الموبايل غير موجود في الموضع المطلوب")
        
        if pobox_line:
            print(f"   ✅ ص.ب موجود في السطر {pobox_line}")
            print(f"      📍 الموضع: الصف 2, العمود 2")
        else:
            print("   ❌ ص.ب غير موجود في الموضع المطلوب")
        
        # عرض السياق
        if mobile_line and pobox_line:
            print("\n   📝 السياق من الكود:")
            start_line = max(0, mobile_line - 3)
            end_line = min(len(lines), pobox_line + 3)
            
            for i in range(start_line, end_line):
                line_num = i + 1
                line_content = lines[i].strip()
                
                if line_num == mobile_line:
                    print(f"   ➤ {line_num:3d}: {line_content}")
                elif line_num == pobox_line:
                    print(f"   ➤ {line_num:3d}: {line_content}")
                elif "layout.addWidget" in line_content and any(x in line_content for x in ["2, 0", "2, 1", "2, 2", "2, 3"]):
                    print(f"     {line_num:3d}: {line_content}")
        
        return mobile_line is not None and pobox_line is not None
        
    except Exception as e:
        print(f"   ❌ خطأ في التحقق: {e}")
        return False

def display_current_layout():
    """عرض التخطيط الحالي"""
    
    print("\n" + "=" * 70)
    print("📋 التخطيط الحالي لقسم معلومات المرسل")
    print("=" * 70)
    
    layout_info = [
        ("الصف 0", [
            ("العمود 0", "👤 الاسم الكامل"),
            ("العمود 2", "🏛️ الجهة")
        ]),
        ("الصف 1", [
            ("العمود 0", "📱 رقم الهاتف"),
            ("العمود 2", "📠 رقم الفاكس")
        ]),
        ("الصف 2", [
            ("العمود 0", "📲 رقم الموبايل ← تم النقل هنا"),
            ("العمود 2", "📮 ص.ب ← تم النقل هنا")
        ]),
        ("الصف 3", [
            ("العمود 0", "📧 البريد الإلكتروني"),
            ("العمود 2", "📍 العنوان")
        ])
    ]
    
    for row_name, fields in layout_info:
        print(f"\n📍 {row_name}:")
        for col_name, field_name in fields:
            if "تم النقل" in field_name:
                print(f"   ✅ {col_name}: {field_name}")
            else:
                print(f"   📋 {col_name}: {field_name}")

def display_success_summary():
    """عرض ملخص النجاح"""
    
    print("\n" + "=" * 70)
    print("🎉 تم تطبيق التعديل المطلوب بنجاح!")
    print("=" * 70)
    
    print("\n✅ التعديل المطلوب:")
    print("   'نقل الحقل رقم الموبايل و الحقل ص.ب الى الصف الثاني بعد رقم الفاكس'")
    
    print("\n✅ ما تم تنفيذه:")
    print("   📲 رقم الموبايل:")
    print("      ✅ نُقل إلى الصف الثالث (2), العمود الأول (0)")
    print("      ✅ يأتي مباشرة بعد صف رقم الفاكس")
    
    print("\n   📮 ص.ب (صندوق البريد):")
    print("      ✅ نُقل إلى الصف الثالث (2), العمود الثالث (2)")
    print("      ✅ يأتي مباشرة بعد صف رقم الفاكس")
    
    print("\n🎯 الفوائد:")
    print("   ✅ ترتيب منطقي للحقول المتعلقة بالاتصال")
    print("   ✅ تجميع أفضل للمعلومات المترابطة")
    print("   ✅ تدفق أكثر سلاسة لإدخال البيانات")
    print("   ✅ واجهة مستخدم محسنة ومنظمة")
    
    print("\n📋 الترتيب النهائي:")
    print("   الصف 1: معلومات الهوية (الاسم + الجهة)")
    print("   الصف 2: الاتصال الأساسي (هاتف + فاكس)")
    print("   الصف 3: الاتصال الإضافي (موبايل + ص.ب) ← الجديد")
    print("   الصف 4: معلومات أخرى (بريد إلكتروني + عنوان)")
    
    print("\n🚀 النتيجة:")
    print("   🎯 شاشة طلب الحوالة محدثة ومحسنة")
    print("   🎯 ترتيب أفضل وأكثر منطقية")
    print("   🎯 تجربة مستخدم محسنة")
    print("   🎯 جاهزة للاستخدام الفوري")

if __name__ == "__main__":
    print("🚀 بدء التحقق من ترتيب الحقول...")
    print("=" * 80)
    
    # التحقق من مواضع الحقول
    positions_correct = verify_field_positions()
    
    # عرض التخطيط الحالي
    display_current_layout()
    
    # النتيجة النهائية
    if positions_correct:
        display_success_summary()
        print("\n🏆 التعديل المطلوب تم تطبيقه بنجاح!")
        
    else:
        print("\n❌ هناك مشكلة في ترتيب الحقول")
    
    print("=" * 80)
