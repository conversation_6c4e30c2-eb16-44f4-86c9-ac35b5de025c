#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("Testing BulkTransferDialog import...")

try:
    from src.ui.remittances.bulk_transfer_dialog import BulkTransferDialog
    print("✅ BulkTransferDialog imported successfully!")
    
    print("🎉 Import test passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
