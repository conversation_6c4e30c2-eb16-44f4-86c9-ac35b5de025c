#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إعدادات التصميم - ProShipment
Design Settings Dialog
"""

import sys
from pathlib import Path
from typing import Dict, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QComboBox, QCheckBox, QGroupBox,
    QSlider, QSpinBox, QTabWidget, QWidget, QFrame,
    QMessageBox, QColorDialog, QFontDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# استيراد مديري التصميم
try:
    from src.ui.responsive.responsive_manager import responsive_manager, ScreenSize
    from src.ui.themes.theme_manager import theme_manager, ThemeType, ColorScheme
    from src.ui.base.base_window import BaseDialog
    MANAGERS_AVAILABLE = True
except ImportError:
    from PySide6.QtWidgets import QDialog as BaseDialog
    MANAGERS_AVAILABLE = False
    print("تحذير: مديري التصميم غير متاحين")

class DesignSettingsDialog(BaseDialog):
    """نافذة إعدادات التصميم"""

    # إشارات
    settings_applied = Signal()

    def __init__(self, parent=None):
        if MANAGERS_AVAILABLE:
            super().__init__(parent, "إعدادات التصميم والمظهر", modal=True)
        else:
            super().__init__(parent)
        
        # إعداد النافذة
        self.resize(600, 500)
        self.setup_ui()
        self.load_current_settings()
        
        # ربط الإشارات
        self.connect_signals()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إزالة الأزرار الافتراضية
        if hasattr(self, 'ok_button'):
            self.ok_button.hide()
        if hasattr(self, 'cancel_button'):
            self.cancel_button.hide()

        # التبويبات
        self.tab_widget = QTabWidget()

        # إضافة التبويبات إلى التخطيط
        if hasattr(self, 'add_content_widget'):
            self.add_content_widget(self.tab_widget)
        else:
            # للحالات التي لا يتوفر فيها BaseDialog
            layout = QVBoxLayout(self)
            layout.addWidget(self.tab_widget)
        
        # تبويب الثيم
        self.create_theme_tab()
        
        # تبويب التصميم المتجاوب
        self.create_responsive_tab()
        
        # تبويب الخطوط
        self.create_fonts_tab()
        
        # تبويب الألوان المخصصة
        self.create_colors_tab()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_theme_tab(self):
        """إنشاء تبويب الثيم"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة الثيم
        theme_group = QGroupBox("🎨 الثيم العام")
        theme_layout = QFormLayout(theme_group)
        
        # اختيار الثيم
        self.theme_combo = QComboBox()
        if MANAGERS_AVAILABLE:
            themes = theme_manager.get_available_themes()
            for key, value in themes.items():
                self.theme_combo.addItem(value, key)
        else:
            self.theme_combo.addItem("فاتح", "light")
            self.theme_combo.addItem("مظلم", "dark")
        
        theme_layout.addRow("نوع الثيم:", self.theme_combo)
        
        # مجموعة مخطط الألوان
        colors_group = QGroupBox("🌈 مخطط الألوان")
        colors_layout = QFormLayout(colors_group)
        
        # اختيار مخطط الألوان
        self.color_scheme_combo = QComboBox()
        if MANAGERS_AVAILABLE:
            color_schemes = theme_manager.get_available_color_schemes()
            for key, value in color_schemes.items():
                self.color_scheme_combo.addItem(value, key)
        else:
            self.color_scheme_combo.addItem("أزرق", "blue")
            self.color_scheme_combo.addItem("أخضر", "green")
        
        colors_layout.addRow("مخطط الألوان:", self.color_scheme_combo)
        
        # معاينة الألوان
        self.create_color_preview(colors_layout)
        
        layout.addWidget(theme_group)
        layout.addWidget(colors_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "الثيم والألوان")
    
    def create_responsive_tab(self):
        """إنشاء تبويب التصميم المتجاوب"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة التصميم المتجاوب
        responsive_group = QGroupBox("📱 التصميم المتجاوب")
        responsive_layout = QFormLayout(responsive_group)
        
        # تفعيل التصميم المتجاوب
        self.responsive_enabled = QCheckBox("تفعيل التصميم المتجاوب")
        self.responsive_enabled.setChecked(True)
        responsive_layout.addRow(self.responsive_enabled)
        
        # التوسيط التلقائي
        self.auto_center = QCheckBox("توسيط النوافذ تلقائياً")
        self.auto_center.setChecked(True)
        responsive_layout.addRow(self.auto_center)
        
        # معلومات الشاشة
        screen_group = QGroupBox("🖥️ معلومات الشاشة")
        screen_layout = QFormLayout(screen_group)
        
        if MANAGERS_AVAILABLE:
            screen_info = responsive_manager.get_screen_info()
            
            self.screen_size_label = QLabel(screen_info.get("size", "غير معروف"))
            screen_layout.addRow("حجم الشاشة:", self.screen_size_label)
            
            self.resolution_label = QLabel(screen_info.get("resolution", "غير معروف"))
            screen_layout.addRow("الدقة:", self.resolution_label)
            
            self.scale_factor_label = QLabel(str(screen_info.get("scale_factor", 1.0)))
            screen_layout.addRow("معامل التكبير:", self.scale_factor_label)
        
        layout.addWidget(responsive_group)
        layout.addWidget(screen_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "التصميم المتجاوب")
    
    def create_fonts_tab(self):
        """إنشاء تبويب الخطوط"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة الخطوط
        fonts_group = QGroupBox("🔤 إعدادات الخطوط")
        fonts_layout = QFormLayout(fonts_group)
        
        # حجم الخط الأساسي
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(10)
        fonts_layout.addRow("حجم الخط الأساسي:", self.font_size_spin)
        
        # معامل تكبير الخط
        self.font_scale_slider = QSlider(Qt.Horizontal)
        self.font_scale_slider.setRange(50, 200)
        self.font_scale_slider.setValue(100)
        self.font_scale_label = QLabel("100%")
        
        font_scale_layout = QHBoxLayout()
        font_scale_layout.addWidget(self.font_scale_slider)
        font_scale_layout.addWidget(self.font_scale_label)
        
        fonts_layout.addRow("معامل تكبير الخط:", font_scale_layout)
        
        # اختيار خط مخصص
        self.custom_font_button = QPushButton("اختيار خط مخصص")
        self.custom_font_button.clicked.connect(self.choose_custom_font)
        fonts_layout.addRow("خط مخصص:", self.custom_font_button)
        
        # معاينة الخط
        self.font_preview = QLabel("نموذج نص للمعاينة - Sample Text Preview")
        self.font_preview.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                padding: 10px;
                background-color: white;
                border-radius: 5px;
            }
        """)
        fonts_layout.addRow("معاينة:", self.font_preview)
        
        layout.addWidget(fonts_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "الخطوط")
    
    def create_colors_tab(self):
        """إنشاء تبويب الألوان المخصصة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة الألوان المخصصة
        colors_group = QGroupBox("🎨 ألوان مخصصة")
        colors_layout = QGridLayout(colors_group)
        
        # ألوان قابلة للتخصيص
        self.color_buttons = {}
        color_names = {
            "primary": "اللون الأساسي",
            "secondary": "اللون الثانوي", 
            "success": "لون النجاح",
            "warning": "لون التحذير",
            "danger": "لون الخطر",
            "background": "لون الخلفية"
        }
        
        row = 0
        for key, name in color_names.items():
            label = QLabel(name + ":")
            button = QPushButton("اختيار اللون")
            button.setProperty("color_key", key)
            button.clicked.connect(self.choose_custom_color)
            
            colors_layout.addWidget(label, row, 0)
            colors_layout.addWidget(button, row, 1)
            
            self.color_buttons[key] = button
            row += 1
        
        # زر إعادة تعيين الألوان
        reset_colors_button = QPushButton("إعادة تعيين الألوان الافتراضية")
        reset_colors_button.clicked.connect(self.reset_colors)
        colors_layout.addWidget(reset_colors_button, row, 0, 1, 2)
        
        layout.addWidget(colors_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "ألوان مخصصة")
    
    def create_color_preview(self, layout):
        """إنشاء معاينة الألوان"""
        preview_frame = QFrame()
        preview_frame.setFixedHeight(60)
        preview_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: white;
            }
        """)
        
        preview_layout = QHBoxLayout(preview_frame)
        
        # عينات الألوان
        self.color_samples = {}
        color_names = ["primary", "secondary", "success", "warning", "danger"]
        
        for color_name in color_names:
            sample = QLabel()
            sample.setFixedSize(40, 40)
            sample.setStyleSheet(f"""
                QLabel {{
                    border-radius: 20px;
                    border: 2px solid #ddd;
                }}
            """)
            preview_layout.addWidget(sample)
            self.color_samples[color_name] = sample
        
        layout.addRow("معاينة الألوان:", preview_frame)
        
        # تحديث المعاينة
        self.update_color_preview()
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        # زر تطبيق
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        buttons_layout.addWidget(apply_button)
        
        # زر معاينة
        preview_button = QPushButton("معاينة")
        preview_button.clicked.connect(self.preview_settings)
        buttons_layout.addWidget(preview_button)
        
        # زر إعادة تعيين
        reset_button = QPushButton("إعادة تعيين")
        reset_button.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(reset_button)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        buttons_layout.addWidget(close_button)
        
        # تطبيق الأنماط
        if MANAGERS_AVAILABLE:
            theme_manager.apply_button_style(apply_button, "primary")
            theme_manager.apply_button_style(preview_button, "info")
            theme_manager.apply_button_style(reset_button, "warning")
            theme_manager.apply_button_style(close_button, "secondary")
        
        self.add_content_widget(buttons_frame)
    
    def connect_signals(self):
        """ربط الإشارات"""
        # تحديث معاينة الألوان عند تغيير المخطط
        self.color_scheme_combo.currentTextChanged.connect(self.update_color_preview)
        
        # تحديث معاينة الخط
        self.font_size_spin.valueChanged.connect(self.update_font_preview)
        self.font_scale_slider.valueChanged.connect(self.update_font_scale_label)
        self.font_scale_slider.valueChanged.connect(self.update_font_preview)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        if not MANAGERS_AVAILABLE:
            return
        
        # تحميل الثيم الحالي
        current_theme = theme_manager.current_theme
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == current_theme.value:
                self.theme_combo.setCurrentIndex(i)
                break
        
        # تحميل مخطط الألوان الحالي
        current_scheme = theme_manager.current_color_scheme
        for i in range(self.color_scheme_combo.count()):
            if self.color_scheme_combo.itemData(i) == current_scheme.value:
                self.color_scheme_combo.setCurrentIndex(i)
                break
        
        # تحميل إعدادات الخط
        font_size = responsive_manager.get_font_size("base")
        if font_size:
            self.font_size_spin.setValue(font_size)
        
        font_scale = int(responsive_manager.font_scale * 100)
        self.font_scale_slider.setValue(font_scale)
        self.update_font_scale_label()
    
    def update_color_preview(self):
        """تحديث معاينة الألوان"""
        if not MANAGERS_AVAILABLE:
            return
        
        scheme_key = self.color_scheme_combo.currentData()
        if scheme_key and hasattr(theme_manager, 'color_schemes'):
            colors = theme_manager.color_schemes.get(ColorScheme(scheme_key), {})
            
            for color_name, sample in self.color_samples.items():
                color = colors.get(color_name, "#cccccc")
                sample.setStyleSheet(f"""
                    QLabel {{
                        background-color: {color};
                        border-radius: 20px;
                        border: 2px solid #ddd;
                    }}
                """)
    
    def update_font_preview(self):
        """تحديث معاينة الخط"""
        font_size = self.font_size_spin.value()
        scale = self.font_scale_slider.value() / 100.0
        final_size = int(font_size * scale)
        
        font = self.font_preview.font()
        font.setPointSize(final_size)
        self.font_preview.setFont(font)
    
    def update_font_scale_label(self):
        """تحديث تسمية معامل تكبير الخط"""
        scale = self.font_scale_slider.value()
        self.font_scale_label.setText(f"{scale}%")
    
    def choose_custom_font(self):
        """اختيار خط مخصص"""
        font, ok = QFontDialog.getFont(self.font_preview.font(), self)
        if ok:
            self.font_preview.setFont(font)
            self.font_size_spin.setValue(font.pointSize())
    
    def choose_custom_color(self):
        """اختيار لون مخصص"""
        button = self.sender()
        color_key = button.property("color_key")
        
        color = QColorDialog.getColor(Qt.white, self)
        if color.isValid():
            # تحديث لون الزر
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color.name()};
                    color: white;
                    border: none;
                    padding: 8px;
                    border-radius: 4px;
                }}
            """)
            
            # حفظ اللون المخصص
            if not hasattr(theme_manager, 'custom_colors'):
                theme_manager.custom_colors = {}
            theme_manager.custom_colors[color_key] = color.name()
    
    def reset_colors(self):
        """إعادة تعيين الألوان الافتراضية"""
        if MANAGERS_AVAILABLE:
            theme_manager.custom_colors = {}
        
        # إعادة تعيين أزرار الألوان
        for button in self.color_buttons.values():
            button.setStyleSheet("")
        
        self.update_color_preview()
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        if not MANAGERS_AVAILABLE:
            QMessageBox.information(self, "معلومات", "مديري التصميم غير متاحين")
            return
        
        try:
            # تطبيق الثيم
            theme_key = self.theme_combo.currentData()
            if theme_key:
                theme_manager.set_theme(ThemeType(theme_key))
            
            # تطبيق مخطط الألوان
            scheme_key = self.color_scheme_combo.currentData()
            if scheme_key:
                theme_manager.set_color_scheme(ColorScheme(scheme_key))
            
            # تطبيق إعدادات الخط
            responsive_manager.font_scale = self.font_scale_slider.value() / 100.0
            
            # إشارة التطبيق
            self.settings_applied.emit()
            
            QMessageBox.information(self, "نجح", "تم تطبيق الإعدادات بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تطبيق الإعدادات:\n{str(e)}")
    
    def preview_settings(self):
        """معاينة الإعدادات"""
        # تطبيق مؤقت للمعاينة
        self.apply_settings()
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إعادة تعيين إلى القيم الافتراضية
            self.theme_combo.setCurrentIndex(0)
            self.color_scheme_combo.setCurrentIndex(0)
            self.font_size_spin.setValue(10)
            self.font_scale_slider.setValue(100)
            self.responsive_enabled.setChecked(True)
            self.auto_center.setChecked(True)
            
            self.reset_colors()
            self.update_font_scale_label()
            self.update_font_preview()
