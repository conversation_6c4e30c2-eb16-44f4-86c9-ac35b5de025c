# تحسينات النوافذ - توسيط وإحضار للمقدمة
## Window Improvements - Centering and Bringing to Front

---

## 🎯 **المشاكل التي تم حلها**

### **المشكلة الأصلية**:
- النوافذ تفتح خلف النافذة الرئيسية
- النوافذ لا تظهر في المقدمة
- النوافذ لا تكون متوسطة في الشاشة

### **الحل المطبق**:
- ✅ **توسيط النوافذ** وسط الشاشة
- ✅ **إحضار النوافذ للمقدمة** باستخدام `raise_()`
- ✅ **تفعيل النوافذ** باستخدام `activateWindow()`

---

## 🔧 **التحسينات المطبقة**

### **1. دالة توسيط النوافذ الجديدة**:
```python
def center_window(self, window):
    """توسيط النافذة وسط الشاشة"""
    try:
        # الحصول على حجم الشاشة
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        
        # الحصول على حجم النافذة
        window_geometry = window.frameGeometry()
        
        # حساب الموقع المركزي
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        
        # تطبيق الموقع الجديد
        window.move(window_geometry.topLeft())
        
    except Exception as e:
        print(f"خطأ في توسيط النافذة: {e}")
```

### **2. النوافذ المحسنة**:

#### **أ. نافذة قائمة الحوالات**:
```python
def open_remittances_window(self):
    """فتح نافذة إدارة الحوالات"""
    try:
        from .remittances.remittances_window import RemittancesWindow
        self.remittances_window = RemittancesWindow()
        
        # توسيط النافذة وسط الشاشة
        self.center_window(self.remittances_window)
        
        # إحضار النافذة للمقدمة
        self.remittances_window.show()
        self.remittances_window.raise_()
        self.remittances_window.activateWindow()
```

#### **ب. نافذة إدارة البنوك**:
```python
def open_banks_management_window(self):
    """فتح نافذة إدارة البنوك والصرافين"""
    try:
        from .remittances.banks_management_window import BanksManagementWindow
        self.banks_management_window = BanksManagementWindow()
        
        # توسيط النافذة وسط الشاشة
        self.center_window(self.banks_management_window)
        
        # إحضار النافذة للمقدمة
        self.banks_management_window.show()
        self.banks_management_window.raise_()
        self.banks_management_window.activateWindow()
```

#### **ج. نافذة إدارة حسابات الموردين**:
```python
def open_supplier_accounts_management_window(self):
    """فتح نافذة إدارة حسابات الموردين"""
    try:
        from .remittances.supplier_accounts_management_window import SupplierAccountsManagementWindow
        self.supplier_accounts_management_window = SupplierAccountsManagementWindow()
        
        # توسيط النافذة وسط الشاشة
        self.center_window(self.supplier_accounts_management_window)
        
        # إحضار النافذة للمقدمة
        self.supplier_accounts_management_window.show()
        self.supplier_accounts_management_window.raise_()
        self.supplier_accounts_management_window.activateWindow()
```

---

## 📋 **النوافذ المحسنة**

### **النوافذ الرئيسية**:
1. ✅ **قائمة الحوالات** - `open_remittances_window()`
2. ✅ **إدارة البنوك** - `open_banks_management_window()`
3. ✅ **إدارة حسابات الموردين** - `open_supplier_accounts_management_window()`
4. ✅ **إنشاء حوالة جديدة** - `open_new_remittance_dialog()`
5. ✅ **إعدادات قاعدة البيانات** - `open_database_settings_window()`

### **الميزات المضافة لكل نافذة**:
- 🎯 **توسيط تلقائي** وسط الشاشة
- 🔝 **إحضار للمقدمة** تلقائياً
- ⚡ **تفعيل فوري** للنافذة
- 🛡️ **معالجة الأخطاء** المحسنة

---

## 🚀 **كيفية الاستخدام**

### **1. تشغيل التطبيق**:
```bash
python main.py
```

### **2. اختبار التحسينات**:
```bash
python test_window_centering.py
```

### **3. تجربة النوافذ**:
1. افتح التطبيق
2. انقر على أي عنصر في القائمة الرئيسية
3. ستلاحظ أن النافذة:
   - تظهر في المقدمة
   - متوسطة في الشاشة
   - نشطة ومركزة

---

## 📊 **النتائج المحققة**

### **قبل التحسين**:
- ❌ النوافذ تفتح خلف النافذة الرئيسية
- ❌ النوافذ تظهر في مواقع عشوائية
- ❌ المستخدم يحتاج للبحث عن النافذة

### **بعد التحسين**:
- ✅ النوافذ تظهر في المقدمة فوراً
- ✅ النوافذ متوسطة وسط الشاشة
- ✅ تجربة مستخدم محسنة وسلسة

---

## 🔧 **التفاصيل التقنية**

### **الدوال المستخدمة**:
- `QApplication.primaryScreen()` - للحصول على معلومات الشاشة
- `screen.availableGeometry()` - للحصول على المساحة المتاحة
- `window.frameGeometry()` - للحصول على حجم النافذة
- `window.move()` - لتحريك النافذة للموقع الجديد
- `window.raise_()` - لإحضار النافذة للمقدمة
- `window.activateWindow()` - لتفعيل النافذة

### **معالجة الأخطاء**:
- تم إضافة `try-except` لجميع العمليات
- طباعة رسائل خطأ واضحة
- عدم توقف التطبيق في حالة الأخطاء

---

## ✅ **الحالة النهائية**

**جميع النوافذ تعمل الآن بشكل مثالي:**
- 🎯 **متوسطة وسط الشاشة**
- 🔝 **تظهر في المقدمة**
- ⚡ **نشطة ومركزة**
- 🛡️ **آمنة ومستقرة**

**تجربة المستخدم محسنة بشكل كبير!** 🎉

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025-07-08  
**الإصدار**: 1.1
