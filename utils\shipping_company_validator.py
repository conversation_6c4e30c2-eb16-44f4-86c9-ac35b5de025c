"""
نظام التحقق من صحة وتصحيح أسماء شركات الشحن العالمية - نسخة مبسطة
يقوم بمقارنة البيانات المدخلة مع قاعدة بيانات شركات الشحن العالمية
ويقترح التصحيحات المناسبة
"""

import re
from difflib import SequenceMatcher
from typing import List, Dict, Tuple, Optional

class ShippingCompanyValidator:
    """فئة للتحقق من صحة وتصحيح أسماء شركات الشحن"""
    
    def __init__(self):
        """تهيئة قاعدة بيانات شركات الشحن العالمية"""
        self.global_shipping_companies = {
            # شركات الشحن البحري العالمية
            "MSC": {
                "full_name": "Mediterranean Shipping Company",
                "arabic_name": "شركة البحر الأبيض المتوسط للشحن",
                "aliases": ["MSC", "Mediterranean Shipping", "MSC Shipping", "ام اس سي"],
                "type": "بحري",
                "country": "سويسرا"
            },
            "MAERSK": {
                "full_name": "A.P. Moller-Maersk",
                "arabic_name": "مايرسك",
                "aliases": ["Maersk", "Maersk Line", "AP Moller", "مايرسك", "مايرسك لاين"],
                "type": "بحري",
                "country": "الدنمارك"
            },
            "CMA_CGM": {
                "full_name": "CMA CGM Group",
                "arabic_name": "سي ام ايه سي جي ام",
                "aliases": ["CMA CGM", "CMA-CGM", "CMACGM", "سي ام ايه", "سي جي ام"],
                "type": "بحري",
                "country": "فرنسا"
            },
            "COSCO": {
                "full_name": "China COSCO Shipping Corporation",
                "arabic_name": "كوسكو الصينية للشحن",
                "aliases": ["COSCO", "COSCO Shipping", "China COSCO", "كوسكو", "كوسكو شيبينغ"],
                "type": "بحري",
                "country": "الصين"
            },
            "HAPAG_LLOYD": {
                "full_name": "Hapag-Lloyd AG",
                "arabic_name": "هاباغ لويد",
                "aliases": ["Hapag Lloyd", "Hapag-Lloyd", "HAPAG", "هاباغ", "هاباغ لويد"],
                "type": "بحري",
                "country": "ألمانيا"
            }
        }
    
    def validate_company_name(self, company_name: str) -> Dict:
        """
        التحقق من صحة اسم شركة الشحن
        
        Args:
            company_name: اسم الشركة المراد التحقق منه
            
        Returns:
            dict: نتيجة التحقق مع الاقتراحات
        """
        if not company_name or not company_name.strip():
            return {
                "is_valid": False,
                "confidence": 0.0,
                "suggestions": [],
                "company_info": None,
                "message": "اسم الشركة فارغ"
            }
        
        company_name = company_name.strip()
        
        # البحث المباشر
        exact_match = self._find_exact_match(company_name)
        if exact_match:
            return {
                "is_valid": True,
                "confidence": 1.0,
                "suggestions": [],
                "company_info": exact_match,
                "message": "تم العثور على مطابقة تامة"
            }
        
        # البحث التقريبي
        fuzzy_matches = self._find_fuzzy_matches(company_name)
        
        if fuzzy_matches:
            best_match = fuzzy_matches[0]
            if best_match["confidence"] >= 0.8:
                return {
                    "is_valid": True,
                    "confidence": best_match["confidence"],
                    "suggestions": fuzzy_matches[:3],
                    "company_info": best_match["company_info"],
                    "message": f"مطابقة تقريبية بثقة {best_match['confidence']:.0%}"
                }
            else:
                return {
                    "is_valid": False,
                    "confidence": best_match["confidence"],
                    "suggestions": fuzzy_matches[:5],
                    "company_info": None,
                    "message": "لم يتم العثور على مطابقة دقيقة، إليك بعض الاقتراحات"
                }
        
        return {
            "is_valid": False,
            "confidence": 0.0,
            "suggestions": [],
            "company_info": None,
            "message": "لم يتم العثور على شركة مطابقة"
        }
    
    def _find_exact_match(self, company_name: str) -> Optional[Dict]:
        """البحث عن مطابقة تامة"""
        company_name_lower = company_name.lower()
        
        for company_key, company_info in self.global_shipping_companies.items():
            # فحص الاسم الكامل
            if company_info["full_name"].lower() == company_name_lower:
                return company_info
            
            # فحص الاسم العربي
            if company_info["arabic_name"].lower() == company_name_lower:
                return company_info
            
            # فحص الأسماء البديلة
            for alias in company_info["aliases"]:
                if alias.lower() == company_name_lower:
                    return company_info
        
        return None
    
    def _find_fuzzy_matches(self, company_name: str) -> List[Dict]:
        """البحث التقريبي"""
        matches = []
        company_name_lower = company_name.lower()
        
        for company_key, company_info in self.global_shipping_companies.items():
            # فحص الاسم الكامل
            confidence = SequenceMatcher(None, company_name_lower, 
                                       company_info["full_name"].lower()).ratio()
            if confidence > 0.3:
                matches.append({
                    "company_info": company_info,
                    "confidence": confidence,
                    "matched_name": company_info["full_name"]
                })
            
            # فحص الاسم العربي
            confidence = SequenceMatcher(None, company_name_lower, 
                                       company_info["arabic_name"].lower()).ratio()
            if confidence > 0.3:
                matches.append({
                    "company_info": company_info,
                    "confidence": confidence,
                    "matched_name": company_info["arabic_name"]
                })
            
            # فحص الأسماء البديلة
            for alias in company_info["aliases"]:
                confidence = SequenceMatcher(None, company_name_lower, alias.lower()).ratio()
                if confidence > 0.3:
                    matches.append({
                        "company_info": company_info,
                        "confidence": confidence,
                        "matched_name": alias
                    })
        
        # ترتيب النتائج حسب الثقة
        matches.sort(key=lambda x: x["confidence"], reverse=True)
        
        # إزالة المكررات
        unique_matches = []
        seen_companies = set()
        
        for match in matches:
            company_key = match["company_info"]["full_name"]
            if company_key not in seen_companies:
                unique_matches.append(match)
                seen_companies.add(company_key)
        
        return unique_matches
    
    def get_company_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """الحصول على اقتراحات أسماء الشركات"""
        if not partial_name or len(partial_name) < 2:
            return []
        
        suggestions = []
        partial_lower = partial_name.lower()
        
        for company_info in self.global_shipping_companies.values():
            # إضافة الاسم الكامل
            if partial_lower in company_info["full_name"].lower():
                suggestions.append(company_info["full_name"])
            
            # إضافة الاسم العربي
            if partial_lower in company_info["arabic_name"].lower():
                suggestions.append(company_info["arabic_name"])
            
            # إضافة الأسماء البديلة
            for alias in company_info["aliases"]:
                if partial_lower in alias.lower():
                    suggestions.append(alias)
        
        # إزالة المكررات وترتيب النتائج
        unique_suggestions = list(set(suggestions))
        unique_suggestions.sort(key=lambda x: (
            0 if x.lower().startswith(partial_lower) else 1,
            len(x),
            x.lower()
        ))
        
        return unique_suggestions[:limit]
    
    def get_all_companies(self) -> List[Dict]:
        """الحصول على قائمة بجميع الشركات"""
        return list(self.global_shipping_companies.values())
    
    def get_companies_by_type(self, shipping_type: str) -> List[Dict]:
        """الحصول على الشركات حسب النوع"""
        return [
            company for company in self.global_shipping_companies.values()
            if company["type"] == shipping_type
        ]
    
    def get_companies_by_country(self, country: str) -> List[Dict]:
        """الحصول على الشركات حسب البلد"""
        return [
            company for company in self.global_shipping_companies.values()
            if company["country"] == country
        ]
