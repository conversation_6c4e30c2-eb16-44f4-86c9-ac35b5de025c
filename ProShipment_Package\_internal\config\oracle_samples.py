#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملفات إعدادات Oracle النموذجية - ProShipment V2.0.0
Oracle Sample Configurations
"""

import json
from pathlib import Path
from typing import Dict, Any

def create_sample_configs():
    """إنشاء ملفات إعدادات نموذجية"""
    
    # إنشاء مجلد config
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # إعدادات التطوير - Oracle XE المحلي
    development_config = {
        "type": "oracle",
        "oracle_config": {
            "host": "localhost",
            "port": 1521,
            "service_name": "XE",
            "sid": None,
            "username": "proshipment_dev",
            "password": "dev_password_123",
            "connection_type": "service_name",
            "pool_size": 5,
            "max_overflow": 10,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "pool_pre_ping": True,
            "use_ssl": False,
            "ssl_cert_path": None,
            "wallet_location": None,
            "encoding": "UTF-8",
            "nencoding": "UTF-8",
            "threaded": True,
            "auto_commit": False
        },
        "sqlite_config": {
            "path": "data/proshipment_dev.db",
            "timeout": 30,
            "check_same_thread": False
        }
    }
    
    # إعدادات الاختبار
    testing_config = {
        "type": "oracle",
        "oracle_config": {
            "host": "test-oracle-server.company.com",
            "port": 1521,
            "service_name": "PROSHIP_TEST",
            "sid": None,
            "username": "proshipment_test",
            "password": "test_password_456",
            "connection_type": "service_name",
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "pool_pre_ping": True,
            "use_ssl": True,
            "ssl_cert_path": "/path/to/ssl/cert.pem",
            "wallet_location": "/path/to/oracle/wallet",
            "encoding": "UTF-8",
            "nencoding": "UTF-8",
            "threaded": True,
            "auto_commit": False
        },
        "sqlite_config": {
            "path": "data/proshipment_test.db",
            "timeout": 30,
            "check_same_thread": False
        }
    }
    
    # إعدادات الإنتاج
    production_config = {
        "type": "oracle",
        "oracle_config": {
            "host": "prod-oracle-cluster.company.com",
            "port": 1521,
            "service_name": "PROSHIP_PROD",
            "sid": None,
            "username": "proshipment_prod",
            "password": "",  # يجب تعيينها من متغيرات البيئة
            "connection_type": "service_name",
            "pool_size": 50,
            "max_overflow": 100,
            "pool_timeout": 60,
            "pool_recycle": 1800,
            "pool_pre_ping": True,
            "use_ssl": True,
            "ssl_cert_path": "/etc/ssl/oracle/cert.pem",
            "wallet_location": "/etc/oracle/wallet",
            "encoding": "UTF-8",
            "nencoding": "UTF-8",
            "threaded": True,
            "auto_commit": False
        },
        "sqlite_config": {
            "path": "data/proshipment_backup.db",
            "timeout": 30,
            "check_same_thread": False
        }
    }
    
    # إعدادات Oracle Cloud
    cloud_config = {
        "type": "oracle",
        "oracle_config": {
            "host": "your-cloud-instance.oraclecloud.com",
            "port": 1522,
            "service_name": "proship_high",
            "sid": None,
            "username": "ADMIN",
            "password": "",  # يجب تعيينها من متغيرات البيئة
            "connection_type": "service_name",
            "pool_size": 25,
            "max_overflow": 50,
            "pool_timeout": 45,
            "pool_recycle": 3600,
            "pool_pre_ping": True,
            "use_ssl": True,
            "ssl_cert_path": None,
            "wallet_location": "/path/to/cloud/wallet",
            "encoding": "UTF-8",
            "nencoding": "UTF-8",
            "threaded": True,
            "auto_commit": False
        },
        "sqlite_config": {
            "path": "data/proshipment_local_backup.db",
            "timeout": 30,
            "check_same_thread": False
        }
    }
    
    # إعدادات Oracle Enterprise مع SID
    enterprise_sid_config = {
        "type": "oracle",
        "oracle_config": {
            "host": "enterprise-oracle.company.com",
            "port": 1521,
            "service_name": None,
            "sid": "ORCL",
            "username": "proshipment",
            "password": "enterprise_password",
            "connection_type": "sid",
            "pool_size": 30,
            "max_overflow": 60,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "pool_pre_ping": True,
            "use_ssl": False,
            "ssl_cert_path": None,
            "wallet_location": None,
            "encoding": "UTF-8",
            "nencoding": "UTF-8",
            "threaded": True,
            "auto_commit": False
        },
        "sqlite_config": {
            "path": "data/proshipment_enterprise.db",
            "timeout": 30,
            "check_same_thread": False
        }
    }
    
    # إعدادات SQLite للمقارنة
    sqlite_config = {
        "type": "sqlite",
        "oracle_config": {
            "host": "localhost",
            "port": 1521,
            "service_name": "XE",
            "sid": None,
            "username": "proshipment",
            "password": "",
            "connection_type": "service_name",
            "pool_size": 20,
            "max_overflow": 30,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "pool_pre_ping": True,
            "use_ssl": False,
            "ssl_cert_path": None,
            "wallet_location": None,
            "encoding": "UTF-8",
            "nencoding": "UTF-8",
            "threaded": True,
            "auto_commit": False
        },
        "sqlite_config": {
            "path": "data/proshipment.db",
            "timeout": 30,
            "check_same_thread": False
        }
    }
    
    # قائمة الإعدادات
    configs = {
        "development": development_config,
        "testing": testing_config,
        "production": production_config,
        "cloud": cloud_config,
        "enterprise_sid": enterprise_sid_config,
        "sqlite": sqlite_config
    }
    
    # حفظ الملفات
    for env_name, config in configs.items():
        config_file = config_dir / f"database_{env_name}.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"✅ تم إنشاء ملف الإعدادات: {config_file}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء {config_file}: {e}")
    
    # إنشاء ملف README
    readme_content = """# ملفات إعدادات Oracle - ProShipment V2.0.0

## الملفات المتاحة:

### 1. database_development.json
- إعدادات التطوير مع Oracle XE المحلي
- مناسب للتطوير والاختبار المحلي
- pool_size صغير (5 اتصالات)

### 2. database_testing.json
- إعدادات بيئة الاختبار
- يتضمن إعدادات SSL
- pool_size متوسط (10 اتصالات)

### 3. database_production.json
- إعدادات بيئة الإنتاج
- أمان عالي مع SSL و Wallet
- pool_size كبير (50 اتصال)
- كلمة المرور فارغة (يجب تعيينها من متغيرات البيئة)

### 4. database_cloud.json
- إعدادات Oracle Cloud
- منفذ 1522 (Oracle Cloud الافتراضي)
- يتطلب Oracle Wallet

### 5. database_enterprise_sid.json
- إعدادات Oracle Enterprise مع SID
- يستخدم SID بدلاً من Service Name

### 6. database_sqlite.json
- إعدادات SQLite للمقارنة
- يمكن استخدامها للعودة إلى SQLite

## كيفية الاستخدام:

### 1. نسخ الملف المناسب:
```bash
cp config/database_development.json config/database.json
```

### 2. تعديل الإعدادات:
- عدل عنوان الخادم
- عدل اسم المستخدم وكلمة المرور
- عدل اسم الخدمة أو SID

### 3. تعيين كلمة المرور من متغيرات البيئة (للإنتاج):
```bash
export ORACLE_PASSWORD="your_secure_password"
```

### 4. اختبار الإعدادات:
```bash
python tools/oracle_setup_wizard.py
```

## متغيرات البيئة المدعومة:

- `DATABASE_TYPE`: نوع قاعدة البيانات (sqlite/oracle)
- `ORACLE_HOST`: عنوان خادم Oracle
- `ORACLE_PORT`: منفذ Oracle
- `ORACLE_SERVICE_NAME`: اسم خدمة Oracle
- `ORACLE_SID`: SID لـ Oracle
- `ORACLE_USERNAME`: اسم مستخدم Oracle
- `ORACLE_PASSWORD`: كلمة مرور Oracle
- `ORACLE_CONNECTION_TYPE`: نوع الاتصال (service_name/sid/tns)
- `ORACLE_USE_SSL`: استخدام SSL (true/false)
- `ORACLE_WALLET_LOCATION`: مسار Oracle Wallet

## أمثلة على الاستخدام:

### تطوير محلي:
```bash
export DATABASE_TYPE=oracle
export ORACLE_HOST=localhost
export ORACLE_SERVICE_NAME=XE
export ORACLE_USERNAME=proshipment
export ORACLE_PASSWORD=dev123
```

### إنتاج:
```bash
export DATABASE_TYPE=oracle
export ORACLE_HOST=prod-oracle.company.com
export ORACLE_SERVICE_NAME=PROSHIP_PROD
export ORACLE_USERNAME=proshipment_prod
export ORACLE_PASSWORD=secure_prod_password
export ORACLE_USE_SSL=true
export ORACLE_WALLET_LOCATION=/etc/oracle/wallet
```
"""
    
    readme_file = config_dir / "README.md"
    try:
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ تم إنشاء ملف README: {readme_file}")
    except Exception as e:
        print(f"❌ خطأ في إنشاء README: {e}")

def create_environment_scripts():
    """إنشاء سكريبتات متغيرات البيئة"""
    
    scripts_dir = Path("scripts")
    scripts_dir.mkdir(exist_ok=True)
    
    # سكريبت Windows
    windows_script = """@echo off
REM إعدادات Oracle لـ ProShipment V2.0.0 - Windows

REM نوع قاعدة البيانات
set DATABASE_TYPE=oracle

REM إعدادات Oracle
set ORACLE_HOST=localhost
set ORACLE_PORT=1521
set ORACLE_SERVICE_NAME=XE
set ORACLE_USERNAME=proshipment
set ORACLE_PASSWORD=your_password

REM إعدادات الأداء
set ORACLE_POOL_SIZE=20
set ORACLE_MAX_OVERFLOW=30

REM إعدادات الأمان (اختياري)
REM set ORACLE_USE_SSL=true
REM set ORACLE_WALLET_LOCATION=C:\\oracle\\wallet

echo ✅ تم تعيين متغيرات البيئة لـ Oracle
echo 🚀 يمكنك الآن تشغيل التطبيق
"""
    
    # سكريبت Linux/Mac
    linux_script = """#!/bin/bash
# إعدادات Oracle لـ ProShipment V2.0.0 - Linux/Mac

# نوع قاعدة البيانات
export DATABASE_TYPE=oracle

# إعدادات Oracle
export ORACLE_HOST=localhost
export ORACLE_PORT=1521
export ORACLE_SERVICE_NAME=XE
export ORACLE_USERNAME=proshipment
export ORACLE_PASSWORD=your_password

# إعدادات الأداء
export ORACLE_POOL_SIZE=20
export ORACLE_MAX_OVERFLOW=30

# إعدادات الأمان (اختياري)
# export ORACLE_USE_SSL=true
# export ORACLE_WALLET_LOCATION=/etc/oracle/wallet

echo "✅ تم تعيين متغيرات البيئة لـ Oracle"
echo "🚀 يمكنك الآن تشغيل التطبيق"
"""
    
    # حفظ السكريبتات
    scripts = {
        "setup_oracle_env.bat": windows_script,
        "setup_oracle_env.sh": linux_script
    }
    
    for script_name, script_content in scripts.items():
        script_file = scripts_dir / script_name
        try:
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # جعل السكريبت قابل للتنفيذ في Linux/Mac
            if script_name.endswith('.sh'):
                import stat
                script_file.chmod(script_file.stat().st_mode | stat.S_IEXEC)
            
            print(f"✅ تم إنشاء سكريبت: {script_file}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء {script_file}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 إنشاء ملفات إعدادات Oracle النموذجية...")
    print("="*60)
    
    # إنشاء ملفات الإعدادات
    create_sample_configs()
    
    print("\n📜 إنشاء سكريبتات متغيرات البيئة...")
    create_environment_scripts()
    
    print("\n✅ تم إنشاء جميع الملفات النموذجية بنجاح!")
    print("\n💡 لاستخدام الإعدادات:")
    print("1. انسخ الملف المناسب إلى config/database.json")
    print("2. عدل الإعدادات حسب بيئتك")
    print("3. شغل معالج الإعداد: python tools/oracle_setup_wizard.py")

if __name__ == "__main__":
    main()
