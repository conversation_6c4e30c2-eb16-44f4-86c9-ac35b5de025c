#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص سريع للتطبيق
Quick Application Audit
"""

import sys
import os
import sqlite3
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def quick_audit():
    """فحص سريع للتطبيق"""
    print("🔍 فحص سريع للتطبيق...")
    print("="*60)
    
    errors = []
    warnings = []
    info = []
    
    # 1. فحص الملفات الأساسية
    print("\n📁 فحص الملفات الأساسية...")
    
    essential_files = [
        "main.py",
        "requirements.txt",
        "src/database/models.py",
        "src/database/database_manager.py",
        "src/ui/main_window.py"
    ]
    
    for file_path in essential_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
            info.append(f"ملف موجود: {file_path}")
        else:
            print(f"   ❌ {file_path}")
            errors.append(f"ملف مطلوب مفقود: {file_path}")
    
    # 2. فحص قاعدة البيانات
    print("\n🗄️ فحص قاعدة البيانات...")
    
    db_path = project_root / "data" / "proshipment.db"
    if db_path.exists():
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = [
                'companies', 'suppliers', 'items', 'shipments',
                'purchase_orders', 'system_settings'
            ]
            
            for table in required_tables:
                if table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table}: {count} سجل")
                    info.append(f"جدول {table}: {count} سجل")
                else:
                    print(f"   ❌ {table}: مفقود")
                    errors.append(f"جدول مطلوب مفقود: {table}")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ خطأ في قاعدة البيانات: {e}")
            errors.append(f"خطأ في قاعدة البيانات: {e}")
    else:
        print("   ❌ ملف قاعدة البيانات مفقود")
        errors.append("ملف قاعدة البيانات مفقود")
    
    # 3. فحص الاستيرادات الأساسية
    print("\n📦 فحص الاستيرادات الأساسية...")
    
    try:
        # فحص PySide6
        import PySide6
        print("   ✅ PySide6")
        info.append("PySide6 متاح")
    except ImportError:
        print("   ❌ PySide6")
        errors.append("PySide6 غير متاح")
    
    try:
        # فحص SQLAlchemy
        import sqlalchemy
        print("   ✅ SQLAlchemy")
        info.append("SQLAlchemy متاح")
    except ImportError:
        print("   ❌ SQLAlchemy")
        errors.append("SQLAlchemy غير متاح")
    
    try:
        # فحص ReportLab
        import reportlab
        print("   ✅ ReportLab")
        info.append("ReportLab متاح")
    except ImportError:
        print("   ❌ ReportLab")
        warnings.append("ReportLab غير متاح - قد يؤثر على PDF")
    
    # 4. فحص النوافذ الرئيسية
    print("\n🖥️ فحص النوافذ الرئيسية...")
    
    main_windows = [
        "src/ui/main_window.py",
        "src/ui/shipments/shipments_window.py",
        "src/ui/suppliers/suppliers_window.py",
        "src/ui/items/items_window.py"
    ]
    
    for window_file in main_windows:
        window_path = project_root / window_file
        if window_path.exists():
            print(f"   ✅ {window_file}")
            info.append(f"نافذة موجودة: {window_file}")
        else:
            print(f"   ❌ {window_file}")
            warnings.append(f"نافذة مفقودة: {window_file}")
    
    # 5. فحص مجلدات المرفقات
    print("\n📎 فحص مجلدات المرفقات...")
    
    attachments_dir = project_root / "attachments"
    if attachments_dir.exists():
        subdirs = [d for d in attachments_dir.iterdir() if d.is_dir()]
        print(f"   ✅ مجلد المرفقات: {len(subdirs)} مجلد فرعي")
        info.append(f"مجلد المرفقات: {len(subdirs)} مجلد فرعي")
    else:
        print("   ⚠️ مجلد المرفقات مفقود")
        warnings.append("مجلد المرفقات مفقود")
    
    # 6. فحص ملفات الاختبار
    print("\n🧪 فحص ملفات الاختبار...")
    
    test_files = list(project_root.glob("test_*.py"))
    print(f"   📊 عدد ملفات الاختبار: {len(test_files)}")
    info.append(f"عدد ملفات الاختبار: {len(test_files)}")
    
    # عرض التقرير النهائي
    print("\n" + "="*60)
    print("📊 التقرير النهائي")
    print("="*60)
    
    print(f"\n🔴 الأخطاء الحرجة: {len(errors)}")
    for error in errors:
        print(f"   ❌ {error}")
    
    print(f"\n🟡 التحذيرات: {len(warnings)}")
    for warning in warnings:
        print(f"   ⚠️ {warning}")
    
    print(f"\n🟢 المعلومات: {len(info)}")
    for i in info[:5]:  # أول 5 معلومات
        print(f"   ℹ️ {i}")
    
    if len(info) > 5:
        print(f"   ... و {len(info) - 5} معلومة أخرى")
    
    # تقييم الحالة
    print(f"\n📈 تقييم الحالة:")
    if len(errors) == 0:
        if len(warnings) == 0:
            print("   🟢 ممتاز: التطبيق جاهز للاستخدام")
            status = "excellent"
        elif len(warnings) <= 3:
            print("   🟡 جيد: بعض التحذيرات البسيطة")
            status = "good"
        else:
            print("   🟠 متوسط: عدة تحذيرات")
            status = "average"
    else:
        if len(errors) <= 2:
            print("   🔴 يحتاج إصلاح: بعض الأخطاء")
            status = "needs_fix"
        else:
            print("   🚨 حرج: عدة أخطاء حرجة")
            status = "critical"
    
    return status, errors, warnings, info

def identify_critical_issues():
    """تحديد المشاكل الحرجة"""
    print("\n🚨 تحديد المشاكل الحرجة...")
    
    critical_issues = []
    
    # فحص الملف الرئيسي
    main_file = project_root / "main.py"
    if not main_file.exists():
        critical_issues.append("الملف الرئيسي main.py مفقود")
    
    # فحص مدير قاعدة البيانات
    db_manager = project_root / "src" / "database" / "database_manager.py"
    if not db_manager.exists():
        critical_issues.append("مدير قاعدة البيانات مفقود")
    
    # فحص النماذج
    models_file = project_root / "src" / "database" / "models.py"
    if not models_file.exists():
        critical_issues.append("ملف النماذج مفقود")
    
    # فحص النافذة الرئيسية
    main_window = project_root / "src" / "ui" / "main_window.py"
    if not main_window.exists():
        critical_issues.append("النافذة الرئيسية مفقودة")
    
    # فحص قاعدة البيانات
    db_file = project_root / "data" / "proshipment.db"
    if not db_file.exists():
        critical_issues.append("ملف قاعدة البيانات مفقود")
    
    if critical_issues:
        print("   🚨 مشاكل حرجة وجدت:")
        for issue in critical_issues:
            print(f"      ❌ {issue}")
    else:
        print("   ✅ لا توجد مشاكل حرجة")
    
    return critical_issues

def suggest_fixes():
    """اقتراح الإصلاحات"""
    print("\n🔧 اقتراحات الإصلاح...")
    
    fixes = []
    
    # فحص requirements.txt
    req_file = project_root / "requirements.txt"
    if not req_file.exists():
        fixes.append("إنشاء ملف requirements.txt مع التبعيات المطلوبة")
    
    # فحص مجلد البيانات
    data_dir = project_root / "data"
    if not data_dir.exists():
        fixes.append("إنشاء مجلد data لقاعدة البيانات")
    
    # فحص مجلد المرفقات
    attachments_dir = project_root / "attachments"
    if not attachments_dir.exists():
        fixes.append("إنشاء مجلد attachments للمرفقات")
    
    if fixes:
        print("   🔧 إصلاحات مقترحة:")
        for fix in fixes:
            print(f"      🛠️ {fix}")
    else:
        print("   ✅ لا توجد إصلاحات مطلوبة")
    
    return fixes

def main():
    """الدالة الرئيسية"""
    try:
        # تشغيل الفحص السريع
        status, errors, warnings, info = quick_audit()
        
        # تحديد المشاكل الحرجة
        critical_issues = identify_critical_issues()
        
        # اقتراح الإصلاحات
        suggested_fixes = suggest_fixes()
        
        # النتيجة النهائية
        print("\n" + "="*60)
        print("🎯 النتيجة النهائية")
        print("="*60)
        
        if status == "excellent":
            print("🎉 التطبيق في حالة ممتازة وجاهز للاستخدام!")
            return True
        elif status == "good":
            print("✅ التطبيق في حالة جيدة مع بعض التحذيرات البسيطة")
            return True
        elif status == "average":
            print("⚠️ التطبيق يحتاج بعض التحسينات")
            return False
        elif status == "needs_fix":
            print("🔴 التطبيق يحتاج إصلاحات قبل الاستخدام")
            return False
        else:
            print("🚨 التطبيق في حالة حرجة ويحتاج إصلاحات فورية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
