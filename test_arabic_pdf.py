#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مكتبات PDF مع دعم اللغة العربية
"""

import sys
from pathlib import Path

def test_reportlab_arabic():
    """اختبار ReportLab مع العربية"""
    print("🧪 اختبار ReportLab مع العربية...")
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from arabic_reshaper import arabic_reshaper
        from bidi.algorithm import get_display
        
        # إنشاء ملف PDF
        pdf_path = "test_reportlab_arabic.pdf"
        c = canvas.Canvas(pdf_path, pagesize=A4)
        
        # النص العربي للاختبار
        arabic_text = "مرحباً بكم في نظام إدارة الحوالات المتقدم"
        
        # تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(arabic_text)
        bidi_text = get_display(reshaped_text)
        
        # كتابة النص (بدون خط مخصص أولاً)
        c.drawString(100, 750, "Testing Arabic PDF Generation")
        c.drawString(100, 700, bidi_text)
        
        # حفظ الملف
        c.save()
        
        print("✅ ReportLab: تم إنشاء ملف PDF بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ ReportLab: خطأ - {e}")
        return False

def test_fpdf2_arabic():
    """اختبار FPDF2 مع العربية"""
    print("\n🧪 اختبار FPDF2 مع العربية...")
    
    try:
        from fpdf import FPDF
        from arabic_reshaper import arabic_reshaper
        from bidi.algorithm import get_display
        
        # إنشاء ملف PDF
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        
        # النص العربي للاختبار
        arabic_text = "نظام إدارة الحوالات - طلب حوالة جديد"
        
        # تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(arabic_text)
        bidi_text = get_display(reshaped_text)
        
        # كتابة النص
        pdf.cell(200, 10, txt="Testing FPDF2 Arabic Support", ln=1, align='C')
        pdf.cell(200, 10, txt=bidi_text.encode('latin-1', 'ignore').decode('latin-1'), ln=1, align='C')
        
        # حفظ الملف
        pdf.output("test_fpdf2_arabic.pdf")
        
        print("✅ FPDF2: تم إنشاء ملف PDF بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ FPDF2: خطأ - {e}")
        return False

def test_weasyprint_arabic():
    """اختبار WeasyPrint مع العربية"""
    print("\n🧪 اختبار WeasyPrint مع العربية...")
    
    try:
        import weasyprint
        
        # HTML مع النص العربي
        html_content = """
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 20px;
                }
                .header {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 20px;
                }
                .content {
                    font-size: 16px;
                    line-height: 1.6;
                }
            </style>
        </head>
        <body>
            <div class="header">نظام إدارة الحوالات المتقدم</div>
            <div class="content">
                <p>مرحباً بكم في نظام إدارة الحوالات</p>
                <p>هذا اختبار لدعم اللغة العربية في ملفات PDF</p>
                <p>تاريخ الإنشاء: ٢٠٢٥/٠١/٠٩</p>
            </div>
        </body>
        </html>
        """
        
        # إنشاء PDF من HTML
        weasyprint.HTML(string=html_content).write_pdf("test_weasyprint_arabic.pdf")
        
        print("✅ WeasyPrint: تم إنشاء ملف PDF بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ WeasyPrint: خطأ - {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار مكتبات PDF مع دعم العربية...\n")
    
    results = []
    
    # اختبار المكتبات
    results.append(test_reportlab_arabic())
    results.append(test_fpdf2_arabic())
    results.append(test_weasyprint_arabic())
    
    # عرض النتائج
    print("\n" + "="*50)
    print("📊 ملخص نتائج الاختبار:")
    print("="*50)
    
    successful_tests = sum(results)
    total_tests = len(results)
    
    libraries = ["ReportLab", "FPDF2", "WeasyPrint"]
    for i, (lib, result) in enumerate(zip(libraries, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{lib}: {status}")
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{total_tests} مكتبات تعمل بنجاح")
    
    if successful_tests > 0:
        print("\n🎉 تم تثبيت مكتبات PDF بنجاح!")
        print("يمكنك الآن استخدام هذه المكتبات لإنشاء ملفات PDF تدعم العربية")
        
        # عرض الملفات المنشأة
        created_files = []
        for filename in ["test_reportlab_arabic.pdf", "test_fpdf2_arabic.pdf", "test_weasyprint_arabic.pdf"]:
            if Path(filename).exists():
                created_files.append(filename)
        
        if created_files:
            print(f"\nالملفات المنشأة: {', '.join(created_files)}")
    else:
        print("\n⚠️ فشل في جميع الاختبارات. يرجى التحقق من التثبيت.")
    
    return successful_tests > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
