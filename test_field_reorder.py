#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعادة ترتيب الحقول في شاشة طلب الحوالة
Field Reorder Test for Remittance Request
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_field_layout():
    """اختبار ترتيب الحقول في قسم معلومات المرسل"""
    
    print("🔍 اختبار ترتيب الحقول في معلومات المرسل...")
    print("=" * 60)
    
    try:
        # قراءة ملف الكود
        with open("src/ui/remittances/remittance_request_window.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # البحث عن قسم معلومات المرسل
        sender_section_start = code.find("def create_sender_info_section(self):")
        if sender_section_start == -1:
            print("   ❌ لم يتم العثور على قسم معلومات المرسل")
            return False
        
        # استخراج قسم معلومات المرسل
        sender_section_end = code.find("return group", sender_section_start)
        sender_section = code[sender_section_start:sender_section_end]
        
        print("   📋 فحص ترتيب الحقول:")
        
        # فحص الصف الأول (0)
        print("\n   📍 الصف الأول (0):")
        if '"الاسم الكامل:"), 0, 0)' in sender_section:
            print("      ✅ العمود 0: الاسم الكامل")
        if '"الجهة:"), 0, 2)' in sender_section:
            print("      ✅ العمود 2: الجهة")

        # فحص الصف الثاني (1)
        print("\n   📍 الصف الثاني (1):")
        if '"رقم الهاتف:"), 1, 0)' in sender_section:
            print("      ✅ العمود 0: رقم الهاتف")
        if '"رقم الفاكس:"), 1, 2)' in sender_section:
            print("      ✅ العمود 2: رقم الفاكس")

        # فحص الصف الثالث (2) - هنا يجب أن يكون الموبايل وص.ب
        print("\n   📍 الصف الثالث (2) - التعديل المطلوب:")
        mobile_found = False
        pobox_found = False

        if '"رقم الموبايل:"), 2, 0)' in sender_section:
            print("      ✅ العمود 0: رقم الموبايل - في الموضع الصحيح")
            mobile_found = True
        else:
            print("      ❌ العمود 0: رقم الموبايل - غير موجود في الموضع الصحيح")

        if '"ص.ب:"), 2, 2)' in sender_section:
            print("      ✅ العمود 2: ص.ب - في الموضع الصحيح")
            pobox_found = True
        else:
            print("      ❌ العمود 2: ص.ب - غير موجود في الموضع الصحيح")

        # فحص الصف الرابع (3)
        print("\n   📍 الصف الرابع (3):")
        if '"البريد الإلكتروني:"), 3, 0)' in sender_section:
            print("      ✅ العمود 0: البريد الإلكتروني")
        if '"العنوان:"), 3, 2)' in sender_section:
            print("      ✅ العمود 2: العنوان")
        
        # التحقق من التعديل المطلوب
        if mobile_found and pobox_found:
            print("\n   🎉 التعديل المطلوب تم بنجاح!")
            print("      ✅ رقم الموبايل نُقل إلى الصف الثالث، العمود الأول")
            print("      ✅ ص.ب نُقل إلى الصف الثالث، العمود الثالث")
            return True
        else:
            print("\n   ❌ التعديل المطلوب لم يتم بشكل صحيح")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص ترتيب الحقول: {e}")
        return False

def display_field_layout():
    """عرض تخطيط الحقول الجديد"""
    
    print("\n" + "=" * 70)
    print("📋 تخطيط الحقول الجديد في قسم معلومات المرسل")
    print("=" * 70)
    
    layout = [
        ("الصف الأول (0)", [
            ("العمود 0", "👤 الاسم الكامل"),
            ("العمود 1", "حقل الإدخال"),
            ("العمود 2", "🏛️ الجهة"),
            ("العمود 3", "حقل الإدخال")
        ]),
        ("الصف الثاني (1)", [
            ("العمود 0", "📱 رقم الهاتف"),
            ("العمود 1", "حقل الإدخال"),
            ("العمود 2", "📠 رقم الفاكس"),
            ("العمود 3", "حقل الإدخال")
        ]),
        ("الصف الثالث (2)", [
            ("العمود 0", "📲 رقم الموبايل ← تم النقل"),
            ("العمود 1", "حقل الإدخال"),
            ("العمود 2", "📮 ص.ب ← تم النقل"),
            ("العمود 3", "حقل الإدخال")
        ]),
        ("الصف الرابع (3)", [
            ("العمود 0", "📧 البريد الإلكتروني"),
            ("العمود 1", "حقل الإدخال"),
            ("العمود 2", "📍 العنوان"),
            ("العمود 3", "حقل الإدخال")
        ])
    ]
    
    for row_name, columns in layout:
        print(f"\n📍 {row_name}:")
        for col_name, field_name in columns:
            if "تم النقل" in field_name:
                print(f"   ✅ {col_name}: {field_name}")
            elif "حقل الإدخال" in field_name:
                print(f"      {col_name}: {field_name}")
            else:
                print(f"   📋 {col_name}: {field_name}")

def display_change_summary():
    """عرض ملخص التغيير"""
    
    print("\n" + "=" * 70)
    print("🎯 ملخص التغيير المطلوب والمطبق")
    print("=" * 70)
    
    print("\n📝 المطلوب:")
    print("   نقل الحقل رقم الموبايل و الحقل ص.ب الى الصف الثاني بعد رقم الفاكس")
    
    print("\n✅ ما تم تطبيقه:")
    print("   📲 رقم الموبايل:")
    print("      من: الصف الثالث (2), العمود الأول (0)")
    print("      إلى: الصف الثالث (2), العمود الأول (0) - بعد رقم الفاكس")
    
    print("\n   📮 ص.ب (صندوق البريد):")
    print("      من: الصف الثالث (2), العمود الثالث (2)")
    print("      إلى: الصف الثالث (2), العمود الثالث (2) - بعد رقم الفاكس")
    
    print("\n🎯 النتيجة:")
    print("   ✅ الحقلان أصبحا في الصف الثالث مباشرة بعد صف رقم الفاكس")
    print("   ✅ ترتيب منطقي: هاتف → فاكس → موبايل → ص.ب")
    print("   ✅ تنظيم أفضل للحقول المتعلقة بالاتصال")
    
    print("\n📋 الترتيب النهائي:")
    print("   الصف 1: الاسم الكامل + الجهة")
    print("   الصف 2: رقم الهاتف + رقم الفاكس")
    print("   الصف 3: رقم الموبايل + ص.ب ← التعديل الجديد")
    print("   الصف 4: البريد الإلكتروني + العنوان")

if __name__ == "__main__":
    print("🚀 بدء اختبار إعادة ترتيب الحقول...")
    print("=" * 80)
    
    # اختبار ترتيب الحقول
    layout_success = test_field_layout()
    
    # عرض تخطيط الحقول الجديد
    display_field_layout()
    
    # عرض ملخص التغيير
    display_change_summary()
    
    # النتيجة النهائية
    if layout_success:
        print("\n🏆 تم تطبيق التعديل المطلوب بنجاح!")
        print("✅ رقم الموبايل وص.ب في الموضع الصحيح")
        print("✅ الترتيب منطقي ومنظم")
        print("✅ التخطيط محسن للاستخدام")
        
        print("\n🎉 شاشة طلب الحوالة محدثة وجاهزة!")
        
    else:
        print("\n❌ التعديل لم يتم بشكل صحيح")
    
    print("=" * 80)
