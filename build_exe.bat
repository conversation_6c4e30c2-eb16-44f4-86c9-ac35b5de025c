@echo off
chcp 65001 > nul
title ProShipment - بناء ملف التثبيت التنفيذي

echo.
echo ========================================
echo   ProShipment V2.0.0
echo   أداة بناء ملف التثبيت التنفيذي
echo ========================================
echo.

echo 📋 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت متطلبات البناء...
pip install -r requirements_build.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo 🔨 بدء عملية البناء...
python build_installer.py
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم إنشاء ملف التثبيت بنجاح!
echo 📁 تحقق من مجلد installer للملفات المنشأة
echo.

pause
