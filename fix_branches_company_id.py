#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة company_id في جدول الفروع
Fix company_id issue in branches table
"""

import sqlite3
from pathlib import Path

def fix_branches_company_id():
    """إصلاح مشكلة company_id في جدول الفروع"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إصلاح مشكلة company_id في جدول الفروع...")
        
        # 1. التحقق من وجود شركة افتراضية
        cursor.execute("SELECT COUNT(*) FROM companies")
        companies_count = cursor.fetchone()[0]
        
        if companies_count == 0:
            print("📋 إضافة شركة افتراضية...")
            cursor.execute("""
                INSERT INTO companies (name, name_en, code, address, phone, email, is_active)
                VALUES ('شركة ProShipment', 'ProShipment Company', 'PSC001', 'الرياض، المملكة العربية السعودية', '011-1234567', '<EMAIL>', 1)
            """)
            company_id = cursor.lastrowid
            print(f"✅ تم إضافة شركة افتراضية (ID: {company_id})")
        else:
            cursor.execute("SELECT id FROM companies WHERE is_active = 1 LIMIT 1")
            result = cursor.fetchone()
            company_id = result[0] if result else 1
            print(f"ℹ️ استخدام الشركة الموجودة (ID: {company_id})")
        
        # 2. تحديث الفروع التي لا تحتوي على company_id
        cursor.execute("UPDATE branches SET company_id = ? WHERE company_id IS NULL", (company_id,))
        updated_rows = cursor.rowcount
        if updated_rows > 0:
            print(f"✅ تم تحديث {updated_rows} فرع بـ company_id")
        
        # 3. تعديل جدول الفروع لجعل company_id له قيمة افتراضية
        print("📋 تعديل هيكل جدول الفروع...")
        
        # إنشاء جدول مؤقت بالهيكل الجديد
        cursor.execute("""
            CREATE TABLE branches_temp (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER DEFAULT 1,
                name TEXT NOT NULL,
                name_en TEXT,
                code TEXT,
                address TEXT,
                phone TEXT,
                fax TEXT,
                email TEXT,
                website TEXT,
                manager_name TEXT,
                manager_phone TEXT,
                type TEXT DEFAULT 'فرع',
                country TEXT DEFAULT 'السعودية',
                parent_type TEXT,
                parent_id INTEGER,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
        """)
        
        # نسخ البيانات من الجدول القديم
        cursor.execute("""
            INSERT INTO branches_temp (
                id, company_id, name, name_en, code, address, phone, fax, email, website,
                manager_name, manager_phone, type, country, parent_type, parent_id, notes, is_active, created_at
            )
            SELECT 
                id, 
                COALESCE(company_id, 1) as company_id,
                name, name_en, code, address, phone, fax, email, website,
                manager_name, manager_phone, 
                COALESCE(type, 'فرع') as type,
                COALESCE(country, 'السعودية') as country,
                parent_type, parent_id, notes, 
                COALESCE(is_active, 1) as is_active,
                COALESCE(created_at, CURRENT_TIMESTAMP) as created_at
            FROM branches
        """)
        
        # حذف الجدول القديم وإعادة تسمية الجديد
        cursor.execute("DROP TABLE branches")
        cursor.execute("ALTER TABLE branches_temp RENAME TO branches")
        
        print("✅ تم تعديل هيكل جدول الفروع بنجاح")
        
        # 4. اختبار إضافة فرع جديد
        print("\n🧪 اختبار إضافة فرع جديد...")
        
        try:
            cursor.execute("""
                INSERT INTO branches (name, name_en, code, address, phone, fax, email, manager_name, type, notes)
                VALUES ('فرع الاختبار', 'Test Branch', 'TEST001', 'شارع الاختبار', '011-1234567', '011-1234568', '<EMAIL>', 'مدير الاختبار', 'اختبار', 'فرع تجريبي')
            """)
            
            branch_id = cursor.lastrowid
            print(f"✅ تم إضافة فرع جديد بنجاح (ID: {branch_id})")
            
            # التحقق من البيانات
            cursor.execute("SELECT name, company_id FROM branches WHERE id = ?", (branch_id,))
            result = cursor.fetchone()
            if result:
                print(f"   الاسم: {result[0]}")
                print(f"   معرف الشركة: {result[1]}")
            
            # حذف البيانات التجريبية
            cursor.execute("DELETE FROM branches WHERE code = 'TEST001'")
            print("✅ تم حذف البيانات التجريبية")
            
        except Exception as e:
            print(f"❌ فشل في إضافة الفرع: {str(e)}")
            return False
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح مشكلة company_id بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح مشكلة company_id: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح مشكلة company_id في جدول الفروع...")
    print("=" * 60)
    
    success = fix_branches_company_id()
    
    if success:
        print("\n✅ تم إنجاز الإصلاح بنجاح!")
        print("📊 الآن يمكنك:")
        print("   • إضافة فروع جديدة بدون خطأ company_id")
        print("   • استخدام جميع الحقول المطلوبة")
        print("   • العمل بثقة تامة")
    else:
        print("\n❌ فشل في إنجاز الإصلاح!")
    
    print("=" * 60)
