#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة المرفقات - ProShipment
Attachments Manager Dialog
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QFileDialog, QMessageBox, QGroupBox, QTextEdit, QLineEdit,
    QComboBox, QProgressBar, QFrame, QSplitter, QWidget, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap, QDesktopServices
from PySide6.QtCore import QUrl

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

class AttachmentsManagerDialog(QDialog):
    """نافذة إدارة المرفقات"""
    
    def __init__(self, parent=None, request_id=None):
        super().__init__(parent)
        self.request_id = request_id
        self.attachments_dir = Path("data/attachments")
        self.attachments_dir.mkdir(parents=True, exist_ok=True)
        
        # إنشاء مجلد خاص بالطلب
        if self.request_id:
            self.request_attachments_dir = self.attachments_dir / str(self.request_id)
            self.request_attachments_dir.mkdir(parents=True, exist_ok=True)
        
        self.setup_ui()
        self.load_attachments()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"إدارة المرفقات - طلب رقم {self.request_id or 'جديد'}")
        self.setModal(True)
        self.resize(900, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("📎 إدارة المرفقات والمستندات")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # قسم إضافة مرفق جديد
        add_section = self.create_add_attachment_section()
        main_layout.addWidget(add_section)
        
        # جدول المرفقات
        self.create_attachments_table()
        main_layout.addWidget(self.attachments_table)
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        main_layout.addLayout(buttons_layout)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #d5dbdb;
                border-radius: 4px;
                color: #2c3e50;
            }
        """)
        main_layout.addWidget(self.status_label)
    
    def create_add_attachment_section(self):
        """إنشاء قسم إضافة مرفق جديد"""
        group = QGroupBox("➕ إضافة مرفق جديد")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # اختيار الملف
        layout.addWidget(QLabel("📁 اختيار الملف:"), 0, 0)
        
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("اختر ملف للإرفاق...")
        self.file_path_edit.setReadOnly(True)
        file_layout.addWidget(self.file_path_edit)
        
        browse_btn = QPushButton("📂 تصفح")
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(browse_btn)
        
        layout.addLayout(file_layout, 0, 1, 1, 2)
        
        # وصف المرفق
        layout.addWidget(QLabel("📝 الوصف:"), 1, 0)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف المرفق (اختياري)")
        layout.addWidget(self.description_edit, 1, 1)
        
        # نوع المرفق
        layout.addWidget(QLabel("🏷️ النوع:"), 1, 2)
        self.attachment_type_combo = QComboBox()
        self.attachment_type_combo.addItems([
            "مستند عام",
            "هوية شخصية",
            "جواز سفر",
            "إثبات عنوان",
            "كشف حساب بنكي",
            "فاتورة",
            "عقد",
            "صورة شخصية",
            "أخرى"
        ])
        layout.addWidget(self.attachment_type_combo, 1, 3)
        
        # زر الإضافة
        add_btn = QPushButton("➕ إضافة المرفق")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_btn.clicked.connect(self.add_attachment)
        layout.addWidget(add_btn, 2, 0, 1, 4)
        
        return group
    
    def create_attachments_table(self):
        """إنشاء جدول المرفقات"""
        self.attachments_table = QTableWidget()
        self.attachments_table.setColumnCount(6)
        self.attachments_table.setHorizontalHeaderLabels([
            "اسم الملف", "النوع", "الحجم", "تاريخ الإضافة", "الوصف", "الإجراءات"
        ])
        
        # تنسيق الجدول
        header = self.attachments_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        
        self.attachments_table.setAlternatingRowColors(True)
        self.attachments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.attachments_table.verticalHeader().setDefaultSectionSize(50)
        
        self.attachments_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # زر عرض المرفق
        view_btn = QPushButton("👁️ عرض")
        view_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        view_btn.clicked.connect(self.view_attachment)
        layout.addWidget(view_btn)
        
        # زر تحميل
        download_btn = QPushButton("💾 تحميل")
        download_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        download_btn.clicked.connect(self.download_attachment)
        layout.addWidget(download_btn)
        
        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(self.delete_attachment)
        layout.addWidget(delete_btn)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        return layout
    
    def browse_file(self):
        """تصفح واختيار ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف للإرفاق",
            "",
            "جميع الملفات (*.*);;"
            "مستندات PDF (*.pdf);;"
            "صور (*.png *.jpg *.jpeg *.gif *.bmp);;"
            "مستندات Word (*.doc *.docx);;"
            "جداول Excel (*.xls *.xlsx);;"
            "ملفات نصية (*.txt)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            
            # تعيين وصف تلقائي بناءً على نوع الملف
            file_name = Path(file_path).name
            file_ext = Path(file_path).suffix.lower()
            
            if not self.description_edit.text():
                if file_ext in ['.pdf']:
                    self.description_edit.setText(f"مستند PDF - {file_name}")
                elif file_ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                    self.description_edit.setText(f"صورة - {file_name}")
                elif file_ext in ['.doc', '.docx']:
                    self.description_edit.setText(f"مستند Word - {file_name}")
                elif file_ext in ['.xls', '.xlsx']:
                    self.description_edit.setText(f"جدول Excel - {file_name}")
                else:
                    self.description_edit.setText(f"ملف - {file_name}")
    
    def add_attachment(self):
        """إضافة مرفق جديد"""
        file_path = self.file_path_edit.text().strip()
        
        if not file_path:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف للإرفاق")
            return
        
        if not Path(file_path).exists():
            QMessageBox.warning(self, "خطأ", "الملف المحدد غير موجود")
            return
        
        try:
            # نسخ الملف إلى مجلد المرفقات
            source_file = Path(file_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"{timestamp}_{source_file.name}"
            
            if self.request_id:
                destination = self.request_attachments_dir / new_filename
            else:
                destination = self.attachments_dir / new_filename
            
            shutil.copy2(source_file, destination)
            
            # إضافة إلى الجدول
            self.add_attachment_to_table(
                filename=new_filename,
                original_name=source_file.name,
                file_type=self.attachment_type_combo.currentText(),
                description=self.description_edit.text() or source_file.name,
                file_path=str(destination)
            )
            
            # تنظيف النموذج
            self.file_path_edit.clear()
            self.description_edit.clear()
            self.attachment_type_combo.setCurrentIndex(0)
            
            self.status_label.setText(f"✅ تم إضافة المرفق: {source_file.name}")
            
            QMessageBox.information(self, "نجح", "تم إضافة المرفق بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المرفق:\n{str(e)}")
    
    def add_attachment_to_table(self, filename, original_name, file_type, description, file_path):
        """إضافة مرفق إلى الجدول"""
        row = self.attachments_table.rowCount()
        self.attachments_table.insertRow(row)
        
        # اسم الملف
        name_item = QTableWidgetItem(original_name)
        name_item.setData(Qt.UserRole, file_path)  # حفظ مسار الملف
        self.attachments_table.setItem(row, 0, name_item)
        
        # النوع
        self.attachments_table.setItem(row, 1, QTableWidgetItem(file_type))
        
        # الحجم
        try:
            file_size = Path(file_path).stat().st_size
            size_text = self.format_file_size(file_size)
        except:
            size_text = "غير معروف"
        self.attachments_table.setItem(row, 2, QTableWidgetItem(size_text))
        
        # تاريخ الإضافة
        date_text = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.attachments_table.setItem(row, 3, QTableWidgetItem(date_text))
        
        # الوصف
        self.attachments_table.setItem(row, 4, QTableWidgetItem(description))
        
        # أزرار الإجراءات
        actions_widget = self.create_action_buttons(row)
        self.attachments_table.setCellWidget(row, 5, actions_widget)
    
    def create_action_buttons(self, row):
        """إنشاء أزرار الإجراءات لكل صف"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # زر عرض
        view_btn = QPushButton("👁️")
        view_btn.setToolTip("عرض المرفق")
        view_btn.setFixedSize(30, 30)
        view_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        view_btn.clicked.connect(lambda: self.view_attachment_by_row(row))
        layout.addWidget(view_btn)
        
        # زر حذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("حذف المرفق")
        delete_btn.setFixedSize(30, 30)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_attachment_by_row(row))
        layout.addWidget(delete_btn)
        
        layout.addStretch()
        return widget
    
    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} كيلوبايت"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} جيجابايت"
    
    def load_attachments(self):
        """تحميل المرفقات الموجودة"""
        if not self.request_id:
            return
        
        try:
            if self.request_attachments_dir.exists():
                for file_path in self.request_attachments_dir.glob("*"):
                    if file_path.is_file():
                        # استخراج الاسم الأصلي (إزالة timestamp)
                        filename = file_path.name
                        if "_" in filename:
                            original_name = "_".join(filename.split("_")[1:])
                        else:
                            original_name = filename
                        
                        self.add_attachment_to_table(
                            filename=filename,
                            original_name=original_name,
                            file_type="مستند عام",
                            description=original_name,
                            file_path=str(file_path)
                        )
        except Exception as e:
            print(f"خطأ في تحميل المرفقات: {e}")
    
    def view_attachment(self):
        """عرض المرفق المحدد"""
        current_row = self.attachments_table.currentRow()
        if current_row >= 0:
            self.view_attachment_by_row(current_row)
    
    def view_attachment_by_row(self, row):
        """عرض مرفق بواسطة رقم الصف"""
        try:
            name_item = self.attachments_table.item(row, 0)
            if name_item:
                file_path = name_item.data(Qt.UserRole)
                if file_path and Path(file_path).exists():
                    # فتح الملف بالتطبيق الافتراضي
                    QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
                    self.status_label.setText(f"تم فتح الملف: {name_item.text()}")
                else:
                    QMessageBox.warning(self, "خطأ", "الملف غير موجود")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الملف:\n{str(e)}")
    
    def download_attachment(self):
        """تحميل المرفق المحدد"""
        current_row = self.attachments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مرفق للتحميل")
            return
        
        try:
            name_item = self.attachments_table.item(current_row, 0)
            file_path = name_item.data(Qt.UserRole)
            
            if not file_path or not Path(file_path).exists():
                QMessageBox.warning(self, "خطأ", "الملف غير موجود")
                return
            
            # اختيار مكان الحفظ
            save_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ المرفق",
                name_item.text(),
                "جميع الملفات (*.*)"
            )
            
            if save_path:
                shutil.copy2(file_path, save_path)
                QMessageBox.information(self, "نجح", f"تم حفظ الملف في:\n{save_path}")
                self.status_label.setText(f"تم تحميل: {name_item.text()}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الملف:\n{str(e)}")
    
    def delete_attachment(self):
        """حذف المرفق المحدد"""
        current_row = self.attachments_table.currentRow()
        if current_row >= 0:
            self.delete_attachment_by_row(current_row)
    
    def delete_attachment_by_row(self, row):
        """حذف مرفق بواسطة رقم الصف"""
        try:
            name_item = self.attachments_table.item(row, 0)
            if not name_item:
                return
            
            filename = name_item.text()
            file_path = name_item.data(Qt.UserRole)
            
            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل تريد حذف المرفق:\n{filename}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # حذف الملف من القرص
                if file_path and Path(file_path).exists():
                    Path(file_path).unlink()
                
                # حذف الصف من الجدول
                self.attachments_table.removeRow(row)
                
                self.status_label.setText(f"تم حذف المرفق: {filename}")
                QMessageBox.information(self, "نجح", "تم حذف المرفق بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف المرفق:\n{str(e)}")
    
    def get_attachments_count(self):
        """الحصول على عدد المرفقات"""
        return self.attachments_table.rowCount()
    
    def has_attachments(self):
        """التحقق من وجود مرفقات"""
        return self.get_attachments_count() > 0
