#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مولد PDF لطلبات الحوالات
"""

import sys
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pdf_generator():
    """اختبار مولد PDF"""
    print("🧪 اختبار مولد PDF لطلبات الحوالات...")
    
    try:
        # استيراد مولد PDF
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # إنشاء بيانات تجريبية مطابقة للنموذج
        test_data = {
            'request_number': '2025-01',
            'request_date': '2025/01/09',
            'remittance_amount': '63,500',
            'currency': 'USD',
            'transfer_purpose': 'COST OF FOODSTUFF',
            
            # بيانات المستفيد
            'receiver_name': 'CHINA TRADING COMPANY LTD',
            'receiver_address': 'BEIJING, CHINA',
            'receiver_account': '****************',
            
            # بيانات البنك
            'receiver_bank': 'BANK OF CHINA',
            'receiver_bank_branch': 'BEIJING BRANCH',
            'receiver_swift': 'BKCHCNBJ',
            'receiver_country': 'CHINA',
            
            # بيانات المرسل
            'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
            'sender_address': 'TAIZ STREET, ORPHAN BUILDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',
            'sender_entity': 'G.M: - NASHA\'AT RASHAD QASIM ALDUBAEE',
            'sender_phone': '+967 1 616109',
            'sender_fax': '+967 1 615909',
            'sender_mobile': '+967 *********',
            'sender_email': '<EMAIL>, <EMAIL>'
        }
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # إنشاء ملف PDF
        output_path = "test_remittance_form.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        print(f"✅ تم إنشاء ملف PDF بنجاح: {result_path}")
        
        # التحقق من وجود الملف
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"📄 حجم الملف: {file_size} بايت")
            print(f"📁 مسار الملف: {Path(result_path).absolute()}")
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت المكتبات المطلوبة:")
        print("pip install reportlab arabic-reshaper python-bidi")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arabic_support():
    """اختبار دعم العربية"""
    print("\n🧪 اختبار دعم اللغة العربية...")
    
    try:
        from arabic_reshaper import arabic_reshaper
        from bidi.algorithm import get_display
        
        # نص تجريبي
        arabic_text = "مرحباً بكم في نظام إدارة الحوالات"
        
        # تشكيل النص
        reshaped_text = arabic_reshaper.reshape(arabic_text)
        bidi_text = get_display(reshaped_text)
        
        print(f"✅ النص الأصلي: {arabic_text}")
        print(f"✅ النص المشكل: {bidi_text}")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبات العربية غير متوفرة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في معالجة العربية: {e}")
        return False

def test_integration():
    """اختبار التكامل مع نافذة طلب الحوالة"""
    print("\n🧪 اختبار التكامل مع نافذة طلب الحوالة...")
    
    try:
        # محاولة استيراد النافذة
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # التحقق من وجود دالة PDF
        if hasattr(RemittanceRequestWindow, 'generate_pdf_report'):
            print("✅ دالة إنشاء PDF موجودة في النافذة")
            return True
        else:
            print("❌ دالة إنشاء PDF غير موجودة في النافذة")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد النافذة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار مولد PDF لطلبات الحوالات...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_arabic_support())
    results.append(test_pdf_generator())
    results.append(test_integration())
    
    # عرض النتائج
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    
    test_names = [
        "دعم اللغة العربية",
        "مولد PDF",
        "التكامل مع النافذة"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ مولد PDF جاهز للاستخدام")
        print("✅ دعم كامل للغة العربية")
        print("✅ تكامل مع نافذة طلب الحوالة")
        
        # عرض الملفات المنشأة
        created_files = []
        for filename in ["test_remittance_form.pdf"]:
            if Path(filename).exists():
                created_files.append(filename)
        
        if created_files:
            print(f"\n📁 الملفات المنشأة: {', '.join(created_files)}")
            print("يمكنك فتح الملفات لمراجعة النتائج")
            
    elif successful_tests >= len(results) * 0.67:
        print("\n✅ معظم الاختبارات نجحت!")
        print("يمكن استخدام مولد PDF مع بعض القيود")
    else:
        print("\n⚠️ عدة اختبارات فشلت. يرجى مراجعة المتطلبات:")
        print("- تثبيت reportlab: pip install reportlab")
        print("- تثبيت دعم العربية: pip install arabic-reshaper python-bidi")
    
    return successful_tests >= len(results) * 0.67

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
