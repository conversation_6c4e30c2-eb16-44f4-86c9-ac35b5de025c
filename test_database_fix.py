#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة قاعدة البيانات
Database Fix Test
"""

import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    
    print("🗄️ اختبار هيكل قاعدة البيانات...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='remittance_requests'
        """)
        
        if cursor.fetchone():
            print("   ✅ جدول remittance_requests موجود")
            
            # فحص أعمدة الجدول
            cursor.execute("PRAGMA table_info(remittance_requests)")
            columns = cursor.fetchall()
            
            print(f"   ✅ عدد الأعمدة: {len(columns)}")
            
            column_names = [col[1] for col in columns]
            print("   📋 الأعمدة الموجودة:")
            for i, col_name in enumerate(column_names, 1):
                print(f"      {i:2d}. {col_name}")
            
            # فحص الأعمدة المطلوبة
            required_columns = [
                'id', 'request_number', 'sender_name', 'receiver_name',
                'amount', 'source_currency', 'target_currency', 'status'
            ]
            
            missing_columns = []
            for col in required_columns:
                if col not in column_names:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"   ❌ أعمدة مفقودة: {missing_columns}")
                return False
            else:
                print("   ✅ جميع الأعمدة الأساسية موجودة")
            
            # فحص الأعمدة الإضافية
            optional_columns = [
                'receiver_country', 'priority', 'sender_phone', 'receiver_phone'
            ]
            
            existing_optional = []
            for col in optional_columns:
                if col in column_names:
                    existing_optional.append(col)
            
            print(f"   ✅ الأعمدة الإضافية الموجودة: {existing_optional}")
            
        else:
            print("   ❌ جدول remittance_requests غير موجود")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_table_creation_function():
    """اختبار دالة إنشاء/تحديث الجدول"""
    
    print("\n🔧 اختبار دالة إنشاء/تحديث الجدول...")
    print("=" * 60)
    
    try:
        # محاولة استيراد الدالة
        import os
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء مثيل مؤقت
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = RemittanceRequestWindow()
        
        # اختبار دالة إنشاء/تحديث الجدول
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("   🔄 تشغيل دالة create_or_update_table...")
        window.create_or_update_table(cursor)
        
        # فحص النتيجة
        cursor.execute("PRAGMA table_info(remittance_requests)")
        columns_after = cursor.fetchall()
        
        print(f"   ✅ عدد الأعمدة بعد التحديث: {len(columns_after)}")
        
        # فحص وجود الأعمدة المهمة
        column_names = [col[1] for col in columns_after]
        
        important_columns = ['receiver_country', 'priority', 'sender_phone', 'receiver_phone']
        found_important = []
        
        for col in important_columns:
            if col in column_names:
                found_important.append(col)
        
        print(f"   ✅ الأعمدة المهمة الموجودة: {found_important}")
        
        conn.close()
        window.close()
        
        print("   ✅ دالة إنشاء/تحديث الجدول تعمل بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار دالة إنشاء الجدول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_safe_query():
    """اختبار الاستعلام الآمن"""
    
    print("\n🔍 اختبار الاستعلام الآمن...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # اختبار الاستعلام الآمن
        print("   🔄 تجربة الاستعلام الآمن...")
        
        try:
            cursor.execute("""
                SELECT id, request_number, sender_name, receiver_name, amount, 
                       source_currency, 
                       CASE WHEN receiver_country IS NOT NULL THEN receiver_country ELSE '' END as receiver_country,
                       created_at, status, 
                       CASE WHEN priority IS NOT NULL THEN priority ELSE 'عادي' END as priority
                FROM remittance_requests 
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            results = cursor.fetchall()
            print(f"   ✅ الاستعلام الآمن نجح - عدد النتائج: {len(results)}")
            
        except sqlite3.OperationalError as e:
            print(f"   ⚠️ الاستعلام الآمن فشل، تجربة الاستعلام البديل: {e}")
            
            cursor.execute("""
                SELECT id, request_number, sender_name, receiver_name, amount, 
                       source_currency, '' as receiver_country, created_at, status, 'عادي' as priority
                FROM remittance_requests 
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            results = cursor.fetchall()
            print(f"   ✅ الاستعلام البديل نجح - عدد النتائج: {len(results)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الاستعلام: {e}")
        return False

def test_insert_sample_data():
    """اختبار إدراج بيانات تجريبية"""
    
    print("\n📝 اختبار إدراج بيانات تجريبية...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # بيانات تجريبية
        test_request_number = f"TEST{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        print("   🔄 محاولة إدراج بيانات كاملة...")
        
        try:
            cursor.execute("""
                INSERT INTO remittance_requests (
                    request_number, sender_name, receiver_name, amount, 
                    source_currency, target_currency, receiver_country, priority, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_request_number, "اختبار مرسل", "اختبار مستقبل", 1000.0,
                "YER", "USD", "أمريكا", "عادي", "معلق"
            ))
            
            print("   ✅ إدراج البيانات الكاملة نجح")
            
        except sqlite3.OperationalError:
            print("   ⚠️ إدراج البيانات الكاملة فشل، تجربة البيانات الأساسية...")
            
            cursor.execute("""
                INSERT INTO remittance_requests (
                    request_number, sender_name, receiver_name, amount, 
                    source_currency, target_currency, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                test_request_number, "اختبار مرسل", "اختبار مستقبل", 1000.0,
                "YER", "USD", "معلق"
            ))
            
            print("   ✅ إدراج البيانات الأساسية نجح")
        
        # التحقق من الإدراج
        cursor.execute("SELECT * FROM remittance_requests WHERE request_number = ?", (test_request_number,))
        result = cursor.fetchone()
        
        if result:
            print("   ✅ تم التحقق من وجود البيانات المدرجة")
            
            # حذف البيانات التجريبية
            cursor.execute("DELETE FROM remittance_requests WHERE request_number = ?", (test_request_number,))
            print("   ✅ تم حذف البيانات التجريبية")
        else:
            print("   ❌ فشل في العثور على البيانات المدرجة")
            return False
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدراج البيانات: {e}")
        return False

def display_fix_summary():
    """عرض ملخص الإصلاح"""
    
    print("\n" + "=" * 70)
    print("🎯 ملخص إصلاح مشكلة قاعدة البيانات")
    print("=" * 70)
    
    print("\n❌ المشكلة الأصلية:")
    print("   no such column: receiver_country")
    
    print("\n🔍 السبب:")
    print("   - الجدول تم إنشاؤه مسبقاً بأعمدة أقل")
    print("   - الكود يحاول الوصول لأعمدة جديدة غير موجودة")
    print("   - عدم توافق بين هيكل الجدول القديم والجديد")
    
    print("\n✅ الحلول المطبقة:")
    print("   1. دالة create_or_update_table() لتحديث الجدول تلقائياً")
    print("   2. استعلامات آمنة مع CASE WHEN للأعمدة المفقودة")
    print("   3. استعلامات بديلة في حالة فشل الاستعلام الأساسي")
    print("   4. إدراج آمن مع fallback للأعمدة الأساسية")
    
    print("\n🚀 النتيجة:")
    print("   ✅ الجدول يتم تحديثه تلقائياً عند فتح الشاشة")
    print("   ✅ الاستعلامات تعمل مع الجداول القديمة والجديدة")
    print("   ✅ لا توجد أخطاء عند الوصول للأعمدة المفقودة")
    print("   ✅ النظام متوافق مع جميع إصدارات قاعدة البيانات")

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح مشكلة قاعدة البيانات...")
    print("=" * 80)
    
    # اختبار هيكل قاعدة البيانات
    structure_ok = test_database_structure()
    
    # اختبار دالة إنشاء/تحديث الجدول
    creation_ok = test_table_creation_function()
    
    # اختبار الاستعلام الآمن
    query_ok = test_safe_query()
    
    # اختبار إدراج البيانات
    insert_ok = test_insert_sample_data()
    
    # عرض ملخص الإصلاح
    display_fix_summary()
    
    # النتيجة النهائية
    if all([structure_ok, creation_ok, query_ok, insert_ok]):
        print("\n🏆 تم إصلاح جميع مشاكل قاعدة البيانات بنجاح!")
        print("✅ هيكل قاعدة البيانات محدث ومتوافق")
        print("✅ الاستعلامات الآمنة تعمل بشكل صحيح")
        print("✅ إدراج البيانات يعمل بدون مشاكل")
        print("✅ النظام متوافق مع جميع الإصدارات")
        
        print("\n🎉 يمكنك الآن استخدام شاشة طلب الحوالة بدون أخطاء!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إلى حل")
    
    print("=" * 80)
