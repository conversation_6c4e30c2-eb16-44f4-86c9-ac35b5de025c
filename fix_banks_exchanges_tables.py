#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جداول البنوك والصرافات - إضافة الأعمدة المفقودة
Fix Banks and Exchanges Tables - Add Missing Columns
"""

import sqlite3
import os
from pathlib import Path

def fix_banks_exchanges_tables():
    """إصلاح جداول البنوك والصرافات"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إصلاح جداول البنوك والصرافات...")
        
        # 1. إضافة عمود fax لجدول البنوك
        print("📋 إضافة عمود fax لجدول البنوك...")
        try:
            cursor.execute("ALTER TABLE banks ADD COLUMN fax VARCHAR(50)")
            print("✅ تم إضافة عمود fax لجدول البنوك")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️ عمود fax موجود بالفعل في جدول البنوك")
            else:
                print(f"⚠️ خطأ في إضافة عمود fax: {e}")
        
        # 2. إضافة عمود name_en لجدول الصرافات
        print("📋 إضافة عمود name_en لجدول الصرافات...")
        try:
            cursor.execute("ALTER TABLE exchanges ADD COLUMN name_en TEXT")
            print("✅ تم إضافة عمود name_en لجدول الصرافات")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️ عمود name_en موجود بالفعل في جدول الصرافات")
            else:
                print(f"⚠️ خطأ في إضافة عمود name_en: {e}")
        
        # 3. إضافة أعمدة إضافية مفيدة للصرافات
        print("📋 إضافة أعمدة إضافية للصرافات...")
        
        additional_columns = [
            ("code", "TEXT UNIQUE"),
            ("transfer_fee", "REAL DEFAULT 0"),
            ("notes", "TEXT")
        ]
        
        for column_name, column_type in additional_columns:
            try:
                cursor.execute(f"ALTER TABLE exchanges ADD COLUMN {column_name} {column_type}")
                print(f"✅ تم إضافة عمود {column_name} لجدول الصرافات")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    print(f"ℹ️ عمود {column_name} موجود بالفعل في جدول الصرافات")
                else:
                    print(f"⚠️ خطأ في إضافة عمود {column_name}: {e}")
        
        # 4. التحقق من الجداول بعد الإصلاح
        print("\n🔍 التحقق من هيكل الجداول بعد الإصلاح...")
        
        # فحص جدول البنوك
        cursor.execute("PRAGMA table_info(banks)")
        banks_columns = cursor.fetchall()
        print(f"📊 جدول البنوك يحتوي على {len(banks_columns)} عمود:")
        banks_column_names = [col[1] for col in banks_columns]
        
        required_banks_columns = ['id', 'code', 'name', 'name_en', 'swift_code', 'country', 
                                 'city', 'address', 'phone', 'fax', 'email', 'website', 
                                 'bank_type', 'is_active', 'notes']
        
        missing_banks_columns = [col for col in required_banks_columns if col not in banks_column_names]
        if missing_banks_columns:
            print(f"⚠️ أعمدة مفقودة في جدول البنوك: {missing_banks_columns}")
        else:
            print("✅ جدول البنوك يحتوي على جميع الأعمدة المطلوبة")
        
        # فحص جدول الصرافات
        cursor.execute("PRAGMA table_info(exchanges)")
        exchanges_columns = cursor.fetchall()
        print(f"📊 جدول الصرافات يحتوي على {len(exchanges_columns)} عمود:")
        exchanges_column_names = [col[1] for col in exchanges_columns]
        
        required_exchanges_columns = ['id', 'name', 'name_en', 'code', 'category', 'license_number', 
                                    'phone', 'mobile', 'email', 'address', 'website', 'transfer_fee', 
                                    'commission_rate', 'notes', 'is_active']
        
        missing_exchanges_columns = [col for col in required_exchanges_columns if col not in exchanges_column_names]
        if missing_exchanges_columns:
            print(f"⚠️ أعمدة مفقودة في جدول الصرافات: {missing_exchanges_columns}")
        else:
            print("✅ جدول الصرافات يحتوي على جميع الأعمدة المطلوبة")
        
        # 5. إضافة بيانات تجريبية إذا لم تكن موجودة
        print("\n📝 إضافة بيانات تجريبية...")
        
        # فحص وجود بيانات في جدول البنوك
        cursor.execute("SELECT COUNT(*) FROM banks")
        banks_count = cursor.fetchone()[0]
        
        if banks_count == 0:
            print("📋 إضافة بنوك تجريبية...")
            sample_banks = [
                ('NCB001', 'البنك الأهلي السعودي', 'National Commercial Bank', 'NCBKSAJE', 'السعودية', 'الرياض', 
                 'شارع الملك عبدالعزيز', '011-4021000', '011-4021001', '<EMAIL>', 'www.alahli.com', 'تجاري', 1, 'البنك الرائد في المملكة'),
                ('RJHI001', 'مصرف الراجحي', 'Al Rajhi Bank', 'RJHISARI', 'السعودية', 'الرياض', 
                 'شارع العليا', '011-2116000', '011-2116001', '<EMAIL>', 'www.alrajhibank.com.sa', 'إسلامي', 1, 'أكبر مصرف إسلامي في العالم'),
                ('SABB001', 'البنك السعودي البريطاني', 'Saudi British Bank', 'SABBSARI', 'السعودية', 'الرياض', 
                 'شارع التحلية', '011-4405555', '011-4405556', '<EMAIL>', 'www.sabb.com', 'تجاري', 1, 'بنك رائد في الخدمات المصرفية'),
                ('RIBL001', 'بنك الرياض', 'Riyad Bank', 'RIBLSARI', 'السعودية', 'الرياض', 
                 'شارع الملك فهد', '011-4013030', '011-4013031', '<EMAIL>', 'www.riyadbank.com', 'تجاري', 1, 'بنك سعودي عريق')
            ]
            
            for bank_data in sample_banks:
                cursor.execute("""
                    INSERT INTO banks (code, name, name_en, swift_code, country, city, address, phone, fax, email, website, bank_type, is_active, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, bank_data)
            
            print(f"✅ تم إضافة {len(sample_banks)} بنك تجريبي")
        else:
            print(f"ℹ️ يوجد {banks_count} بنك في النظام")
        
        # فحص وجود بيانات في جدول الصرافات
        cursor.execute("SELECT COUNT(*) FROM exchanges")
        exchanges_count = cursor.fetchone()[0]
        
        if exchanges_count == 0:
            print("📋 إضافة صرافات تجريبية...")
            sample_exchanges = [
                ('الراجحي للصرافة', 'Al Rajhi Exchange', 'RAJEX001', 'صرافة', 'SR-EX-001', '011-2116000', '**********', 
                 '<EMAIL>', 'الرياض، شارع العليا', 'www.alrajhi-exchange.com', 15.0, 0.5, 'صرافة رائدة في المملكة', 1),
                ('الأهلي للصرافة', 'Al Ahli Exchange', 'AHLEXC001', 'صرافة', 'SR-EX-002', '011-4021000', '**********', 
                 '<EMAIL>', 'الرياض، شارع الملك عبدالعزيز', 'www.alahli-exchange.com', 12.0, 0.4, 'خدمات صرافة متميزة', 1),
                ('صرافة الإمارات', 'Emirates Exchange', 'EMIREX001', 'صرافة', 'SR-EX-003', '011-5551234', '0551234569', 
                 '<EMAIL>', 'الرياض، حي الملز', 'www.emirates-exchange.com', 10.0, 0.3, 'صرافة دولية', 1),
                ('الخليج للصرافة', 'Gulf Exchange', 'GULFEX001', 'صرافة', 'SR-EX-004', '011-5551235', '**********', 
                 '<EMAIL>', 'جدة، شارع التحلية', 'www.gulf-exchange.com', 18.0, 0.6, 'خدمات سريعة وموثوقة', 1)
            ]
            
            for exchange_data in sample_exchanges:
                cursor.execute("""
                    INSERT INTO exchanges (name, name_en, code, category, license_number, phone, mobile, email, address, website, transfer_fee, commission_rate, notes, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, exchange_data)
            
            print(f"✅ تم إضافة {len(sample_exchanges)} صرافة تجريبية")
        else:
            print(f"ℹ️ يوجد {exchanges_count} صرافة في النظام")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح جداول البنوك والصرافات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الجداول: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح جداول البنوك والصرافات...")
    print("=" * 60)
    
    success = fix_banks_exchanges_tables()
    
    if success:
        print("\n✅ تم إنجاز الإصلاح بنجاح!")
        print("📊 الآن يمكنك:")
        print("   • إضافة بنوك جديدة بدون أخطاء")
        print("   • إضافة صرافات جديدة بدون أخطاء")
        print("   • استخدام جميع الحقول المطلوبة")
    else:
        print("\n❌ فشل في إنجاز الإصلاح!")
    
    print("=" * 60)
