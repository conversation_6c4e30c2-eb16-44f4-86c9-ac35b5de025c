hyph_en_US.dic - American English hyphenation patterns for OpenOffice.org

version 2011-10-07

- remove unnecessary parts for the new Hyphen 2.8.2

version 2010-03-16

Changes

- forbid hyphenation at 1-character distances from dashes (eg. ad=d-on)
  and at the dashes (fix for OpenOffice.org 3.2)
- set correct LEFTHYPHENMIN = 2, RIGHTHYPHENMIN = 3
- handle apostrophes (forbid *o'=clock etc.)
- set COMPOUNDLEFTHYPHENMIN, COMPOUNDRIGHTHYPHENMIN values
- UTF-8 encoding
- Unicode ligature support

License

BSD-style. Unlimited copying, redistribution and modification of this file
is permitted with this copyright and license information.

See original license in this file.

Conversion and modifications by <PERSON><PERSON><PERSON><PERSON><PERSON> (nemeth at OOo).

Based on the plain TeX hyphenation table
(http://tug.ctan.org/text-archive/macros/plain/base/hyphen.tex) and
the TugBoat hyphenation exceptions log in
http://www.ctan.org/tex-archive/info/digests/tugboat/tb0hyf.tex, processed
by the hyphenex.sh script (see in the same directory).

Originally developed and distributed with the Hyphen hyphenation library,
see http://hunspell.sourceforge.net/ for the source files and the conversion
scripts.

Licenses

hyphen.tex:
% The Plain TeX hyphenation tables [NOT TO BE CHANGED IN ANY WAY!]
% Unlimited copying and redistribution of this file are permitted as long
% as this file is not modified. Modifications are permitted, but only if
% the resulting file is not named hyphen.tex.

output of hyphenex.sh:
% Hyphenation exceptions for US English, based on hyphenation exception
% log articles in TUGboat.
%
% Copyright 2007 TeX Users Group.
% You may freely use, modify and/or distribute this file.
%
% This is an automatically generated file.  Do not edit!
%
% Please contact the TUGboat editorial staff <<EMAIL>>
% for corrections and omissions.

hyph_en_US.txt:
See the previous licenses.
