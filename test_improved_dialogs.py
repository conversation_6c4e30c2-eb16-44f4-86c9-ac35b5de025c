#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_improved_dialogs():
    """اختبار النوافذ المحسنة"""
    print("🔍 اختبار النوافذ المحسنة...")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # اختبار نافذة البنك المحسنة
        print("\n🏦 اختبار نافذة البنك المحسنة...")
        from src.ui.remittances.add_new_bank_dialog import AddNewBankDialog
        
        bank_dialog = AddNewBankDialog()
        print(f"   📏 حجم النافذة: {bank_dialog.size().width()} x {bank_dialog.size().height()}")
        print(f"   📍 موقع النافذة: ({bank_dialog.x()}, {bank_dialog.y()})")
        
        # التحقق من ارتفاع الحقول
        if hasattr(bank_dialog, 'bank_name_input'):
            height = bank_dialog.bank_name_input.minimumHeight()
            print(f"   📐 ارتفاع حقل اسم البنك: {height} بكسل")
            if height >= 35:
                print("   ✅ ارتفاع الحقول محسن")
            else:
                print("   ❌ ارتفاع الحقول يحتاج تحسين")
        
        print("   ✅ نافذة البنك محسنة بنجاح")
        
        # اختبار نافذة الصراف المحسنة
        print("\n💱 اختبار نافذة الصراف المحسنة...")
        from src.ui.remittances.add_new_exchange_dialog import AddNewExchangeDialog
        
        exchange_dialog = AddNewExchangeDialog()
        print(f"   📏 حجم النافذة: {exchange_dialog.size().width()} x {exchange_dialog.size().height()}")
        print(f"   📍 موقع النافذة: ({exchange_dialog.x()}, {exchange_dialog.y()})")
        
        # التحقق من ارتفاع الحقول
        if hasattr(exchange_dialog, 'exchange_name_input'):
            height = exchange_dialog.exchange_name_input.minimumHeight()
            print(f"   📐 ارتفاع حقل اسم الصراف: {height} بكسل")
            if height >= 35:
                print("   ✅ ارتفاع الحقول محسن")
            else:
                print("   ❌ ارتفاع الحقول يحتاج تحسين")
        
        print("   ✅ نافذة الصراف محسنة بنجاح")
        
        # اختبار نافذة الفرع المحسنة
        print("\n🏢 اختبار نافذة الفرع المحسنة...")
        from src.ui.remittances.add_new_branch_dialog import AddNewBranchDialog
        
        branch_dialog = AddNewBranchDialog()
        print(f"   📏 حجم النافذة: {branch_dialog.size().width()} x {branch_dialog.size().height()}")
        print(f"   📍 موقع النافذة: ({branch_dialog.x()}, {branch_dialog.y()})")
        
        # التحقق من ارتفاع الحقول
        if hasattr(branch_dialog, 'branch_name_input'):
            height = branch_dialog.branch_name_input.minimumHeight()
            print(f"   📐 ارتفاع حقل اسم الفرع: {height} بكسل")
            if height >= 35:
                print("   ✅ ارتفاع الحقول محسن")
            else:
                print("   ❌ ارتفاع الحقول يحتاج تحسين")
        
        print("   ✅ نافذة الفرع محسنة بنجاح")
        
        # اختبار دالة التوسيط
        print("\n🎯 اختبار دالة التوسيط...")
        
        # اختبار وجود دالة التوسيط
        dialogs = [
            ("البنك", bank_dialog),
            ("الصراف", exchange_dialog),
            ("الفرع", branch_dialog)
        ]
        
        for name, dialog in dialogs:
            if hasattr(dialog, 'center_window'):
                print(f"   ✅ دالة التوسيط موجودة في نافذة {name}")
                try:
                    dialog.center_window()
                    print(f"   ✅ دالة التوسيط تعمل في نافذة {name}")
                except Exception as e:
                    print(f"   ❌ خطأ في دالة التوسيط لنافذة {name}: {e}")
            else:
                print(f"   ❌ دالة التوسيط مفقودة في نافذة {name}")
        
        print("\n" + "=" * 50)
        print("📊 ملخص التحسينات:")
        print("✅ تم تغيير ارتفاع جميع النوافذ إلى 900 بكسل")
        print("✅ تم إعادة ترتيب الحقول لتظهر النصوص واضحة")
        print("✅ تم إضافة دالة توسيط النوافذ في الشاشة")
        print("✅ تم تحسين ارتفاع الحقول لتكون أكثر وضوحاً")
        print("✅ تم تحسين تخطيط الحقول لاستغلال المساحة بشكل أفضل")
        
        print("\n🎉 جميع التحسينات تمت بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_improvements_summary():
    """عرض ملخص التحسينات"""
    print("\n📋 ملخص التحسينات المطبقة:")
    print("=" * 40)
    
    print("\n🏦 نافذة إضافة بنك جديد:")
    print("   📏 الحجم: 800 x 900 بكسل")
    print("   📐 ارتفاع الحقول: 35 بكسل")
    print("   📝 ارتفاع النصوص: 100 بكسل")
    print("   🎯 توسيط تلقائي في الشاشة")
    print("   📊 ترتيب محسن للحقول")
    
    print("\n💱 نافذة إضافة صراف جديد:")
    print("   📏 الحجم: 850 x 900 بكسل")
    print("   📐 ارتفاع الحقول: 35 بكسل")
    print("   📝 ارتفاع النصوص: 100 بكسل")
    print("   🎯 توسيط تلقائي في الشاشة")
    print("   📊 ترتيب محسن للحقول")
    
    print("\n🏢 نافذة إضافة فرع جديد:")
    print("   📏 الحجم: 800 x 900 بكسل")
    print("   📐 ارتفاع الحقول: 35 بكسل")
    print("   📝 ارتفاع النصوص: 100 بكسل")
    print("   🎯 توسيط تلقائي في الشاشة")
    print("   📊 ترتيب محسن للحقول")
    
    print("\n🎨 التحسينات العامة:")
    print("   • حقول أوسع لعرض النصوص بوضوح")
    print("   • ترتيب منطقي للحقول")
    print("   • استغلال أفضل للمساحة")
    print("   • توسيط تلقائي للنوافذ")
    print("   • تحسين تجربة المستخدم")

def show_usage_tips():
    """عرض نصائح الاستخدام"""
    print("\n💡 نصائح الاستخدام:")
    print("=" * 25)
    print("1. النوافذ تظهر الآن في وسط الشاشة تلقائياً")
    print("2. الحقول أصبحت أوسع وأوضح للقراءة")
    print("3. النصوص الطويلة لها مساحة أكبر")
    print("4. الترتيب الجديد يجعل الملء أسهل")
    print("5. يمكن تحريك النوافذ حسب الحاجة")

if __name__ == "__main__":
    success = test_improved_dialogs()
    
    if success:
        show_improvements_summary()
        show_usage_tips()
        print("\n🚀 جميع النوافذ محسنة وجاهزة للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
