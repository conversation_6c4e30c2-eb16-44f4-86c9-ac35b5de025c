#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة حوالات الموردين الشامل
Test Comprehensive Supplier Remittances Management System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from src.ui.suppliers.supplier_remittances_window import SupplierRemittancesWindow
from src.utils.arabic_support import setup_arabic_support

def test_supplier_remittances_system():
    """اختبار نظام إدارة حوالات الموردين"""
    
    print("🧪 اختبار نظام إدارة حوالات الموردين الشامل")
    print("=" * 60)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إعداد دعم اللغة العربية
        setup_arabic_support(app)
        
        print("✅ تم إنشاء التطبيق وإعداد دعم العربية")
        
        # إنشاء النافذة
        window = SupplierRemittancesWindow()
        print("✅ تم إنشاء نافذة إدارة حوالات الموردين")
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة")
        
        print("\n📋 معلومات النافذة:")
        print(f"   العنوان: {window.windowTitle()}")
        print(f"   الحجم: {window.size().width()} x {window.size().height()}")
        print(f"   الحد الأدنى للحجم: {window.minimumSize().width()} x {window.minimumSize().height()}")
        
        print("\n🎯 النافذة جاهزة للاستخدام!")
        print("   يمكنك الآن:")
        print("   📊 إدارة الحوالات:")
        print("     • إنشاء حوالة جديدة")
        print("     • إضافة عدة موردين لحوالة واحدة")
        print("     • تتبع حالة الحوالة")
        print("     • تأكيد وصول الحوالة للبنك الخارجي")
        print("     • ترحيل الحوالة لحسابات الموردين")
        print("   💰 إدارة الحسابات:")
        print("     • عرض حسابات الموردين")
        print("     • متابعة الأرصدة والمعاملات")
        print("     • إدارة حدود الائتمان")
        print("   📈 التقارير:")
        print("     • تقارير يومية وشهرية")
        print("     • تقارير حسب المورد")
        print("     • إحصائيات شاملة")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    
    print("\n🔍 اختبار تكامل قاعدة البيانات")
    print("-" * 40)
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import (Supplier, Currency, SupplierRemittance, 
                                       SupplierRemittanceItem, SupplierAccount)
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # فحص الجداول
        suppliers_count = session.query(Supplier).count()
        currencies_count = session.query(Currency).count()
        remittances_count = session.query(SupplierRemittance).count()
        remittance_items_count = session.query(SupplierRemittanceItem).count()
        accounts_count = session.query(SupplierAccount).count()
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   الموردين: {suppliers_count}")
        print(f"   العملات: {currencies_count}")
        print(f"   الحوالات: {remittances_count}")
        print(f"   تفاصيل الحوالات: {remittance_items_count}")
        print(f"   حسابات الموردين: {accounts_count}")
        
        # فحص بعض البيانات التجريبية
        if remittances_count > 0:
            sample_remittance = session.query(SupplierRemittance).first()
            print(f"   حوالة تجريبية: {sample_remittance.remittance_number}")
            print(f"   المبلغ: {sample_remittance.total_amount:,.2f} {sample_remittance.currency.code}")
            print(f"   الحالة: {sample_remittance.status}")
            print(f"   عدد الموردين: {len(sample_remittance.items)}")
            
        if accounts_count > 0:
            sample_account = session.query(SupplierAccount).first()
            print(f"   حساب تجريبي: {sample_account.supplier.name}")
            print(f"   الرصيد: {sample_account.current_balance:,.2f} {sample_account.currency.code}")
            
        session.close()
        print("✅ تكامل قاعدة البيانات يعمل بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ في تكامل قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

def test_remittance_workflow():
    """اختبار سير عمل الحوالات"""
    
    print("\n🔄 اختبار سير عمل الحوالات")
    print("-" * 40)
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import SupplierRemittance
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # فحص الحالات المختلفة للحوالات
        statuses = ["مرسلة", "في الطريق", "وصلت للبنك", "مؤكدة", "مرحلة", "ملغاة"]
        
        print("📋 حالات الحوالات المتاحة:")
        for status in statuses:
            count = session.query(SupplierRemittance).filter_by(status=status).count()
            print(f"   {status}: {count} حوالة")
        
        # فحص سير العمل
        print("\n🔄 سير العمل المطلوب:")
        print("   1. إنشاء حوالة جديدة (حالة: مرسلة)")
        print("   2. إضافة موردين متعددين للحوالة")
        print("   3. إرسال الحوالة (حالة: في الطريق)")
        print("   4. وصول للبنك الخارجي (حالة: وصلت للبنك)")
        print("   5. تأكيد الاستلام (حالة: مؤكدة)")
        print("   6. ترحيل للحسابات (حالة: مرحلة)")
        
        session.close()
        print("✅ سير العمل محدد بوضوح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سير العمل: {str(e)}")
        import traceback
        traceback.print_exc()

def display_features():
    """عرض ميزات النظام"""
    
    print("\n🌟 ميزات نظام إدارة حوالات الموردين")
    print("=" * 60)
    
    features = [
        "💸 إدارة شاملة للحوالات:",
        "   • إنشاء حوالات جديدة بأرقام تلقائية",
        "   • دعم العملات المتعددة",
        "   • معلومات البنوك المرسلة والمستقبلة",
        "   • تتبع حالة الحوالة خطوة بخطوة",
        "",
        "👥 دعم الموردين المتعددين:",
        "   • إضافة عدة موردين لحوالة واحدة",
        "   • مبالغ منفصلة لكل مورد",
        "   • معلومات مصرفية مستقلة لكل مورد",
        "   • أرقام فواتير وأغراض منفصلة",
        "",
        "🏦 إدارة متقدمة للحسابات:",
        "   • حسابات منفصلة لكل مورد وعملة",
        "   • تتبع الأرصدة والمعاملات",
        "   • حدود ائتمان قابلة للتخصيص",
        "   • إحصائيات شاملة للمشتريات والدفعات",
        "",
        "⏳ انتظار تأكيد البنك الخارجي:",
        "   • حالات واضحة لتتبع الحوالة",
        "   • تأكيد وصول الحوالة للبنك",
        "   • عدم ترحيل قبل التأكيد",
        "   • تواريخ دقيقة لكل مرحلة",
        "",
        "📊 تقارير متقدمة:",
        "   • تقارير يومية وشهرية",
        "   • تقارير حسب المورد",
        "   • إحصائيات مالية شاملة",
        "   • تصدير البيانات",
        "",
        "🔒 أمان وتحكم:",
        "   • صلاحيات للمستخدمين",
        "   • تتبع من قام بكل عملية",
        "   • حماية من التعديل غير المصرح",
        "   • نسخ احتياطية للبيانات"
    ]
    
    for feature in features:
        print(feature)

if __name__ == "__main__":
    print("🚀 بدء اختبار نظام إدارة حوالات الموردين الشامل")
    print("=" * 70)
    
    # عرض الميزات
    display_features()
    
    # اختبار تكامل قاعدة البيانات أولاً
    test_database_integration()
    
    # اختبار سير العمل
    test_remittance_workflow()
    
    # اختبار النافذة
    exit_code = test_supplier_remittances_system()
    
    print(f"\n🏁 انتهى الاختبار برمز الخروج: {exit_code}")
    sys.exit(exit_code)
