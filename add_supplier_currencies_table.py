#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة جدول ربط الموردين بالعملات
Add Supplier Currencies Relationship Table
"""

import sqlite3
import os
from pathlib import Path

def add_supplier_currencies_table():
    """إضافة جدول ربط الموردين بالعملات"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 إنشاء جدول ربط الموردين بالعملات...")
        
        # فحص وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='supplier_currencies'
        """)
        
        if cursor.fetchone():
            print("ℹ️ جدول supplier_currencies موجود بالفعل")
            conn.close()
            return True
        
        # إنشاء جدول ربط الموردين بالعملات
        create_table_sql = """
        CREATE TABLE supplier_currencies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER NOT NULL,
            currency_id INTEGER NOT NULL,
            is_primary BOOLEAN DEFAULT 0,
            exchange_rate_override REAL,
            credit_limit_in_currency REAL DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
            FOREIGN KEY (currency_id) REFERENCES currencies(id),
            UNIQUE(supplier_id, currency_id)
        )
        """
        
        cursor.execute(create_table_sql)
        
        # إنشاء فهارس
        indexes = [
            "CREATE INDEX idx_supplier_currencies_supplier ON supplier_currencies(supplier_id)",
            "CREATE INDEX idx_supplier_currencies_currency ON supplier_currencies(currency_id)",
            "CREATE INDEX idx_supplier_currencies_primary ON supplier_currencies(is_primary)",
            "CREATE INDEX idx_supplier_currencies_active ON supplier_currencies(is_active)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إنشاء جدول supplier_currencies بنجاح!")
        
        # إضافة بعض البيانات التجريبية
        print("📝 إضافة بيانات تجريبية...")
        
        # التحقق من وجود عملات
        cursor.execute("SELECT COUNT(*) FROM currencies")
        currencies_count = cursor.fetchone()[0]
        
        if currencies_count == 0:
            # إضافة عملات أساسية
            currencies_data = [
                ('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.0, 1, 1),
                ('USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 0, 1),
                ('EUR', 'يورو', 'Euro', '€', 4.1, 0, 1),
                ('AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ', 1.02, 0, 1),
                ('CNY', 'يوان صيني', 'Chinese Yuan', '¥', 0.52, 0, 1)
            ]
            
            cursor.executemany("""
                INSERT INTO currencies (code, name, name_en, symbol, exchange_rate, is_base, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, currencies_data)
            
            print("✅ تم إضافة العملات الأساسية!")
        
        # التحقق من وجود موردين
        cursor.execute("SELECT id, name FROM suppliers LIMIT 3")
        suppliers = cursor.fetchall()
        
        if suppliers:
            # التحقق من وجود عملات
            cursor.execute("SELECT id, code FROM currencies WHERE is_active = 1 LIMIT 3")
            currencies = cursor.fetchall()
            
            if currencies:
                # ربط الموردين بالعملات
                sample_data = []
                for i, supplier in enumerate(suppliers):
                    supplier_id = supplier[0]
                    
                    # ربط كل مورد بالريال السعودي كعملة أساسية
                    sar_currency = next((c for c in currencies if c[1] == 'SAR'), currencies[0])
                    sample_data.append((supplier_id, sar_currency[0], 1, None, 50000.0, 1))
                    
                    # ربط ببعض العملات الأخرى
                    for j, currency in enumerate(currencies[:2]):
                        if currency[1] != 'SAR':
                            sample_data.append((supplier_id, currency[0], 0, None, 10000.0, 1))
                
                cursor.executemany("""
                    INSERT INTO supplier_currencies 
                    (supplier_id, currency_id, is_primary, exchange_rate_override, credit_limit_in_currency, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, sample_data)
                
                print("✅ تم ربط الموردين بالعملات!")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إنشاء جدول ربط الموردين بالعملات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة جدول ربط الموردين بالعملات...")
    print("=" * 50)
    
    success = add_supplier_currencies_table()
    
    if success:
        print("\n✅ تم إنجاز المهمة بنجاح!")
    else:
        print("\n❌ فشل في إنجاز المهمة!")
    
    print("=" * 50)
