#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing imports...")
    
    # اختبار استيراد PySide6
    from PySide6.QtWidgets import QApplication
    print("✅ PySide6 imported successfully")
    
    # اختبار استيراد النوافذ
    from src.ui.remittances.new_transaction_dialog import NewTransactionDialog
    print("✅ NewTransactionDialog imported successfully")
    
    from src.ui.remittances.bulk_transfer_dialog import BulkTransferDialog
    print("✅ BulkTransferDialog imported successfully")
    
    from src.ui.remittances.account_reconciliation_dialog import AccountReconciliationDialog
    print("✅ AccountReconciliationDialog imported successfully")
    
    print("\n🎉 All imports successful! The windows are ready to use.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

if __name__ == "__main__":
    print("Running import test...")
