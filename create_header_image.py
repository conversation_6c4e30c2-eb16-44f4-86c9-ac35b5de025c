#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء صورة رأس الشركة بناءً على المعلومات المرفقة
"""

import os
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path

def create_company_header():
    """إنشاء صورة رأس الشركة"""
    print("🎨 إنشاء صورة رأس الشركة...")
    
    try:
        # أبعاد الصورة (عرض A4 تقريباً)
        width = 800
        height = 150
        
        # إنشاء صورة جديدة بخلفية بيضاء
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        # محاولة تحميل خط عربي
        try:
            # خطوط عربية محتملة في النظام
            arabic_fonts = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "arial.ttf",
                "tahoma.ttf"
            ]
            
            arabic_font_large = None
            arabic_font_small = None
            english_font_large = None
            english_font_small = None
            
            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    arabic_font_large = ImageFont.truetype(font_path, 16)
                    arabic_font_small = ImageFont.truetype(font_path, 12)
                    english_font_large = ImageFont.truetype(font_path, 16)
                    english_font_small = ImageFont.truetype(font_path, 12)
                    break
            
            # استخدام خط افتراضي إذا لم يتم العثور على خط
            if not arabic_font_large:
                arabic_font_large = ImageFont.load_default()
                arabic_font_small = ImageFont.load_default()
                english_font_large = ImageFont.load_default()
                english_font_small = ImageFont.load_default()
                
        except Exception as e:
            print(f"تحذير: لم يتم تحميل الخط: {e}")
            arabic_font_large = ImageFont.load_default()
            arabic_font_small = ImageFont.load_default()
            english_font_large = ImageFont.load_default()
            english_font_small = ImageFont.load_default()
        
        # رسم خط علوي وسفلي
        draw.line([(10, 10), (width-10, 10)], fill='black', width=2)
        draw.line([(10, height-10), (width-10, height-10)], fill='black', width=2)
        
        # النصوص الإنجليزية (يسار)
        english_company = "AL FOGEHI FOR TRADING AND CATERING LTD, CO"
        english_address = "Sana'a -Algarda'a -24st."
        english_contact = "Tel: 616109  Fax: 615909"
        
        draw.text((20, 25), english_company, fill='black', font=english_font_large)
        draw.text((20, 50), english_address, fill='black', font=english_font_small)
        draw.text((20, 70), english_contact, fill='black', font=english_font_small)
        
        # النصوص العربية (يمين)
        arabic_company = "شركة الفقيهي للتجارة والتموينات المحدودة"
        arabic_address = "صنعاء - الجردة - شارع 24"
        arabic_contact = "تلفون: 616109 فاكس: 615909"
        
        # حساب عرض النص للمحاذاة اليمنى
        try:
            arabic_company_width = draw.textlength(arabic_company, font=arabic_font_large)
            arabic_address_width = draw.textlength(arabic_address, font=arabic_font_small)
            arabic_contact_width = draw.textlength(arabic_contact, font=arabic_font_small)
        except:
            # للخطوط القديمة التي لا تدعم textlength
            arabic_company_width = len(arabic_company) * 10
            arabic_address_width = len(arabic_address) * 8
            arabic_contact_width = len(arabic_contact) * 8
        
        draw.text((width - 20 - arabic_company_width, 25), arabic_company, fill='black', font=arabic_font_large)
        draw.text((width - 20 - arabic_address_width, 50), arabic_address, fill='black', font=arabic_font_small)
        draw.text((width - 20 - arabic_contact_width, 70), arabic_contact, fill='black', font=arabic_font_small)
        
        # رسم شعار بسيط في الوسط
        center_x = width // 2
        center_y = height // 2
        
        # دائرة خارجية
        draw.ellipse([center_x-40, center_y-25, center_x+40, center_y+25], outline='black', width=3)
        
        # دائرة داخلية
        draw.ellipse([center_x-30, center_y-18, center_x+30, center_y+18], outline='black', width=2)
        
        # نص في الوسط
        draw.text((center_x-25, center_y-8), "AL FOGEHI", fill='black', font=english_font_small)
        draw.text((center_x-20, center_y+2), "TRADING", fill='black', font=english_font_small)
        
        # حفظ الصورة
        output_path = "assets/header.png"
        img.save(output_path, 'PNG', quality=95)
        
        print(f"✅ تم إنشاء صورة الرأس: {output_path}")
        print(f"📏 أبعاد الصورة: {width}x{height} بكسل")
        
        # التحقق من حجم الملف
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"📄 حجم الملف: {file_size} بايت")
            return output_path
        else:
            print("❌ فشل في حفظ الصورة")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء صورة الرأس: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_alternative_headers():
    """إنشاء عدة إصدارات من رأس الشركة"""
    print("\n🎨 إنشاء إصدارات متعددة من رأس الشركة...")
    
    headers = []
    
    # إصدار أساسي
    basic_header = create_company_header()
    if basic_header:
        headers.append(basic_header)
    
    # إصدار مضغوط
    try:
        print("\n📐 إنشاء إصدار مضغوط...")
        
        width = 800
        height = 100  # ارتفاع أقل
        
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        # خط افتراضي
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
        
        # خط علوي وسفلي
        draw.line([(5, 5), (width-5, 5)], fill='black', width=1)
        draw.line([(5, height-5), (width-5, height-5)], fill='black', width=1)
        
        # النصوص الإنجليزية (يسار)
        draw.text((10, 15), "AL FOGEHI FOR TRADING AND CATERING LTD, CO", fill='black', font=font_large)
        draw.text((10, 35), "Sana'a -Algarda'a -24st.", fill='black', font=font_small)
        draw.text((10, 50), "Tel: 616109  Fax: 615909", fill='black', font=font_small)
        
        # النصوص العربية (يمين) - مبسطة
        arabic_texts = [
            "شركة الفقيهي للتجارة والتموينات المحدودة",
            "صنعاء - الجردة - شارع 24", 
            "تلفون: 616109 فاكس: 615909"
        ]
        
        y_positions = [15, 35, 50]
        for i, text in enumerate(arabic_texts):
            text_width = len(text) * 8  # تقدير عرض النص
            draw.text((width - 10 - text_width, y_positions[i]), text, fill='black', font=font_small)
        
        # شعار مبسط في الوسط
        center_x = width // 2
        center_y = height // 2
        
        draw.ellipse([center_x-25, center_y-15, center_x+25, center_y+15], outline='black', width=2)
        draw.text((center_x-15, center_y-5), "FOGEHI", fill='black', font=font_small)
        
        # حفظ الإصدار المضغوط
        compact_path = "assets/header_compact.png"
        img.save(compact_path, 'PNG', quality=95)
        
        print(f"✅ تم إنشاء الإصدار المضغوط: {compact_path}")
        headers.append(compact_path)
        
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الإصدار المضغوط: {e}")
    
    return headers

def test_header_images():
    """اختبار صور الرأس المنشأة"""
    print("\n🧪 اختبار صور الرأس المنشأة...")
    
    test_paths = [
        "assets/header.png",
        "assets/header_compact.png"
    ]
    
    working_images = []
    
    for path in test_paths:
        if os.path.exists(path):
            try:
                with Image.open(path) as img:
                    width, height = img.size
                    file_size = os.path.getsize(path)
                    
                    print(f"✅ {path}:")
                    print(f"   📏 الأبعاد: {width}x{height}")
                    print(f"   📄 الحجم: {file_size} بايت")
                    print(f"   🎨 النمط: {img.mode}")
                    
                    working_images.append(path)
                    
            except Exception as e:
                print(f"❌ خطأ في فتح {path}: {e}")
        else:
            print(f"❌ الملف غير موجود: {path}")
    
    return working_images

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء صور رأس الشركة...\n")
    
    # إنشاء مجلد assets إذا لم يكن موجوداً
    os.makedirs("assets", exist_ok=True)
    
    # إنشاء صور الرأس
    headers = create_alternative_headers()
    
    # اختبار الصور المنشأة
    working_images = test_header_images()
    
    # عرض النتائج
    print("\n" + "="*60)
    print("📊 ملخص إنشاء صور الرأس:")
    print("="*60)
    
    if working_images:
        print(f"✅ تم إنشاء {len(working_images)} صورة بنجاح:")
        for img in working_images:
            print(f"   • {img}")
        
        print(f"\n📁 المجلد: assets/")
        print("🎯 يمكن الآن استخدام هذه الصور في مولد PDF")
        print("💡 ضع صورة الرأس الأصلية في مجلد assets/ باسم header.png")
        
    else:
        print("❌ لم يتم إنشاء أي صورة بنجاح")
        print("💡 تأكد من وجود مكتبة Pillow: pip install Pillow")
    
    return len(working_images) > 0

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 تم إنشاء صور رأس الشركة بنجاح!")
    else:
        print("\n⚠️ فشل في إنشاء صور رأس الشركة")
