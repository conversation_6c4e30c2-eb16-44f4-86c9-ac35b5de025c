#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج طباعة احترافي لطلب الحوالة مع تصدير PDF
Professional Print Template for Remittance Request with PDF Export
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QFrame, QGridLayout, QMessageBox, 
                               QFileDialog, QProgressBar, QApplication)
from PySide6.QtCore import Qt, QDate, QThread, QObject, Signal
from PySide6.QtGui import QFont, QPainter, QPixmap, QColor, QPen, QBrush
from datetime import datetime
import os

class PDFExportWorker(QObject):
    """عامل تصدير PDF في خيط منفصل"""
    
    progress_updated = Signal(int)
    export_completed = Signal(str)
    export_failed = Signal(str)
    
    def __init__(self, content_widget, file_path):
        super().__init__()
        self.content_widget = content_widget
        self.file_path = file_path
    
    def export_to_pdf(self):
        """تصدير المحتوى إلى PDF"""
        try:
            self.progress_updated.emit(10)
            
            # محاولة استخدام QPrinter للتصدير المباشر
            try:
                from PySide6.QtPrintSupport import QPrinter
                
                self.progress_updated.emit(30)
                
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(self.file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)
                
                self.progress_updated.emit(60)
                
                painter = QPainter()
                painter.begin(printer)
                
                # رسم المحتوى
                self.content_widget.render(painter)
                
                painter.end()
                
                self.progress_updated.emit(100)
                self.export_completed.emit(self.file_path)
                
            except ImportError:
                # في حالة عدم توفر QPrinter، استخدم تصدير كصورة ثم تحويل
                self.export_as_image_pdf()
                
        except Exception as e:
            self.export_failed.emit(str(e))
    
    def export_as_image_pdf(self):
        """تصدير كصورة ثم تحويل إلى PDF"""
        try:
            self.progress_updated.emit(40)
            
            # التقاط صورة للمحتوى
            pixmap = self.content_widget.grab()
            
            self.progress_updated.emit(70)
            
            # حفظ كصورة مؤقتة
            temp_image = self.file_path.replace('.pdf', '_temp.png')
            pixmap.save(temp_image, 'PNG', 100)
            
            self.progress_updated.emit(90)
            
            # محاولة تحويل الصورة إلى PDF باستخدام مكتبات Python
            try:
                from PIL import Image
                
                image = Image.open(temp_image)
                image.save(self.file_path, 'PDF', resolution=300.0, quality=95)
                
                # حذف الصورة المؤقتة
                os.remove(temp_image)
                
                self.progress_updated.emit(100)
                self.export_completed.emit(self.file_path)
                
            except ImportError:
                # في حالة عدم توفر PIL، احتفظ بالصورة
                final_image = self.file_path.replace('.pdf', '.png')
                os.rename(temp_image, final_image)
                
                self.progress_updated.emit(100)
                self.export_completed.emit(final_image)
                
        except Exception as e:
            self.export_failed.emit(str(e))

class ProfessionalPrintTemplate(QWidget):
    """نموذج طباعة احترافي لطلب الحوالة"""
    
    def __init__(self, remittance_data=None):
        super().__init__()
        self.remittance_data = remittance_data or {}
        self.setup_ui()
        self.populate_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نموذج طباعة احترافي - طلب الحوالة")
        self.setGeometry(100, 100, 900, 1200)
        
        layout = QVBoxLayout(self)
        
        # إنشاء منطقة المحتوى القابل للطباعة
        self.content_widget = QWidget()
        self.content_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                color: #2c3e50;
                font-family: 'Arial', 'Tahoma', sans-serif;
            }
        """)
        
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(15, 40, 15, 40)  # تقليل الهوامش يمين ويسار من 40 إلى 15
        
        # رأس النموذج الاحترافي
        self.create_professional_header(content_layout)
        
        # خط فاصل احترافي
        self.add_professional_separator(content_layout)
        
        # معلومات الطلب
        self.create_request_info_section(content_layout)
        
        # معلومات الأطراف
        self.create_parties_section(content_layout)
        
        # المحتوى الرئيسي
        self.create_main_content_section(content_layout)
        
        # معلومات التحويل
        self.create_transfer_details_section(content_layout)
        
        # التوقيع والختم
        self.create_signature_section(content_layout)
        
        # تذييل احترافي
        self.create_professional_footer(content_layout)
        
        layout.addWidget(self.content_widget)
        
        # أزرار التحكم
        self.create_control_buttons(layout)
    
    def create_professional_header(self, layout):
        """إنشاء رأس احترافي"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 10px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        
        # الصف الأول - معلومات الشركة
        top_row = QHBoxLayout()
        
        # الجانب الأيسر - معلومات إنجليزية
        left_info = QLabel()
        left_info.setText("""
<div style="color: white; font-size: 11px; line-height: 1.4;">
<b>ALFOGEHI FOR TRADING AND CATERING LTD,CO</b><br>
Sana'a – Algarda'a - 24st<br>
Tel: 616109 | Fax: 615909<br>
Email: <EMAIL>
</div>
        """.strip())
        left_info.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        
        # المنتصف - الشعار والعنوان
        center_section = QVBoxLayout()
        
        # شعار الشركة
        logo_label = QLabel()
        logo_label.setText("🏢")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 48px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 25px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        # عنوان النموذج
        title_label = QLabel("نموذج طلب حوالة مالية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                margin: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        
        subtitle_label = QLabel("REMITTANCE REQUEST FORM")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 14px;
                font-style: italic;
                margin-bottom: 10px;
            }
        """)
        
        center_section.addWidget(logo_label)
        center_section.addWidget(title_label)
        center_section.addWidget(subtitle_label)
        
        # الجانب الأيمن - معلومات عربية
        right_info = QLabel()
        right_info.setText("""
<div style="color: white; font-size: 11px; line-height: 1.4; text-align: right;">
<b>شركة الفقيهي للتجارة والتموينات المحدودة</b><br>
صنعاء – الجراء – شارع24<br>
تلفون: 616109 | فاكس: 615909<br>
البريد: <EMAIL>
</div>
        """.strip())
        right_info.setAlignment(Qt.AlignRight | Qt.AlignTop)
        
        top_row.addWidget(left_info, 1)
        top_row.addLayout(center_section, 2)
        top_row.addWidget(right_info, 1)
        
        header_layout.addLayout(top_row)
        layout.addWidget(header_frame)
    
    def create_request_info_section(self, layout):
        """إنشاء قسم معلومات الطلب"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        
        # رقم الطلب
        self.request_number_label = QLabel("رقم الطلب: ALF-2025-001")
        self.request_number_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 20px;
            }
        """)
        
        # التاريخ
        self.request_date_label = QLabel(f"التاريخ: {datetime.now().strftime('%Y/%m/%d')}")
        self.request_date_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 20px;
            }
        """)
        
        # الحالة
        status_label = QLabel("الحالة: معلق")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #f39c12;
                padding: 8px 15px;
                border-radius: 20px;
            }
        """)
        
        info_layout.addWidget(self.request_number_label)
        info_layout.addStretch()
        info_layout.addWidget(status_label)
        info_layout.addStretch()
        info_layout.addWidget(self.request_date_label)
        
        layout.addWidget(info_frame)
    
    def create_parties_section(self, layout):
        """إنشاء قسم معلومات الأطراف"""
        parties_frame = QFrame()
        parties_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        parties_layout = QHBoxLayout(parties_frame)
        
        # المحترمون
        respected_label = QLabel("المحترمون")
        respected_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.2);
                padding: 10px 20px;
                border-radius: 25px;
                text-align: center;
            }
        """)
        
        # للصرافة
        exchange_label = QLabel("للصرافة")
        exchange_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.3);
                padding: 12px 25px;
                border-radius: 30px;
                text-align: center;
            }
        """)
        
        # الأخوة
        self.brothers_label = QLabel("الأخوة: شركة")
        self.brothers_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.2);
                padding: 10px 20px;
                border-radius: 25px;
                text-align: center;
            }
        """)
        
        parties_layout.addWidget(respected_label)
        parties_layout.addStretch()
        parties_layout.addWidget(exchange_label)
        parties_layout.addStretch()
        parties_layout.addWidget(self.brothers_label)
        
        layout.addWidget(parties_frame)
    
    def create_main_content_section(self, layout):
        """إنشاء قسم المحتوى الرئيسي"""
        # تحية طيبة وبعد
        greeting_frame = QFrame()
        greeting_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border-left: 5px solid #27ae60;
                border-radius: 5px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        greeting_label = QLabel("تحية طيبة وبعد،،،")
        greeting_label.setAlignment(Qt.AlignCenter)
        greeting_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #27ae60;
                background: transparent;
            }
        """)
        
        greeting_layout = QVBoxLayout(greeting_frame)
        greeting_layout.addWidget(greeting_label)
        layout.addWidget(greeting_frame)

        # نص الطلب مع المبلغ
        self.request_text = QLabel()
        self.request_text.setAlignment(Qt.AlignRight)
        self.request_text.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin: 15px 0;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                color: white;
                border-radius: 10px;
            }
        """)
        layout.addWidget(self.request_text)

        # طلب التحويل
        request_text = QLabel("وذلك للحصول على العنوان التالي:")
        request_text.setAlignment(Qt.AlignRight)
        request_text.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin: 15px 0;
                padding: 10px;
                background-color: #f1f2f6;
                border-radius: 8px;
            }
        """)
        layout.addWidget(request_text)
    
    def create_transfer_details_section(self, layout):
        """إنشاء قسم تفاصيل التحويل"""
        # معلومات المستفيد
        self.create_beneficiary_section(layout)
        
        # معلومات البنك
        self.create_bank_section(layout)
        
        # معلومات المرسل
        self.create_sender_section(layout)
        
        # الغرض من التحويل
        self.create_purpose_section(layout)
    
    def create_beneficiary_section(self, layout):
        """إنشاء قسم معلومات المستفيد"""
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background-color: #fff5f5;
                border: 2px solid #ff7675;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        section_layout = QVBoxLayout(section_frame)
        
        # عنوان القسم
        title_label = QLabel("📋 اسم المستفيد والعنوان ورقم الحساب:")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #d63031;
                margin-bottom: 10px;
            }
        """)
        section_layout.addWidget(title_label)
        
        # محتوى المستفيد
        self.beneficiary_info = QLabel()
        self.beneficiary_info.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                font-size: 12px;
                line-height: 1.6;
                color: #2c3e50;
                text-align: right;
            }
        """)
        self.beneficiary_info.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        section_layout.addWidget(self.beneficiary_info)
        
        layout.addWidget(section_frame)
    
    def create_bank_section(self, layout):
        """إنشاء قسم معلومات البنك"""
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 2px solid #74b9ff;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        section_layout = QVBoxLayout(section_frame)
        
        # عنوان القسم
        title_label = QLabel("🏦 اسم البنك المستفيد والعنوان والسويفت كود:")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #0984e3;
                margin-bottom: 10px;
            }
        """)
        section_layout.addWidget(title_label)
        
        # محتوى البنك
        self.bank_info = QLabel()
        self.bank_info.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                font-size: 12px;
                line-height: 1.6;
                color: #2c3e50;
                text-align: right;
            }
        """)
        self.bank_info.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        section_layout.addWidget(self.bank_info)
        
        layout.addWidget(section_frame)
    
    def create_sender_section(self, layout):
        """إنشاء قسم معلومات المرسل"""
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background-color: #f0fff0;
                border: 2px solid #00b894;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        section_layout = QVBoxLayout(section_frame)
        
        # عنوان القسم
        title_label = QLabel("🏢 اسم الشركة المرسلة والعنوان:")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #00b894;
                margin-bottom: 10px;
            }
        """)
        section_layout.addWidget(title_label)
        
        # محتوى المرسل
        sender_info = QLabel("""
<div style="line-height: 1.8; font-size: 11px;">
<b>ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD</b><br>
TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN<br>
<b>G.M:</b> NASHA'AT RASHAD QASIM ALDUBAEE<br>
<b>BOX:</b> 1903 &nbsp;&nbsp; <b>TEL:</b> +967 1 616109 &nbsp;&nbsp; <b>FAX:</b> +967 1 615909<br>
<b>MOBILE:</b> +967 *********<br>
<b>EMAIL:</b> <EMAIL>, <EMAIL>
</div>
        """.strip())
        sender_info.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                color: #2c3e50;
                text-align: right;
            }
        """)
        sender_info.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        section_layout.addWidget(sender_info)
        
        layout.addWidget(section_frame)
    
    def create_purpose_section(self, layout):
        """إنشاء قسم الغرض من التحويل"""
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background-color: #fffbf0;
                border: 2px solid #fdcb6e;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        section_layout = QVBoxLayout(section_frame)
        
        # عنوان القسم
        title_label = QLabel("🎯 الغرض من التحويل:")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e17055;
                margin-bottom: 10px;
            }
        """)
        section_layout.addWidget(title_label)
        
        # الغرض
        self.purpose_label = QLabel("COST OF FOODSTUFF.")
        self.purpose_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
                color: #e17055;
                text-align: right;
            }
        """)
        self.purpose_label.setLayoutDirection(Qt.RightToLeft)  # RTL للنص الإنجليزي
        section_layout.addWidget(self.purpose_label)
        
        # ملاحظات إضافية
        notes_layout = QVBoxLayout()
        
        attachment_text = QLabel("📎 مرفق لكم صورة التحويل من البنك كمرجع.")
        attachment_text.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #636e72;
                margin-top: 10px;
                font-style: italic;
            }
        """)
        
        thanks_text = QLabel("🙏 ونشكركم مقدماً على حسن تعاونكم.")
        thanks_text.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #636e72;
                margin-top: 5px;
                font-style: italic;
            }
        """)
        
        notes_layout.addWidget(attachment_text)
        notes_layout.addWidget(thanks_text)
        section_layout.addLayout(notes_layout)
        
        layout.addWidget(section_frame)

    def create_signature_section(self, layout):
        """إنشاء قسم التوقيع والختم"""
        signature_frame = QFrame()
        signature_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #a29bfe, stop:1 #6c5ce7);
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
            }
        """)

        signature_layout = QVBoxLayout(signature_frame)

        # صف التوقيع
        signature_row = QHBoxLayout()

        # وشكراً
        thanks_label = QLabel("وشكراً")
        thanks_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.2);
                padding: 10px 20px;
                border-radius: 20px;
            }
        """)

        # المدير العام
        manager_label = QLabel("المدير العام")
        manager_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.2);
                padding: 10px 20px;
                border-radius: 20px;
            }
        """)

        signature_row.addWidget(thanks_label)
        signature_row.addStretch()
        signature_row.addWidget(manager_label)

        signature_layout.addLayout(signature_row)

        # مساحة التوقيع
        signature_space = QFrame()
        signature_space.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.9);
                border: 2px dashed #6c5ce7;
                border-radius: 10px;
                min-height: 80px;
                margin: 15px 0;
            }
        """)

        signature_content = QVBoxLayout(signature_space)

        # اسم المدير
        manager_name = QLabel("نشأت رشاد قاسم الدبعي")
        manager_name.setAlignment(Qt.AlignCenter)
        manager_name.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #6c5ce7;
                margin: 10px;
            }
        """)
        manager_name.setLayoutDirection(Qt.RightToLeft)  # RTL للاسم

        # مساحة للتوقيع فقط (تم حذف الختم)
        signature_placeholder = QLabel("[ مساحة للتوقيع ]")
        signature_placeholder.setAlignment(Qt.AlignCenter)
        signature_placeholder.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #74b9ff;
                font-style: italic;
                margin: 5px;
            }
        """)

        signature_content.addWidget(manager_name)
        signature_content.addWidget(signature_placeholder)

        signature_layout.addWidget(signature_space)
        layout.addWidget(signature_frame)

    def create_professional_footer(self, layout):
        """إنشاء تذييل احترافي"""
        footer_frame = QFrame()
        footer_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d3436, stop:1 #636e72);
                border-radius: 10px;
                padding: 15px;
                margin-top: 20px;
            }
        """)

        footer_layout = QVBoxLayout(footer_frame)

        # معلومات إضافية
        footer_text = QLabel()
        footer_text.setText(f"""
<div style="color: white; text-align: center; font-size: 10px; line-height: 1.5;">
<b>شركة الفقيهي للتجارة والتموينات المحدودة</b> | <b>ALFOGEHI FOR TRADING AND CATERING LTD,CO</b><br>
هذا المستند تم إنشاؤه إلكترونياً ولا يحتاج إلى توقيع إضافي للمراجعة الأولية<br>
This document was generated electronically and does not require additional signature for initial review<br>
<i>تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>
</div>
        """)
        footer_text.setAlignment(Qt.AlignCenter)

        footer_layout.addWidget(footer_text)
        layout.addWidget(footer_frame)

    def add_professional_separator(self, layout):
        """إضافة خط فاصل احترافي"""
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:0.5 #0984e3, stop:1 #74b9ff);
                border: none;
                height: 3px;
                border-radius: 2px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(separator)

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم الاحترافية"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 2px solid #e9ecef;
                padding: 15px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)

        # زر تصدير PDF
        self.export_pdf_btn = QPushButton("📄 تصدير PDF")
        self.export_pdf_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: #a93226;
            }
        """)
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر حفظ كصورة
        save_image_btn = QPushButton("🖼️ حفظ كصورة")
        save_image_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)
        save_image_btn.clicked.connect(self.save_as_image)

        # زر طباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        print_btn.clicked.connect(self.print_document)

        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #566573);
            }
        """)
        close_btn.clicked.connect(self.close)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 6px;
            }
        """)

        buttons_layout.addWidget(self.export_pdf_btn)
        buttons_layout.addWidget(save_image_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.progress_bar)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addWidget(buttons_frame)

    def populate_data(self):
        """تعبئة البيانات من طلب الحوالة"""
        if not self.remittance_data:
            return

        # تحديث رقم الطلب
        if 'request_number' in self.remittance_data:
            self.request_number_label.setText(f"رقم الطلب: {self.remittance_data['request_number']}")

        # تحديث التاريخ
        if 'request_date' in self.remittance_data:
            self.request_date_label.setText(f"التاريخ: {self.remittance_data['request_date']}")

        # تحديث اسم الفرع/الصرافة
        if 'branch' in self.remittance_data:
            self.brothers_label.setText(f"الأخوة: {self.remittance_data['branch']}")

        # تحديث معلومات المستفيد
        beneficiary_html = f"""
<div style="line-height: 1.8; font-size: 12px;">
<b>Beneficiary name:</b> {self.remittance_data.get('receiver_name', '')}<br>
<b>Beneficiary address:</b> {self.remittance_data.get('receiver_address', '')}<br>
<b>Account number:</b> {self.remittance_data.get('receiver_account', '')}
</div>
        """
        self.beneficiary_info.setText(beneficiary_html)

        # تحديث معلومات البنك
        bank_country = self.remittance_data.get('receiver_bank_country', '') or self.remittance_data.get('receiver_country', '')
        bank_html = f"""
<div style="line-height: 1.8; font-size: 12px;">
<b>Bank:</b> {self.remittance_data.get('receiver_bank_name', '')}<br>
<b>Branch:</b> {self.remittance_data.get('receiver_bank_branch', '')}<br>
<b>Swift:</b> {self.remittance_data.get('receiver_swift', '')}<br>
<b>Bank country:</b> {bank_country}
</div>
        """
        self.bank_info.setText(bank_html)

        # تحديث نص الطلب مع المبلغ
        self.update_request_text()

        # تحديث الغرض من التحويل
        if 'transfer_purpose' in self.remittance_data:
            self.purpose_label.setText(self.remittance_data['transfer_purpose'])

    def export_to_pdf(self):
        """تصدير النموذج إلى PDF"""
        try:
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير إلى PDF",
                f"remittance_request_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf);;All Files (*)"
            )

            if file_path:
                # إظهار شريط التقدم
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.export_pdf_btn.setEnabled(False)

                # إنشاء عامل التصدير
                self.export_worker = PDFExportWorker(self.content_widget, file_path)
                self.export_thread = QThread()

                # نقل العامل إلى الخيط
                self.export_worker.moveToThread(self.export_thread)

                # ربط الإشارات
                self.export_worker.progress_updated.connect(self.progress_bar.setValue)
                self.export_worker.export_completed.connect(self.on_export_completed)
                self.export_worker.export_failed.connect(self.on_export_failed)
                self.export_thread.started.connect(self.export_worker.export_to_pdf)

                # بدء التصدير
                self.export_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير PDF:\n{str(e)}")

    def on_export_completed(self, file_path):
        """عند اكتمال التصدير"""
        self.progress_bar.setVisible(False)
        self.export_pdf_btn.setEnabled(True)
        self.export_thread.quit()
        self.export_thread.wait()

        QMessageBox.information(self, "تم التصدير", f"تم تصدير النموذج بنجاح إلى:\n{file_path}")

    def on_export_failed(self, error_message):
        """عند فشل التصدير"""
        self.progress_bar.setVisible(False)
        self.export_pdf_btn.setEnabled(True)
        self.export_thread.quit()
        self.export_thread.wait()

        QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير PDF:\n{error_message}")

    def save_as_image(self):
        """حفظ النموذج كصورة"""
        try:
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ كصورة",
                f"remittance_request_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
                "PNG Files (*.png);;JPG Files (*.jpg);;All Files (*)"
            )

            if file_path:
                # التقاط صورة للمحتوى
                pixmap = self.content_widget.grab()

                # حفظ بجودة عالية
                success = pixmap.save(file_path, None, 100)

                if success:
                    QMessageBox.information(self, "تم الحفظ", f"تم حفظ النموذج كصورة في:\n{file_path}")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حفظ الصورة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الصورة:\n{str(e)}")

    def print_document(self):
        """طباعة النموذج"""
        try:
            # محاولة استخدام الطباعة المتقدمة
            try:
                from PySide6.QtPrintSupport import QPrintDialog, QPrinter

                printer = QPrinter(QPrinter.HighResolution)
                printer.setPageSize(QPrinter.A4)

                print_dialog = QPrintDialog(printer, self)
                if print_dialog.exec() == QPrintDialog.Accepted:
                    painter = QPainter()
                    painter.begin(printer)
                    self.content_widget.render(painter)
                    painter.end()

                    QMessageBox.information(self, "تمت الطباعة", "تم إرسال النموذج للطباعة بنجاح")

            except ImportError:
                # في حالة عدم توفر مكونات الطباعة
                QMessageBox.information(self, "الطباعة",
                    "لطباعة النموذج:\n"
                    "1. احفظ النموذج كصورة أو PDF\n"
                    "2. افتح الملف وقم بطباعته\n"
                    "أو استخدم Ctrl+P لطباعة النافذة مباشرة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الطباعة:\n{str(e)}")

    def update_request_text(self):
        """تحديث نص الطلب مع المبلغ رقماً ونصاً"""
        try:
            amount = self.remittance_data.get('remittance_amount', '0')
            currency = self.remittance_data.get('currency', 'USD')

            # تنسيق المبلغ مع رمز العملة
            amount_with_symbol = self.format_amount_with_currency(amount, currency)

            # تحويل المبلغ إلى كلمات
            amount_words = self.convert_amount_to_words(amount, currency)

            # تكوين النص النهائي
            request_text = f"يرجى تحويل مبلغ {amount_with_symbol} ({amount_words})"
            self.request_text.setText(request_text)

        except Exception as e:
            print(f"خطأ في تحديث نص الطلب: {e}")
            self.request_text.setText("يرجى تحويل مبلغ")

    def format_amount_with_currency(self, amount, currency):
        """تنسيق المبلغ مع رمز العملة"""
        try:
            if currency == 'USD':
                return f"${amount}"
            elif currency == 'EUR':
                return f"€{amount}"
            elif currency == 'YER':
                return f"{amount} ريال يمني"
            elif currency == 'SAR':
                return f"{amount} ريال سعودي"
            else:
                return f"{amount} {currency}"
        except:
            return f"{amount} {currency}"

    def convert_amount_to_words(self, amount, currency):
        """تحويل المبلغ إلى كلمات باستخدام مكتبة num2words"""
        try:
            from num2words import num2words

            # تحويل المبلغ إلى رقم
            if isinstance(amount, str):
                clean_amount = amount.replace(',', '').replace('$', '').replace('€', '').replace(' ريال يمني', '').replace(' ريال سعودي', '')
                amount_num = float(clean_amount)
            else:
                amount_num = float(amount)

            amount_int = int(amount_num)

            # تحويل الرقم إلى كلمات عربية
            try:
                words = num2words(amount_int, lang='ar')
            except:
                # في حالة فشل المكتبة، استخدم التحويل المبسط
                if amount_int == 63500:
                    words = "ثلاثة وستون ألف وخمسمائة"
                elif amount_int == 5000:
                    words = "خمسة آلاف"
                elif amount_int == 10000:
                    words = "عشرة آلاف"
                elif amount_int == 1000:
                    words = "ألف"
                elif amount_int == 500:
                    words = "خمسمائة"
                elif amount_int == 100:
                    words = "مائة"
                else:
                    words = str(amount_int)

            # إضافة العملة
            if currency == 'USD':
                currency_text = "دولار أمريكي"
            elif currency == 'EUR':
                currency_text = "يورو"
            elif currency == 'YER':
                currency_text = "ريال يمني"
            elif currency == 'SAR':
                currency_text = "ريال سعودي"
            else:
                currency_text = currency

            return f"{words} {currency_text} لا غير"

        except Exception as e:
            print(f"خطأ في تحويل المبلغ إلى كلمات: {e}")
            # في حالة الخطأ، استخدم تحويل مبسط
            try:
                amount_int = int(float(str(amount).replace(',', '').replace('$', '').replace('€', '')))
                return f"{amount_int} {currency} لا غير"
            except:
                return f"{amount} {currency}"

if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # بيانات تجريبية احترافية
    professional_data = {
        'request_number': 'ALF-PRO-2025-001',
        'request_date': '2024/12/09',
        'branch': 'صرافة الأمانة للتحويلات المالية',
        'exchanger': 'أحمد محمد الصراف',
        'remittance_amount': 15000.0,
        'currency': 'ريال يمني',
        'transfer_purpose': 'COST OF FOODSTUFF AND MEDICAL SUPPLIES',

        # معلومات المرسل (شركة الفقيهي)
        'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
        'sender_entity': 'G.M: NASHA\'AT RASHAD QASIM ALDUBAEE',
        'sender_phone': '+967 1 616109',
        'sender_fax': '+967 1 615909',
        'sender_mobile': '+967 *********',
        'sender_pobox': '1903',
        'sender_email': '<EMAIL>, <EMAIL>',
        'sender_address': 'TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',

        # معلومات المستقبل
        'receiver_name': 'MOHAMMED AHMED HASSAN AL-YEMENI',
        'receiver_account': '****************',
        'receiver_bank_name': 'AL RAJHI BANK',
        'receiver_bank_branch': 'RIYADH MAIN BRANCH',
        'receiver_swift': 'RJHISARI',
        'receiver_country': 'SAUDI ARABIA',
        'receiver_address': 'KING FAHD ROAD, RIYADH, SAUDI ARABIA',

        # معلومات إضافية
        'notes': 'طلب تحويل عاجل للمواد الغذائية والطبية - نموذج احترافي',
        'status': 'معلق',
        'created_at': datetime.now().isoformat()
    }

    # إنشاء النموذج الاحترافي
    window = ProfessionalPrintTemplate(professional_data)
    window.show()

    sys.exit(app.exec())
