# متطلبات بناء ملف التثبيت التنفيذي
# Build Requirements for Executable Installer

# Core Build Tools - أدوات البناء الأساسية
pyinstaller>=5.13.0
auto-py-to-exe>=2.40.0      # GUI for PyInstaller (Optional)

# All Runtime Requirements - جميع متطلبات وقت التشغيل
-r requirements.txt

# Additional Build Dependencies - تبعيات إضافية للبناء
setuptools>=68.0.0
wheel>=0.41.0
pip>=23.0.0

# Windows Specific - خاص بويندوز
pywin32>=306; sys_platform == "win32"
pywin32-ctypes>=0.2.2; sys_platform == "win32"

# Icon Creation - إنشاء الأيقونات
Pillow>=10.0.0

# Archive Creation - إنشاء الأرشيف
patool>=1.12.0

# Optional: Advanced Packaging - تغليف متقدم (اختياري)
# nuitka>=1.8.0             # Alternative to PyInstaller
# briefcase>=0.3.16         # Cross-platform packaging
