#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("Testing dialog creation...")

try:
    # Test imports first
    print("1. Testing imports...")
    from PySide6.QtWidgets import QApplication, QDialog
    from PySide6.QtCore import Qt
    print("   ✅ PySide6 imported")
    
    from src.ui.remittances.bulk_transfer_dialog import BulkTransferDialog
    print("   ✅ BulkTransferDialog imported")
    
    # Test app creation
    print("2. Creating QApplication...")
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    print("   ✅ QApplication created")
    
    # Test dialog creation
    print("3. Creating BulkTransferDialog...")
    dialog = BulkTransferDialog()
    print("   ✅ BulkTransferDialog created successfully!")
    
    print("\n🎉 All tests passed! The dialog is working correctly.")
    
except Exception as e:
    print(f"\n❌ Error occurred: {e}")
    import traceback
    print("\nFull traceback:")
    traceback.print_exc()

print("\nTest completed.")
