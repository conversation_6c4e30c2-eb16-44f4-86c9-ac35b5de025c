#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث جدول الشركات في قاعدة البيانات
"""

import sqlite3
from pathlib import Path

def update_company_table():
    """تحديث جدول الشركات لإضافة الأعمدة الجديدة"""
    print("🔄 تحديث جدول الشركات...")
    
    try:
        db_path = Path("data/proshipment.db")
        if not db_path.exists():
            print("❌ قاعدة البيانات غير موجودة")
            return False
            
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # إضافة الأعمدة الجديدة
        try:
            cursor.execute("ALTER TABLE companies ADD COLUMN address_en TEXT")
            print("✅ تم إضافة عمود address_en")
        except sqlite3.OperationalError:
            print("ℹ️ عمود address_en موجود بالفعل")
        
        try:
            cursor.execute("ALTER TABLE companies ADD COLUMN fax TEXT")
            print("✅ تم إضافة عمود fax")
        except sqlite3.OperationalError:
            print("ℹ️ عمود fax موجود بالفعل")
        
        # التحقق من وجود الأعمدة
        cursor.execute("PRAGMA table_info(companies)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print("\n📋 أعمدة جدول الشركات:")
        for column in columns:
            print(f"   • {column}")
        
        # التحقق من وجود الأعمدة الجديدة
        new_columns = ['address_en', 'fax']
        all_columns_present = all(column in columns for column in new_columns)
        
        if all_columns_present:
            print("\n✅ جميع الأعمدة الجديدة متوفرة في الجدول")
        else:
            missing_columns = [col for col in new_columns if col not in columns]
            print(f"\n❌ الأعمدة التالية غير موجودة: {', '.join(missing_columns)}")
            return False
        
        # تحديث البيانات الحالية
        cursor.execute("SELECT id FROM companies")
        company_ids = [row[0] for row in cursor.fetchall()]
        
        if company_ids:
            print(f"\n🔄 تحديث بيانات {len(company_ids)} شركة...")
            
            for company_id in company_ids:
                # الحصول على بيانات الشركة الحالية
                cursor.execute("SELECT name, address FROM companies WHERE id = ?", (company_id,))
                company_data = cursor.fetchone()
                
                if company_data:
                    name, address = company_data
                    
                    # إنشاء عنوان إنجليزي افتراضي
                    address_en = f"English address for {name}" if name else "Company Address"
                    
                    # إنشاء رقم فاكس افتراضي
                    cursor.execute("SELECT phone FROM companies WHERE id = ?", (company_id,))
                    phone_data = cursor.fetchone()
                    phone = phone_data[0] if phone_data and phone_data[0] else ""
                    
                    # استخدام رقم الهاتف كأساس لرقم الفاكس
                    fax = phone.replace("7", "8") if phone else "+966-11-1234568"
                    
                    # تحديث البيانات
                    cursor.execute("""
                        UPDATE companies 
                        SET address_en = ?, fax = ?
                        WHERE id = ?
                    """, (address_en, fax, company_id))
                    
                    print(f"   ✅ تم تحديث الشركة {company_id}")
            
            conn.commit()
            print("✅ تم تحديث جميع الشركات بنجاح")
        else:
            print("ℹ️ لا توجد شركات لتحديثها")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول الشركات: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = update_company_table()
    print("\n" + "="*60)
    if success:
        print("🎉 تم تحديث جدول الشركات بنجاح!")
        print("✅ تم إضافة عمود العنوان بالإنجليزية")
        print("✅ تم إضافة عمود رقم الفاكس")
        print("✅ تم تحديث البيانات الحالية")
    else:
        print("⚠️ حدثت مشكلة في تحديث جدول الشركات")
        print("يرجى مراجعة الأخطاء أعلاه")
