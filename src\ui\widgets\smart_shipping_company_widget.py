"""
واجهة ذكية لإدخال شركة الشحن مع الاقتراحات التلقائية والتصحيح
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, 
                               QCompleter, QPushButton, QLabel, QFrame, 
                               QListWidget, QListWidgetItem, QMessageBox,
                               QToolTip, QApplication)
from PySide6.QtCore import Qt, Signal, QTimer, QStringListModel
from PySide6.QtGui import QFont, QPalette, QIcon
import sys
import os

# إضافة مسار المشروع لاستيراد الوحدات
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from src.utils.shipping_company_validator import ShippingCompanyValidator
except ImportError:
    try:
        from utils.shipping_company_validator import ShippingCompanyValidator
    except ImportError:
        # إنشاء فئة بديلة بسيطة في حالة فشل الاستيراد
        class ShippingCompanyValidator:
            def __init__(self):
                self.global_shipping_companies = {}

            def validate_company_name(self, name):
                return {"is_valid": True, "suggestions": [], "company_info": None}

            def get_company_suggestions(self, partial_name):
                return []

class SmartShippingCompanyWidget(QWidget):
    """واجهة ذكية لإدخال شركة الشحن"""
    
    # إشارات
    company_selected = Signal(dict)  # عند اختيار شركة
    company_validated = Signal(dict)  # عند التحقق من الشركة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.validator = ShippingCompanyValidator()
        self.current_company_info = None
        self.suggestion_timer = QTimer()
        self.suggestion_timer.setSingleShot(True)
        self.suggestion_timer.timeout.connect(self.show_suggestions)
        self.auto_fill_enabled = False  # التعبئة التلقائية معطلة افتراضياً

        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # الحقل الرئيسي مع الأزرار
        input_layout = QHBoxLayout()
        
        # حقل الإدخال
        self.company_input = QLineEdit()
        self.company_input.setPlaceholderText("اكتب اسم شركة الشحن...")
        self.company_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                outline: none;
            }
            QLineEdit[validation-status="valid"] {
                border-color: #10b981;
                background-color: #f0fdf4;
            }
            QLineEdit[validation-status="invalid"] {
                border-color: #ef4444;
                background-color: #fef2f2;
            }
            QLineEdit[validation-status="suggestion"] {
                border-color: #f59e0b;
                background-color: #fffbeb;
            }
        """)
        
        # زر التحقق
        self.validate_button = QPushButton("✓")
        self.validate_button.setFixedSize(35, 35)
        self.validate_button.setToolTip("التحقق من صحة اسم الشركة")
        self.validate_button.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
            QPushButton:pressed {
                background-color: #1d4ed8;
            }
        """)
        
        # زر الاقتراحات
        self.suggestions_button = QPushButton("💡")
        self.suggestions_button.setFixedSize(35, 35)
        self.suggestions_button.setToolTip("عرض الاقتراحات")

        # زر تفعيل/تعطيل التعبئة التلقائية
        self.auto_fill_toggle_button = QPushButton("🔒 تعطيل")
        self.auto_fill_toggle_button.setFixedSize(80, 35)  # حجم أكبر
        self.auto_fill_toggle_button.setToolTip("تفعيل/تعطيل التعبئة التلقائية")
        self.auto_fill_toggle_button.setCheckable(True)
        self.auto_fill_toggle_button.setChecked(False)  # معطل افتراضياً
        self.suggestions_button.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d97706;
            }
            QPushButton:pressed {
                background-color: #b45309;
            }
        """)

        # تصميم زر التفعيل/التعطيل
        self.auto_fill_toggle_button.setStyleSheet("""
            QPushButton {
                background-color: #dc2626;
                color: white;
                border: 2px solid #dc2626;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11px;
                padding: 4px 8px;
            }
            QPushButton:hover {
                background-color: #b91c1c;
                border-color: #b91c1c;
            }
            QPushButton:pressed {
                background-color: #991b1b;
                border-color: #991b1b;
            }
            QPushButton:checked {
                background-color: #16a34a;
                border-color: #16a34a;
            }
            QPushButton:checked:hover {
                background-color: #15803d;
                border-color: #15803d;
            }
        """)

        input_layout.addWidget(self.company_input)
        input_layout.addWidget(self.validate_button)
        input_layout.addWidget(self.suggestions_button)
        input_layout.addWidget(self.auto_fill_toggle_button)
        
        layout.addLayout(input_layout)
        
        # منطقة المعلومات والحالة
        self.info_frame = QFrame()
        self.info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        self.info_frame.setVisible(False)
        
        info_layout = QVBoxLayout(self.info_frame)
        info_layout.setSpacing(4)
        info_layout.setContentsMargins(8, 8, 8, 8)
        
        # تسمية الحالة
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #374151;
            }
        """)
        
        # معلومات الشركة
        self.company_info_label = QLabel()
        self.company_info_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6b7280;
                margin-top: 2px;
            }
        """)
        self.company_info_label.setWordWrap(True)
        
        info_layout.addWidget(self.status_label)
        info_layout.addWidget(self.company_info_label)
        
        layout.addWidget(self.info_frame)
        
        # قائمة الاقتراحات
        self.suggestions_list = QListWidget()
        self.suggestions_list.setMaximumHeight(120)
        self.suggestions_list.setVisible(False)
        self.suggestions_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: white;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #f3f4f6;
            }
            QListWidget::item:hover {
                background-color: #f3f4f6;
            }
            QListWidget::item:selected {
                background-color: #3b82f6;
                color: white;
            }
        """)
        
        layout.addWidget(self.suggestions_list)
        
        # إعداد الإكمال التلقائي
        self.setup_autocomplete()
    
    def setup_autocomplete(self):
        """إعداد الإكمال التلقائي"""
        # جمع جميع أسماء الشركات للإكمال التلقائي
        company_names = []
        for company_data in self.validator.get_all_companies().values():
            company_names.append(company_data["arabic_name"])
            company_names.append(company_data["full_name"])
            company_names.extend(company_data["aliases"])
        
        # إزالة التكرارات وترتيب القائمة
        company_names = sorted(list(set(company_names)))
        
        # إنشاء الإكمال التلقائي
        completer = QCompleter(company_names)
        completer.setCaseSensitivity(Qt.CaseInsensitive)
        completer.setFilterMode(Qt.MatchContains)
        self.company_input.setCompleter(completer)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.company_input.textChanged.connect(self.on_text_changed)
        self.company_input.editingFinished.connect(self.validate_input)
        self.validate_button.clicked.connect(self.validate_input)
        self.suggestions_button.clicked.connect(self.toggle_suggestions)
        self.auto_fill_toggle_button.clicked.connect(self.toggle_auto_fill)
        self.suggestions_list.itemClicked.connect(self.on_suggestion_selected)
    
    def on_text_changed(self, text):
        """عند تغيير النص"""
        # إخفاء المعلومات والاقتراحات عند التغيير
        self.info_frame.setVisible(False)
        self.suggestions_list.setVisible(False)
        
        # إعادة تعيين حالة التحقق
        self.company_input.setProperty("validation-status", "")
        self.company_input.setStyleSheet(self.company_input.styleSheet())
        
        # تأخير عرض الاقتراحات (فقط إذا كانت التعبئة التلقائية مفعلة)
        if text.strip() and self.auto_fill_enabled:
            self.suggestion_timer.start(500)  # انتظار 500ms قبل عرض الاقتراحات
    
    def validate_input(self):
        """التحقق من صحة الإدخال"""
        input_text = self.company_input.text().strip()

        if not input_text:
            self.clear_validation()
            return

        # التحقق من تفعيل التعبئة التلقائية
        if not self.auto_fill_enabled:
            # التعبئة التلقائية معطلة - لا نقوم بالتحقق التلقائي
            self.status_label.setText("🔒 التعبئة التلقائية معطلة - استخدم زر التحقق للتفعيل")
            self.status_label.setStyleSheet("color: #6b7280; font-style: italic;")
            self.company_info_label.setText("")
            return

        # التحقق من صحة الاسم
        validation_result = self.validator.validate_and_correct(input_text)

        self.display_validation_result(validation_result)
        self.company_validated.emit(validation_result)
    
    def display_validation_result(self, result):
        """عرض نتيجة التحقق"""
        if result["is_valid"]:
            # اسم صحيح
            self.company_input.setProperty("validation-status", "valid")
            self.status_label.setText("✅ شركة شحن معتمدة")
            self.status_label.setStyleSheet("color: #10b981; font-weight: bold;")
            
            # عرض معلومات الشركة
            company_info = result["company_info"]
            info_text = f"🏢 {company_info['full_name']}\n"
            info_text += f"🌍 {company_info['country']} | 📦 {company_info['type']}"
            self.company_info_label.setText(info_text)
            
            self.current_company_info = company_info
            self.company_selected.emit(company_info)
            
            # إخفاء الاقتراحات
            self.suggestions_list.setVisible(False)
            
        elif result["suggestions"]:
            # يوجد اقتراحات
            self.company_input.setProperty("validation-status", "suggestion")
            self.status_label.setText("💡 هل تقصد إحدى هذه الشركات؟")
            self.status_label.setStyleSheet("color: #f59e0b; font-weight: bold;")
            
            best_suggestion = result["suggestions"][0]
            confidence_percent = int(best_suggestion["similarity"] * 100)
            self.company_info_label.setText(f"أفضل تطابق: {best_suggestion['suggested_name']} ({confidence_percent}%)")
            
            # عرض الاقتراحات
            self.populate_suggestions(result["suggestions"])
            
        else:
            # لا يوجد تطابق
            self.company_input.setProperty("validation-status", "invalid")
            self.status_label.setText("❌ شركة غير معروفة")
            self.status_label.setStyleSheet("color: #ef4444; font-weight: bold;")
            self.company_info_label.setText("لم يتم العثور على هذه الشركة في قاعدة البيانات")
            
            self.suggestions_list.setVisible(False)
        
        # تحديث الأنماط وعرض المعلومات
        self.company_input.setStyleSheet(self.company_input.styleSheet())
        self.info_frame.setVisible(True)
    
    def populate_suggestions(self, suggestions):
        """ملء قائمة الاقتراحات"""
        self.suggestions_list.clear()
        
        for suggestion in suggestions:
            company_data = suggestion["company_data"]
            confidence = int(suggestion["similarity"] * 100)
            
            item_text = f"🏢 {suggestion['suggested_name']}"
            item_text += f"\n   📍 {company_data['country']} | 📦 {company_data['type']} | 🎯 {confidence}%"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, suggestion)
            self.suggestions_list.addItem(item)
        
        self.suggestions_list.setVisible(True)
    
    def show_suggestions(self):
        """عرض الاقتراحات التلقائية"""
        # التحقق من تفعيل التعبئة التلقائية
        if not self.auto_fill_enabled:
            return

        input_text = self.company_input.text().strip()
        if input_text:
            suggestions = self.validator.suggest_corrections(input_text, max_suggestions=5)
            if suggestions:
                self.populate_suggestions(suggestions)
    
    def toggle_suggestions(self):
        """تبديل عرض الاقتراحات"""
        if self.suggestions_list.isVisible():
            self.suggestions_list.setVisible(False)
        else:
            self.show_suggestions()

    def toggle_auto_fill(self):
        """تبديل حالة التعبئة التلقائية"""
        new_state = self.auto_fill_toggle_button.isChecked()

        # تحديث نص وأيقونة الزر أولاً
        if new_state:
            self.auto_fill_toggle_button.setText("🔓 تفعيل")
            self.auto_fill_toggle_button.setToolTip("تعطيل التعبئة التلقائية")
        else:
            self.auto_fill_toggle_button.setText("🔒 تعطيل")
            self.auto_fill_toggle_button.setToolTip("تفعيل التعبئة التلقائية")

        # ثم تحديث الحالة الداخلية (بدون تحديث الزر مرة أخرى)
        self._set_auto_fill_enabled_internal(new_state)

    def _set_auto_fill_enabled_internal(self, enabled: bool):
        """تحديث حالة التعبئة التلقائية داخلياً بدون تحديث الزر"""
        self.auto_fill_enabled = enabled

        # تحديث مظهر الأزرار الأخرى حسب الحالة
        if enabled:
            self.validate_button.setToolTip("التحقق من صحة شركة الشحن مع التعبئة التلقائية")
            self.suggestions_button.setToolTip("عرض اقتراحات شركات الشحن")
            self.status_label.setText("🔓 التعبئة التلقائية مفعلة")
            self.status_label.setStyleSheet("color: #10b981; font-weight: bold;")
        else:
            self.validate_button.setToolTip("التحقق اليدوي فقط - التعبئة التلقائية معطلة")
            self.suggestions_button.setToolTip("الاقتراحات معطلة - التعبئة التلقائية معطلة")
            self.status_label.setText("🔒 التعبئة التلقائية معطلة")
            self.status_label.setStyleSheet("color: #6b7280; font-style: italic;")

    def on_suggestion_selected(self, item):
        """عند اختيار اقتراح"""
        suggestion = item.data(Qt.UserRole)
        if suggestion:
            # تعيين الاسم المقترح
            self.company_input.setText(suggestion["suggested_name"])
            
            # إخفاء الاقتراحات
            self.suggestions_list.setVisible(False)
            
            # التحقق من الإدخال الجديد
            self.validate_input()
    
    def clear_validation(self):
        """مسح حالة التحقق"""
        self.company_input.setProperty("validation-status", "")
        self.company_input.setStyleSheet(self.company_input.styleSheet())
        self.info_frame.setVisible(False)
        self.suggestions_list.setVisible(False)
        self.current_company_info = None
    
    def get_company_name(self):
        """الحصول على اسم الشركة"""
        return self.company_input.text().strip()
    
    def set_company_name(self, name, auto_validate=True):
        """تعيين اسم الشركة

        Args:
            name: اسم الشركة
            auto_validate: تفعيل التحقق التلقائي (افتراضي: True)
        """
        self.company_input.setText(name)
        if auto_validate:
            self.validate_input()
    
    def get_company_info(self):
        """الحصول على معلومات الشركة الحالية"""
        return self.current_company_info

    def set_auto_fill_enabled(self, enabled):
        """تفعيل أو تعطيل التعبئة التلقائية

        Args:
            enabled: True لتفعيل التعبئة التلقائية، False لتعطيلها
        """
        # تحديث الحالة الداخلية
        self._set_auto_fill_enabled_internal(enabled)

        # تحديث زر التفعيل
        if enabled:
            self.auto_fill_toggle_button.setText("🔓 تفعيل")
            self.auto_fill_toggle_button.setChecked(True)
        else:
            self.auto_fill_toggle_button.setText("🔒 تعطيل")
            self.auto_fill_toggle_button.setChecked(False)

    def is_auto_fill_enabled(self):
        """التحقق من حالة التعبئة التلقائية"""
        return self.auto_fill_enabled
