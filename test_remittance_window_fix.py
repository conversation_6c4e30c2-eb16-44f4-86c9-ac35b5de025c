#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح نافذة طلب الحوالة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_remittance_window_creation():
    """اختبار إنشاء نافذة طلب الحوالة"""
    print("🪟 اختبار إنشاء نافذة طلب الحوالة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ تم إنشاء تطبيق Qt")
        
        # استيراد نافذة طلب الحوالة
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        print("✅ تم استيراد نافذة طلب الحوالة")
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        print("✅ تم إنشاء نافذة طلب الحوالة بنجاح")
        
        # التحقق من وجود الحقول الجديدة
        if hasattr(window, 'receiver_country_input'):
            print("✅ حقل البلد (نصي) موجود")
        else:
            print("❌ حقل البلد (نصي) غير موجود")
            return False
            
        if hasattr(window, 'receiver_bank_country_input'):
            print("✅ حقل بلد البنك موجود")
        else:
            print("❌ حقل بلد البنك غير موجود")
            return False
        
        # التحقق من عدم وجود الحقل القديم
        if not hasattr(window, 'receiver_country_combo'):
            print("✅ الحقل القديم (receiver_country_combo) تم إزالته")
        else:
            print("⚠️ الحقل القديم (receiver_country_combo) ما زال موجود")
        
        # اختبار عرض النافذة لفترة قصيرة
        window.show()
        print("✅ تم عرض النافذة")
        
        # إغلاق النافذة بعد ثانية واحدة
        QTimer.singleShot(1000, window.close)
        QTimer.singleShot(1500, app.quit)
        
        # تشغيل التطبيق لفترة قصيرة
        app.processEvents()
        
        print("✅ النافذة تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_fields():
    """اختبار حقول النموذج"""
    print("\n📝 اختبار حقول النموذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # اختبار تعيين قيم في الحقول الجديدة
        test_country = "SAUDI ARABIA"
        test_bank_country = "UNITED ARAB EMIRATES"
        
        window.receiver_country_input.setText(test_country)
        window.receiver_bank_country_input.setText(test_bank_country)
        
        # التحقق من القيم
        if window.receiver_country_input.text() == test_country:
            print("✅ حقل البلد يعمل بشكل صحيح")
        else:
            print("❌ حقل البلد لا يعمل")
            return False
            
        if window.receiver_bank_country_input.text() == test_bank_country:
            print("✅ حقل بلد البنك يعمل بشكل صحيح")
        else:
            print("❌ حقل بلد البنك لا يعمل")
            return False
        
        # اختبار مسح الحقول
        window.receiver_country_input.clear()
        window.receiver_bank_country_input.clear()
        
        if not window.receiver_country_input.text() and not window.receiver_bank_country_input.text():
            print("✅ مسح الحقول يعمل بشكل صحيح")
        else:
            print("❌ مسح الحقول لا يعمل")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقول: {e}")
        return False

def test_data_collection():
    """اختبار جمع البيانات"""
    print("\n📊 اختبار جمع البيانات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # تعيين بيانات اختبار
        window.receiver_name_input.setText("TEST COMPANY")
        window.receiver_country_input.setText("SAUDI ARABIA")
        window.receiver_bank_country_input.setText("UAE")
        window.receiver_address_input.setText("TEST ADDRESS")
        
        # محاولة جمع البيانات
        try:
            data = window.collect_new_request_data()
            
            if 'receiver_country' in data and data['receiver_country'] == "SAUDI ARABIA":
                print("✅ جمع بيانات البلد يعمل")
            else:
                print("❌ جمع بيانات البلد لا يعمل")
                return False
                
            if 'receiver_bank_country' in data and data['receiver_bank_country'] == "UAE":
                print("✅ جمع بيانات بلد البنك يعمل")
            else:
                print("❌ جمع بيانات بلد البنك لا يعمل")
                return False
            
            print("✅ جمع البيانات يعمل بشكل صحيح")
            
        except Exception as e:
            print(f"❌ خطأ في جمع البيانات: {e}")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار جمع البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح نافذة طلب الحوالة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_remittance_window_creation())
    results.append(test_form_fields())
    results.append(test_data_collection())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار إصلاح نافذة طلب الحوالة:")
    print("="*60)
    
    test_names = [
        "إنشاء النافذة",
        "حقول النموذج",
        "جمع البيانات"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تم إصلاح نافذة طلب الحوالة بنجاح!")
        print("✅ النافذة تعمل بدون أخطاء")
        print("✅ الحقول الجديدة تعمل بشكل صحيح")
        print("✅ جمع البيانات يعمل بشكل صحيح")
        print("✅ تم إزالة المراجع القديمة")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم الاختبارات نجحت!")
        print("قد تحتاج بعض التحسينات الإضافية")
    else:
        print("\n⚠️ عدة اختبارات فشلت. يرجى مراجعة:")
        print("- مراجع الحقول القديمة")
        print("- تهيئة الحقول الجديدة")
        print("- دوال جمع البيانات")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
