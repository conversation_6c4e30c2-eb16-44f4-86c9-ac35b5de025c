#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة حفظ طلب الحوالة
Diagnose Remittance Request Save Issue
"""

import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_structure():
    """فحص هيكل قاعدة البيانات"""
    
    print("🔍 فحص هيكل قاعدة البيانات...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        if not db_path.exists():
            print("   ❌ ملف قاعدة البيانات غير موجود")
            return False
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='remittance_requests'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("   ❌ جدول remittance_requests غير موجود")
            conn.close()
            return False
        
        print("   ✅ جدول remittance_requests موجود")
        
        # فحص هيكل الجدول
        cursor.execute("PRAGMA table_info(remittance_requests)")
        columns = cursor.fetchall()
        
        print(f"   📊 عدد الأعمدة: {len(columns)}")
        
        # الحقول المطلوبة للحفظ الجديد
        required_fields = [
            'request_number', 'request_date', 'branch', 'exchanger', 
            'remittance_amount', 'currency', 'transfer_purpose',
            'sender_name', 'sender_entity', 'sender_phone', 'sender_fax',
            'sender_mobile', 'sender_pobox', 'sender_email', 'sender_address',
            'receiver_name', 'receiver_account', 'receiver_bank_name', 
            'receiver_bank_branch', 'receiver_swift', 'receiver_country', 
            'receiver_address', 'notes', 'status', 'created_at'
        ]
        
        existing_columns = [col[1] for col in columns]
        missing_fields = []
        
        print("\n   📋 فحص الحقول المطلوبة:")
        for field in required_fields:
            if field in existing_columns:
                print(f"      ✅ {field}")
            else:
                print(f"      ❌ {field} - مفقود")
                missing_fields.append(field)
        
        conn.close()
        
        if missing_fields:
            print(f"\n   ⚠️ حقول مفقودة: {len(missing_fields)}")
            return False
        else:
            print("\n   ✅ جميع الحقول المطلوبة موجودة")
            return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_sample_insert():
    """اختبار إدراج عينة"""
    
    print("\n🧪 اختبار إدراج عينة...")
    print("=" * 60)
    
    try:
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # بيانات تجريبية
        test_data = {
            'request_number': f'TEST{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'request_date': '2024-12-09',
            'branch': 'الفرع الرئيسي',
            'branch_id': None,
            'exchanger': 'أحمد الصراف',
            'exchanger_id': None,
            'remittance_amount': 1000.0,
            'currency': 'ريال يمني',
            'currency_code': 'YER',
            'transfer_purpose': 'اختبار النظام',
            'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
            'sender_entity': 'G.M: NASHA\'AT RASHAD QASIM ALDUBAEE',
            'sender_phone': '+967 1 616109',
            'sender_fax': '+967 1 615909',
            'sender_mobile': '+967 *********',
            'sender_pobox': '1903',
            'sender_email': '<EMAIL>, <EMAIL>',
            'sender_address': 'TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',
            'receiver_name': 'محمد أحمد علي',
            'receiver_account': '**********',
            'receiver_bank_name': 'البنك الأهلي',
            'receiver_bank_branch': 'فرع الرياض',
            'receiver_swift': 'NBYSAA01',
            'receiver_country': 'السعودية',
            'receiver_address': 'الرياض، السعودية',
            'notes': 'طلب تجريبي للاختبار',
            'sms_notification': True,
            'email_notification': True,
            'auto_create_remittance': False,
            'status': 'معلق',
            'created_at': datetime.now().isoformat()
        }
        
        print("   📝 محاولة الإدراج الكامل...")
        
        try:
            cursor.execute("""
                INSERT INTO remittance_requests (
                    request_number, request_date, branch, branch_id, exchanger, exchanger_id,
                    remittance_amount, currency, currency_code, transfer_purpose,
                    sender_name, sender_entity, sender_phone, sender_fax, sender_mobile, sender_pobox,
                    sender_email, sender_address,
                    receiver_name, receiver_account, receiver_bank_name, receiver_bank_branch, 
                    receiver_swift, receiver_country, receiver_address,
                    notes, sms_notification, email_notification, auto_create_remittance, 
                    status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_data['request_number'], test_data['request_date'], test_data['branch'], test_data['branch_id'],
                test_data['exchanger'], test_data['exchanger_id'], test_data['remittance_amount'], 
                test_data['currency'], test_data['currency_code'], test_data['transfer_purpose'],
                test_data['sender_name'], test_data['sender_entity'], test_data['sender_phone'], 
                test_data['sender_fax'], test_data['sender_mobile'], test_data['sender_pobox'],
                test_data['sender_email'], test_data['sender_address'],
                test_data['receiver_name'], test_data['receiver_account'], test_data['receiver_bank_name'], 
                test_data['receiver_bank_branch'], test_data['receiver_swift'], test_data['receiver_country'], 
                test_data['receiver_address'], test_data['notes'], test_data['sms_notification'], 
                test_data['email_notification'], test_data['auto_create_remittance'], 
                test_data['status'], test_data['created_at']
            ))
            
            request_id = cursor.lastrowid
            conn.commit()
            
            print(f"   ✅ نجح الإدراج! معرف الطلب: {request_id}")
            
            # حذف البيانات التجريبية
            cursor.execute("DELETE FROM remittance_requests WHERE id = ?", (request_id,))
            conn.commit()
            print("   ✅ تم حذف البيانات التجريبية")
            
            conn.close()
            return True
            
        except sqlite3.Error as e:
            print(f"   ❌ فشل الإدراج: {e}")
            conn.close()
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الإدراج: {e}")
        return False

def display_fix_recommendations():
    """عرض توصيات الإصلاح"""
    
    print("\n" + "=" * 70)
    print("🔧 توصيات الإصلاح")
    print("=" * 70)
    
    print("\n✅ الحلول المطبقة:")
    print("   1. تحديث دالة save_request_to_database:")
    print("      - استخدام الحقول الجديدة")
    print("      - إزالة المراجع للحقول القديمة")
    print("      - إضافة معالجة أفضل للأخطاء")
    
    print("\n   2. تحديث استعلام الإدراج:")
    print("      - 31 حقل جديد")
    print("      - تطابق مع هيكل قاعدة البيانات")
    print("      - دعم البيانات الافتراضية")
    
    print("\n🎯 النتائج المتوقعة:")
    print("   ✅ حفظ ناجح للطلبات")
    print("   ✅ عدم ظهور رسائل خطأ")
    print("   ✅ حفظ جميع البيانات الجديدة")
    print("   ✅ تجربة مستخدم محسنة")

if __name__ == "__main__":
    print("🚀 بدء تشخيص مشكلة حفظ طلب الحوالة...")
    print("=" * 80)
    
    # فحص هيكل قاعدة البيانات
    db_structure_ok = test_database_structure()
    
    # اختبار إدراج عينة
    insert_test_ok = test_sample_insert()
    
    # عرض توصيات الإصلاح
    display_fix_recommendations()
    
    # النتيجة النهائية
    if db_structure_ok and insert_test_ok:
        print("\n🏆 تم إصلاح مشكلة الحفظ بنجاح!")
        print("✅ هيكل قاعدة البيانات صحيح")
        print("✅ اختبار الإدراج نجح")
        print("✅ دالة الحفظ محدثة")
        print("✅ شاشة طلب الحوالة جاهزة للاستخدام")
        
        print("\n🎉 يمكنك الآن حفظ طلبات الحوالة بدون مشاكل!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إلى حل")
        if not db_structure_ok:
            print("   - مشكلة في هيكل قاعدة البيانات")
        if not insert_test_ok:
            print("   - مشكلة في عملية الإدراج")
    
    print("=" * 80)
