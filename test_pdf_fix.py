#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح خطأ colors في مولد PDF
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pdf_imports():
    """اختبار استيراد مكتبات PDF"""
    print("📚 اختبار استيراد مكتبات PDF...")
    
    try:
        from reportlab.lib import colors
        print("   ✅ تم استيراد colors بنجاح")
        
        from reportlab.lib.colors import black, blue, red, grey
        print("   ✅ تم استيراد الألوان الأساسية بنجاح")
        
        from reportlab.pdfgen import canvas
        print("   ✅ تم استيراد canvas بنجاح")
        
        from reportlab.lib.pagesizes import A4
        print("   ✅ تم استيراد A4 بنجاح")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
        return False

def test_pdf_generator_creation():
    """اختبار إنشاء مولد PDF"""
    print("\n🏭 اختبار إنشاء مولد PDF...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        generator = RemittancePDFGenerator()
        print("   ✅ تم إنشاء مولد PDF بنجاح")
        
        # التحقق من الخصائص الأساسية
        if hasattr(generator, 'page_width') and hasattr(generator, 'page_height'):
            print("   ✅ خصائص الصفحة موجودة")
        else:
            print("   ❌ خصائص الصفحة مفقودة")
            return False
            
        if hasattr(generator, 'arabic_font'):
            print("   ✅ خاصية الخط العربي موجودة")
        else:
            print("   ❌ خاصية الخط العربي مفقودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء مولد PDF: {e}")
        return False

def test_colors_usage():
    """اختبار استخدام الألوان"""
    print("\n🎨 اختبار استخدام الألوان...")
    
    try:
        from reportlab.lib import colors
        
        # اختبار الألوان المستخدمة
        test_colors = [
            ('black', colors.black),
            ('grey', colors.grey),
            ('blue', colors.blue),
            ('red', colors.red)
        ]
        
        for color_name, color_obj in test_colors:
            if color_obj:
                print(f"   ✅ اللون {color_name} متاح")
            else:
                print(f"   ❌ اللون {color_name} غير متاح")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الألوان: {e}")
        return False

def test_signature_section_method():
    """اختبار دالة قسم التوقيع"""
    print("\n✍️ اختبار دالة قسم التوقيع...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        generator = RemittancePDFGenerator()
        
        # التحقق من وجود الدالة
        if hasattr(generator, 'add_signature_section'):
            print("   ✅ دالة add_signature_section موجودة")
            
            # فحص الكود للتأكد من استخدام colors
            import inspect
            source = inspect.getsource(generator.add_signature_section)
            
            if "colors.grey" in source:
                print("   ✅ استخدام colors.grey صحيح")
            else:
                print("   ❌ استخدام colors.grey غير موجود")
                return False
                
            if "colors.black" in source:
                print("   ✅ استخدام colors.black صحيح")
            else:
                print("   ❌ استخدام colors.black غير موجود")
                return False
                
            if "setStrokeColor" in source and "setFillColor" in source:
                print("   ✅ دوال الألوان مستخدمة بشكل صحيح")
            else:
                print("   ❌ دوال الألوان غير مستخدمة بشكل صحيح")
                return False
        else:
            print("   ❌ دالة add_signature_section غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار دالة التوقيع: {e}")
        return False

def test_pdf_generation():
    """اختبار إنشاء PDF تجريبي"""
    print("\n📄 اختبار إنشاء PDF تجريبي...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        generator = RemittancePDFGenerator()
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'TEST-PDF-001',
            'request_date': '2024/12/09',
            'remittance_amount': '5000',
            'currency': 'USD',
            'receiver_name': 'MOHAMMED AHMED ALI',
            'receiver_bank_name': 'TEST BANK',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        # مسار مؤقت للاختبار
        test_output = "test_pdf_output.pdf"
        
        try:
            # محاولة إنشاء PDF
            result = generator.generate_pdf(test_data, test_output)
            
            if result and os.path.exists(test_output):
                print("   ✅ تم إنشاء PDF بنجاح")
                
                # حذف الملف التجريبي
                os.remove(test_output)
                print("   ✅ تم حذف الملف التجريبي")
                
                return True
            else:
                print("   ❌ فشل في إنشاء PDF")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء PDF: {e}")
            
            # حذف الملف في حالة الخطأ
            if os.path.exists(test_output):
                os.remove(test_output)
            
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إنشاء PDF: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح خطأ colors في مولد PDF...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_pdf_imports())
    results.append(test_pdf_generator_creation())
    results.append(test_colors_usage())
    results.append(test_signature_section_method())
    results.append(test_pdf_generation())
    
    # عرض النتائج النهائية
    print("\n" + "="*80)
    print("🎯 ملخص اختبار إصلاح خطأ colors:")
    print("="*80)
    
    test_names = [
        "استيراد مكتبات PDF",
        "إنشاء مولد PDF",
        "استخدام الألوان",
        "دالة قسم التوقيع",
        "إنشاء PDF تجريبي"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تم إصلاح خطأ colors بنجاح!")
        print("✅ جميع مكتبات PDF تعمل بشكل صحيح")
        print("✅ مولد PDF يعمل بدون أخطاء")
        print("✅ الألوان متاحة ومستخدمة بشكل صحيح")
        print("✅ دالة التوقيع تعمل بشكل مثالي")
        print("✅ إنشاء PDF يعمل بنجاح")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   📚 إضافة استيراد colors الكامل")
        print("   🎨 إضافة استيراد grey المطلوب")
        print("   ✍️ تحديث دالة التوقيع")
        print("   📄 اختبار شامل لإنشاء PDF")
        
    elif successful_tests >= len(results) * 0.8:
        print("\n✅ معظم المشاكل تم إصلاحها!")
        print("بعض الاختبارات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة مشاكل لم يتم إصلاحها. يرجى مراجعة:")
        print("- استيراد مكتبات ReportLab")
        print("- استخدام الألوان")
        print("- دالة قسم التوقيع")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
