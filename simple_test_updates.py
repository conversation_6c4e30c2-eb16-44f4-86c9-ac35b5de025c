#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للتحديثات
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication
    
    # إنشاء تطبيق Qt
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
    
    print("🚀 بدء الاختبار المبسط...")
    
    # إنشاء النافذة
    window = RemittanceRequestWindow()
    
    # اختبار حقل الفرع
    print("\n🏢 اختبار حقل الفرع:")
    if hasattr(window, 'branch_combo'):
        branch_count = window.branch_combo.count()
        print(f"   عدد الفروع: {branch_count}")
        
        for i in range(branch_count):
            branch_text = window.branch_combo.itemText(i)
            print(f"   {i+1}. {branch_text}")
        
        # التحقق من الفروع الجديدة
        expected_branches = ["الادارة العامة صنعاء", "فرع عدن", "فرع المكلا"]
        found_new_branches = []
        
        for i in range(branch_count):
            branch_text = window.branch_combo.itemText(i)
            for expected in expected_branches:
                if expected in branch_text:
                    found_new_branches.append(expected)
        
        if len(found_new_branches) >= 3:
            print("   ✅ الفروع الجديدة موجودة")
        else:
            print(f"   ⚠️ وُجد {len(found_new_branches)} من 3 فروع")
    else:
        print("   ❌ حقل الفرع غير موجود")
    
    # اختبار الغرض من التحويل
    print("\n📝 اختبار الغرض من التحويل:")
    if hasattr(window, 'transfer_purpose_input'):
        default_value = window.transfer_purpose_input.text()
        print(f"   القيمة الافتراضية: '{default_value}'")
        
        if default_value == "COST OF FOODSTUFF":
            print("   ✅ القيمة الافتراضية صحيحة")
        else:
            print("   ❌ القيمة الافتراضية غير صحيحة")
    else:
        print("   ❌ حقل الغرض من التحويل غير موجود")
    
    # اختبار جمع البيانات
    print("\n📊 اختبار جمع البيانات:")
    try:
        data = window.collect_new_request_data()
        
        if 'branch' in data:
            print(f"   الفرع: '{data['branch']}'")
        
        if 'transfer_purpose' in data:
            print(f"   الغرض من التحويل: '{data['transfer_purpose']}'")
        
        print("   ✅ جمع البيانات يعمل")
        
    except Exception as e:
        print(f"   ❌ خطأ في جمع البيانات: {e}")
    
    window.close()
    print("\n🎉 الاختبار اكتمل بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
