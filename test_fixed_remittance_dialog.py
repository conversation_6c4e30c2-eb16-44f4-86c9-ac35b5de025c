#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_remittance_dialog():
    """اختبار نافذة إنشاء الحوالة بعد الإصلاح"""
    print("🔍 اختبار نافذة إنشاء الحوالة بعد الإصلاح...")
    print("=" * 60)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة
        dialog = NewRemittanceDialog()
        print("✅ تم إنشاء نافذة الحوالة بنجاح")
        
        # اختبار الحقول الجديدة
        print("\n🔄 اختبار الحقول الجديدة...")
        
        # التحقق من وجود الحقول الأساسية
        required_fields = [
            ('remittance_date', 'تاريخ الحوالة'),
            ('remittance_number_input', 'رقم الحوالة'),
            ('transfer_entity_combo', 'جهة التحويل'),
            ('transfer_entity_name_combo', 'اسم جهة التحويل'),
            ('reference_number_input', 'الرقم المرجعي'),
            ('currency_combo', 'العملة'),
            ('amount_input', 'المبلغ'),
            ('status_combo', 'حالة الحوالة'),
            ('notes_input', 'الملاحظات'),
            ('suppliers_table', 'جدول الموردين')
        ]
        
        missing_fields = []
        for field_name, field_desc in required_fields:
            if hasattr(dialog, field_name):
                print(f"   ✅ {field_desc} موجود")
            else:
                print(f"   ❌ {field_desc} مفقود")
                missing_fields.append(field_desc)
        
        # اختبار الأزرار الجديدة
        print("\n🔄 اختبار الأزرار الجديدة...")
        
        required_buttons = [
            ('save_draft_btn', 'زر حفظ كمسودة'),
            ('confirm_btn', 'زر تأكيد الحوالة'),
            ('transfer_to_suppliers_btn', 'زر ترحيل للموردين')
        ]
        
        missing_buttons = []
        for button_name, button_desc in required_buttons:
            if hasattr(dialog, button_name):
                print(f"   ✅ {button_desc} موجود")
                if button_name == 'transfer_to_suppliers_btn':
                    if not getattr(dialog, button_name).isEnabled():
                        print(f"   ✅ {button_desc} معطل بشكل صحيح")
                    else:
                        print(f"   ⚠️ {button_desc} يجب أن يكون معطل في البداية")
            else:
                print(f"   ❌ {button_desc} مفقود")
                missing_buttons.append(button_desc)
        
        # اختبار الدوال الجديدة
        print("\n🔄 اختبار الدوال الجديدة...")
        
        required_functions = [
            ('update_transfer_entity_name', 'دالة تحديث جهة التحويل'),
            ('add_supplier_to_remittance', 'دالة إضافة مورد'),
            ('remove_supplier_from_remittance', 'دالة حذف مورد'),
            ('setup_suppliers_table', 'دالة إعداد جدول الموردين'),
            ('save_as_draft', 'دالة حفظ كمسودة'),
            ('confirm_remittance', 'دالة تأكيد الحوالة'),
            ('transfer_to_suppliers', 'دالة ترحيل للموردين'),
            ('get_suppliers_data_for_save', 'دالة جمع بيانات الموردين'),
            ('get_total_distributed_amount', 'دالة حساب إجمالي المبلغ الموزع')
        ]
        
        missing_functions = []
        for func_name, func_desc in required_functions:
            if hasattr(dialog, func_name):
                print(f"   ✅ {func_desc} موجودة")
            else:
                print(f"   ❌ {func_desc} مفقودة")
                missing_functions.append(func_desc)
        
        # اختبار جدول الموردين
        print("\n🔄 اختبار جدول الموردين...")
        if hasattr(dialog, 'suppliers_table'):
            table = dialog.suppliers_table
            expected_columns = ["المورد", "المبلغ", "العملة", "الوصف", "الحالة"]
            actual_columns = []
            for i in range(table.columnCount()):
                header_item = table.horizontalHeaderItem(i)
                if header_item:
                    actual_columns.append(header_item.text())
            
            if actual_columns == expected_columns:
                print("   ✅ أعمدة جدول الموردين صحيحة")
            else:
                print(f"   ❌ أعمدة جدول الموردين غير صحيحة: {actual_columns}")
        
        # تقرير النتائج
        print("\n📊 تقرير النتائج:")
        print(f"   • الحقول المفقودة: {len(missing_fields)}")
        print(f"   • الأزرار المفقودة: {len(missing_buttons)}")
        print(f"   • الدوال المفقودة: {len(missing_functions)}")
        
        if not missing_fields and not missing_buttons and not missing_functions:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ النافذة جاهزة للاستخدام مع جميع الميزات الجديدة.")
            return True
        else:
            print("\n⚠️ يوجد بعض العناصر المفقودة:")
            if missing_fields:
                print(f"   - حقول مفقودة: {', '.join(missing_fields)}")
            if missing_buttons:
                print(f"   - أزرار مفقودة: {', '.join(missing_buttons)}")
            if missing_functions:
                print(f"   - دوال مفقودة: {', '.join(missing_functions)}")
            return False
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📝 كيفية استخدام النافذة المحسنة:")
    print("=" * 40)
    print("1. فتح النافذة:")
    print("   - من القائمة الرئيسية: إدارة الحوالات → إنشاء حوالة جديدة")
    print()
    print("2. ملء البيانات الأساسية:")
    print("   - اختر جهة التحويل (بنك أو صراف)")
    print("   - اختر اسم الجهة (سيتم تحديث القائمة تلقائياً)")
    print("   - أدخل المبلغ والعملة")
    print("   - أدخل الرقم المرجعي")
    print()
    print("3. إضافة الموردين:")
    print("   - انقر 'إضافة مورد'")
    print("   - اختر المورد والمبلغ")
    print("   - تأكد من تطابق إجمالي المبلغ الموزع")
    print()
    print("4. حفظ وتأكيد:")
    print("   - احفظ كمسودة أولاً")
    print("   - راجع البيانات")
    print("   - اضغط 'تأكيد الحوالة'")
    print("   - اضغط 'ترحيل للموردين' لإتمام العملية")

if __name__ == "__main__":
    success = test_fixed_remittance_dialog()
    
    if success:
        show_usage_instructions()
        print("\n🚀 النافذة المحسنة جاهزة للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
