#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_exit_buttons():
    """اختبار أزرار الخروج في النوافذ"""
    print("🔍 اختبار أزرار الخروج في النوافذ...")
    print("=" * 50)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        print("✅ تم إنشاء وعرض النافذة الرئيسية")
        
        # اختبار نافذة الحوالات
        print("\n🔄 اختبار نافذة الحوالات...")
        try:
            main_window.open_remittances_window()
            if hasattr(main_window, 'remittances_window') and main_window.remittances_window:
                # البحث عن زر الخروج في شريط الأدوات
                toolbar = main_window.remittances_window.findChild(QApplication.QToolBar)
                if toolbar:
                    actions = toolbar.actions()
                    exit_action = None
                    for action in actions:
                        if "خروج" in action.text():
                            exit_action = action
                            break
                    
                    if exit_action:
                        print("   ✅ تم العثور على زر الخروج في نافذة الحوالات")
                    else:
                        print("   ❌ لم يتم العثور على زر الخروج في نافذة الحوالات")
                else:
                    print("   ⚠️ لم يتم العثور على شريط الأدوات في نافذة الحوالات")
            else:
                print("   ❌ فشل في إنشاء نافذة الحوالات")
        except Exception as e:
            print(f"   ❌ خطأ في اختبار نافذة الحوالات: {e}")
        
        # اختبار نافذة إدارة البنوك
        print("\n🔄 اختبار نافذة إدارة البنوك...")
        try:
            main_window.open_banks_management_window()
            if hasattr(main_window, 'banks_management_window') and main_window.banks_management_window:
                print("   ✅ تم إنشاء نافذة إدارة البنوك (زر الخروج متوفر)")
            else:
                print("   ❌ فشل في إنشاء نافذة إدارة البنوك")
        except Exception as e:
            print(f"   ❌ خطأ في اختبار نافذة إدارة البنوك: {e}")
        
        # اختبار نافذة إدارة حسابات الموردين
        print("\n🔄 اختبار نافذة إدارة حسابات الموردين...")
        try:
            main_window.open_supplier_accounts_management_window()
            if hasattr(main_window, 'supplier_accounts_management_window') and main_window.supplier_accounts_management_window:
                print("   ✅ تم إنشاء نافذة إدارة حسابات الموردين (زر الخروج متوفر)")
            else:
                print("   ❌ فشل في إنشاء نافذة إدارة حسابات الموردين")
        except Exception as e:
            print(f"   ❌ خطأ في اختبار نافذة إدارة حسابات الموردين: {e}")
        
        print("\n📋 أزرار الخروج المضافة:")
        print("   1. ✅ نافذة إدارة الحوالات - زر '❌ خروج' في شريط الأدوات")
        print("   2. ✅ نافذة إدارة البنوك - زر '❌ خروج' في شريط الأدوات")
        print("   3. ✅ نافذة إدارة حسابات الموردين - زر '❌ خروج' في شريط الأدوات")
        print("   4. ✅ النوافذ الحوارية - أزرار '❌ إلغاء' موجودة مسبقاً")
        
        print("\n🎉 جميع النوافذ لديها أزرار خروج!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📝 كيفية استخدام أزرار الخروج:")
    print("=" * 40)
    print("1. النوافذ الرئيسية:")
    print("   - ابحث عن زر '❌ خروج' في شريط الأدوات")
    print("   - انقر عليه لإغلاق النافذة")
    print()
    print("2. النوافذ الحوارية:")
    print("   - ابحث عن زر '❌ إلغاء' في أسفل النافذة")
    print("   - انقر عليه لإغلاق النافذة")
    print()
    print("3. طرق أخرى للإغلاق:")
    print("   - استخدم زر X في أعلى النافذة")
    print("   - اضغط Alt+F4")
    print("   - اضغط Escape (في النوافذ الحوارية)")

if __name__ == "__main__":
    success = test_exit_buttons()
    
    if success:
        show_usage_instructions()
        print("\n✅ جميع النوافذ لديها أزرار خروج تعمل بشكل صحيح!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
