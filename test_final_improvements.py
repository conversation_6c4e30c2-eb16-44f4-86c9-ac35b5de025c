#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_improvements():
    """اختبار التحسينات النهائية للنوافذ"""
    print("🔍 اختبار التحسينات النهائية للنوافذ...")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # اختبار نافذة البنك المحسنة
        print("\n🏦 اختبار نافذة البنك المحسنة (920 بكسل)...")
        from src.ui.remittances.add_new_bank_dialog import AddNewBankDialog
        
        bank_dialog = AddNewBankDialog()
        print(f"   📏 حجم النافذة: {bank_dialog.size().width()} x {bank_dialog.size().height()}")
        
        # التحقق من الارتفاع الجديد
        if bank_dialog.size().height() >= 920:
            print("   ✅ ارتفاع النافذة محسن إلى 920 بكسل")
        else:
            print(f"   ❌ ارتفاع النافذة {bank_dialog.size().height()} أقل من المطلوب")
        
        # التحقق من التوسيط
        if hasattr(bank_dialog, 'center_window'):
            print("   ✅ دالة التوسيط موجودة")
        else:
            print("   ❌ دالة التوسيط مفقودة")
        
        print("   ✅ نافذة البنك محسنة بنجاح")
        
        # اختبار نافذة الصراف المحسنة
        print("\n💱 اختبار نافذة الصراف المحسنة (920 بكسل - بدون أوقات العمل)...")
        from src.ui.remittances.add_new_exchange_dialog import AddNewExchangeDialog
        
        exchange_dialog = AddNewExchangeDialog()
        print(f"   📏 حجم النافذة: {exchange_dialog.size().width()} x {exchange_dialog.size().height()}")
        
        # التحقق من الارتفاع الجديد
        if exchange_dialog.size().height() >= 920:
            print("   ✅ ارتفاع النافذة محسن إلى 920 بكسل")
        else:
            print(f"   ❌ ارتفاع النافذة {exchange_dialog.size().height()} أقل من المطلوب")
        
        # التحقق من حذف أوقات العمل
        working_hours_removed = True
        for attr in ['start_time_input', 'end_time_input', 'working_days_input']:
            if hasattr(exchange_dialog, attr):
                working_hours_removed = False
                print(f"   ❌ {attr} لا يزال موجود")
        
        if working_hours_removed:
            print("   ✅ تم حذف قسم أوقات العمل بنجاح")
        else:
            print("   ❌ لم يتم حذف قسم أوقات العمل بالكامل")
        
        print("   ✅ نافذة الصراف محسنة بنجاح")
        
        # اختبار نافذة الفرع المحسنة
        print("\n🏢 اختبار نافذة الفرع المحسنة (920 بكسل)...")
        from src.ui.remittances.add_new_branch_dialog import AddNewBranchDialog
        
        branch_dialog = AddNewBranchDialog()
        print(f"   📏 حجم النافذة: {branch_dialog.size().width()} x {branch_dialog.size().height()}")
        
        # التحقق من الارتفاع الجديد
        if branch_dialog.size().height() >= 920:
            print("   ✅ ارتفاع النافذة محسن إلى 920 بكسل")
        else:
            print(f"   ❌ ارتفاع النافذة {branch_dialog.size().height()} أقل من المطلوب")
        
        # التحقق من التوسيط
        if hasattr(branch_dialog, 'center_window'):
            print("   ✅ دالة التوسيط موجودة")
        else:
            print("   ❌ دالة التوسيط مفقودة")
        
        print("   ✅ نافذة الفرع محسنة بنجاح")
        
        # اختبار دالة التوسيط لجميع النوافذ
        print("\n🎯 اختبار دالة التوسيط لجميع النوافذ...")
        
        dialogs = [
            ("البنك", bank_dialog),
            ("الصراف", exchange_dialog),
            ("الفرع", branch_dialog)
        ]
        
        for name, dialog in dialogs:
            try:
                dialog.center_window()
                print(f"   ✅ دالة التوسيط تعمل في نافذة {name}")
            except Exception as e:
                print(f"   ❌ خطأ في دالة التوسيط لنافذة {name}: {e}")
        
        # اختبار ترتيب الحقول
        print("\n📊 اختبار ترتيب الحقول المحسن...")
        
        # التحقق من ارتفاع الحقول
        test_fields = [
            ("البنك", bank_dialog, 'bank_name_input'),
            ("الصراف", exchange_dialog, 'exchange_name_input'),
            ("الفرع", branch_dialog, 'branch_name_input')
        ]
        
        for name, dialog, field_name in test_fields:
            if hasattr(dialog, field_name):
                field = getattr(dialog, field_name)
                height = field.minimumHeight()
                if height >= 35:
                    print(f"   ✅ ارتفاع حقول نافذة {name}: {height} بكسل")
                else:
                    print(f"   ❌ ارتفاع حقول نافذة {name}: {height} بكسل (أقل من المطلوب)")
            else:
                print(f"   ❌ حقل {field_name} مفقود في نافذة {name}")
        
        print("\n" + "=" * 60)
        print("📊 ملخص التحسينات النهائية:")
        print("✅ تم تغيير ارتفاع جميع النوافذ إلى 920 بكسل")
        print("✅ تم حذف قسم أوقات العمل من نافذة الصراف")
        print("✅ تم إعادة ترتيب الحقول لوضوح أكبر")
        print("✅ تم تحسين التوسيط التلقائي للنوافذ")
        print("✅ تم تحسين ارتفاع الحقول لسهولة القراءة")
        
        print("\n🎉 جميع التحسينات النهائية تمت بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_final_improvements_summary():
    """عرض ملخص التحسينات النهائية"""
    print("\n📋 ملخص التحسينات النهائية:")
    print("=" * 45)
    
    print("\n🏦 نافذة إضافة بنك جديد:")
    print("   📏 الحجم الجديد: 800 x 920 بكسل")
    print("   📐 ارتفاع الحقول: 35 بكسل")
    print("   📝 ارتفاع النصوص: 100 بكسل")
    print("   🎯 توسيط تلقائي في الشاشة")
    print("   📊 ترتيب محسن للحقول")
    
    print("\n💱 نافذة إضافة صراف جديد:")
    print("   📏 الحجم الجديد: 850 x 920 بكسل")
    print("   ❌ تم حذف قسم أوقات العمل")
    print("   📐 ارتفاع الحقول: 35 بكسل")
    print("   📝 ارتفاع النصوص: 100 بكسل")
    print("   🎯 توسيط تلقائي في الشاشة")
    print("   📊 ترتيب محسن للحقول")
    
    print("\n🏢 نافذة إضافة فرع جديد:")
    print("   📏 الحجم الجديد: 800 x 920 بكسل")
    print("   📐 ارتفاع الحقول: 35 بكسل")
    print("   📝 ارتفاع النصوص: 100 بكسل")
    print("   🎯 توسيط تلقائي في الشاشة")
    print("   📊 ترتيب محسن للحقول")
    
    print("\n🎨 التحسينات الخاصة:")
    print("   • حذف أوقات العمل من نافذة الصراف")
    print("   • ترتيب أفضل للحقول في جميع النوافذ")
    print("   • استغلال أمثل للمساحة الإضافية")
    print("   • تحسين تجربة المستخدم")

def show_removed_features():
    """عرض الميزات المحذوفة"""
    print("\n🗑️ الميزات المحذوفة:")
    print("=" * 25)
    print("❌ قسم أوقات العمل من نافذة الصراف:")
    print("   • وقت البداية")
    print("   • وقت النهاية") 
    print("   • أيام العمل")
    print("\n💡 السبب: تبسيط النافذة وتركيز على المعلومات الأساسية")

def show_usage_tips():
    """عرض نصائح الاستخدام"""
    print("\n💡 نصائح الاستخدام:")
    print("=" * 25)
    print("1. النوافذ أصبحت أطول (920 بكسل) لمزيد من الوضوح")
    print("2. نافذة الصراف أبسط بدون أوقات العمل")
    print("3. الحقول أوسع وأوضح للقراءة")
    print("4. التوسيط التلقائي يعمل مع جميع أحجام الشاشات")
    print("5. الترتيب الجديد يسهل ملء النماذج")

if __name__ == "__main__":
    success = test_final_improvements()
    
    if success:
        show_final_improvements_summary()
        show_removed_features()
        show_usage_tips()
        print("\n🚀 جميع التحسينات النهائية مطبقة وجاهزة للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
