#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات مع دعم Oracle
Database Manager with Oracle Support
"""

import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, date

class UniversalDatabaseManager:
    """مدير قاعدة البيانات الشامل (SQLite + Oracle)"""
    
    def __init__(self, db_config: dict = None):
        """تهيئة مدير قاعدة البيانات"""
        
        if db_config is None:
            # الإعدادات الافتراضية - SQLite
            db_config = {
                'type': 'sqlite',
                'path': 'data/proshipment.db'
            }
        
        self.config = db_config
        self.db_type = db_config.get('type', 'sqlite').lower()
        
        # إنشاء engine حسب نوع قاعدة البيانات
        if self.db_type == 'sqlite':
            self._setup_sqlite()
        elif self.db_type == 'oracle':
            self._setup_oracle()
        else:
            raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {self.db_type}")
        
        self.SessionLocal = sessionmaker(
            autocommit=False, 
            autoflush=False, 
            bind=self.engine
        )
    
    def _setup_sqlite(self):
        """إعداد SQLite"""
        db_path = self.config.get('path', 'data/proshipment.db')
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        data_dir = Path(db_path).parent
        data_dir.mkdir(exist_ok=True)
        
        self.engine = create_engine(
            f"sqlite:///{db_path}", 
            echo=False,
            pool_pre_ping=True
        )
        
        print(f"✅ تم إعداد SQLite: {db_path}")
    
    def _setup_oracle(self):
        """إعداد Oracle"""
        required_keys = ['host', 'port', 'service_name', 'username', 'password']
        
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"مفتاح مطلوب مفقود في إعدادات Oracle: {key}")
        
        # بناء connection string
        connection_string = (
            f"oracle+cx_oracle://{self.config['username']}:"
            f"{self.config['password']}@"
            f"{self.config['host']}:{self.config['port']}/"
            f"?service_name={self.config['service_name']}"
        )
        
        self.engine = create_engine(
            connection_string,
            echo=False,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        print(f"✅ تم إعداد Oracle: {self.config['host']}:{self.config['port']}")
    
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.engine.connect() as connection:
                if self.db_type == 'sqlite':
                    result = connection.execute(text("SELECT 1"))
                elif self.db_type == 'oracle':
                    result = connection.execute(text("SELECT 1 FROM DUAL"))
                
                row = result.fetchone()
                if row and row[0] == 1:
                    print(f"✅ تم الاتصال بـ {self.db_type.upper()} بنجاح")
                    return True
                else:
                    print(f"❌ فشل في اختبار الاتصال بـ {self.db_type.upper()}")
                    return False
        except Exception as e:
            print(f"❌ خطأ في الاتصال بـ {self.db_type.upper()}: {e}")
            return False
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            # استيراد النماذج
            from src.database.models import Base
            
            # إنشاء جميع الجداول
            Base.metadata.create_all(bind=self.engine)
            
            # إدراج البيانات الأساسية
            self._insert_default_data()
            
            print(f"تم تهيئة قاعدة بيانات {self.db_type.upper()} بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات"""
        return self.SessionLocal()

    def close_all_sessions(self):
        """إغلاق جميع الجلسات النشطة"""
        try:
            self.engine.dispose()
        except Exception as e:
            print(f"خطأ في إغلاق الجلسات: {e}")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        session = self.get_session()
        try:
            from src.database.models import SystemSettings, Currency, FiscalYear, Company
            
            # التحقق من وجود بيانات
            if session.query(SystemSettings).count() > 0:
                return
            
            # إعدادات النظام الافتراضية
            default_settings = [
                SystemSettings(
                    key="app_name",
                    value="نظام إدارة الشحنات",
                    description="اسم التطبيق",
                    category="general"
                ),
                SystemSettings(
                    key="database_type",
                    value=self.db_type.upper(),
                    description="نوع قاعدة البيانات",
                    category="system"
                ),
                SystemSettings(
                    key="app_version",
                    value="1.0.0",
                    description="إصدار التطبيق",
                    category="general"
                ),
                SystemSettings(
                    key="default_currency",
                    value="SAR",
                    description="العملة الافتراضية",
                    category="financial"
                )
            ]
            
            # العملات الافتراضية
            default_currencies = [
                Currency(
                    code="SAR",
                    name="ريال سعودي",
                    name_en="Saudi Riyal",
                    symbol="ر.س",
                    exchange_rate=1.0,
                    is_base=True
                ),
                Currency(
                    code="USD",
                    name="دولار أمريكي",
                    name_en="US Dollar",
                    symbol="$",
                    exchange_rate=3.75
                ),
                Currency(
                    code="EUR",
                    name="يورو",
                    name_en="Euro",
                    symbol="€",
                    exchange_rate=4.10
                )
            ]
            
            # السنة المالية الحالية
            current_year = datetime.now().year
            fiscal_year = FiscalYear(
                year=current_year,
                start_date=datetime(current_year, 1, 1),
                end_date=datetime(current_year, 12, 31),
                is_current=True
            )
            
            # بيانات الشركة الافتراضية
            default_company = Company(
                name="شركة الشحنات المتقدمة",
                name_en="Advanced Shipping Company",
                address="الرياض، المملكة العربية السعودية",
                phone="+966-11-1234567",
                email="<EMAIL>"
            )
            
            # إضافة البيانات
            session.add_all(default_settings)
            session.add_all(default_currencies)
            session.add(fiscal_year)
            session.add(default_company)
            
            session.commit()
            print(f"تم إدراج البيانات الافتراضية في {self.db_type.upper()} بنجاح")
            
        except Exception as e:
            session.rollback()
            print(f"خطأ في إدراج البيانات الافتراضية: {e}")
        finally:
            session.close()
    
    def backup_database(self, backup_path: str = None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_path is None:
                backup_dir = Path("backups")
                backup_dir.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                if self.db_type == 'sqlite':
                    backup_path = backup_dir / f"sqlite_backup_{timestamp}.db"
                elif self.db_type == 'oracle':
                    backup_path = backup_dir / f"oracle_backup_{timestamp}.sql"
            
            if self.db_type == 'sqlite':
                # نسخ ملف SQLite
                import shutil
                shutil.copy2(self.config['path'], backup_path)
                print(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
                
            elif self.db_type == 'oracle':
                # لـ Oracle، نحتاج لاستخدام أدوات Oracle
                print(f"لإنشاء نسخة احتياطية من Oracle، استخدم:")
                print(f"expdp {self.config['username']}/{self.config['password']}@{self.config['host']}:{self.config['port']}/{self.config['service_name']} directory=backup_dir dumpfile={backup_path.name}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def get_setting(self, key: str, default_value: str = None):
        """الحصول على قيمة إعداد"""
        session = self.get_session()
        try:
            from src.database.models import SystemSettings
            setting = session.query(SystemSettings).filter_by(key=key).first()
            if setting:
                return setting.value
            return default_value
        finally:
            session.close()
    
    def set_setting(self, key: str, value: str, description: str = None, category: str = "general"):
        """تعيين قيمة إعداد"""
        session = self.get_session()
        try:
            from src.database.models import SystemSettings
            setting = session.query(SystemSettings).filter_by(key=key).first()
            if setting:
                setting.value = value
                setting.updated_at = datetime.now()
            else:
                setting = SystemSettings(
                    key=key,
                    value=value,
                    description=description,
                    category=category
                )
                session.add(setting)
            
            session.commit()
            return True
            
        except Exception as e:
            session.rollback()
            print(f"خطأ في تعيين الإعداد: {e}")
            return False
        finally:
            session.close()

# دوال مساعدة لإنشاء إعدادات مختلفة
def create_sqlite_config(db_path: str = "data/proshipment.db"):
    """إنشاء إعدادات SQLite"""
    return {
        'type': 'sqlite',
        'path': db_path
    }

def create_oracle_config(host: str, port: int, service_name: str, username: str, password: str):
    """إنشاء إعدادات Oracle"""
    return {
        'type': 'oracle',
        'host': host,
        'port': port,
        'service_name': service_name,
        'username': username,
        'password': password
    }

# مثال على الاستخدام
if __name__ == "__main__":
    # مثال 1: استخدام SQLite (الافتراضي)
    print("🔄 اختبار SQLite...")
    sqlite_config = create_sqlite_config()
    sqlite_manager = UniversalDatabaseManager(sqlite_config)
    
    if sqlite_manager.test_connection():
        sqlite_manager.initialize_database()
    
    # مثال 2: استخدام Oracle
    print("\n🔄 اختبار Oracle...")
    oracle_config = create_oracle_config(
        host='localhost',
        port=1521,
        service_name='XE',
        username='proshipment',
        password='your_password'
    )
    
    try:
        oracle_manager = UniversalDatabaseManager(oracle_config)
        if oracle_manager.test_connection():
            oracle_manager.initialize_database()
    except Exception as e:
        print(f"❌ خطأ في Oracle: {e}")
    
    print("\n✅ انتهى الاختبار")
