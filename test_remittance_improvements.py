#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات نافذة طلب الحوالة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt
    from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
    
    def test_remittance_window():
        """اختبار نافذة طلب الحوالة"""
        print("🧪 بدء اختبار تحسينات نافذة طلب الحوالة...")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        try:
            # إنشاء النافذة
            print("📝 إنشاء نافذة طلب الحوالة...")
            window = RemittanceRequestWindow()
            
            # اختبار 1: التأكد من وجود الجدول
            print("🔍 اختبار 1: فحص جدول طلبات الحوالات...")
            if hasattr(window, 'requests_table'):
                print("✅ جدول طلبات الحوالات موجود")
                
                # فحص ارتفاع الصفوف
                row_height = window.requests_table.verticalHeader().defaultSectionSize()
                print(f"📏 ارتفاع الصفوف: {row_height} بكسل")
                
                if row_height >= 50:
                    print("✅ ارتفاع الصفوف محسن بنجاح")
                else:
                    print("⚠️ ارتفاع الصفوف لم يتم تحسينه")
                    
            else:
                print("❌ جدول طلبات الحوالات غير موجود")
            
            # اختبار 2: فحص الحقول الجديدة
            print("\n🔍 اختبار 2: فحص الحقول في قسم البيانات الأساسية...")
            
            # فحص قائمة الصرافين
            if hasattr(window, 'exchanger_combo'):
                print("✅ قائمة الصرافين موجودة")
                print(f"📊 عدد الصرافين: {window.exchanger_combo.count()}")
            else:
                print("❌ قائمة الصرافين غير موجودة")
            
            # فحص قائمة العملات
            if hasattr(window, 'currency_combo'):
                print("✅ قائمة العملات موجودة")
                print(f"💱 عدد العملات: {window.currency_combo.count()}")
            else:
                print("❌ قائمة العملات غير موجودة")
            
            # اختبار 3: التأكد من حذف حقل الفرع
            print("\n🔍 اختبار 3: التأكد من حذف الحقول المطلوبة...")
            if not hasattr(window, 'branch_combo'):
                print("✅ تم حذف حقل الفرع بنجاح")
            else:
                print("⚠️ حقل الفرع ما زال موجوداً")
            
            # اختبار 4: فحص النقر المزدوج
            print("\n🔍 اختبار 4: فحص إعداد النقر المزدوج...")
            if hasattr(window, 'requests_table') and hasattr(window, 'edit_selected_request'):
                print("✅ النقر المزدوج مربوط بدالة التحرير")
            else:
                print("⚠️ النقر المزدوج غير مربوط")
            
            # عرض النافذة للاختبار البصري
            print("\n🖥️ عرض النافذة للاختبار البصري...")
            window.show()
            
            # رسالة نجاح
            QMessageBox.information(None, "اختبار التحسينات", 
                                  "تم تطبيق التحسينات التالية بنجاح:\n\n"
                                  "✅ زيادة ارتفاع صفوف الجدول\n"
                                  "✅ تحويل حقول الجدول للقراءة فقط\n"
                                  "✅ تفعيل وضع التحرير بالنقر المزدوج\n"
                                  "✅ حذف حقل الفرع من البيانات الأساسية\n"
                                  "✅ إضافة قائمة منسدلة للصرافين\n"
                                  "✅ إضافة قائمة منسدلة للعملات\n\n"
                                  "يمكنك الآن اختبار النافذة بصرياً!")
            
            print("🎉 جميع الاختبارات نجحت!")
            return window
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    if __name__ == "__main__":
        window = test_remittance_window()
        if window:
            # تشغيل التطبيق
            app = QApplication.instance()
            if app:
                sys.exit(app.exec())
        
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من تثبيت PySide6 وأن مسار المشروع صحيح")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
