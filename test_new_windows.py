#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النوافذ الجديدة
Test New Windows
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# استيراد النوافذ الجديدة
from src.ui.remittances.new_transaction_dialog import NewTransactionDialog
from src.ui.remittances.bulk_transfer_dialog import BulkTransferDialog
from src.ui.remittances.account_reconciliation_dialog import AccountReconciliationDialog

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار النوافذ الجديدة")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # زر اختبار نافذة المعاملة الجديدة
        transaction_btn = QPushButton("اختبار نافذة المعاملة الجديدة")
        transaction_btn.clicked.connect(self.test_transaction_dialog)
        layout.addWidget(transaction_btn)
        
        # زر اختبار نافذة التحويل الجماعي
        bulk_transfer_btn = QPushButton("اختبار نافذة التحويل الجماعي")
        bulk_transfer_btn.clicked.connect(self.test_bulk_transfer_dialog)
        layout.addWidget(bulk_transfer_btn)
        
        # زر اختبار نافذة تسوية الحسابات
        reconciliation_btn = QPushButton("اختبار نافذة تسوية الحسابات")
        reconciliation_btn.clicked.connect(self.test_reconciliation_dialog)
        layout.addWidget(reconciliation_btn)
        
    def test_transaction_dialog(self):
        """اختبار نافذة المعاملة الجديدة"""
        try:
            dialog = NewTransactionDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"خطأ في نافذة المعاملة الجديدة: {e}")
            
    def test_bulk_transfer_dialog(self):
        """اختبار نافذة التحويل الجماعي"""
        try:
            dialog = BulkTransferDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"خطأ في نافذة التحويل الجماعي: {e}")
            
    def test_reconciliation_dialog(self):
        """اختبار نافذة تسوية الحسابات"""
        try:
            dialog = AccountReconciliationDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"خطأ في نافذة تسوية الحسابات: {e}")

def main():
    app = QApplication(sys.argv)
    
    # إنشاء النافذة الرئيسية
    window = TestWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
