#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة جداول نظام حوالات الموردين
Add Supplier Remittances System Tables
"""

import sqlite3
import os
from pathlib import Path
from datetime import datetime, date

def add_supplier_remittances_tables():
    """إضافة جداول نظام حوالات الموردين"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 إنشاء جداول نظام حوالات الموردين...")
        
        # 1. جدول حوالات الموردين الرئيسي
        print("🔄 إنشاء جدول supplier_remittances...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS supplier_remittances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                remittance_number VARCHAR(50) UNIQUE NOT NULL,
                remittance_date DATE NOT NULL,
                
                -- البيانات المالية
                total_amount REAL NOT NULL,
                currency_id INTEGER NOT NULL,
                exchange_rate REAL DEFAULT 1.0,
                amount_in_base_currency REAL,
                
                -- بيانات البنك المرسل
                sender_bank_name VARCHAR(200),
                sender_bank_code VARCHAR(50),
                sender_account_number VARCHAR(100),
                sender_account_name VARCHAR(200),
                
                -- بيانات البنك المستقبل
                receiver_bank_name VARCHAR(200),
                receiver_bank_code VARCHAR(50),
                receiver_bank_country VARCHAR(100),
                swift_code VARCHAR(20),
                
                -- حالة الحوالة
                status VARCHAR(50) DEFAULT 'مرسلة',
                
                -- تواريخ مهمة
                sent_date DATETIME,
                received_date DATETIME,
                confirmed_date DATETIME,
                posted_date DATETIME,
                
                -- معلومات إضافية
                reference_number VARCHAR(100),
                purpose VARCHAR(500),
                notes TEXT,
                fees_amount REAL DEFAULT 0.0,
                
                -- معلومات المستخدم
                created_by INTEGER,
                confirmed_by INTEGER,
                posted_by INTEGER,
                
                -- حقول النظام
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (currency_id) REFERENCES currencies(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (confirmed_by) REFERENCES users(id),
                FOREIGN KEY (posted_by) REFERENCES users(id)
            )
        """)
        
        # 2. جدول تفاصيل حوالات الموردين
        print("🔄 إنشاء جدول supplier_remittance_items...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS supplier_remittance_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                remittance_id INTEGER NOT NULL,
                supplier_id INTEGER NOT NULL,
                
                -- المبلغ المحول للمورد
                amount REAL NOT NULL,
                currency_id INTEGER NOT NULL,
                exchange_rate REAL DEFAULT 1.0,
                amount_in_base_currency REAL,
                
                -- بيانات المورد المصرفية
                supplier_bank_name VARCHAR(200),
                supplier_account_number VARCHAR(100),
                supplier_account_name VARCHAR(200),
                supplier_bank_code VARCHAR(50),
                supplier_swift_code VARCHAR(20),
                
                -- الغرض والمرجع
                purpose VARCHAR(500),
                reference_number VARCHAR(100),
                invoice_numbers TEXT,
                
                -- حالة التحويل للمورد
                status VARCHAR(50) DEFAULT 'مرسل',
                
                -- تواريخ خاصة بالمورد
                supplier_received_date DATETIME,
                supplier_confirmed_date DATETIME,
                posted_to_supplier_date DATETIME,
                
                -- ملاحظات
                notes TEXT,
                
                -- حقول النظام
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (remittance_id) REFERENCES supplier_remittances(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (currency_id) REFERENCES currencies(id)
            )
        """)
        
        # 3. جدول حسابات الموردين
        print("🔄 إنشاء جدول supplier_accounts...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS supplier_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                currency_id INTEGER NOT NULL,
                
                -- أرصدة الحساب
                opening_balance REAL DEFAULT 0.0,
                current_balance REAL DEFAULT 0.0,
                credit_limit REAL DEFAULT 0.0,
                
                -- إحصائيات
                total_purchases REAL DEFAULT 0.0,
                total_payments REAL DEFAULT 0.0,
                total_remittances REAL DEFAULT 0.0,
                
                -- تواريخ مهمة
                last_transaction_date DATETIME,
                last_payment_date DATETIME,
                last_remittance_date DATETIME,
                
                -- حقول النظام
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (currency_id) REFERENCES currencies(id),
                UNIQUE(supplier_id, currency_id)
            )
        """)
        
        # إنشاء فهارس للأداء
        print("🔄 إنشاء الفهارس...")
        indexes = [
            # فهارس جدول الحوالات
            "CREATE INDEX IF NOT EXISTS idx_remittances_number ON supplier_remittances(remittance_number)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_date ON supplier_remittances(remittance_date)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_status ON supplier_remittances(status)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_currency ON supplier_remittances(currency_id)",
            
            # فهارس جدول تفاصيل الحوالات
            "CREATE INDEX IF NOT EXISTS idx_remittance_items_remittance ON supplier_remittance_items(remittance_id)",
            "CREATE INDEX IF NOT EXISTS idx_remittance_items_supplier ON supplier_remittance_items(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_remittance_items_status ON supplier_remittance_items(status)",
            
            # فهارس جدول حسابات الموردين
            "CREATE INDEX IF NOT EXISTS idx_supplier_accounts_supplier ON supplier_accounts(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_supplier_accounts_currency ON supplier_accounts(currency_id)",
            "CREATE INDEX IF NOT EXISTS idx_supplier_accounts_active ON supplier_accounts(is_active)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إنشاء جداول نظام حوالات الموردين بنجاح!")
        
        # إضافة بيانات تجريبية
        print("📝 إضافة بيانات تجريبية...")
        add_sample_data(cursor)
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إنشاء نظام حوالات الموردين بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def add_sample_data(cursor):
    """إضافة بيانات تجريبية"""
    
    try:
        # التحقق من وجود موردين
        cursor.execute("SELECT id, name FROM suppliers LIMIT 3")
        suppliers = cursor.fetchall()
        
        if not suppliers:
            print("⚠️ لا توجد موردين في النظام")
            return
        
        # التحقق من وجود عملات
        cursor.execute("SELECT id, code FROM currencies WHERE is_active = 1")
        currencies = cursor.fetchall()
        
        if not currencies:
            print("⚠️ لا توجد عملات في النظام")
            return
        
        # إنشاء حسابات للموردين
        print("📊 إنشاء حسابات الموردين...")
        for supplier in suppliers:
            supplier_id = supplier[0]
            for currency in currencies[:2]:  # أول عملتين
                currency_id = currency[0]
                
                # فحص وجود الحساب
                cursor.execute("""
                    SELECT id FROM supplier_accounts 
                    WHERE supplier_id = ? AND currency_id = ?
                """, (supplier_id, currency_id))
                
                if not cursor.fetchone():
                    cursor.execute("""
                        INSERT INTO supplier_accounts 
                        (supplier_id, currency_id, opening_balance, current_balance, credit_limit)
                        VALUES (?, ?, ?, ?, ?)
                    """, (supplier_id, currency_id, 0.0, 0.0, 100000.0))
        
        # إنشاء حوالة تجريبية
        print("💸 إنشاء حوالة تجريبية...")
        today = date.today()
        
        cursor.execute("""
            INSERT INTO supplier_remittances 
            (remittance_number, remittance_date, total_amount, currency_id, 
             sender_bank_name, receiver_bank_name, receiver_bank_country, 
             purpose, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            f"REM-{today.strftime('%Y%m%d')}-001",
            today,
            250000.0,
            currencies[0][0],  # أول عملة
            "البنك الأهلي السعودي",
            "Bank of China",
            "الصين",
            "دفع مستحقات الموردين",
            "مرسلة"
        ))
        
        remittance_id = cursor.lastrowid
        
        # إضافة تفاصيل الحوالة
        print("📋 إضافة تفاصيل الحوالة...")
        for i, supplier in enumerate(suppliers[:2]):  # أول موردين
            amount = 125000.0 if i == 0 else 125000.0
            cursor.execute("""
                INSERT INTO supplier_remittance_items 
                (remittance_id, supplier_id, amount, currency_id, 
                 supplier_bank_name, purpose, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                remittance_id,
                supplier[0],
                amount,
                currencies[0][0],
                f"بنك {supplier[1]}",
                f"دفع مستحقات {supplier[1]}",
                "مرسل"
            ))
        
        print("✅ تم إضافة البيانات التجريبية!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إضافة جداول نظام حوالات الموردين...")
    print("=" * 60)
    
    success = add_supplier_remittances_tables()
    
    if success:
        print("\n✅ تم إنجاز المهمة بنجاح!")
        print("📊 الجداول المضافة:")
        print("   • supplier_remittances - جدول الحوالات الرئيسي")
        print("   • supplier_remittance_items - تفاصيل الحوالات")
        print("   • supplier_accounts - حسابات الموردين")
    else:
        print("\n❌ فشل في إنجاز المهمة!")
    
    print("=" * 60)
