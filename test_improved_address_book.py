#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النموذج المحسن لدفتر العناوين
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_address_book_interface():
    """اختبار واجهة دفتر العناوين المحسنة"""
    print("🎨 اختبار واجهة دفتر العناوين المحسنة...")
    
    try:
        from PySide6.QtWidgets import QApplication, QPushButton, QLabel, QLineEdit
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # الانتقال إلى تبويب دفتر العناوين
        window.tab_widget.setCurrentIndex(3)
        
        # التحقق من العناصر الجديدة
        print("🔍 فحص العناصر الجديدة:")
        
        # التحقق من شريط البحث المحسن
        if hasattr(window, 'address_search_input'):
            placeholder = window.address_search_input.placeholderText()
            print(f"   ✅ شريط البحث: {placeholder}")
        else:
            print("   ❌ شريط البحث غير موجود")
            return False
        
        # التحقق من عداد العناوين
        if hasattr(window, 'address_count_label'):
            count_text = window.address_count_label.text()
            print(f"   ✅ عداد العناوين: {count_text}")
        else:
            print("   ❌ عداد العناوين غير موجود")
            return False
        
        # التحقق من مؤشر حالة النموذج
        if hasattr(window, 'form_status_label'):
            status_text = window.form_status_label.text()
            print(f"   ✅ مؤشر حالة النموذج: {status_text}")
        else:
            print("   ❌ مؤشر حالة النموذج غير موجود")
            return False
        
        # التحقق من الأزرار الجديدة
        buttons_to_check = [
            ('save_address_btn', 'زر الحفظ المحسن'),
            ('cancel_edit_btn', 'زر إلغاء التعديل')
        ]
        
        for button_attr, button_desc in buttons_to_check:
            if hasattr(window, button_attr):
                button = getattr(window, button_attr)
                button_text = button.text()
                print(f"   ✅ {button_desc}: {button_text}")
            else:
                print(f"   ❌ {button_desc} غير موجود")
                return False
        
        # البحث عن الأزرار الإضافية
        all_buttons = window.findChildren(QPushButton)
        button_texts = [btn.text() for btn in all_buttons]
        
        expected_buttons = [
            "🔄 تحديث القائمة",
            "📤 تصدير", 
            "📥 استيراد",
            "📋 نسخ للحوالة",
            "🧹 مسح النموذج"
        ]
        
        found_buttons = []
        for expected in expected_buttons:
            for text in button_texts:
                if expected in text:
                    found_buttons.append(expected)
                    break
        
        print(f"   📊 الأزرار الموجودة: {len(found_buttons)}/{len(expected_buttons)}")
        for btn in found_buttons:
            print(f"      ✅ {btn}")
        
        missing_buttons = [btn for btn in expected_buttons if btn not in found_buttons]
        if missing_buttons:
            print("   ⚠️ الأزرار المفقودة:")
            for btn in missing_buttons:
                print(f"      ❌ {btn}")
        
        window.close()
        return len(found_buttons) >= len(expected_buttons) * 0.8
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_functionality():
    """اختبار وظائف النموذج"""
    print("\n⚙️ اختبار وظائف النموذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # الانتقال إلى تبويب دفتر العناوين
        window.tab_widget.setCurrentIndex(3)
        
        # اختبار ملء النموذج
        test_data = {
            'name': 'شركة الاختبار المحدودة',
            'account': '****************',
            'bank': 'البنك الأهلي السعودي',
            'branch': 'فرع الرياض الرئيسي',
            'swift': 'NCBKSARI',
            'country': 'المملكة العربية السعودية',
            'bank_country': 'المملكة العربية السعودية',
            'address': 'الرياض، طريق الملك فهد'
        }
        
        print("📝 اختبار ملء النموذج:")
        
        # ملء الحقول
        window.ab_receiver_name_input.setText(test_data['name'])
        window.ab_receiver_account_input.setText(test_data['account'])
        window.ab_receiver_bank_input.setText(test_data['bank'])
        window.ab_receiver_bank_branch_input.setText(test_data['branch'])
        window.ab_receiver_swift_input.setText(test_data['swift'])
        window.ab_receiver_country_input.setText(test_data['country'])
        window.ab_receiver_bank_country_input.setText(test_data['bank_country'])
        window.ab_receiver_address_input.setText(test_data['address'])
        
        # التحقق من الملء
        if window.ab_receiver_name_input.text() == test_data['name']:
            print("   ✅ ملء الحقول يعمل بشكل صحيح")
        else:
            print("   ❌ ملء الحقول لا يعمل")
            return False
        
        # اختبار مسح النموذج
        print("🧹 اختبار مسح النموذج:")
        window.clear_address_book_form()
        
        if not window.ab_receiver_name_input.text():
            print("   ✅ مسح النموذج يعمل بشكل صحيح")
        else:
            print("   ❌ مسح النموذج لا يعمل")
            return False
        
        # اختبار البحث
        print("🔍 اختبار البحث:")
        window.address_search_input.setText("اختبار")
        
        if window.address_search_input.text() == "اختبار":
            print("   ✅ البحث يعمل بشكل صحيح")
        else:
            print("   ❌ البحث لا يعمل")
            return False
        
        # مسح البحث
        window.address_search_input.clear()
        
        if not window.address_search_input.text():
            print("   ✅ مسح البحث يعمل بشكل صحيح")
        else:
            print("   ❌ مسح البحث لا يعمل")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_states():
    """اختبار حالات الأزرار"""
    print("\n🔘 اختبار حالات الأزرار...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # الانتقال إلى تبويب دفتر العناوين
        window.tab_widget.setCurrentIndex(3)
        
        # التحقق من الحالة الافتراضية
        print("📊 الحالة الافتراضية:")
        
        if hasattr(window, 'save_address_btn'):
            save_text = window.save_address_btn.text()
            print(f"   زر الحفظ: {save_text}")
            
            if "حفظ عنوان جديد" in save_text:
                print("   ✅ حالة الإضافة صحيحة")
            else:
                print("   ❌ حالة الإضافة غير صحيحة")
                return False
        
        if hasattr(window, 'cancel_edit_btn'):
            is_visible = window.cancel_edit_btn.isVisible()
            print(f"   زر الإلغاء مرئي: {'نعم' if is_visible else 'لا'}")
            
            if not is_visible:
                print("   ✅ زر الإلغاء مخفي افتراضياً")
            else:
                print("   ❌ زر الإلغاء يجب أن يكون مخفياً")
                return False
        
        if hasattr(window, 'form_status_label'):
            status = window.form_status_label.text()
            print(f"   حالة النموذج: {status}")
            
            if "إضافة عنوان جديد" in status:
                print("   ✅ حالة النموذج صحيحة")
            else:
                print("   ❌ حالة النموذج غير صحيحة")
                return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حالات الأزرار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النموذج المحسن لدفتر العناوين...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_address_book_interface())
    results.append(test_form_functionality())
    results.append(test_button_states())
    
    # عرض النتائج النهائية
    print("\n" + "="*70)
    print("🎯 ملخص اختبار النموذج المحسن:")
    print("="*70)
    
    test_names = [
        "واجهة دفتر العناوين المحسنة",
        "وظائف النموذج",
        "حالات الأزرار"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 النموذج المحسن يعمل بنجاح!")
        print("✅ واجهة شاملة مع جميع أزرار التحكم")
        print("✅ تخطيط واضح ومنظم")
        print("✅ أزرار في المواضع الصحيحة")
        print("✅ وظائف متقدمة (تصدير، استيراد، نسخ)")
        print("✅ مؤشرات حالة واضحة")
        print("✅ تجربة مستخدم محسنة")
        
        print("\n🌟 الميزات الجديدة:")
        print("   🔍 بحث محسن مع نص توضيحي شامل")
        print("   📊 عداد العناوين المباشر")
        print("   📝 مؤشر حالة النموذج")
        print("   🔄 أزرار تحديث وإدارة")
        print("   📤📥 تصدير واستيراد البيانات")
        print("   📋 نسخ مباشر لنموذج الحوالة")
        print("   ❌ إلغاء التعديل الذكي")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم الميزات تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة ميزات فشلت. يرجى مراجعة:")
        print("- تخطيط الواجهة")
        print("- أزرار التحكم")
        print("- وظائف النموذج")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
