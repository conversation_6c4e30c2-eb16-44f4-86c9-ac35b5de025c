#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جدول الفروع وحل مشاكل إضافة فرع جديد
Fix Branches Table and Solve Add New Branch Issues
"""

import sqlite3
import os
from pathlib import Path

def fix_branches_table():
    """إصلاح جدول الفروع"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إصلاح جدول الفروع...")
        
        # 1. التحقق من الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        print("📊 الجداول الموجودة:")
        for table in table_names:
            print(f"   • {table}")
        
        # 2. إنشاء جدول الفروع إذا لم يكن موجوداً
        if 'branches' not in table_names:
            print("\n📋 إنشاء جدول الفروع...")
            cursor.execute("""
                CREATE TABLE branches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    code TEXT UNIQUE NOT NULL,
                    bank_id INTEGER,
                    exchange_id INTEGER,
                    type TEXT NOT NULL,
                    country TEXT NOT NULL,
                    city TEXT NOT NULL,
                    address TEXT,
                    phone TEXT,
                    fax TEXT,
                    email TEXT,
                    website TEXT,
                    manager_name TEXT,
                    manager_phone TEXT,
                    working_hours TEXT,
                    services TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (bank_id) REFERENCES banks(id),
                    FOREIGN KEY (exchange_id) REFERENCES exchanges(id)
                )
            """)
            print("✅ تم إنشاء جدول الفروع بنجاح")
        else:
            print("\nℹ️ جدول الفروع موجود بالفعل")
            
            # التحقق من الأعمدة الموجودة
            cursor.execute("PRAGMA table_info(branches)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            print("📋 الأعمدة الموجودة في جدول الفروع:")
            for col in column_names:
                print(f"   • {col}")
            
            # إضافة الأعمدة المفقودة
            required_columns = {
                'name_en': 'TEXT',
                'code': 'TEXT UNIQUE',
                'fax': 'TEXT',
                'manager_name': 'TEXT',
                'manager_phone': 'TEXT',
                'working_hours': 'TEXT',
                'services': 'TEXT',
                'notes': 'TEXT'
            }
            
            for column_name, column_type in required_columns.items():
                if column_name not in column_names:
                    try:
                        if 'UNIQUE' in column_type:
                            # لا يمكن إضافة عمود UNIQUE مباشرة، سنضيفه بدون UNIQUE أولاً
                            cursor.execute(f"ALTER TABLE branches ADD COLUMN {column_name} TEXT")
                        else:
                            cursor.execute(f"ALTER TABLE branches ADD COLUMN {column_name} {column_type}")
                        print(f"✅ تم إضافة عمود {column_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e).lower():
                            print(f"⚠️ خطأ في إضافة عمود {column_name}: {e}")
        
        # 3. إضافة بيانات تجريبية للفروع
        cursor.execute("SELECT COUNT(*) FROM branches")
        branches_count = cursor.fetchone()[0]
        
        if branches_count == 0:
            print("\n📋 إضافة فروع تجريبية...")
            
            # الحصول على البنوك والصرافات الموجودة
            cursor.execute("SELECT id, name FROM banks LIMIT 2")
            banks = cursor.fetchall()
            
            cursor.execute("SELECT id, name FROM exchanges LIMIT 2")
            exchanges = cursor.fetchall()
            
            sample_branches = []
            
            # فروع البنوك
            if banks:
                for i, (bank_id, bank_name) in enumerate(banks):
                    branch_data = (
                        f"فرع {bank_name} الرئيسي",  # name
                        f"{bank_name} Main Branch",  # name_en
                        f"BR{bank_id:03d}",  # code
                        bank_id,  # bank_id
                        None,  # exchange_id
                        'بنك',  # type
                        'السعودية',  # country
                        'الرياض' if i == 0 else 'جدة',  # city
                        f'شارع الملك فهد، {bank_name}',  # address
                        f'011-{1000000 + i}',  # phone
                        f'011-{2000000 + i}',  # fax
                        f'info@{bank_name.replace(" ", "").lower()}.com',  # email
                        f'www.{bank_name.replace(" ", "").lower()}.com',  # website
                        f'مدير فرع {bank_name}',  # manager_name
                        f'050-{1000000 + i}',  # manager_phone
                        'الأحد - الخميس: 8:00 - 17:00',  # working_hours
                        'خدمات مصرفية شاملة، تحويلات، قروض',  # services
                        f'الفرع الرئيسي لـ {bank_name}',  # notes
                        1  # is_active
                    )
                    sample_branches.append(branch_data)
            
            # فروع الصرافات
            if exchanges:
                for i, (exchange_id, exchange_name) in enumerate(exchanges):
                    branch_data = (
                        f"فرع {exchange_name} الرئيسي",  # name
                        f"{exchange_name} Main Branch",  # name_en
                        f"EX{exchange_id:03d}",  # code
                        None,  # bank_id
                        exchange_id,  # exchange_id
                        'صرافة',  # type
                        'السعودية',  # country
                        'الدمام' if i == 0 else 'المدينة',  # city
                        f'شارع العروبة، {exchange_name}',  # address
                        f'013-{3000000 + i}',  # phone
                        f'013-{4000000 + i}',  # fax
                        f'info@{exchange_name.replace(" ", "").lower()}.com',  # email
                        f'www.{exchange_name.replace(" ", "").lower()}.com',  # website
                        f'مدير فرع {exchange_name}',  # manager_name
                        f'055-{2000000 + i}',  # manager_phone
                        'السبت - الخميس: 9:00 - 22:00',  # working_hours
                        'تحويلات نقدية، صرف عملات، خدمات سريعة',  # services
                        f'الفرع الرئيسي لـ {exchange_name}',  # notes
                        1  # is_active
                    )
                    sample_branches.append(branch_data)
            
            # إدراج البيانات
            for branch_data in sample_branches:
                cursor.execute("""
                    INSERT INTO branches (name, name_en, code, bank_id, exchange_id, type, country, city, address, phone, fax, email, website, manager_name, manager_phone, working_hours, services, notes, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, branch_data)
            
            print(f"✅ تم إضافة {len(sample_branches)} فرع تجريبي")
        else:
            print(f"ℹ️ يوجد {branches_count} فرع في النظام")
        
        # 4. التحقق النهائي من الجدول
        print("\n🔍 التحقق النهائي من جدول الفروع...")
        cursor.execute("PRAGMA table_info(branches)")
        columns = cursor.fetchall()
        print(f"📊 جدول الفروع يحتوي على {len(columns)} عمود:")
        
        required_columns = ['id', 'name', 'name_en', 'code', 'bank_id', 'exchange_id', 'type', 
                           'country', 'city', 'address', 'phone', 'fax', 'email', 'website', 
                           'manager_name', 'manager_phone', 'working_hours', 'services', 'notes', 'is_active']
        
        column_names = [col[1] for col in columns]
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"⚠️ أعمدة مفقودة: {missing_columns}")
        else:
            print("✅ جدول الفروع يحتوي على جميع الأعمدة المطلوبة")
        
        # عرض عينة من البيانات
        cursor.execute("SELECT id, name, type, city FROM branches LIMIT 3")
        sample_data = cursor.fetchall()
        if sample_data:
            print("\n📋 عينة من بيانات الفروع:")
            for row in sample_data:
                print(f"   ID: {row[0]}, الاسم: {row[1]}, النوع: {row[2]}, المدينة: {row[3]}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح جدول الفروع بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول الفروع: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_branch_insertion():
    """اختبار إضافة فرع جديد"""
    
    print("\n🧪 اختبار إضافة فرع جديد...")
    
    db_path = Path("data/proshipment.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # بيانات فرع تجريبي
        test_branch_data = (
            'فرع الاختبار',  # name
            'Test Branch',  # name_en
            'TEST001',  # code
            None,  # bank_id
            None,  # exchange_id
            'اختبار',  # type
            'السعودية',  # country
            'الرياض',  # city
            'شارع الاختبار',  # address
            '011-1234567',  # phone
            '011-1234568',  # fax
            '<EMAIL>',  # email
            'www.testbranch.com',  # website
            'مدير الاختبار',  # manager_name
            '050-1234567',  # manager_phone
            '24/7',  # working_hours
            'خدمات اختبار',  # services
            'فرع تجريبي للاختبار',  # notes
            1  # is_active
        )
        
        cursor.execute("""
            INSERT INTO branches (name, name_en, code, bank_id, exchange_id, type, country, city, address, phone, fax, email, website, manager_name, manager_phone, working_hours, services, notes, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, test_branch_data)
        
        branch_id = cursor.lastrowid
        print(f"✅ تم إضافة فرع جديد بنجاح (ID: {branch_id})")
        
        # التحقق من البيانات المضافة
        cursor.execute("SELECT name, fax, manager_name FROM branches WHERE id = ?", (branch_id,))
        result = cursor.fetchone()
        if result:
            print(f"   الاسم: {result[0]}")
            print(f"   الفاكس: {result[1]}")
            print(f"   المدير: {result[2]}")
        
        # حذف البيانات التجريبية
        cursor.execute("DELETE FROM branches WHERE code = 'TEST001'")
        print("✅ تم حذف البيانات التجريبية")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إضافة الفرع: {str(e)}")
        conn.rollback()
        conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح جدول الفروع...")
    print("=" * 60)
    
    success = fix_branches_table()
    
    if success:
        # اختبار إضافة فرع جديد
        test_success = test_branch_insertion()
        
        if test_success:
            print("\n✅ تم إنجاز الإصلاح والاختبار بنجاح!")
            print("📊 الآن يمكنك:")
            print("   • إضافة فروع جديدة بدون أخطاء")
            print("   • استخدام جميع الحقول المطلوبة")
            print("   • ربط الفروع بالبنوك والصرافات")
        else:
            print("\n⚠️ تم الإصلاح ولكن فشل الاختبار")
    else:
        print("\n❌ فشل في إنجاز الإصلاح!")
    
    print("=" * 60)
