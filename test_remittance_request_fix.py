#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح شاشة طلب الحوالة
Remittance Request Fix Test
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import_and_creation():
    """اختبار الاستيراد وإنشاء النافذة"""
    
    print("🧪 اختبار الاستيراد وإنشاء النافذة...")
    print("=" * 60)
    
    try:
        # محاولة الاستيراد
        print("   📝 محاولة استيراد RemittanceRequestWindow...")
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        print("   ✅ الاستيراد نجح")
        
        # إعداد البيئة للاختبار
        import os
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # محاولة إنشاء النافذة
        print("   🏗️ محاولة إنشاء النافذة...")
        window = RemittanceRequestWindow()
        print("   ✅ إنشاء النافذة نجح")
        
        # فحص الحقول الجديدة
        print("\n   🔍 فحص الحقول الجديدة:")
        
        # البيانات الأساسية
        basic_fields = [
            ('request_date_input', 'حقل التاريخ'),
            ('request_number_input', 'حقل رقم الطلب'),
            ('branch_combo', 'حقل الفرع'),
            ('exchanger_combo', 'حقل الصراف'),
            ('remittance_amount_input', 'حقل مبلغ الحوالة'),
            ('currency_combo', 'حقل العملة'),
            ('transfer_purpose_input', 'حقل الغرض من التحويل')
        ]
        
        for field_name, description in basic_fields:
            if hasattr(window, field_name):
                print(f"      ✅ {description} - موجود")
            else:
                print(f"      ❌ {description} - مفقود")
        
        # معلومات المرسل المحدثة
        print("\n   👤 فحص معلومات المرسل:")
        sender_fields = [
            ('sender_entity_input', 'حقل الجهة'),
            ('sender_fax_input', 'حقل الفاكس'),
            ('sender_mobile_input', 'حقل الموبايل'),
            ('sender_pobox_input', 'حقل ص.ب')
        ]
        
        for field_name, description in sender_fields:
            if hasattr(window, field_name):
                print(f"      ✅ {description} - موجود")
            else:
                print(f"      ❌ {description} - مفقود")
        
        # معلومات المستقبل المحدثة
        print("\n   👥 فحص معلومات المستقبل:")
        receiver_fields = [
            ('receiver_account_input', 'حقل رقم الحساب'),
            ('receiver_bank_input', 'حقل اسم البنك'),
            ('receiver_bank_branch_input', 'حقل فرع البنك'),
            ('receiver_swift_input', 'حقل السويفت')
        ]
        
        for field_name, description in receiver_fields:
            if hasattr(window, field_name):
                print(f"      ✅ {description} - موجود")
            else:
                print(f"      ❌ {description} - مفقود")
        
        # فحص عدم وجود الحقول المحذوفة
        print("\n   🗑️ فحص الحقول المحذوفة:")
        deleted_fields = [
            ('source_currency_combo', 'العملة المرسلة'),
            ('target_currency_combo', 'العملة المستقبلة'),
            ('exchange_rate_input', 'سعر الصرف'),
            ('sender_bank_combo', 'البنك المرسل'),
            ('receiver_bank_combo', 'البنك المستقبل'),
            ('transfer_date_input', 'تاريخ التحويل'),
            ('priority_combo', 'الأولوية')
        ]
        
        for field_name, description in deleted_fields:
            if not hasattr(window, field_name):
                print(f"      ✅ {description} - محذوف بنجاح")
            else:
                print(f"      ❌ {description} - لا يزال موجوداً")
        
        # إغلاق النافذة
        window.close()
        
        print("\n   🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_code_structure():
    """اختبار هيكل الكود"""
    
    print("\n🔍 اختبار هيكل الكود...")
    print("=" * 60)
    
    try:
        # قراءة ملف الكود
        with open("src/ui/remittances/remittance_request_window.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص عدم وجود المراجع المحذوفة
        problematic_refs = [
            'source_currency_combo',
            'target_currency_combo', 
            'exchange_rate_input',
            'sender_bank_combo',
            'receiver_bank_combo',
            'transfer_date_input',
            'priority_combo'
        ]
        
        print("   🔍 فحص المراجع المحذوفة:")
        issues_found = []
        
        for ref in problematic_refs:
            if ref in code:
                issues_found.append(ref)
                print(f"      ❌ {ref} - لا يزال موجوداً في الكود")
            else:
                print(f"      ✅ {ref} - تم حذفه بنجاح")
        
        if issues_found:
            print(f"\n   ⚠️ تم العثور على {len(issues_found)} مرجع مشكوك فيه")
            return False
        else:
            print("\n   ✅ جميع المراجع المحذوفة تم تنظيفها")
        
        # فحص وجود الدوال الجديدة
        new_functions = [
            'create_basic_data_section',
            'load_branches_and_exchangers_data'
        ]
        
        print("\n   🔍 فحص الدوال الجديدة:")
        for func in new_functions:
            if func in code:
                print(f"      ✅ {func} - موجودة")
            else:
                print(f"      ❌ {func} - مفقودة")
        
        # فحص عدم وجود الدوال المحذوفة
        deleted_functions = [
            'create_remittance_details_section',
            'update_exchange_rate',
            'load_banks_data'
        ]
        
        print("\n   🗑️ فحص الدوال المحذوفة:")
        for func in deleted_functions:
            if func not in code:
                print(f"      ✅ {func} - محذوفة بنجاح")
            else:
                print(f"      ❌ {func} - لا تزال موجودة")
        
        print("\n   ✅ فحص هيكل الكود مكتمل")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الكود: {e}")
        return False

def display_fix_summary():
    """عرض ملخص الإصلاح"""
    
    print("\n" + "=" * 70)
    print("🎯 ملخص إصلاح شاشة طلب الحوالة")
    print("=" * 70)
    
    print("\n❌ المشكلة الأصلية:")
    print("   'RemittanceRequestWindow' object has no attribute 'source_currency_combo'")
    
    print("\n🔍 السبب:")
    print("   - وجود مراجع لحقول تم حذفها من التصميم الجديد")
    print("   - دوال تحاول الوصول لحقول غير موجودة")
    print("   - عدم تنظيف الكود من المراجع القديمة")
    
    print("\n✅ الحلول المطبقة:")
    print("   1. حذف جميع المراجع للحقول المحذوفة:")
    print("      - source_currency_combo")
    print("      - target_currency_combo")
    print("      - exchange_rate_input")
    print("      - sender_bank_combo")
    print("      - receiver_bank_combo")
    print("      - transfer_date_input")
    print("      - priority_combo")
    
    print("\n   2. حذف الدوال غير المطلوبة:")
    print("      - update_exchange_rate()")
    print("      - load_banks_data()")
    
    print("\n   3. تحديث الدوال المتبقية:")
    print("      - setup_connections()")
    print("      - load_currencies_data()")
    
    print("\n🚀 النتيجة:")
    print("   ✅ إزالة جميع المراجع المشكلة")
    print("   ✅ تنظيف الكود من الدوال غير المطلوبة")
    print("   ✅ النافذة تعمل بدون أخطاء")
    print("   ✅ التصميم الجديد مكتمل ومتسق")

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح شاشة طلب الحوالة...")
    print("=" * 80)
    
    # اختبار الاستيراد وإنشاء النافذة
    import_success = test_import_and_creation()
    
    # اختبار هيكل الكود
    code_success = test_code_structure()
    
    # عرض ملخص الإصلاح
    display_fix_summary()
    
    # النتيجة النهائية
    if import_success and code_success:
        print("\n🏆 تم إصلاح جميع المشاكل بنجاح!")
        print("✅ النافذة تعمل بدون أخطاء")
        print("✅ جميع الحقول الجديدة موجودة")
        print("✅ الحقول المحذوفة تم تنظيفها")
        print("✅ الكود نظيف ومتسق")
        
        print("\n🎉 شاشة طلب الحوالة جاهزة للاستخدام!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إلى حل")
    
    print("=" * 80)
