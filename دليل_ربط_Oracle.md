# دليل ربط التطبيق بقاعدة بيانات Oracle

## 🎯 نعم، يمكن ربط التطبيق بـ Oracle بسهولة!

التطبيق مبني باستخدام **SQLAlchemy** وهو يدعم Oracle بشكل كامل. إليك الدليل الشامل للربط.

---

## 📋 المتطلبات الأساسية

### **1. قاعدة بيانات Oracle** 🗄️
- Oracle Database 11g أو أحدث
- Oracle XE (Express Edition) للاختبار
- Oracle Cloud Database
- أو أي إصدار Oracle آخر

### **2. مكتبات Python المطلوبة** 📦

```bash
# الطريقة الأولى - cx_Oracle (الأكثر استخداماً)
pip install cx_Oracle

# الطريقة الثانية - oracledb (الأحدث من Oracle)
pip install oracledb

# مكتبة SQLAlchemy (موجودة مسبقاً)
pip install SQLAlchemy
```

### **3. Oracle Instant Client** 🔧

#### **تحميل وتثبيت**:
1. اذهب إلى: https://www.oracle.com/database/technologies/instant-client.html
2. حمل النسخة المناسبة لنظام التشغيل
3. استخرج الملفات إلى مجلد (مثل: `C:\oracle\instantclient_21_8`)
4. أضف المسار إلى متغير البيئة `PATH`

#### **للتحقق من التثبيت**:
```bash
# في Windows
echo %PATH%

# في Linux/Mac
echo $PATH
```

---

## ⚙️ خطوات الربط

### **الخطوة 1: إعداد إعدادات الاتصال** 🔗

إنشاء ملف `oracle_config.py`:

```python
# إعدادات الاتصال بـ Oracle
ORACLE_CONFIG = {
    'host': 'localhost',           # عنوان الخادم
    'port': 1521,                 # المنفذ (افتراضي 1521)
    'service_name': 'XE',         # اسم الخدمة
    'username': 'proshipment',    # اسم المستخدم
    'password': 'your_password'   # كلمة المرور
}

# أو للاتصال بـ Oracle Cloud
ORACLE_CLOUD_CONFIG = {
    'host': 'your-cloud-host.oraclecloud.com',
    'port': 1522,
    'service_name': 'your_service_name',
    'username': 'your_username',
    'password': 'your_password'
}
```

### **الخطوة 2: تعديل main.py** 🔄

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الشحنات المتكامل - مع دعم Oracle
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, Qt
from PySide6.QtGui import QFont

from src.ui.main_window import MainWindow
from database_manager_oracle_support import UniversalDatabaseManager, create_oracle_config
from src.utils.arabic_support import setup_arabic_support

def main():
    """دالة تشغيل البرنامج الرئيسية"""
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إعداد دعم اللغة العربية
        setup_arabic_support(app)
        
        # إعداد قاعدة البيانات - Oracle
        oracle_config = create_oracle_config(
            host='localhost',
            port=1521,
            service_name='XE',
            username='proshipment',
            password='your_password'
        )
        
        # إنشاء مدير قاعدة البيانات
        db_manager = UniversalDatabaseManager(oracle_config)
        
        # اختبار الاتصال
        if not db_manager.test_connection():
            print("❌ فشل في الاتصال بـ Oracle")
            return
        
        # تهيئة قاعدة البيانات
        db_manager.initialize_database()
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
```

### **الخطوة 3: اختبار الاتصال** 🧪

```python
# ملف test_oracle_connection.py
from database_manager_oracle_support import UniversalDatabaseManager, create_oracle_config

def test_oracle():
    """اختبار الاتصال بـ Oracle"""
    
    print("🔄 اختبار الاتصال بـ Oracle...")
    
    # إعدادات الاتصال
    config = create_oracle_config(
        host='localhost',
        port=1521,
        service_name='XE',
        username='proshipment',
        password='your_password'
    )
    
    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = UniversalDatabaseManager(config)
        
        # اختبار الاتصال
        if db_manager.test_connection():
            print("✅ نجح الاتصال بـ Oracle!")
            
            # تهيئة قاعدة البيانات
            if db_manager.initialize_database():
                print("✅ تم إنشاء الجداول بنجاح!")
            else:
                print("❌ فشل في إنشاء الجداول")
        else:
            print("❌ فشل في الاتصال بـ Oracle")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_oracle()
```

---

## 🔧 إعدادات Oracle المتقدمة

### **1. إنشاء مستخدم Oracle** 👤

```sql
-- الاتصال كـ SYSTEM أو DBA
sqlplus system/password@localhost:1521/XE

-- إنشاء مستخدم جديد
CREATE USER proshipment IDENTIFIED BY your_password;

-- منح الصلاحيات
GRANT CONNECT, RESOURCE, DBA TO proshipment;
GRANT CREATE SESSION TO proshipment;
GRANT CREATE TABLE TO proshipment;
GRANT CREATE SEQUENCE TO proshipment;
GRANT CREATE VIEW TO proshipment;

-- منح مساحة تخزين
ALTER USER proshipment QUOTA UNLIMITED ON USERS;

-- تأكيد الإنشاء
SELECT username FROM all_users WHERE username = 'PROSHIPMENT';
```

### **2. إعدادات الأداء** ⚡

```python
# في database_manager_oracle_support.py
self.engine = create_engine(
    connection_string,
    echo=False,                    # إظهار SQL queries للتطوير
    pool_size=20,                  # حجم pool الاتصالات
    max_overflow=30,               # الحد الأقصى للاتصالات الإضافية
    pool_pre_ping=True,            # فحص الاتصال قبل الاستخدام
    pool_recycle=3600,             # إعادة تدوير الاتصالات (ثانية)
    connect_args={
        "encoding": "UTF-8",       # ترميز UTF-8
        "nencoding": "UTF-8",      # ترميز الأرقام
        "threaded": True           # دعم multi-threading
    }
)
```

### **3. إعدادات الأمان** 🔒

```python
# استخدام متغيرات البيئة لكلمات المرور
import os

ORACLE_CONFIG = {
    'host': os.getenv('ORACLE_HOST', 'localhost'),
    'port': int(os.getenv('ORACLE_PORT', 1521)),
    'service_name': os.getenv('ORACLE_SERVICE', 'XE'),
    'username': os.getenv('ORACLE_USER', 'proshipment'),
    'password': os.getenv('ORACLE_PASSWORD', 'default_password')
}
```

---

## 📊 مقارنة SQLite vs Oracle

| الميزة | SQLite | Oracle |
|--------|--------|--------|
| **الحجم** | ملف واحد | خادم كامل |
| **الأداء** | جيد للتطبيقات الصغيرة | ممتاز للتطبيقات الكبيرة |
| **التزامن** | محدود | ممتاز |
| **الأمان** | أساسي | متقدم جداً |
| **النسخ الاحتياطي** | نسخ الملف | أدوات متقدمة |
| **التكلفة** | مجاني | مدفوع (عدا XE) |
| **الصيانة** | بسيطة | تحتاج خبرة |

---

## 🚀 خطوات التحويل من SQLite إلى Oracle

### **1. نسخ البيانات الموجودة** 📋

```python
# ملف migrate_to_oracle.py
def migrate_sqlite_to_oracle():
    """نقل البيانات من SQLite إلى Oracle"""
    
    # الاتصال بـ SQLite
    sqlite_config = {'type': 'sqlite', 'path': 'data/proshipment.db'}
    sqlite_manager = UniversalDatabaseManager(sqlite_config)
    
    # الاتصال بـ Oracle
    oracle_config = create_oracle_config(
        host='localhost', port=1521, service_name='XE',
        username='proshipment', password='your_password'
    )
    oracle_manager = UniversalDatabaseManager(oracle_config)
    
    # تهيئة Oracle
    oracle_manager.initialize_database()
    
    # نقل البيانات
    sqlite_session = sqlite_manager.get_session()
    oracle_session = oracle_manager.get_session()
    
    try:
        from src.database.models import Company, Supplier, Item, Shipment
        
        # نقل الشركات
        companies = sqlite_session.query(Company).all()
        for company in companies:
            oracle_session.merge(company)
        
        # نقل الموردين
        suppliers = sqlite_session.query(Supplier).all()
        for supplier in suppliers:
            oracle_session.merge(supplier)
        
        # نقل الأصناف
        items = sqlite_session.query(Item).all()
        for item in items:
            oracle_session.merge(item)
        
        # نقل الشحنات
        shipments = sqlite_session.query(Shipment).all()
        for shipment in shipments:
            oracle_session.merge(shipment)
        
        oracle_session.commit()
        print("✅ تم نقل البيانات بنجاح!")
        
    except Exception as e:
        oracle_session.rollback()
        print(f"❌ خطأ في نقل البيانات: {e}")
    finally:
        sqlite_session.close()
        oracle_session.close()
```

### **2. تحديث ملفات التكوين** ⚙️

```python
# في src/ui/main_window.py - تحديث الاتصال
def __init__(self):
    super().__init__()
    
    # استخدام Oracle بدلاً من SQLite
    from database_manager_oracle_support import UniversalDatabaseManager, create_oracle_config
    
    oracle_config = create_oracle_config(
        host='localhost',
        port=1521,
        service_name='XE',
        username='proshipment',
        password='your_password'
    )
    
    self.db_manager = UniversalDatabaseManager(oracle_config)
```

---

## 🛠️ حل المشاكل الشائعة

### **1. خطأ "Oracle client library not found"** ❌

**الحل**:
```bash
# تأكد من تثبيت Oracle Instant Client
# أضف مسار Instant Client إلى PATH
# في Windows:
set PATH=%PATH%;C:\oracle\instantclient_21_8

# في Linux:
export LD_LIBRARY_PATH=/opt/oracle/instantclient_21_8:$LD_LIBRARY_PATH
```

### **2. خطأ "TNS: could not resolve the connect identifier"** ❌

**الحل**:
```python
# تأكد من صحة إعدادات الاتصال
# جرب استخدام SID بدلاً من service_name
connection_string = (
    f"oracle+cx_oracle://{username}:{password}@"
    f"{host}:{port}/{sid}"  # استخدم SID
)
```

### **3. خطأ "ORA-12541: TNS:no listener"** ❌

**الحل**:
- تأكد من تشغيل Oracle Database
- تأكد من صحة المنفذ (عادة 1521)
- تأكد من إعدادات الـ Firewall

### **4. مشاكل الترميز العربي** 🔤

**الحل**:
```python
# إضافة إعدادات الترميز
connect_args = {
    "encoding": "UTF-8",
    "nencoding": "UTF-8"
}
```

---

## 📈 مزايا استخدام Oracle

### **1. الأداء العالي** ⚡
- دعم ملايين السجلات
- فهرسة متقدمة
- تحسين الاستعلامات تلقائياً

### **2. الأمان المتقدم** 🔒
- تشفير البيانات
- صلاحيات مفصلة
- مراجعة العمليات

### **3. الموثوقية** 🛡️
- نسخ احتياطية متقدمة
- استرداد البيانات
- تحمل الأخطاء

### **4. التوسع** 📈
- دعم عدة مستخدمين
- توزيع البيانات
- تجميع الخوادم

---

## 🎯 الخلاصة

**نعم، يمكن ربط التطبيق بـ Oracle بسهولة!** 

### **الخطوات الأساسية**:
1. ✅ تثبيت `cx_Oracle` أو `oracledb`
2. ✅ تثبيت Oracle Instant Client
3. ✅ استخدام `database_manager_oracle_support.py`
4. ✅ تحديث إعدادات الاتصال
5. ✅ اختبار الاتصال والتشغيل

### **المزايا**:
- 🚀 أداء أفضل للبيانات الكبيرة
- 🔒 أمان متقدم
- 👥 دعم عدة مستخدمين
- 📊 إمكانيات تحليل متقدمة

**التطبيق مصمم ليكون مرن ويدعم قواعد بيانات متعددة!** 🎉
