#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة إعدادات التصميم المبسطة
Test Simple Design Settings Dialog
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_simple_design_settings():
    """اختبار نافذة إعدادات التصميم المبسطة"""
    print("🧪 اختبار نافذة إعدادات التصميم المبسطة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.dialogs.simple_design_settings_dialog import SimpleDesignSettingsDialog
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة إعدادات التصميم
        dialog = SimpleDesignSettingsDialog()
        
        print("   ✅ تم إنشاء نافذة إعدادات التصميم المبسطة")
        
        # التحقق من عنوان النافذة
        window_title = dialog.windowTitle()
        if "إعدادات التصميم" in window_title:
            print(f"   ✅ عنوان النافذة صحيح: {window_title}")
        else:
            print(f"   ⚠️ عنوان النافذة: {window_title}")
        
        # التحقق من التبويبات
        if hasattr(dialog, 'tab_widget'):
            tab_count = dialog.tab_widget.count()
            print(f"   ✅ عدد التبويبات: {tab_count}")
            
            # عرض أسماء التبويبات
            for i in range(tab_count):
                tab_name = dialog.tab_widget.tabText(i)
                print(f"      {i+1}. {tab_name}")
        
        # التحقق من العناصر
        elements = [
            ('theme_combo', 'قائمة الثيمات'),
            ('color_scheme_combo', 'قائمة مخططات الألوان'),
            ('responsive_enabled', 'خيار التصميم المتجاوب'),
            ('font_size_spin', 'حجم الخط'),
            ('font_scale_slider', 'معامل تكبير الخط')
        ]
        
        for element_name, description in elements:
            if hasattr(dialog, element_name):
                print(f"   ✅ {description} موجود")
            else:
                print(f"   ❌ {description} غير موجود")
        
        # اختبار الدوال
        functions = [
            ('apply_settings', 'تطبيق الإعدادات'),
            ('preview_settings', 'معاينة الإعدادات'),
            ('reset_settings', 'إعادة تعيين الإعدادات')
        ]
        
        for func_name, description in functions:
            if hasattr(dialog, func_name):
                print(f"   ✅ دالة {description} موجودة")
            else:
                print(f"   ❌ دالة {description} غير موجودة")
        
        # إغلاق النافذة
        dialog.close()
        print("   ✅ تم إغلاق النافذة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النافذة المبسطة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """اختبار دمج النافذة في التطبيق الرئيسي"""
    print("\n🧪 اختبار دمج النافذة في التطبيق الرئيسي...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        print("   ✅ تم إنشاء النافذة الرئيسية")
        
        # محاولة فتح نافذة إعدادات التصميم
        main_window.open_design_settings()
        
        # التحقق من إنشاء النافذة
        if hasattr(main_window, 'design_settings_window') and main_window.design_settings_window:
            print("   ✅ تم إنشاء نافذة إعدادات التصميم من النافذة الرئيسية")
            
            # التحقق من نوع النافذة
            window_type = type(main_window.design_settings_window).__name__
            print(f"   📋 نوع النافذة: {window_type}")
            
            # إغلاق النافذة
            main_window.design_settings_window.close()
            print("   ✅ تم إغلاق النافذة بنجاح")
            
            return True
        else:
            print("   ❌ فشل في إنشاء نافذة إعدادات التصميم")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الدمج: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نافذة إعدادات التصميم المبسطة")
    print("="*50)
    
    tests = [
        ("النافذة المبسطة", test_simple_design_settings),
        ("الدمج في التطبيق الرئيسي", test_main_window_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*50)
    print("📊 نتائج اختبار نافذة إعدادات التصميم")
    print("="*50)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.8:
        print("\n🎉 نافذة إعدادات التصميم تعمل بامتياز!")
        print("✅ يمكن الوصول إليها من:")
        print("   📋 قائمة العرض → ⚙️ إعدادات التصميم")
        print("   🌳 القائمة الجانبية → الإعدادات → إعدادات التصميم")
        status = "ممتاز"
    elif success_rate >= 0.5:
        print("\n✅ نافذة إعدادات التصميم تعمل بشكل جيد")
        print("⚠️ بعض الميزات قد تحتاج مراجعة")
        status = "جيد"
    else:
        print("\n⚠️ نافذة إعدادات التصميم تحتاج إصلاحات")
        print("🔧 راجع الأخطاء أعلاه")
        status = "يحتاج إصلاح"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    # إرشادات للمستخدم
    if success_rate >= 0.5:
        print(f"\n📋 كيفية الوصول إلى إعدادات التصميم:")
        print(f"   1️⃣ من شريط القوائم: العرض → ⚙️ إعدادات التصميم")
        print(f"   2️⃣ من القائمة الجانبية: الإعدادات → إعدادات التصميم")
        
        print(f"\n🎨 الميزات المتاحة:")
        print(f"   🌈 تغيير مخطط الألوان (6 مخططات)")
        print(f"   🌙 التبديل بين الثيم الفاتح والمظلم")
        print(f"   📱 إعدادات التصميم المتجاوب")
        print(f"   🔤 تخصيص أحجام الخطوط")
        print(f"   📊 معلومات الشاشة الحالية")
    
    return success_rate >= 0.5

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
