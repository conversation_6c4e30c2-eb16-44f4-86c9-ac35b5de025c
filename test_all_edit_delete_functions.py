#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع نوافذ التعديل والحذف
Comprehensive Test for All Edit and Delete Functions
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد جميع النوافذ الجديدة"""
    
    print("🧪 اختبار استيراد النوافذ الجديدة...")
    print("=" * 60)
    
    try:
        # اختبار استيراد نوافذ التعديل
        print("📝 اختبار نوافذ التعديل:")
        
        from src.ui.remittances.edit_bank_dialog import EditBankDialog
        print("   ✅ EditBankDialog - تم الاستيراد بنجاح")
        
        from src.ui.remittances.edit_exchange_dialog import EditExchangeDialog
        print("   ✅ EditExchangeDialog - تم الاستيراد بنجاح")
        
        from src.ui.remittances.edit_branch_dialog import EditBranchDialog
        print("   ✅ EditBranchDialog - تم الاستيراد بنجاح")
        
        # اختبار استيراد نوافذ الحذف
        print("\n🗑️ اختبار نوافذ الحذف:")
        
        from src.ui.remittances.delete_bank_dialog import DeleteBankDialog
        print("   ✅ DeleteBankDialog - تم الاستيراد بنجاح")
        
        from src.ui.remittances.delete_exchange_dialog import DeleteExchangeDialog
        print("   ✅ DeleteExchangeDialog - تم الاستيراد بنجاح")
        
        from src.ui.remittances.delete_branch_dialog import DeleteBranchDialog
        print("   ✅ DeleteBranchDialog - تم الاستيراد بنجاح")
        
        # اختبار استيراد النافذة الرئيسية المحدثة
        print("\n🏠 اختبار النافذة الرئيسية:")
        
        from src.ui.remittances.banks_management_window import BanksManagementWindow
        print("   ✅ BanksManagementWindow - تم الاستيراد بنجاح")
        
        print("\n🎉 جميع الاستيرادات نجحت!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاستيراد: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_creation():
    """اختبار إنشاء النوافذ"""
    
    print("\n🏗️ اختبار إنشاء النوافذ...")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار إنشاء نوافذ التعديل
        print("📝 اختبار إنشاء نوافذ التعديل:")
        
        from src.ui.remittances.edit_bank_dialog import EditBankDialog
        edit_bank_dialog = EditBankDialog(1)  # معرف تجريبي
        print("   ✅ EditBankDialog - تم الإنشاء بنجاح")
        edit_bank_dialog.close()
        
        from src.ui.remittances.edit_exchange_dialog import EditExchangeDialog
        edit_exchange_dialog = EditExchangeDialog(1)  # معرف تجريبي
        print("   ✅ EditExchangeDialog - تم الإنشاء بنجاح")
        edit_exchange_dialog.close()
        
        from src.ui.remittances.edit_branch_dialog import EditBranchDialog
        edit_branch_dialog = EditBranchDialog(1)  # معرف تجريبي
        print("   ✅ EditBranchDialog - تم الإنشاء بنجاح")
        edit_branch_dialog.close()
        
        # اختبار إنشاء نوافذ الحذف
        print("\n🗑️ اختبار إنشاء نوافذ الحذف:")
        
        from src.ui.remittances.delete_bank_dialog import DeleteBankDialog
        delete_bank_dialog = DeleteBankDialog(1, "بنك تجريبي")
        print("   ✅ DeleteBankDialog - تم الإنشاء بنجاح")
        delete_bank_dialog.close()
        
        from src.ui.remittances.delete_exchange_dialog import DeleteExchangeDialog
        delete_exchange_dialog = DeleteExchangeDialog(1, "صراف تجريبي")
        print("   ✅ DeleteExchangeDialog - تم الإنشاء بنجاح")
        delete_exchange_dialog.close()
        
        from src.ui.remittances.delete_branch_dialog import DeleteBranchDialog
        delete_branch_dialog = DeleteBranchDialog(1, "فرع تجريبي")
        print("   ✅ DeleteBranchDialog - تم الإنشاء بنجاح")
        delete_branch_dialog.close()
        
        print("\n🎉 جميع النوافذ تم إنشاؤها بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء النوافذ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """اختبار تكامل النافذة الرئيسية"""
    
    print("\n🔗 اختبار تكامل النافذة الرئيسية...")
    print("=" * 60)
    
    try:
        from src.ui.remittances.banks_management_window import BanksManagementWindow
        
        # إنشاء النافذة الرئيسية
        main_window = BanksManagementWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود الدوال الجديدة
        functions_to_check = [
            'edit_selected_bank',
            'delete_selected_bank',
            'edit_selected_exchange', 
            'delete_selected_exchange',
            'edit_selected_branch',
            'delete_selected_branch',
            'on_bank_updated',
            'on_bank_deleted',
            'on_exchange_updated',
            'on_exchange_deleted',
            'on_branch_updated',
            'on_branch_deleted'
        ]
        
        print("\n📋 التحقق من الدوال الجديدة:")
        for func_name in functions_to_check:
            if hasattr(main_window, func_name):
                print(f"   ✅ {func_name} - موجودة")
            else:
                print(f"   ❌ {func_name} - مفقودة")
        
        main_window.close()
        
        print("\n🎉 تكامل النافذة الرئيسية نجح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في تكامل النافذة الرئيسية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def display_summary():
    """عرض ملخص الوظائف الجديدة"""
    
    print("\n📋 ملخص الوظائف الجديدة المضافة:")
    print("=" * 70)
    
    features = [
        "🏦 نوافذ البنوك:",
        "   ✅ EditBankDialog - نافذة تعديل البنك مع جميع الحقول",
        "   ✅ DeleteBankDialog - نافذة تأكيد حذف البنك مع فحص التبعيات",
        "   ✅ edit_selected_bank() - دالة تعديل البنك المحدد",
        "   ✅ delete_selected_bank() - دالة حذف البنك المحدد",
        "",
        "💱 نوافذ الصرافات:",
        "   ✅ EditExchangeDialog - نافذة تعديل الصراف مع جميع الحقول",
        "   ✅ DeleteExchangeDialog - نافذة تأكيد حذف الصراف مع فحص التبعيات",
        "   ✅ edit_selected_exchange() - دالة تعديل الصراف المحدد",
        "   ✅ delete_selected_exchange() - دالة حذف الصراف المحدد",
        "",
        "🏢 نوافذ الفروع:",
        "   ✅ EditBranchDialog - نافذة تعديل الفرع مع جميع الحقول",
        "   ✅ DeleteBranchDialog - نافذة تأكيد حذف الفرع مع فحص التبعيات",
        "   ✅ edit_selected_branch() - دالة تعديل الفرع المحدد",
        "   ✅ delete_selected_branch() - دالة حذف الفرع المحدد",
        "",
        "🔄 معالجات الأحداث:",
        "   ✅ on_bank_updated() - معالج تحديث البنك",
        "   ✅ on_bank_deleted() - معالج حذف البنك",
        "   ✅ on_exchange_updated() - معالج تحديث الصراف",
        "   ✅ on_exchange_deleted() - معالج حذف الصراف",
        "   ✅ on_branch_updated() - معالج تحديث الفرع",
        "   ✅ on_branch_deleted() - معالج حذف الفرع",
        "",
        "🛡️ ميزات الأمان:",
        "   ✅ فحص التبعيات قبل الحذف",
        "   ✅ تأكيد مزدوج للحذف",
        "   ✅ حذف آمن (soft delete)",
        "   ✅ رسائل تحذير واضحة",
        "",
        "🎨 ميزات الواجهة:",
        "   ✅ تصميم عصري ومتجاوب",
        "   ✅ أيقونات تعبيرية",
        "   ✅ رسائل واضحة بالعربية",
        "   ✅ تحديث تلقائي للقوائم",
        "   ✅ شريط تقدم أثناء العمليات"
    ]
    
    for feature in features:
        print(feature)

if __name__ == "__main__":
    print("🚀 بدء الاختبار الشامل لجميع نوافذ التعديل والحذف...")
    print("=" * 80)
    
    # اختبار الاستيرادات
    imports_success = test_imports()
    
    # اختبار إنشاء النوافذ
    creation_success = test_dialog_creation()
    
    # اختبار تكامل النافذة الرئيسية
    integration_success = test_main_window_integration()
    
    # عرض الملخص
    display_summary()
    
    # النتيجة النهائية
    if imports_success and creation_success and integration_success:
        print("\n🏆 جميع الاختبارات نجحت!")
        print("✅ جميع نوافذ التعديل والحذف تعمل بشكل صحيح")
        print("✅ التكامل مع النافذة الرئيسية مكتمل")
        print("✅ التطبيق جاهز للاستخدام الكامل")
        
        print("\n🎯 الآن يمكنك:")
        print("   • تعديل البنوك والصرافات والفروع")
        print("   • حذف البنوك والصرافات والفروع بأمان")
        print("   • رؤية التحديثات فوراً في القوائم")
        print("   • العمل بثقة تامة بدون رسائل 'قيد التطوير'")
        
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 80)
