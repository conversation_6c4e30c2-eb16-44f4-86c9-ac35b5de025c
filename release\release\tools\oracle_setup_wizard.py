#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالج إعداد Oracle - ProShipment V2.0.0
Oracle Setup Wizard
"""

import sys
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.oracle_config import (
    OracleConfig, OracleConnectionType, DatabaseConfig, DatabaseType,
    DatabaseConfigManager
)

class OracleSetupWizard:
    """معالج إعداد Oracle التفاعلي"""
    
    def __init__(self):
        self.config = None
        self.config_manager = DatabaseConfigManager()
    
    def run_wizard(self) -> bool:
        """تشغيل المعالج التفاعلي"""
        print("🧙‍♂️ معالج إعداد Oracle - ProShipment V2.0.0")
        print("="*60)
        print("هذا المعالج سيساعدك في إعداد الاتصال مع Oracle")
        print()
        
        try:
            # الخطوة 1: اختيار نوع الإعداد
            setup_type = self._choose_setup_type()
            
            if setup_type == "1":
                # إعداد سريع
                return self._quick_setup()
            elif setup_type == "2":
                # إعداد متقدم
                return self._advanced_setup()
            elif setup_type == "3":
                # إعداد من ملف
                return self._setup_from_file()
            elif setup_type == "4":
                # اختبار إعداد موجود
                return self._test_existing_setup()
            else:
                print("❌ خيار غير صحيح")
                return False
                
        except KeyboardInterrupt:
            print("\n\n⚠️ تم إلغاء الإعداد بواسطة المستخدم")
            return False
        except Exception as e:
            print(f"\n❌ خطأ في المعالج: {e}")
            return False
    
    def _choose_setup_type(self) -> str:
        """اختيار نوع الإعداد"""
        print("📋 اختر نوع الإعداد:")
        print("1. إعداد سريع (للمبتدئين)")
        print("2. إعداد متقدم (للخبراء)")
        print("3. استيراد من ملف")
        print("4. اختبار إعداد موجود")
        print()
        
        while True:
            choice = input("اختر رقم (1-4): ").strip()
            if choice in ["1", "2", "3", "4"]:
                return choice
            print("❌ يرجى اختيار رقم صحيح (1-4)")
    
    def _quick_setup(self) -> bool:
        """الإعداد السريع"""
        print("\n🚀 الإعداد السريع لـ Oracle")
        print("-" * 40)
        
        # إعدادات افتراضية
        default_configs = {
            "Oracle XE المحلي": {
                "host": "localhost",
                "port": 1521,
                "service_name": "XE",
                "username": "proshipment",
                "password": ""
            },
            "Oracle Cloud": {
                "host": "",
                "port": 1522,
                "service_name": "",
                "username": "",
                "password": ""
            },
            "Oracle Enterprise": {
                "host": "localhost",
                "port": 1521,
                "service_name": "ORCL",
                "username": "proshipment",
                "password": ""
            }
        }
        
        print("اختر نوع Oracle:")
        for i, config_name in enumerate(default_configs.keys(), 1):
            print(f"{i}. {config_name}")
        
        while True:
            try:
                choice = int(input("\nاختر رقم: ").strip())
                if 1 <= choice <= len(default_configs):
                    config_name = list(default_configs.keys())[choice - 1]
                    base_config = default_configs[config_name]
                    break
                else:
                    print("❌ رقم غير صحيح")
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح")
        
        print(f"\n📝 إعداد {config_name}:")
        
        # جمع البيانات المطلوبة
        config_data = {}
        for key, default_value in base_config.items():
            if key == "password":
                import getpass
                value = getpass.getpass(f"{key} (مخفي): ")
            else:
                prompt = f"{key}"
                if default_value:
                    prompt += f" [{default_value}]"
                prompt += ": "
                
                value = input(prompt).strip()
                if not value and default_value:
                    value = default_value
            
            config_data[key] = value
        
        # إنشاء إعدادات Oracle
        oracle_config = OracleConfig(
            host=config_data["host"],
            port=int(config_data["port"]),
            service_name=config_data["service_name"],
            username=config_data["username"],
            password=config_data["password"]
        )
        
        return self._save_and_test_config(oracle_config)
    
    def _advanced_setup(self) -> bool:
        """الإعداد المتقدم"""
        print("\n🔧 الإعداد المتقدم لـ Oracle")
        print("-" * 40)
        
        # جمع جميع الإعدادات
        config_data = {}
        
        # إعدادات الاتصال الأساسية
        print("📡 إعدادات الاتصال:")
        config_data["host"] = input("عنوان الخادم [localhost]: ").strip() or "localhost"
        config_data["port"] = int(input("المنفذ [1521]: ").strip() or "1521")
        
        # نوع الاتصال
        print("\nنوع الاتصال:")
        print("1. Service Name (الافتراضي)")
        print("2. SID")
        print("3. TNS")
        
        connection_type_choice = input("اختر نوع الاتصال [1]: ").strip() or "1"
        
        if connection_type_choice == "1":
            config_data["connection_type"] = OracleConnectionType.SERVICE_NAME
            config_data["service_name"] = input("اسم الخدمة [XE]: ").strip() or "XE"
            config_data["sid"] = None
        elif connection_type_choice == "2":
            config_data["connection_type"] = OracleConnectionType.SID
            config_data["sid"] = input("SID [ORCL]: ").strip() or "ORCL"
            config_data["service_name"] = None
        else:
            config_data["connection_type"] = OracleConnectionType.TNS
            config_data["service_name"] = input("TNS Name: ").strip()
            config_data["sid"] = None
        
        # بيانات المستخدم
        print("\n👤 بيانات المستخدم:")
        config_data["username"] = input("اسم المستخدم [proshipment]: ").strip() or "proshipment"
        
        import getpass
        config_data["password"] = getpass.getpass("كلمة المرور: ")
        
        # إعدادات الأداء
        print("\n⚡ إعدادات الأداء:")
        config_data["pool_size"] = int(input("حجم pool الاتصالات [20]: ").strip() or "20")
        config_data["max_overflow"] = int(input("الحد الأقصى للاتصالات الإضافية [30]: ").strip() or "30")
        config_data["pool_timeout"] = int(input("مهلة الاتصال (ثانية) [30]: ").strip() or "30")
        config_data["pool_recycle"] = int(input("إعادة تدوير الاتصالات (ثانية) [3600]: ").strip() or "3600")
        
        # إعدادات الأمان
        print("\n🔒 إعدادات الأمان:")
        use_ssl = input("استخدام SSL؟ (y/n) [n]: ").strip().lower()
        config_data["use_ssl"] = use_ssl in ["y", "yes", "نعم"]
        
        if config_data["use_ssl"]:
            config_data["wallet_location"] = input("مسار Oracle Wallet (اختياري): ").strip() or None
            config_data["ssl_cert_path"] = input("مسار شهادة SSL (اختياري): ").strip() or None
        else:
            config_data["wallet_location"] = None
            config_data["ssl_cert_path"] = None
        
        # إنشاء إعدادات Oracle
        oracle_config = OracleConfig(**config_data)
        
        return self._save_and_test_config(oracle_config)
    
    def _setup_from_file(self) -> bool:
        """إعداد من ملف"""
        print("\n📁 استيراد إعدادات من ملف")
        print("-" * 40)
        
        file_path = input("مسار ملف الإعدادات (JSON): ").strip()
        
        if not file_path:
            print("❌ لم يتم تحديد مسار الملف")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # التحقق من وجود إعدادات Oracle
            if 'oracle_config' in data:
                oracle_data = data['oracle_config']
            elif 'host' in data:  # ملف إعدادات Oracle مباشر
                oracle_data = data
            else:
                print("❌ ملف الإعدادات لا يحتوي على إعدادات Oracle")
                return False
            
            # إنشاء إعدادات Oracle
            oracle_config = OracleConfig.from_dict(oracle_data)
            
            print("✅ تم تحميل الإعدادات من الملف بنجاح")
            return self._save_and_test_config(oracle_config)
            
        except FileNotFoundError:
            print(f"❌ الملف غير موجود: {file_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ خطأ في تحليل ملف JSON: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
            return False
    
    def _test_existing_setup(self) -> bool:
        """اختبار إعداد موجود"""
        print("\n🧪 اختبار إعداد Oracle موجود")
        print("-" * 40)
        
        try:
            # تحميل الإعدادات الحالية
            config = self.config_manager.load_config()
            
            if config.type != DatabaseType.ORACLE:
                print("⚠️ نوع قاعدة البيانات الحالي ليس Oracle")
                
                # تغيير إلى Oracle
                change = input("هل تريد تغيير نوع قاعدة البيانات إلى Oracle؟ (y/n): ").strip().lower()
                if change in ["y", "yes", "نعم"]:
                    self.config_manager.set_database_type(DatabaseType.ORACLE)
                    config = self.config_manager.load_config()
                else:
                    return False
            
            # اختبار الاتصال
            return self._test_oracle_connection(config.oracle_config)
            
        except Exception as e:
            print(f"❌ خطأ في اختبار الإعداد الموجود: {e}")
            return False
    
    def _save_and_test_config(self, oracle_config: OracleConfig) -> bool:
        """حفظ واختبار الإعدادات"""
        print("\n💾 حفظ الإعدادات...")
        
        try:
            # إنشاء إعدادات قاعدة البيانات
            db_config = DatabaseConfig(
                type=DatabaseType.ORACLE,
                oracle_config=oracle_config
            )
            
            # حفظ الإعدادات
            self.config_manager._config = db_config
            if self.config_manager.save_config():
                print("✅ تم حفظ الإعدادات بنجاح")
            else:
                print("⚠️ تحذير: فشل في حفظ الإعدادات")
            
            # اختبار الاتصال
            return self._test_oracle_connection(oracle_config)
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الإعدادات: {e}")
            return False
    
    def _test_oracle_connection(self, oracle_config: OracleConfig) -> bool:
        """اختبار اتصال Oracle"""
        print("\n🔌 اختبار الاتصال مع Oracle...")
        
        try:
            from src.database.universal_database_manager import UniversalDatabaseManager
            
            # إنشاء إعدادات قاعدة البيانات
            db_config = DatabaseConfig(
                type=DatabaseType.ORACLE,
                oracle_config=oracle_config
            )
            
            # إنشاء مدير قاعدة البيانات
            db_manager = UniversalDatabaseManager(db_config)
            
            # اختبار الاتصال
            if db_manager.test_connection():
                print("✅ نجح الاتصال مع Oracle!")
                
                # اختبار تهيئة قاعدة البيانات
                print("🔄 اختبار تهيئة قاعدة البيانات...")
                if db_manager.initialize_database():
                    print("✅ تم تهيئة قاعدة البيانات بنجاح!")
                    
                    # عرض معلومات قاعدة البيانات
                    db_info = db_manager.get_database_info()
                    print(f"📊 معلومات قاعدة البيانات: {db_info}")
                    
                    return True
                else:
                    print("❌ فشل في تهيئة قاعدة البيانات")
                    return False
            else:
                print("❌ فشل في الاتصال مع Oracle")
                self._show_connection_troubleshooting()
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الاتصال: {e}")
            self._show_connection_troubleshooting()
            return False
    
    def _show_connection_troubleshooting(self):
        """عرض نصائح حل مشاكل الاتصال"""
        print("\n🔧 نصائح حل مشاكل الاتصال:")
        print("1. تأكد من تشغيل Oracle Database")
        print("2. تأكد من صحة عنوان الخادم والمنفذ")
        print("3. تأكد من صحة اسم المستخدم وكلمة المرور")
        print("4. تأكد من تثبيت Oracle Instant Client")
        print("5. تأكد من إعدادات الشبكة والـ Firewall")
        print("6. تحقق من سجلات Oracle للمزيد من التفاصيل")

def main():
    """الدالة الرئيسية"""
    wizard = OracleSetupWizard()
    success = wizard.run_wizard()
    
    if success:
        print("\n🎉 تم إعداد Oracle بنجاح!")
        print("✅ يمكنك الآن استخدام التطبيق مع Oracle")
    else:
        print("\n⚠️ لم يتم إكمال إعداد Oracle")
        print("💡 يمكنك إعادة تشغيل المعالج لاحقاً")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
