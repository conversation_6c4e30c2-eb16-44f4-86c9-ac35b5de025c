#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ast
import sys
import os

def test_syntax_and_import():
    """اختبار نهائي للـ syntax والاستيراد"""
    print("🔍 اختبار نهائي للـ syntax والاستيراد...")
    print("=" * 60)
    
    file_path = "src/ui/remittances/new_remittance_dialog.py"
    
    # 1. فحص الـ syntax
    print("1️⃣ فحص الـ syntax...")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        print("   ✅ لا توجد أخطاء syntax!")
        syntax_ok = True
    except SyntaxError as e:
        print(f"   ❌ خطأ syntax في السطر {e.lineno}: {e.msg}")
        syntax_ok = False
    except Exception as e:
        print(f"   ❌ خطأ في قراءة الملف: {e}")
        syntax_ok = False
    
    if not syntax_ok:
        return False
    
    # 2. اختبار الاستيراد
    print("\n2️⃣ اختبار الاستيراد...")
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        print("   ✅ تم استيراد الكلاس بنجاح!")
        import_ok = True
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        import_ok = False
    
    if not import_ok:
        return False
    
    # 3. اختبار إنشاء الكلاس
    print("\n3️⃣ اختبار إنشاء الكلاس...")
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        dialog = NewRemittanceDialog()
        print("   ✅ تم إنشاء الكلاس بنجاح!")
        creation_ok = True
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء الكلاس: {e}")
        creation_ok = False
    
    if not creation_ok:
        return False
    
    # 4. اختبار الدوال الأساسية
    print("\n4️⃣ اختبار الدوال الأساسية...")
    
    functions_to_test = [
        'setup_validators',
        'validate_form', 
        'collect_form_data',
        'update_transfer_entity_name',
        'update_calculations'
    ]
    
    all_functions_ok = True
    for func_name in functions_to_test:
        try:
            if hasattr(dialog, func_name):
                func = getattr(dialog, func_name)
                func()  # استدعاء الدالة
                print(f"   ✅ {func_name}")
            else:
                print(f"   ❌ {func_name} غير موجودة")
                all_functions_ok = False
        except Exception as e:
            print(f"   ❌ {func_name}: {e}")
            all_functions_ok = False
    
    # 5. فحص الحقول الأساسية
    print("\n5️⃣ فحص الحقول الأساسية...")
    
    essential_fields = [
        'remittance_date',
        'remittance_number_input',
        'amount_input',
        'currency_combo',
        'suppliers_table'
    ]
    
    all_fields_ok = True
    for field_name in essential_fields:
        if hasattr(dialog, field_name):
            print(f"   ✅ {field_name}")
        else:
            print(f"   ❌ {field_name} مفقود")
            all_fields_ok = False
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if syntax_ok and import_ok and creation_ok and all_functions_ok and all_fields_ok:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ النافذة جاهزة للاستخدام بدون أي أخطاء.")
        return True
    else:
        print("❌ يوجد مشاكل تحتاج إصلاح.")
        return False

def show_success_message():
    """عرض رسالة النجاح"""
    print("\n🎊 تهانينا! تم إصلاح جميع الأخطاء بنجاح!")
    print("=" * 50)
    print("✅ لا توجد أخطاء syntax")
    print("✅ الاستيراد يعمل بشكل صحيح")
    print("✅ إنشاء الكلاس يعمل")
    print("✅ الدوال الأساسية تعمل")
    print("✅ الحقول الأساسية موجودة")
    print("\n🚀 يمكنك الآن تشغيل التطبيق واستخدام النافذة!")

if __name__ == "__main__":
    success = test_syntax_and_import()
    
    if success:
        show_success_message()
    else:
        print("\n🔧 يحتاج إصلاحات إضافية.")
