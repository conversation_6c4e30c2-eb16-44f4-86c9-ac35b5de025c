# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

block_cipher = None

# البيانات المطلوبة
added_files = [
    ('data', 'data'),
    ('config', 'config'),
    ('assets', 'assets'),
    ('docs', 'docs'),
    ('LOGO_FOGEHI.png', '.'),
    ('LOGO_FOGEHI.jpg', '.'),
    ('src/utils', 'src/utils'),
    ('src/ui/widgets', 'src/ui/widgets'),
    ('src/ui/themes', 'src/ui/themes'),
    ('src/ui/responsive', 'src/ui/responsive'),
]

# المكتبات المخفية - قائمة شاملة
hidden_imports = [
    # PySide6 الأساسية
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtPrintSupport',
    'PySide6.QtSvg',
    
    # قاعدة البيانات
    'SQLAlchemy',
    'sqlite3',
    
    # تقارير PDF
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    'reportlab.platypus',
    'reportlab.lib.pagesizes',
    'reportlab.lib.styles',
    'reportlab.lib.units',
    
    # دعم العربية
    'arabic_reshaper',
    'bidi',
    'bidi.algorithm',
    
    # Excel
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    
    # الشبكة
    'requests',
    'beautifulsoup4',
    'bs4',
    
    # الصور
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    
    # أدوات إضافية
    'num2words',
    'qdarkstyle',
    'qtawesome',
    
    # وحدات المشروع - src.utils
    'src',
    'src.utils',
    'src.utils.shipping_company_validator',
    'src.utils.arabic_support',
    'src.utils.formatters',
    'src.utils.security_manager',
    'src.utils.shipment_data_filler',
    'src.utils.shipping_data_enhancer',
    
    # وحدات المشروع - src.ui
    'src.ui',
    'src.ui.main_window',
    'src.ui.base',
    'src.ui.base.base_window',
    'src.ui.dialogs',
    'src.ui.shipments',
    'src.ui.suppliers',
    'src.ui.remittances',
    'src.ui.items',
    'src.ui.settings',
    'src.ui.widgets',
    'src.ui.widgets.smart_shipping_company_widget',
    'src.ui.themes',
    'src.ui.themes.theme_manager',
    'src.ui.responsive',
    'src.ui.responsive.responsive_manager',
    
    # وحدات المشروع - src.database
    'src.database',
    'src.database.database_manager',
    'src.database.models',
    
    # وحدات المشروع - src.reports
    'src.reports',
    
    # وحدات المشروع - src.services
    'src.services',
    
    # مكتبات إضافية
    'psutil',
    'colorlog',
    'cryptography',
    'dateutil',
    'pytz',
    'pandas',
    'numpy',
    'matplotlib',
    'folium',
    'selenium',
    'tornado',
    'twisted',
    'aiohttp',
    'multidict',
    'yarl',
    'frozenlist',
    'attrs',
    'certifi',
    'charset_normalizer',
    'idna',
    'urllib3',
    'setuptools',
    'pkg_resources',
]

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ProShipment',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='LOGO_FOGEHI.ico' if Path('LOGO_FOGEHI.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ProShipment',
)
