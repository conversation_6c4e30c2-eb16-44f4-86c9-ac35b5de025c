# ميزة مندوبي المشتريات
## Purchase Representatives Feature

تم تطوير ميزة شاملة لإدارة مندوبي المشتريات وربطهم بالموردين في نظام إدارة الشحنات.

## 🎯 الهدف من الميزة

تهدف هذه الميزة إلى:
- إدارة بيانات مندوبي المشتريات بشكل شامل
- ربط الموردين بمندوبي المشتريات المسؤولين عنهم
- تتبع أداء المندوبين وعمولاتهم
- تحسين عملية إدارة المشتريات والموردين

## 📊 قاعدة البيانات

### جدول مندوبي المشتريات (purchase_representatives)

| الحقل | النوع | الوصف |
|-------|-------|--------|
| id | Integer | المعرف الفريد |
| code | String(50) | كود المندوب (فريد) |
| name | String(200) | اسم المندوب |
| name_en | String(200) | الاسم بالإنجليزية |
| phone | String(50) | رقم الهاتف |
| mobile | String(50) | رقم الجوال |
| email | String(100) | البريد الإلكتروني |
| department | String(100) | القسم |
| position | String(100) | المنصب |
| hire_date | Date | تاريخ التوظيف |
| salary | Float | الراتب |
| commission_rate | Float | نسبة العمولة |
| address | Text | العنوان |
| city | String(100) | المدينة |
| notes | Text | ملاحظات |
| emergency_contact | String(200) | جهة الاتصال في الطوارئ |
| emergency_phone | String(50) | هاتف الطوارئ |
| is_active | Boolean | حالة النشاط |
| created_at | DateTime | تاريخ الإنشاء |
| updated_at | DateTime | تاريخ التحديث |

### جدول ربط الموردين بالمندوبين (supplier_representatives)

| الحقل | النوع | الوصف |
|-------|-------|--------|
| id | Integer | المعرف الفريد |
| supplier_id | Integer | معرف المورد |
| representative_id | Integer | معرف المندوب |
| is_primary | Boolean | المندوب الرئيسي |
| assigned_date | Date | تاريخ التعيين |
| notes | Text | ملاحظات |
| is_active | Boolean | حالة النشاط |
| created_at | DateTime | تاريخ الإنشاء |
| updated_at | DateTime | تاريخ التحديث |

## 🖥️ واجهة المستخدم

### تبويب مندوبي المشتريات

**الموقع:** إدارة الموردين → بيانات الموردين → تبويب "👥 مندوبي المشتريات"

**الميزات:**
- ✅ إضافة مندوبين جدد
- ✅ تعديل بيانات المندوبين الموجودين
- ✅ البحث في المندوبين (بالاسم، الكود، القسم)
- ✅ حذف ناعم للمندوبين (إلغاء تفعيل)
- ✅ عرض جدولي منظم للمندوبين

**الحقول المتاحة:**
- البيانات الأساسية: الكود، الاسم، الاسم بالإنجليزية
- بيانات الاتصال: الهاتف، الجوال، البريد الإلكتروني
- بيانات العمل: القسم، المنصب، تاريخ التوظيف، الراتب، نسبة العمولة
- معلومات إضافية: المدينة، العنوان، الملاحظات

### تبويب ربط الموردين بالمندوبين

**الموقع:** إدارة الموردين → بيانات الموردين → تبويب "🤝 ربط الموردين بالمندوبين"

**الميزات:**
- ✅ ربط الموردين بمندوبي المشتريات
- ✅ تحديد المندوب الرئيسي لكل مورد
- ✅ البحث المتقدم في الموردين (F9)
- ✅ تتبع تاريخ التعيين
- ✅ إضافة ملاحظات للتعيينات

**الوظائف الخاصة:**
- **البحث المتقدم (F9):** فتح نافذة البحث المتقدم للموردين
- **المندوب الرئيسي:** ضمان وجود مندوب رئيسي واحد فقط لكل مورد
- **الحذف الناعم:** إلغاء تفعيل التعيينات بدلاً من الحذف النهائي

## 🔧 الميزات التقنية

### الأمان والتحقق
- ✅ التحقق من صحة البيانات قبل الحفظ
- ✅ منع تكرار أكواد المندوبين
- ✅ منع تكرار ربط نفس المورد بنفس المندوب
- ✅ ضمان وجود مندوب رئيسي واحد فقط لكل مورد

### الأداء
- ✅ فهرسة قاعدة البيانات للبحث السريع
- ✅ البحث الفوري مع تأخير 300ms
- ✅ تحميل البيانات بشكل مُحسَّن

### سهولة الاستخدام
- ✅ واجهة مستخدم عربية بالكامل
- ✅ اختصارات لوحة المفاتيح (F9 للبحث المتقدم)
- ✅ رسائل تأكيد وتحذير واضحة
- ✅ تصميم متجاوب ومنظم

## 📝 بيانات تجريبية

تم إضافة 5 مندوبين تجريبيين:

1. **أحمد محمد السعد (REP001)**
   - مندوب مشتريات أول - الرياض
   - راتب: 8,000 ريال - عمولة: 2.5%

2. **فاطمة علي الأحمد (REP002)**
   - مندوبة مشتريات - جدة
   - راتب: 7,000 ريال - عمولة: 2.0%

3. **خالد عبدالله النصر (REP003)**
   - مندوب مشتريات متخصص - الدمام
   - راتب: 9,000 ريال - عمولة: 3.0%

4. **نورا سعد الغامدي (REP004)**
   - مندوبة مشتريات كبيرة - مكة
   - راتب: 10,000 ريال - عمولة: 3.5%

5. **محمد عبدالرحمن القحطاني (REP005)**
   - مدير مندوبي المشتريات - المدينة المنورة
   - راتب: 12,000 ريال - عمولة: 4.0%

## 🚀 كيفية الاستخدام

### إضافة مندوب جديد
1. انتقل إلى: إدارة الموردين → بيانات الموردين
2. اختر تبويب "👥 مندوبي المشتريات"
3. املأ البيانات المطلوبة (الكود والاسم مطلوبان)
4. اضغط "إضافة مندوب"

### ربط مورد بمندوب
1. انتقل إلى تبويب "🤝 ربط الموردين بالمندوبين"
2. ابحث عن المورد (أو استخدم F9 للبحث المتقدم)
3. اختر المندوب من القائمة
4. حدد إذا كان المندوب الرئيسي
5. اضغط "إضافة تعيين"

### البحث المتقدم
- اضغط F9 في أي مكان في تبويب ربط الموردين بالمندوبين
- أو اضغط على زر 🔍 بجانب حقل البحث
- استخدم معايير البحث المتعددة للعثور على المورد المطلوب

## 🔄 التحديثات المستقبلية

يمكن إضافة الميزات التالية مستقبلاً:
- تقارير أداء المندوبين
- حساب العمولات التلقائي
- تتبع المبيعات لكل مندوب
- إشعارات تذكير بمتابعة الموردين
- تكامل مع نظام الرواتب

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
