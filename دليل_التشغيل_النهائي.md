# دليل التشغيل النهائي - نظام إدارة الشحنات المتكامل

## 🚀 بدء التشغيل السريع

### **1. متطلبات النظام** 📋
- **نظام التشغيل**: Windows 10/11, macOS, Linux
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة القرص**: 2 GB مساحة فارغة
- **الشاشة**: دقة 1366x768 كحد أدنى

### **2. تثبيت المتطلبات** 📦

#### **أ. تثبيت Python (إذا لم يكن مثبتاً)**:
1. قم بتحميل Python من: https://python.org
2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت

#### **ب. تثبيت المكتبات المطلوبة**:
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install PySide6 SQLAlchemy reportlab requests beautifulsoup4 openpyxl Pillow python-bidi arabic-reshaper num2words
```

### **3. تشغيل التطبيق** ▶️

#### **الطريقة الأولى - تشغيل مباشر**:
```bash
python main.py
```

#### **الطريقة الثانية - من مجلد المشروع**:
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد المشروع
3. شغل الأمر: `python main.py`

---

## 🖥️ واجهة التطبيق الرئيسية

### **النافذة الرئيسية** 🏠
عند تشغيل التطبيق، ستظهر النافذة الرئيسية التي تحتوي على:

#### **شريط القوائم العلوي**:
- **ملف**: إنشاء، فتح، حفظ، خروج
- **تحرير**: تراجع، إعادة، نسخ، لصق
- **عرض**: تغيير المظهر، إعدادات العرض
- **أدوات**: أدوات مساعدة ومرافق
- **مساعدة**: دليل المستخدم، حول البرنامج

#### **شريط الأدوات**:
- **إدارة الشحنات** 📦
- **إدارة الموردين** 🏢
- **إدارة الأصناف** 📋
- **طلبات الشراء** 🛒
- **طلبات الحوالة** 💰
- **التقارير** 📊
- **الإعدادات** ⚙️

#### **المنطقة الرئيسية**:
- **لوحة المعلومات**: إحصائيات سريعة
- **الاختصارات السريعة**: للعمليات الشائعة
- **آخر النشاطات**: قائمة بآخر العمليات

---

## 📦 إدارة الشحنات

### **إضافة شحنة جديدة** ➕

1. **انقر على "إدارة الشحنات"** من الشريط الرئيسي
2. **انقر على "إضافة شحنة جديدة"**
3. **املأ البيانات المطلوبة**:
   - رقم الشحنة (تلقائي)
   - تاريخ الشحنة
   - المورد
   - شركة الشحن
   - ميناء المغادرة/الوصول
   - تاريخ الوصول المتوقع

4. **إضافة الأصناف**:
   - انقر على "إضافة صنف"
   - اختر الصنف من القائمة
   - أدخل الكمية والسعر
   - كرر للأصناف الأخرى

5. **حفظ الشحنة**: انقر على "حفظ"

### **تعديل شحنة موجودة** ✏️

1. **ابحث عن الشحنة**: استخدم مربع البحث
2. **انقر مرتين على الشحنة** لفتحها
3. **عدل البيانات المطلوبة**
4. **احفظ التغييرات**: انقر على "حفظ"

### **حذف شحنة** 🗑️

1. **اختر الشحنة** من القائمة
2. **انقر على "حذف"**
3. **أكد الحذف** في النافذة المنبثقة

---

## 🏢 إدارة الموردين

### **إضافة مورد جديد** ➕

1. **انقر على "إدارة الموردين"**
2. **انقر على "إضافة مورد جديد"**
3. **املأ البيانات**:
   - **البيانات الأساسية**: الاسم، الكود، النوع
   - **بيانات الاتصال**: الهاتف، البريد الإلكتروني
   - **العنوان**: الدولة، المدينة، العنوان التفصيلي
   - **البيانات المالية**: حد الائتمان، مدة السداد

4. **حفظ المورد**: انقر على "حفظ"

### **استيراد موردين من Excel** 📊

1. **انقر على "استيراد من Excel"**
2. **اختر ملف Excel** (يجب أن يحتوي على الأعمدة المطلوبة)
3. **راجع البيانات** في نافذة المعاينة
4. **انقر على "استيراد"** لإتمام العملية

---

## 📋 إدارة الأصناف

### **إضافة صنف جديد** ➕

1. **انقر على "إدارة الأصناف"**
2. **انقر على "إضافة صنف جديد"**
3. **املأ البيانات**:
   - الكود (تلقائي أو يدوي)
   - الاسم بالعربية والإنجليزية
   - المجموعة
   - وحدة القياس
   - الأسعار والأوزان

4. **حفظ الصنف**: انقر على "حفظ"

### **استيراد أصناف من Excel** 📊

1. **انقر على "استيراد من Excel"**
2. **اختر ملف Excel** مع البيانات
3. **راجع وأكد الاستيراد**

---

## 💰 نظام طلبات الحوالة

### **إنشاء طلب حوالة جديد** ➕

1. **انقر على "طلبات الحوالة"**
2. **انقر على "طلب حوالة جديد"**
3. **املأ بيانات المرسل**:
   - الاسم
   - رقم الهوية
   - العنوان
   - الهاتف

4. **املأ بيانات المستقبل**:
   - الاسم
   - رقم الحساب
   - اسم البنك
   - فرع البنك
   - رمز السويفت
   - البلد

5. **املأ بيانات الحوالة**:
   - المبلغ
   - العملة
   - الغرض من التحويل
   - الفرع

6. **إنشاء PDF**: انقر على "إنشاء PDF"

### **طباعة طلب الحوالة** 🖨️

1. **افتح طلب الحوالة**
2. **انقر على "طباعة"**
3. **اختر الطابعة والإعدادات**
4. **انقر على "طباعة"**

---

## 📊 التقارير والإحصائيات

### **تقارير الشحنات** 📦

1. **انقر على "التقارير"**
2. **اختر "تقارير الشحنات"**
3. **حدد الفترة الزمنية**
4. **اختر المرشحات** (المورد، الحالة، إلخ)
5. **انقر على "إنشاء التقرير"**

### **تقارير الموردين** 🏢

1. **اختر "تقارير الموردين"**
2. **حدد نوع التقرير**:
   - قائمة الموردين
   - أداء الموردين
   - المعاملات المالية

3. **انقر على "إنشاء التقرير"**

### **تصدير التقارير** 📤

- **PDF**: للطباعة والأرشفة
- **Excel**: للتحليل والمعالجة
- **CSV**: للاستيراد في برامج أخرى

---

## ⚙️ الإعدادات والتخصيص

### **إعدادات الشركة** 🏢

1. **انقر على "الإعدادات"**
2. **اختر "إعدادات الشركة"**
3. **املأ البيانات**:
   - اسم الشركة (عربي/إنجليزي)
   - العنوان (عربي/إنجليزي)
   - الهاتف والفاكس
   - البريد الإلكتروني
   - الشعار

4. **احفظ الإعدادات**

### **إعدادات النظام** 🔧

- **العملة الافتراضية**
- **تنسيق التاريخ**
- **اللغة**
- **المظهر (فاتح/داكن)**
- **النسخ الاحتياطي التلقائي**

### **إدارة المستخدمين** 👥

1. **انقر على "إدارة المستخدمين"**
2. **إضافة مستخدم جديد**:
   - اسم المستخدم
   - كلمة المرور
   - الصلاحيات
   - البيانات الشخصية

3. **تعديل الصلاحيات** حسب الحاجة

---

## 🔧 الصيانة والنسخ الاحتياطي

### **النسخ الاحتياطي** 💾

#### **نسخ احتياطي يدوي**:
1. **انقر على "أدوات"**
2. **اختر "نسخ احتياطي"**
3. **اختر مكان الحفظ**
4. **انقر على "إنشاء نسخة احتياطية"**

#### **النسخ الاحتياطي التلقائي**:
- يتم تلقائياً كل ساعة
- يحفظ في مجلد `backups`
- يحتفظ بآخر 30 نسخة

### **استعادة النسخة الاحتياطية** 🔄

1. **انقر على "أدوات"**
2. **اختر "استعادة نسخة احتياطية"**
3. **اختر ملف النسخة الاحتياطية**
4. **أكد الاستعادة**

### **تنظيف قاعدة البيانات** 🧹

1. **انقر على "أدوات"**
2. **اختر "تنظيف قاعدة البيانات"**
3. **اختر نوع التنظيف**:
   - حذف البيانات المؤقتة
   - ضغط قاعدة البيانات
   - إعادة فهرسة

4. **انقر على "تنظيف"**

---

## 🆘 حل المشاكل الشائعة

### **التطبيق لا يبدأ** ❌

**الحلول**:
1. تأكد من تثبيت Python بشكل صحيح
2. تأكد من تثبيت جميع المكتبات المطلوبة
3. شغل الأمر: `pip install -r requirements.txt`
4. تأكد من وجود ملف `main.py`

### **خطأ في قاعدة البيانات** 🗄️

**الحلول**:
1. تأكد من وجود مجلد `data`
2. تأكد من أذونات الكتابة
3. استعد نسخة احتياطية إذا كانت متاحة
4. شغل التطبيق كمدير (Run as Administrator)

### **مشاكل في الطباعة** 🖨️

**الحلول**:
1. تأكد من تثبيت طابعة افتراضية
2. تأكد من تثبيت مكتبة ReportLab
3. جرب طباعة إلى PDF أولاً
4. تحقق من إعدادات الطابعة

### **مشاكل في الاستيراد من Excel** 📊

**الحلول**:
1. تأكد من تنسيق ملف Excel الصحيح
2. تأكد من وجود الأعمدة المطلوبة
3. تأكد من عدم وجود خلايا فارغة في البيانات المهمة
4. جرب حفظ الملف بصيغة .xlsx

---

## 📞 الدعم الفني

### **معلومات الاتصال** 📧
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-1234567
- **ساعات العمل**: الأحد - الخميس، 8:00 ص - 5:00 م

### **الموارد المفيدة** 📚
- **دليل المستخدم الكامل**: متاح في قائمة المساعدة
- **فيديوهات تعليمية**: على موقعنا الإلكتروني
- **منتدى المستخدمين**: للأسئلة والنقاشات
- **قاعدة المعرفة**: حلول للمشاكل الشائعة

---

## 🎉 مبروك!

**تم تثبيت وتشغيل نظام إدارة الشحنات المتكامل بنجاح!**

التطبيق الآن جاهز للاستخدام الكامل مع جميع الميزات المتقدمة. استمتع بتجربة إدارة شحنات سهلة وفعالة!
