# 📋 توثيق نظام ربط الموردين بالعملات

## 🎯 نظرة عامة

تم تطوير نظام شامل لإدارة ربط الموردين بالعملات المختلفة مع دعم تعدد العملات لكل مورد. يتيح هذا النظام للمستخدمين إدارة العلاقات المالية المعقدة مع الموردين الذين يتعاملون بعملات متعددة.

## 🏗️ البنية التقنية

### جدول قاعدة البيانات الجديد

تم إنشاء جدول `supplier_currencies` لربط الموردين بالعملات:

```sql
CREATE TABLE supplier_currencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_id INTEGER NOT NULL,
    currency_id INTEGER NOT NULL,
    is_primary BOOLEAN DEFAULT 0,
    exchange_rate_override REAL,
    credit_limit_in_currency REAL DEFAULT 0.0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    UNIQUE(supplier_id, currency_id)
)
```

### الحقول والخصائص

| الحقل | النوع | الوصف |
|-------|--------|--------|
| `supplier_id` | INTEGER | معرف المورد |
| `currency_id` | INTEGER | معرف العملة |
| `is_primary` | BOOLEAN | هل هذه العملة الأساسية للمورد |
| `exchange_rate_override` | REAL | سعر صرف مخصص للمورد (اختياري) |
| `credit_limit_in_currency` | REAL | حد الائتمان بهذه العملة |
| `is_active` | BOOLEAN | حالة الربط (نشط/غير نشط) |

## 🖥️ واجهة المستخدم

### نافذة إدارة ربط الموردين بالعملات

تم إنشاء نافذة متخصصة (`SupplierCurrenciesWindow`) تتضمن:

#### اللوحة اليسرى - عرض البيانات
- **اختيار المورد**: قائمة منسدلة لاختيار المورد
- **جدول العملات المرتبطة**: عرض جميع العملات المرتبطة بالمورد المحدد
- **أزرار العمليات**: تعديل، حذف، تعيين كأساسية

#### اللوحة اليمنى - إدارة الروابط
- **اختيار العملة**: قائمة منسدلة للعملات المتاحة
- **العملة الأساسية**: خيار لتعيين العملة كأساسية
- **سعر الصرف المخصص**: إمكانية تعيين سعر صرف خاص بالمورد
- **حد الائتمان**: تحديد حد الائتمان بالعملة المحددة
- **الحالة**: تفعيل أو إلغاء تفعيل الربط

## 🚀 الميزات الرئيسية

### 1. تعدد العملات للمورد الواحد
- يمكن ربط المورد الواحد بعدة عملات
- كل ربط له إعدادات منفصلة
- إمكانية تعيين عملة أساسية واحدة لكل مورد

### 2. أسعار الصرف المخصصة
- إمكانية تعيين سعر صرف خاص لكل مورد
- إذا لم يتم تعيين سعر مخصص، يتم استخدام السعر العام
- مرونة في التعامل مع الاتفاقيات التجارية الخاصة

### 3. إدارة حدود الائتمان
- تحديد حد ائتمان منفصل لكل عملة
- إمكانية مراقبة المديونية بعملات متعددة
- تحكم دقيق في المخاطر المالية

### 4. واجهة مستخدم متقدمة
- تصميم متجاوب ومتوافق مع RTL
- عرض البيانات في جداول منظمة
- أزرار عمليات واضحة ومباشرة

## 📊 البيانات التجريبية

تم إضافة بيانات تجريبية تشمل:

### العملات الأساسية
- **ريال سعودي (SAR)**: العملة الأساسية
- **دولار أمريكي (USD)**: سعر الصرف 3.75
- **يورو (EUR)**: سعر الصرف 4.1
- **درهم إماراتي (AED)**: سعر الصرف 1.02
- **يوان صيني (CNY)**: سعر الصرف 0.52

### روابط تجريبية
- ربط الموردين الموجودين بالريال السعودي كعملة أساسية
- ربط إضافي ببعض العملات الأخرى مع حدود ائتمان مختلفة

## 🔧 كيفية الاستخدام

### 1. الوصول للنافذة
- من نافذة إدارة الموردين الرئيسية
- انقر على زر "💱 ربط الموردين بالعملات"

### 2. إضافة ربط جديد
1. اختر المورد من القائمة المنسدلة
2. في اللوحة اليمنى، اختر العملة
3. حدد الإعدادات (عملة أساسية، سعر صرف، حد ائتمان)
4. انقر "حفظ الربط"

### 3. تعديل ربط موجود
1. اختر المورد
2. انقر مرتين على الربط في الجدول أو انقر "تعديل"
3. عدل الإعدادات في اللوحة اليمنى
4. انقر "حفظ الربط"

### 4. حذف ربط
1. اختر الربط من الجدول
2. انقر "حذف الربط"
3. أكد الحذف

### 5. تعيين عملة أساسية
1. اختر الربط من الجدول
2. انقر "تعيين كأساسية"
3. سيتم إلغاء تعيين العملات الأخرى كأساسية تلقائياً

## 🛡️ الأمان والتحقق

### قيود قاعدة البيانات
- فهرس فريد لمنع ربط نفس المورد بنفس العملة مرتين
- قيود المفاتيح الخارجية لضمان سلامة البيانات
- فهارس للأداء المحسن

### التحقق من البيانات
- التأكد من اختيار مورد وعملة قبل الحفظ
- منع الحفظ في حالة وجود ربط مكرر
- تحديث تلقائي للعملة الأساسية

## 🔄 التكامل مع النظام

### ربط مع الجداول الموجودة
- **جدول الموردين**: علاقة واحد إلى متعدد
- **جدول العملات**: استخدام العملات المعرفة في النظام
- **طلبات الشراء**: يمكن الاستفادة من أسعار الصرف المخصصة

### إمكانيات التطوير المستقبلية
- تقارير مالية بعملات متعددة
- تحويل تلقائي للعملات في الفواتير
- تتبع تقلبات أسعار الصرف
- تنبيهات عند تجاوز حدود الائتمان

## 📈 الإحصائيات

من خلال الاختبار الأولي:
- **182 مورد** في النظام
- **4 عملات** أساسية
- **6 روابط** تجريبية تم إنشاؤها

## 🎉 الخلاصة

تم تطوير نظام شامل ومتقدم لإدارة ربط الموردين بالعملات يوفر:

✅ **مرونة كاملة** في التعامل مع عملات متعددة  
✅ **واجهة مستخدم بديهية** وسهلة الاستخدام  
✅ **أمان عالي** مع قيود قاعدة البيانات  
✅ **تكامل مثالي** مع النظام الموجود  
✅ **إمكانيات توسع** للمستقبل  

النظام جاهز للاستخدام الفوري ويمكن الوصول إليه من نافذة إدارة الموردين الرئيسية.
