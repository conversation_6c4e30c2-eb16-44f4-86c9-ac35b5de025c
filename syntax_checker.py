#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ast
import sys
import os

def check_syntax_errors():
    """فحص أخطاء الـ syntax في الملف"""
    print("🔍 فحص أخطاء الـ syntax...")
    print("=" * 50)
    
    file_path = "src/ui/remittances/new_remittance_dialog.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ تم قراءة الملف: {file_path}")
        print(f"📊 عدد الأسطر: {len(content.splitlines())}")
        
        # فحص الـ syntax
        try:
            ast.parse(content)
            print("✅ لا توجد أخطاء syntax!")
            return True
        except SyntaxError as e:
            print(f"❌ خطأ syntax:")
            print(f"   السطر: {e.lineno}")
            print(f"   العمود: {e.offset}")
            print(f"   الرسالة: {e.msg}")
            print(f"   النص: {e.text}")
            return False
        except Exception as e:
            print(f"❌ خطأ آخر: {e}")
            return False
            
    except FileNotFoundError:
        print(f"❌ الملف غير موجود: {file_path}")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def check_indentation_issues():
    """فحص مشاكل المسافات البادئة"""
    print("\n🔍 فحص مشاكل المسافات البادئة...")
    print("=" * 40)
    
    file_path = "src/ui/remittances/new_remittance_dialog.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues_found = []
        
        for i, line in enumerate(lines, 1):
            # فحص الخلط بين المسافات والتابات
            if '\t' in line and '    ' in line:
                issues_found.append(f"السطر {i}: خلط بين المسافات والتابات")
            
            # فحص المسافات الزائدة في نهاية السطر
            if line.rstrip() != line.rstrip(' \t'):
                issues_found.append(f"السطر {i}: مسافات زائدة في النهاية")
        
        if issues_found:
            print("⚠️ مشاكل المسافات البادئة:")
            for issue in issues_found[:10]:  # أول 10 مشاكل فقط
                print(f"   {issue}")
            if len(issues_found) > 10:
                print(f"   ... و {len(issues_found) - 10} مشاكل أخرى")
        else:
            print("✅ لا توجد مشاكل في المسافات البادئة!")
        
        return len(issues_found) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص المسافات: {e}")
        return False

def test_import():
    """اختبار استيراد الملف"""
    print("\n🔍 اختبار استيراد الملف...")
    print("=" * 30)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # محاولة استيراد الملف
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        print("✅ تم استيراد الملف بنجاح!")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ syntax في الاستيراد:")
        print(f"   السطر: {e.lineno}")
        print(f"   الرسالة: {e.msg}")
        return False
    except ImportError as e:
        print(f"❌ خطأ استيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ آخر: {e}")
        return False

def test_class_creation():
    """اختبار إنشاء الكلاس"""
    print("\n🔍 اختبار إنشاء الكلاس...")
    print("=" * 30)
    
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        dialog = NewRemittanceDialog()
        print("✅ تم إنشاء الكلاس بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الكلاس: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص شامل لملف نافذة إنشاء الحوالة")
    print("=" * 60)
    
    results = []
    
    # فحص الـ syntax
    syntax_ok = check_syntax_errors()
    results.append(("Syntax", syntax_ok))
    
    # فحص المسافات البادئة
    indent_ok = check_indentation_issues()
    results.append(("Indentation", indent_ok))
    
    # اختبار الاستيراد
    import_ok = test_import()
    results.append(("Import", import_ok))
    
    # اختبار إنشاء الكلاس
    if import_ok:
        creation_ok = test_class_creation()
        results.append(("Class Creation", creation_ok))
    
    # تقرير النتائج
    print("\n" + "=" * 60)
    print("📊 تقرير الفحص الشامل:")
    
    all_ok = True
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if not result:
            all_ok = False
    
    if all_ok:
        print("\n🎉 جميع الفحوصات نجحت!")
        print("✅ الملف جاهز للاستخدام.")
    else:
        print("\n❌ يوجد مشاكل تحتاج إصلاح.")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 الملف يعمل بشكل مثالي!")
    else:
        print("\n🔧 يحتاج إصلاحات إضافية.")
