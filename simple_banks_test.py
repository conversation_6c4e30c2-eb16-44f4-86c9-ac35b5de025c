#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 اختبار نوافذ إدارة البنوك...")

try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from PySide6.QtWidgets import QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    print("✅ تم إنشاء QApplication")
    
    # اختبار نافذة البنك
    from src.ui.remittances.add_new_bank_dialog import AddNewBankDialog
    bank_dialog = AddNewBankDialog()
    print("✅ نافذة إضافة بنك جديد - مكتملة")
    
    # اختبار نافذة الصراف
    from src.ui.remittances.add_new_exchange_dialog import AddNewExchangeDialog
    exchange_dialog = AddNewExchangeDialog()
    print("✅ نافذة إضافة صراف جديد - مكتملة")
    
    # اختبار نافذة الفرع
    from src.ui.remittances.add_new_branch_dialog import AddNewBranchDialog
    branch_dialog = AddNewBranchDialog()
    print("✅ نافذة إضافة فرع جديد - مكتملة")
    
    # اختبار التكامل
    from src.ui.remittances.banks_management_window import BanksManagementWindow
    banks_window = BanksManagementWindow()
    print("✅ التكامل مع نافذة إدارة البنوك - مكتمل")
    
    print("\n🎉 جميع النوافذ تم تطويرها بنجاح!")
    print("📋 النوافذ المكتملة:")
    print("   🏦 نافذة إضافة بنك جديد")
    print("   💱 نافذة إضافة صراف جديد")
    print("   🏢 نافذة إضافة فرع جديد")
    print("   🔗 التكامل مع إدارة البنوك")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
