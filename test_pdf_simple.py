#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لإصلاح مولد PDF
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pdf_generation():
    """اختبار إنشاء PDF"""
    print("📄 اختبار إنشاء PDF...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        generator = RemittancePDFGenerator()
        print("   ✅ تم إنشاء مولد PDF بنجاح")
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'TEST-001',
            'request_date': '2024/12/09',
            'remittance_amount': '5000',
            'currency': 'USD',
            'receiver_name': 'MOHAMMED AHMED ALI',
            'receiver_bank_name': 'TEST BANK',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        # مسار مؤقت للاختبار
        test_output = "test_simple.pdf"
        
        try:
            # محاولة إنشاء PDF
            result = generator.generate_pdf(test_data, test_output)
            
            if result and os.path.exists(test_output):
                print("   ✅ تم إنشاء PDF بنجاح")
                
                # التحقق من حجم الملف
                file_size = os.path.getsize(test_output)
                if file_size > 1000:  # أكبر من 1KB
                    print(f"   ✅ حجم الملف مناسب: {file_size} بايت")
                else:
                    print(f"   ⚠️ حجم الملف صغير: {file_size} بايت")
                
                # حذف الملف التجريبي
                os.remove(test_output)
                print("   ✅ تم حذف الملف التجريبي")
                
                return True
            else:
                print("   ❌ فشل في إنشاء PDF")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء PDF: {e}")
            
            # حذف الملف في حالة الخطأ
            if os.path.exists(test_output):
                os.remove(test_output)
            
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار PDF: {e}")
        return False

def test_colors_import():
    """اختبار استيراد الألوان"""
    print("\n🎨 اختبار استيراد الألوان...")
    
    try:
        from reportlab.lib import colors
        from reportlab.lib.colors import black, blue, red, grey
        
        print("   ✅ تم استيراد جميع الألوان بنجاح")
        
        # اختبار استخدام الألوان
        test_colors = [colors.black, colors.grey, colors.blue, colors.red]
        
        for i, color in enumerate(test_colors):
            if color:
                print(f"   ✅ اللون {i+1} متاح")
            else:
                print(f"   ❌ اللون {i+1} غير متاح")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استيراد الألوان: {e}")
        return False

def test_signature_method():
    """اختبار طريقة التوقيع"""
    print("\n✍️ اختبار طريقة التوقيع...")
    
    try:
        # فحص الكود مباشرة
        with open("src/ui/remittances/remittance_pdf_generator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الإصلاحات
        fixes_found = 0
        
        if "from reportlab.lib import colors" in content:
            print("   ✅ استيراد colors موجود")
            fixes_found += 1
        
        if "colors.grey" in content:
            print("   ✅ استخدام colors.grey موجود")
            fixes_found += 1
        
        if "colors.black" in content:
            print("   ✅ استخدام colors.black موجود")
            fixes_found += 1
        
        if "drawCentredString" in content:
            print("   ✅ استخدام drawCentredString موجود")
            fixes_found += 1
        
        if "drawCentredText" in content:
            print("   ❌ استخدام drawCentredText القديم لا يزال موجود")
            return False
        
        if fixes_found >= 4:
            print(f"   ✅ جميع الإصلاحات مطبقة ({fixes_found}/4)")
            return True
        else:
            print(f"   ❌ بعض الإصلاحات مفقودة ({fixes_found}/4)")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص طريقة التوقيع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح مولد PDF...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_colors_import())
    results.append(test_signature_method())
    results.append(test_pdf_generation())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار إصلاح مولد PDF:")
    print("="*60)
    
    test_names = [
        "استيراد الألوان",
        "طريقة التوقيع",
        "إنشاء PDF"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تم إصلاح مولد PDF بنجاح!")
        print("✅ خطأ 'colors' is not defined تم حله")
        print("✅ خطأ 'drawCentredText' تم حله")
        print("✅ مولد PDF يعمل بشكل مثالي")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   📚 إضافة استيراد colors الكامل")
        print("   🎨 إضافة استيراد grey المطلوب")
        print("   ✍️ تغيير drawCentredText إلى drawCentredString")
        print("   📄 اختبار إنشاء PDF بنجاح")
        
    elif successful_tests >= len(results) * 0.66:
        print("\n✅ معظم المشاكل تم إصلاحها!")
        print("بعض الاختبارات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة مشاكل لم يتم إصلاحها. يرجى مراجعة:")
        print("- استيراد مكتبات ReportLab")
        print("- استخدام الألوان")
        print("- طرق الرسم في PDF")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
