#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة المرفقات في طلبات الحوالات
Test Attachments Feature in Remittance Requests
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_attachments_dialog():
    """اختبار نافذة إدارة المرفقات"""
    print("🧪 اختبار نافذة إدارة المرفقات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.dialogs.attachments_manager_dialog import AttachmentsManagerDialog
        
        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار إنشاء النافذة
        dialog = AttachmentsManagerDialog(None, request_id=123)
        
        print("   ✅ تم إنشاء نافذة إدارة المرفقات بنجاح")
        print(f"   📁 مجلد المرفقات: {dialog.attachments_dir}")
        print(f"   📁 مجلد الطلب: {dialog.request_attachments_dir}")
        print(f"   📊 عدد المرفقات الحالي: {dialog.get_attachments_count()}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نافذة المرفقات: {e}")
        return False

def test_remittance_window_integration():
    """اختبار دمج المرفقات في نافذة طلب الحوالة"""
    print("\n🧪 اختبار دمج المرفقات في نافذة طلب الحوالة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار إنشاء النافذة
        window = RemittanceRequestWindow()
        
        print("   ✅ تم إنشاء نافذة طلب الحوالة بنجاح")
        
        # اختبار وجود عمود المستندات
        table = window.requests_table
        headers = [table.horizontalHeaderItem(i).text() for i in range(table.columnCount())]
        
        if "المستندات" in headers:
            print("   ✅ عمود المستندات موجود في الجدول")
            print(f"   📊 عدد الأعمدة: {table.columnCount()}")
            print(f"   📋 العناوين: {headers}")
        else:
            print("   ❌ عمود المستندات غير موجود في الجدول")
            return False
        
        # اختبار دالة حساب عدد المرفقات
        count = window.get_attachments_count(123)
        print(f"   📊 عدد المرفقات للطلب 123: {count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نافذة طلب الحوالة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_attachments_directory_structure():
    """اختبار هيكل مجلدات المرفقات"""
    print("\n🧪 اختبار هيكل مجلدات المرفقات...")
    
    try:
        from pathlib import Path
        
        # إنشاء مجلد المرفقات الرئيسي
        attachments_dir = Path("data/attachments")
        attachments_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"   ✅ مجلد المرفقات الرئيسي: {attachments_dir}")
        print(f"   📁 موجود: {attachments_dir.exists()}")
        
        # إنشاء مجلد تجريبي لطلب
        test_request_dir = attachments_dir / "test_request_123"
        test_request_dir.mkdir(exist_ok=True)
        
        print(f"   ✅ مجلد طلب تجريبي: {test_request_dir}")
        print(f"   📁 موجود: {test_request_dir.exists()}")
        
        # إنشاء ملف تجريبي
        test_file = test_request_dir / "test_document.txt"
        test_file.write_text("هذا ملف تجريبي للاختبار", encoding='utf-8')
        
        print(f"   ✅ ملف تجريبي: {test_file}")
        print(f"   📄 موجود: {test_file.exists()}")
        print(f"   📊 الحجم: {test_file.stat().st_size} بايت")
        
        # عد الملفات
        files = list(test_request_dir.glob("*"))
        print(f"   📊 عدد الملفات في المجلد: {len(files)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار هيكل المجلدات: {e}")
        return False

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("\n🧪 اختبار عمليات الملفات...")
    
    try:
        from pathlib import Path
        import shutil
        from datetime import datetime
        
        # إنشاء ملف مصدر تجريبي
        source_file = Path("test_source.txt")
        source_file.write_text("محتوى ملف تجريبي للاختبار", encoding='utf-8')
        
        print(f"   ✅ ملف المصدر: {source_file}")
        
        # محاكاة عملية نسخ الملف
        attachments_dir = Path("data/attachments/test_request_456")
        attachments_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_filename = f"{timestamp}_{source_file.name}"
        destination = attachments_dir / new_filename
        
        shutil.copy2(source_file, destination)
        
        print(f"   ✅ تم نسخ الملف إلى: {destination}")
        print(f"   📄 موجود: {destination.exists()}")
        
        # تنظيف الملفات التجريبية
        source_file.unlink()
        destination.unlink()
        
        print("   ✅ تم تنظيف الملفات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار عمليات الملفات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار ميزة المرفقات في طلبات الحوالات")
    print("="*60)
    
    tests = [
        ("نافذة إدارة المرفقات", test_attachments_dialog),
        ("دمج المرفقات في نافذة الحوالة", test_remittance_window_integration),
        ("هيكل مجلدات المرفقات", test_attachments_directory_structure),
        ("عمليات الملفات", test_file_operations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج اختبار ميزة المرفقات")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.8:
        print("\n🎉 ميزة المرفقات تعمل بشكل ممتاز!")
        print("✅ جاهزة للاستخدام")
        status = "ممتاز"
    elif success_rate >= 0.6:
        print("\n✅ ميزة المرفقات تعمل بشكل جيد")
        print("⚠️ بعض الميزات قد تحتاج مراجعة")
        status = "جيد"
    else:
        print("\n⚠️ ميزة المرفقات تحتاج إصلاحات")
        print("🔧 راجع الأخطاء أعلاه")
        status = "يحتاج إصلاح"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    # معلومات إضافية
    print(f"\n📋 ملخص الميزة الجديدة:")
    print(f"   📎 عمود المستندات في جدول طلبات الحوالات")
    print(f"   🗂️ نافذة إدارة المرفقات الشاملة")
    print(f"   📁 نظام تنظيم الملفات حسب الطلب")
    print(f"   🔍 عرض وتحميل المرفقات")
    print(f"   🗑️ حذف المرفقات")
    print(f"   📊 عداد المرفقات في الجدول")
    
    return success_rate >= 0.6

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
