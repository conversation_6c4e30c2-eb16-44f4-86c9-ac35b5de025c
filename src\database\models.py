# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Date, Boolean, Text, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Company(Base):
    """جدول بيانات الشركة"""
    __tablename__ = 'companies'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, comment='اسم الشركة')
    name_en = Column(String(200), comment='اسم الشركة بالإنجليزية')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    email = Column(String(100), comment='البريد الإلكتروني')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    commercial_register = Column(String(50), comment='السجل التجاري')
    logo_path = Column(String(500), comment='مسار الشعار')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Branch(Base):
    """جدول الفروع"""
    __tablename__ = 'branches'
    
    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'), nullable=False)
    name = Column(String(200), nullable=False, comment='اسم الفرع')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    manager_name = Column(String(100), comment='اسم المدير')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    
    company = relationship("Company", back_populates="branches")

Company.branches = relationship("Branch", back_populates="company")

class User(Base):
    """جدول المستخدمين"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False, comment='اسم المستخدم')
    password_hash = Column(String(255), nullable=False, comment='كلمة المرور المشفرة')
    full_name = Column(String(100), nullable=False, comment='الاسم الكامل')
    email = Column(String(100), comment='البريد الإلكتروني')
    phone = Column(String(50), comment='الهاتف')
    role = Column(String(50), default='user', comment='الدور')
    is_active = Column(Boolean, default=True, comment='نشط')
    last_login = Column(DateTime, comment='آخر تسجيل دخول')
    created_at = Column(DateTime, default=datetime.now)

class Currency(Base):
    """جدول العملات"""
    __tablename__ = 'currencies'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(3), unique=True, nullable=False, comment='رمز العملة')
    name = Column(String(100), nullable=False, comment='اسم العملة')
    name_en = Column(String(100), comment='اسم العملة بالإنجليزية')
    symbol = Column(String(10), comment='رمز العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    is_base = Column(Boolean, default=False, comment='العملة الأساسية')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    suppliers = relationship("SupplierCurrency", back_populates="currency")


class FiscalYear(Base):
    """جدول السنوات المالية"""
    __tablename__ = 'fiscal_years'
    
    id = Column(Integer, primary_key=True)
    year = Column(Integer, nullable=False, comment='السنة')
    start_date = Column(DateTime, nullable=False, comment='تاريخ البداية')
    end_date = Column(DateTime, nullable=False, comment='تاريخ النهاية')
    is_current = Column(Boolean, default=False, comment='السنة الحالية')
    is_closed = Column(Boolean, default=False, comment='مغلقة')
    created_at = Column(DateTime, default=datetime.now)

class UnitOfMeasure(Base):
    """جدول وحدات القياس"""
    __tablename__ = 'units_of_measure'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, comment='اسم الوحدة')
    name_en = Column(String(100), comment='اسم الوحدة بالإنجليزية')
    symbol = Column(String(10), comment='رمز الوحدة')
    symbol_en = Column(String(10), comment='رمز الوحدة بالإنجليزية')
    description = Column(Text, comment='وصف الوحدة')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class ItemGroup(Base):
    """جدول مجموعات الأصناف"""
    __tablename__ = 'item_groups'

    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, comment='اسم المجموعة')
    name_en = Column(String(200), comment='اسم المجموعة بالإنجليزية')
    code = Column(String(50), unique=True, comment='كود المجموعة')
    description = Column(Text, comment='الوصف')
    parent_id = Column(Integer, ForeignKey('item_groups.id'), comment='المجموعة الأب')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    parent = relationship("ItemGroup", remote_side=[id])
    children = relationship("ItemGroup")

class Item(Base):
    """جدول الأصناف"""
    __tablename__ = 'items'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, nullable=False, comment='كود الصنف')
    name = Column(String(200), nullable=False, comment='اسم الصنف')
    name_en = Column(String(200), comment='اسم الصنف بالإنجليزية')
    description = Column(Text, comment='الوصف')
    group_id = Column(Integer, ForeignKey('item_groups.id'), comment='مجموعة الصنف')
    unit_id = Column(Integer, ForeignKey('units_of_measure.id'), comment='وحدة القياس')
    cost_price = Column(Float, default=0.0, comment='سعر التكلفة')
    selling_price = Column(Float, default=0.0, comment='سعر البيع')
    weight = Column(Float, comment='الوزن')
    dimensions = Column(String(100), comment='الأبعاد')
    total_package = Column(Integer, default=0, comment='عبوة الكلي')
    partial_package = Column(Integer, default=0, comment='عبوة الجزئي')
    gram_weight = Column(Float, default=0.0, comment='الوزن بالجرام')
    item_weight = Column(Float, default=0.0, comment='وزن الصنف')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    group = relationship("ItemGroup")
    unit = relationship("UnitOfMeasure")

class Supplier(Base):
    """جدول الموردين"""
    __tablename__ = 'suppliers'

    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, comment='كود المورد')
    name = Column(String(200), nullable=False, comment='اسم المورد')
    name_en = Column(String(200), comment='اسم المورد بالإنجليزية')
    supplier_type = Column(String(50), comment='نوع المورد')
    contact_person = Column(String(100), comment='الشخص المسؤول')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    commercial_register = Column(String(50), comment='السجل التجاري')

    # بيانات الاتصال
    phone = Column(String(50), comment='الهاتف')
    mobile = Column(String(50), comment='الجوال')
    email = Column(String(100), comment='البريد الإلكتروني')
    website = Column(String(200), comment='الموقع الإلكتروني')

    # العنوان
    country = Column(String(100), comment='الدولة')
    city = Column(String(100), comment='المدينة')
    address = Column(Text, comment='العنوان')
    postal_code = Column(String(20), comment='الرمز البريدي')

    # الإعدادات المالية
    credit_limit = Column(Float, default=0.0, comment='حد الائتمان')
    payment_terms = Column(Integer, default=0, comment='مدة السداد بالأيام')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    currencies = relationship("SupplierCurrency", back_populates="supplier")
    representatives = relationship("SupplierRepresentative", back_populates="supplier")


class SupplierCurrency(Base):
    """جدول ربط الموردين بالعملات"""
    __tablename__ = 'supplier_currencies'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='معرف المورد')
    currency_id = Column(Integer, ForeignKey('currencies.id'), nullable=False, comment='معرف العملة')
    is_preferred = Column(Boolean, default=False, comment='العملة المفضلة للمورد')
    exchange_rate_override = Column(Float, comment='سعر صرف مخصص للمورد')
    notes = Column(Text, comment='ملاحظات')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier", back_populates="currencies")
    currency = relationship("Currency", back_populates="suppliers")

    # فهرس فريد لمنع تكرار ربط نفس المورد بنفس العملة
    __table_args__ = (
        Index('idx_supplier_currency_unique', 'supplier_id', 'currency_id', unique=True),
    )


class PurchaseRepresentative(Base):
    """جدول مندوبي المشتريات"""
    __tablename__ = 'purchase_representatives'

    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, comment='كود المندوب')
    name = Column(String(200), nullable=False, comment='اسم المندوب')
    name_en = Column(String(200), comment='اسم المندوب بالإنجليزية')

    # بيانات الاتصال
    phone = Column(String(50), comment='الهاتف')
    mobile = Column(String(50), comment='الجوال')
    email = Column(String(100), comment='البريد الإلكتروني')

    # بيانات العمل
    department = Column(String(100), comment='القسم')
    position = Column(String(100), comment='المنصب')
    hire_date = Column(Date, comment='تاريخ التوظيف')
    salary = Column(Float, comment='الراتب')
    commission_rate = Column(Float, default=0.0, comment='نسبة العمولة')
    currency = Column(String(50), default='ريال سعودي', comment='العملة')

    # العنوان
    address = Column(Text, comment='العنوان')
    city = Column(String(100), comment='المدينة')

    # بيانات إضافية
    notes = Column(Text, comment='ملاحظات')
    emergency_contact = Column(String(200), comment='جهة الاتصال في الطوارئ')
    emergency_phone = Column(String(50), comment='هاتف الطوارئ')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    suppliers = relationship("SupplierRepresentative", back_populates="representative")


class SupplierRepresentative(Base):
    """جدول ربط الموردين بمندوبي المشتريات"""
    __tablename__ = 'supplier_representatives'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='معرف المورد')
    representative_id = Column(Integer, ForeignKey('purchase_representatives.id'), nullable=False, comment='معرف المندوب')
    is_primary = Column(Boolean, default=False, comment='المندوب الرئيسي للمورد')
    assigned_date = Column(Date, default=datetime.now, comment='تاريخ التعيين')
    notes = Column(Text, comment='ملاحظات')

    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier", back_populates="representatives")
    representative = relationship("PurchaseRepresentative", back_populates="suppliers")
    commission_rules = relationship("ShipmentCommission", back_populates="supplier_representative", cascade="all, delete-orphan")

    # فهرس فريد لمنع تكرار ربط نفس المورد بنفس المندوب
    __table_args__ = (
        Index('idx_supplier_representative_unique', 'supplier_id', 'representative_id', unique=True),
    )


class ShipmentCommission(Base):
    """نموذج عمولة الشحنات"""
    __tablename__ = 'shipment_commissions'

    id = Column(Integer, primary_key=True)
    supplier_representative_id = Column(Integer, ForeignKey('supplier_representatives.id'), nullable=False, comment='معرف ربط المورد بالمندوب')

    # نوع احتساب العمولة
    calculation_type = Column(String(50), default='quantity', comment='نوع الاحتساب: quantity (حسب الكمية) أو value (حسب قيمة البضاعة)')

    # شروط العمولة حسب الكمية
    min_quantity = Column(Float, default=0, comment='الحد الأدنى للكمية')
    max_quantity = Column(Float, comment='الحد الأقصى للكمية (اختياري)')
    quantity_commission_rate = Column(Float, default=0, comment='معدل العمولة للكمية (مبلغ ثابت لكل وحدة)')

    # شروط العمولة حسب قيمة البضاعة
    min_value = Column(Float, default=0, comment='الحد الأدنى لقيمة البضاعة')
    max_value = Column(Float, comment='الحد الأقصى لقيمة البضاعة (اختياري)')
    value_commission_percentage = Column(Float, default=0, comment='نسبة العمولة من قيمة البضاعة (%)')

    # معلومات إضافية
    description = Column(Text, comment='وصف شروط العمولة')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier_representative = relationship("SupplierRepresentative", back_populates="commission_rules")

    def __repr__(self):
        return f"<ShipmentCommission(type='{self.calculation_type}', supplier_rep_id={self.supplier_representative_id})>"


class SystemSettings(Base):
    """جدول إعدادات النظام"""
    __tablename__ = 'system_settings'

    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False, comment='مفتاح الإعداد')
    value = Column(Text, comment='قيمة الإعداد')
    description = Column(Text, comment='وصف الإعداد')
    category = Column(String(50), comment='فئة الإعداد')
    data_type = Column(String(20), default='string', comment='نوع البيانات')
    is_system = Column(Boolean, default=False, comment='إعداد نظام')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Shipment(Base):
    """جدول الشحنات"""
    __tablename__ = 'shipments'

    id = Column(Integer, primary_key=True)
    shipment_number = Column(String(50), unique=True, nullable=False, comment='رقم الشحنة')
    shipment_date = Column(DateTime, comment='تاريخ الشحنة')
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='المورد')
    supplier_invoice_number = Column(String(100), comment='رقم فاتورة المورد')

    # حالة الشحنة
    shipment_status = Column(String(50), default='تحت الطلب', comment='حالة الشحنة')
    # الخيارات: تحت الطلب - مؤكدة - تم الشحن - في الطريق - وصلت الميناء - في الجمارك - تم التسليم - ملغية - متاخرة

    # حالة الإفراج
    clearance_status = Column(String(50), default='بدون الافراج', comment='حالة الافراج')
    # الخيارات: بدون الافراج - مع الافراج

    # البيانات المالية
    total_amount = Column(Float, default=0.0, comment='المبلغ الإجمالي')
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment='العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    insurance_amount = Column(Float, default=0.0, comment='مبلغ التأمين')
    freight_amount = Column(Float, default=0.0, comment='مبلغ الشحن')
    customs_amount = Column(Float, default=0.0, comment='مبلغ الجمارك')
    other_charges = Column(Float, default=0.0, comment='رسوم أخرى')

    # بيانات الشحن
    shipping_method = Column(String(100), comment='طريقة الشحن')
    port_of_loading = Column(String(200), comment='ميناء التحميل')
    port_of_discharge = Column(String(200), comment='ميناء التفريغ')
    port_of_arrival = Column(String(200), comment='ميناء الوصول')
    final_destination = Column(String(200), comment='الوجهة النهائية')
    estimated_departure_date = Column(DateTime, comment='تاريخ المغادرة المتوقع')
    actual_departure_date = Column(DateTime, comment='تاريخ المغادرة الفعلي')
    estimated_arrival_date = Column(DateTime, comment='تاريخ الوصول المتوقع')
    actual_arrival_date = Column(DateTime, comment='تاريخ الوصول الفعلي')
    shipping_company = Column(String(200), comment='شركة الشحن')
    vessel_name = Column(String(200), comment='اسم السفينة')
    voyage_number = Column(String(100), comment='رقم الرحلة')
    dhl_number = Column(String(100), comment='رقم DHL')

    # بيانات إضافية
    notes = Column(Text, comment='ملاحظات')
    tracking_number = Column(String(100), comment='رقم التتبع')
    bill_of_lading = Column(String(100), comment='بوليصة الشحن')
    shipping_policy = Column(String(200), comment='بوليصة الشحن')
    container_number = Column(String(100), comment='رقم الحاوية')

    # روابط المستندات
    initial_documents_url = Column(String(1000), comment='رابط المستندات الأولية')
    dn_documents_url = Column(String(1000), comment='رابط مستندات DN')
    customs_documents_url = Column(String(1000), comment='رابط المستندات المرسلة للجمارك')
    bill_of_lading_url = Column(String(1000), comment='رابط بوليصة الشحن')
    items_images_url = Column(String(1000), comment='رابط صور الأصناف')
    other_documents_url = Column(String(1000), comment='رابط مستندات أخرى')

    # مسارات المرفقات
    initial_documents_files = Column(Text, comment='مسارات ملفات المستندات الأولية (JSON)')
    dn_documents_files = Column(Text, comment='مسارات ملفات مستندات DN (JSON)')
    customs_documents_files = Column(Text, comment='مسارات ملفات المستندات الجمركية (JSON)')
    bill_of_lading_files = Column(Text, comment='مسارات ملفات بوليصة الشحن (JSON)')
    items_images_files = Column(Text, comment='مسارات ملفات صور الأصناف (JSON)')
    other_documents_files = Column(Text, comment='مسارات ملفات مستندات أخرى (JSON)')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    created_by_user = relationship("User")

class PurchaseOrder(Base):
    """جدول طلبات الشراء من الموردين"""
    __tablename__ = 'purchase_orders'

    id = Column(Integer, primary_key=True)
    order_number = Column(String(50), unique=True, nullable=False, comment='رقم طلب الشراء')
    order_date = Column(DateTime, default=datetime.now, comment='تاريخ الطلب')
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='المورد')

    # حالة الطلب
    order_status = Column(String(50), default='مسودة', comment='حالة الطلب')
    # الخيارات: مسودة - مرسل - مؤكد - جزئي - مكتمل - ملغي

    # البيانات المالية
    total_amount = Column(Float, default=0.0, comment='المبلغ الإجمالي')
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment='العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    discount_amount = Column(Float, default=0.0, comment='مبلغ الخصم')
    tax_amount = Column(Float, default=0.0, comment='مبلغ الضريبة')

    # تواريخ مهمة
    expected_delivery_date = Column(DateTime, comment='تاريخ التسليم المتوقع')
    actual_delivery_date = Column(DateTime, comment='تاريخ التسليم الفعلي')

    # ملاحظات
    notes = Column(Text, comment='ملاحظات')
    terms_conditions = Column(Text, comment='الشروط والأحكام')

    # المستندات والمرفقات
    contract_url = Column(Text, comment='رابط العقد مع المورد')
    initial_designs_url = Column(Text, comment='رابط التصاميم الأولية')
    final_design_url = Column(Text, comment='رابط التصميم النهائي المعتمد')
    other_attachments_url = Column(Text, comment='رابط المرفقات الأخرى')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    created_by_user = relationship("User")
    items = relationship("PurchaseOrderItem", back_populates="purchase_order", cascade="all, delete-orphan")

class PurchaseOrderItem(Base):
    """جدول أصناف طلبات الشراء"""
    __tablename__ = 'purchase_order_items'

    id = Column(Integer, primary_key=True)
    purchase_order_id = Column(Integer, ForeignKey('purchase_orders.id'), nullable=False, comment='طلب الشراء')
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, comment='الصنف')
    quantity = Column(Float, nullable=False, comment='الكمية المطلوبة')
    unit_price = Column(Float, default=0.0, comment='سعر الوحدة')
    total_price = Column(Float, default=0.0, comment='السعر الإجمالي')
    discount_percentage = Column(Float, default=0.0, comment='نسبة الخصم')
    discount_amount = Column(Float, default=0.0, comment='مبلغ الخصم')

    # كميات التسليم
    delivered_quantity = Column(Float, default=0.0, comment='الكمية المسلمة')
    remaining_quantity = Column(Float, default=0.0, comment='الكمية المتبقية')

    # تواريخ
    expected_delivery_date = Column(DateTime, comment='تاريخ التسليم المتوقع للصنف')
    production_date = Column(Date, comment='تاريخ الإنتاج')
    expiry_date = Column(Date, comment='تاريخ الانتهاء')

    # ملاحظات
    notes = Column(Text, comment='ملاحظات الصنف')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    purchase_order = relationship("PurchaseOrder", back_populates="items")
    item = relationship("Item")

class ShipmentItem(Base):
    """جدول أصناف الشحنة"""
    __tablename__ = 'shipment_items'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False, comment='الشحنة')
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, comment='الصنف')
    purchase_order_item_id = Column(Integer, ForeignKey('purchase_order_items.id'), nullable=True, comment='صنف طلب الشراء المرتبط')
    quantity = Column(Float, nullable=False, comment='الكمية')
    unit_price = Column(Float, default=0.0, comment='سعر الوحدة')
    total_price = Column(Float, default=0.0, comment='السعر الإجمالي')
    production_date = Column(DateTime, comment='تاريخ الإنتاج')
    expiry_date = Column(DateTime, comment='تاريخ الانتهاء')
    weight = Column(Float, comment='الوزن')
    volume = Column(Float, comment='الحجم')
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    shipment = relationship("Shipment")
    item = relationship("Item")
    purchase_order_item = relationship("PurchaseOrderItem")

class Container(Base):
    """جدول الحاويات"""
    __tablename__ = 'containers'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False, comment='الشحنة')
    container_number = Column(String(50), nullable=False, comment='رقم الحاوية')
    container_type = Column(String(50), comment='نوع الحاوية')
    container_size = Column(String(20), comment='حجم الحاوية')
    seal_number = Column(String(50), comment='رقم الختم')
    weight_empty = Column(Float, comment='الوزن فارغة')
    weight_loaded = Column(Float, comment='الوزن محملة')
    max_weight = Column(Float, comment='الحد الأقصى للوزن')
    status = Column(String(50), default='فارغة', comment='حالة الحاوية')
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    shipment = relationship("Shipment")

class ShipmentDocument(Base):
    """جدول مستندات الشحنة"""
    __tablename__ = 'shipment_documents'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False, comment='الشحنة')
    document_name = Column(String(200), nullable=False, comment='اسم المستند')
    document_type = Column(String(100), comment='نوع المستند')
    document_url = Column(String(500), comment='رابط المستند')
    file_path = Column(String(500), comment='مسار الملف')
    description = Column(Text, comment='وصف المستند')
    upload_date = Column(DateTime, default=datetime.now, comment='تاريخ الرفع')

    # حقول النظام
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    shipment = relationship("Shipment")
    created_by_user = relationship("User")

# نماذج نظام إدارة الحوالات المتقدم

class Bank(Base):
    """جدول البنوك"""
    __tablename__ = 'banks'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False, comment='كود البنك')
    name = Column(String(200), nullable=False, comment='اسم البنك')
    name_en = Column(String(200), comment='اسم البنك بالإنجليزية')
    swift_code = Column(String(20), comment='كود SWIFT')
    country = Column(String(100), comment='الدولة')
    city = Column(String(100), comment='المدينة')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    email = Column(String(100), comment='البريد الإلكتروني')
    website = Column(String(200), comment='الموقع الإلكتروني')

    # معلومات إضافية
    bank_type = Column(String(50), comment='نوع البنك: محلي، دولي، إسلامي')
    is_active = Column(Boolean, default=True, comment='نشط')
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    accounts = relationship("BankAccount", back_populates="bank")
    remittances_sender = relationship("Remittance", foreign_keys="Remittance.sender_bank_id", back_populates="sender_bank")
    remittances_receiver = relationship("Remittance", foreign_keys="Remittance.receiver_bank_id", back_populates="receiver_bank")

class BankAccount(Base):
    """جدول حسابات البنوك"""
    __tablename__ = 'bank_accounts'

    id = Column(Integer, primary_key=True)
    bank_id = Column(Integer, ForeignKey('banks.id'), nullable=False, comment='البنك')
    account_number = Column(String(50), nullable=False, comment='رقم الحساب')
    account_name = Column(String(200), nullable=False, comment='اسم الحساب')
    account_type = Column(String(50), comment='نوع الحساب: جاري، توفير، استثماري')
    currency_id = Column(Integer, ForeignKey('currencies.id'), nullable=False, comment='العملة')

    # معلومات الحساب
    iban = Column(String(50), comment='رقم IBAN')
    balance = Column(Float, default=0.0, comment='الرصيد الحالي')
    available_balance = Column(Float, default=0.0, comment='الرصيد المتاح')
    credit_limit = Column(Float, default=0.0, comment='حد الائتمان')

    # إعدادات الحساب
    is_active = Column(Boolean, default=True, comment='نشط')
    is_default = Column(Boolean, default=False, comment='الحساب الافتراضي')
    allow_overdraft = Column(Boolean, default=False, comment='السماح بالسحب على المكشوف')

    # معلومات إضافية
    branch_name = Column(String(200), comment='اسم الفرع')
    contact_person = Column(String(100), comment='الشخص المسؤول')
    contact_phone = Column(String(50), comment='هاتف الاتصال')
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    bank = relationship("Bank", back_populates="accounts")
    currency = relationship("Currency")
    remittances_sender = relationship("Remittance", foreign_keys="Remittance.sender_account_id", back_populates="sender_account")
    remittances_receiver = relationship("Remittance", foreign_keys="Remittance.receiver_account_id", back_populates="receiver_account")

class RemittanceStatus(Base):
    """جدول حالات الحوالات"""
    __tablename__ = 'remittance_statuses'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False, comment='كود الحالة')
    name = Column(String(100), nullable=False, comment='اسم الحالة')
    name_en = Column(String(100), comment='اسم الحالة بالإنجليزية')
    description = Column(Text, comment='وصف الحالة')
    color = Column(String(7), comment='لون الحالة (HEX)')
    icon = Column(String(50), comment='أيقونة الحالة')
    order_index = Column(Integer, default=0, comment='ترتيب الحالة')
    is_final = Column(Boolean, default=False, comment='حالة نهائية')
    is_active = Column(Boolean, default=True, comment='نشط')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Remittance(Base):
    """جدول الحوالات"""
    __tablename__ = 'remittances'

    id = Column(Integer, primary_key=True)
    remittance_number = Column(String(50), unique=True, nullable=False, comment='رقم الحوالة')
    reference_number = Column(String(100), comment='الرقم المرجعي')

    # بيانات المرسل
    sender_name = Column(String(200), nullable=False, comment='اسم المرسل')
    sender_id_number = Column(String(50), comment='رقم هوية المرسل')
    sender_phone = Column(String(50), comment='هاتف المرسل')
    sender_address = Column(Text, comment='عنوان المرسل')

    # بيانات المستقبل
    receiver_name = Column(String(200), nullable=False, comment='اسم المستقبل')
    receiver_id_number = Column(String(50), comment='رقم هوية المستقبل')
    receiver_phone = Column(String(50), comment='هاتف المستقبل')
    receiver_address = Column(Text, comment='عنوان المستقبل')

    # بيانات المورد
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='المورد')

    # البيانات المالية
    amount = Column(Float, nullable=False, comment='مبلغ الحوالة')
    currency_id = Column(Integer, ForeignKey('currencies.id'), nullable=False, comment='العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    amount_in_base_currency = Column(Float, comment='المبلغ بالعملة الأساسية')

    # رسوم الحوالة
    transfer_fee = Column(Float, default=0.0, comment='رسوم التحويل')
    bank_charges = Column(Float, default=0.0, comment='رسوم البنك')
    other_charges = Column(Float, default=0.0, comment='رسوم أخرى')
    total_charges = Column(Float, default=0.0, comment='إجمالي الرسوم')
    net_amount = Column(Float, comment='المبلغ الصافي')

    # بيانات البنوك
    sender_bank_id = Column(Integer, ForeignKey('banks.id'), comment='البنك المرسل')
    sender_account_id = Column(Integer, ForeignKey('bank_accounts.id'), comment='حساب المرسل')
    receiver_bank_id = Column(Integer, ForeignKey('banks.id'), comment='البنك المستقبل')
    receiver_account_id = Column(Integer, ForeignKey('bank_accounts.id'), comment='حساب المستقبل')

    # حالة الحوالة
    status_id = Column(Integer, ForeignKey('remittance_statuses.id'), comment='حالة الحوالة')
    current_status = Column(String(50), default='مسودة', comment='الحالة الحالية')

    # التواريخ المهمة
    remittance_date = Column(DateTime, default=datetime.now, comment='تاريخ الحوالة')
    value_date = Column(DateTime, comment='تاريخ القيمة')
    expected_completion_date = Column(DateTime, comment='تاريخ الإنجاز المتوقع')
    actual_completion_date = Column(DateTime, comment='تاريخ الإنجاز الفعلي')

    # معلومات إضافية
    purpose = Column(String(200), comment='الغرض من الحوالة')
    notes = Column(Text, comment='ملاحظات')
    internal_notes = Column(Text, comment='ملاحظات داخلية')

    # معلومات الأمان والتتبع
    is_active = Column(Boolean, default=True, comment='نشط')
    is_locked = Column(Boolean, default=False, comment='مقفل للتعديل')
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    approved_by = Column(Integer, ForeignKey('users.id'), comment='اعتمد بواسطة')
    approved_at = Column(DateTime, comment='تاريخ الاعتماد')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    status = relationship("RemittanceStatus")
    sender_bank = relationship("Bank", foreign_keys=[sender_bank_id])
    receiver_bank = relationship("Bank", foreign_keys=[receiver_bank_id])
    sender_account = relationship("BankAccount", foreign_keys=[sender_account_id])
    receiver_account = relationship("BankAccount", foreign_keys=[receiver_account_id])
    created_by_user = relationship("User", foreign_keys=[created_by])
    approved_by_user = relationship("User", foreign_keys=[approved_by])
    status_history = relationship("RemittanceStatusHistory", back_populates="remittance", cascade="all, delete-orphan")
    transactions = relationship("SupplierAccountTransaction", back_populates="remittance", cascade="all, delete-orphan")

class RemittanceStatusHistory(Base):
    """جدول تاريخ حالات الحوالات"""
    __tablename__ = 'remittance_status_history'

    id = Column(Integer, primary_key=True)
    remittance_id = Column(Integer, ForeignKey('remittances.id'), nullable=False, comment='الحوالة')
    status_id = Column(Integer, ForeignKey('remittance_statuses.id'), comment='الحالة')
    old_status = Column(String(50), comment='الحالة السابقة')
    new_status = Column(String(50), nullable=False, comment='الحالة الجديدة')

    # معلومات التغيير
    change_reason = Column(Text, comment='سبب التغيير')
    notes = Column(Text, comment='ملاحظات')

    # معلومات المستخدم والوقت
    changed_by = Column(Integer, ForeignKey('users.id'), nullable=False, comment='غير بواسطة')
    changed_at = Column(DateTime, default=datetime.now, comment='تاريخ التغيير')
    ip_address = Column(String(45), comment='عنوان IP')
    user_agent = Column(Text, comment='معلومات المتصفح')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)

    # العلاقات
    remittance = relationship("Remittance", back_populates="status_history")
    status = relationship("RemittanceStatus")
    changed_by_user = relationship("User")

class SupplierAccount(Base):
    """جدول حسابات الموردين"""
    __tablename__ = 'supplier_accounts'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='المورد')
    currency_id = Column(Integer, ForeignKey('currencies.id'), nullable=False, comment='العملة')
    account_number = Column(String(50), comment='رقم الحساب')

    # الأرصدة
    opening_balance = Column(Float, default=0.0, comment='الرصيد الافتتاحي')
    current_balance = Column(Float, default=0.0, comment='الرصيد الحالي')
    available_balance = Column(Float, default=0.0, comment='الرصيد المتاح')
    blocked_balance = Column(Float, default=0.0, comment='الرصيد المحجوز')

    # حدود الائتمان
    credit_limit = Column(Float, default=0.0, comment='حد الائتمان')
    used_credit = Column(Float, default=0.0, comment='الائتمان المستخدم')
    available_credit = Column(Float, default=0.0, comment='الائتمان المتاح')

    # إعدادات الحساب
    is_active = Column(Boolean, default=True, comment='نشط')
    is_blocked = Column(Boolean, default=False, comment='محجوز')
    block_reason = Column(Text, comment='سبب الحجز')

    # تواريخ مهمة
    last_transaction_date = Column(DateTime, comment='تاريخ آخر معاملة')
    last_balance_update = Column(DateTime, comment='تاريخ آخر تحديث للرصيد')

    # معلومات إضافية
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    transactions = relationship("SupplierAccountTransaction", back_populates="account", cascade="all, delete-orphan")

    # فهرس فريد لمنع تكرار حساب نفس المورد بنفس العملة
    __table_args__ = (
        Index('idx_supplier_account_unique', 'supplier_id', 'currency_id', unique=True),
    )

class SupplierAccountTransaction(Base):
    """جدول معاملات حسابات الموردين"""
    __tablename__ = 'supplier_account_transactions'

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('supplier_accounts.id'), nullable=False, comment='الحساب')
    remittance_id = Column(Integer, ForeignKey('remittances.id'), comment='الحوالة المرتبطة')

    # بيانات المعاملة
    transaction_number = Column(String(50), unique=True, nullable=False, comment='رقم المعاملة')
    transaction_type = Column(String(50), nullable=False, comment='نوع المعاملة: دائن، مدين، تحويل')
    transaction_date = Column(DateTime, default=datetime.now, comment='تاريخ المعاملة')
    value_date = Column(DateTime, comment='تاريخ القيمة')

    # المبالغ
    debit_amount = Column(Float, default=0.0, comment='المبلغ المدين')
    credit_amount = Column(Float, default=0.0, comment='المبلغ الدائن')
    balance_before = Column(Float, comment='الرصيد قبل المعاملة')
    balance_after = Column(Float, comment='الرصيد بعد المعاملة')

    # وصف المعاملة
    description = Column(Text, nullable=False, comment='وصف المعاملة')
    reference_number = Column(String(100), comment='الرقم المرجعي')
    notes = Column(Text, comment='ملاحظات')

    # معلومات الأمان
    is_reversed = Column(Boolean, default=False, comment='معكوسة')
    reversed_by = Column(Integer, ForeignKey('users.id'), comment='عكست بواسطة')
    reversed_at = Column(DateTime, comment='تاريخ العكس')
    reversal_reason = Column(Text, comment='سبب العكس')

    # حقول النظام
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False, comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    account = relationship("SupplierAccount", back_populates="transactions")
    remittance = relationship("Remittance", back_populates="transactions")
    created_by_user = relationship("User", foreign_keys=[created_by])
    reversed_by_user = relationship("User", foreign_keys=[reversed_by])

class RemittanceDocument(Base):
    """جدول مستندات الحوالات"""
    __tablename__ = 'remittance_documents'

    id = Column(Integer, primary_key=True)
    remittance_id = Column(Integer, ForeignKey('remittances.id'), nullable=False, comment='الحوالة')

    # بيانات المستند
    document_name = Column(String(200), nullable=False, comment='اسم المستند')
    document_type = Column(String(100), comment='نوع المستند')
    file_path = Column(String(500), comment='مسار الملف')
    file_size = Column(Integer, comment='حجم الملف بالبايت')
    file_extension = Column(String(10), comment='امتداد الملف')
    mime_type = Column(String(100), comment='نوع MIME')

    # معلومات المستند
    description = Column(Text, comment='وصف المستند')
    is_required = Column(Boolean, default=False, comment='مستند مطلوب')
    is_verified = Column(Boolean, default=False, comment='مستند محقق')
    verified_by = Column(Integer, ForeignKey('users.id'), comment='حقق بواسطة')
    verified_at = Column(DateTime, comment='تاريخ التحقق')

    # حقول النظام
    uploaded_by = Column(Integer, ForeignKey('users.id'), nullable=False, comment='رفع بواسطة')
    uploaded_at = Column(DateTime, default=datetime.now, comment='تاريخ الرفع')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    remittance = relationship("Remittance")
    uploaded_by_user = relationship("User", foreign_keys=[uploaded_by])
    verified_by_user = relationship("User", foreign_keys=[verified_by])

class RemittanceReport(Base):
    """جدول تقارير الحوالات"""
    __tablename__ = 'remittance_reports'

    id = Column(Integer, primary_key=True)
    report_name = Column(String(200), nullable=False, comment='اسم التقرير')
    report_type = Column(String(50), nullable=False, comment='نوع التقرير: يومي، شهري، سنوي، مورد')

    # فترة التقرير
    date_from = Column(DateTime, comment='من تاريخ')
    date_to = Column(DateTime, comment='إلى تاريخ')
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), comment='المورد (للتقارير الخاصة بمورد)')
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment='العملة')

    # بيانات التقرير
    total_remittances = Column(Integer, default=0, comment='عدد الحوالات')
    total_amount = Column(Float, default=0.0, comment='إجمالي المبلغ')
    total_charges = Column(Float, default=0.0, comment='إجمالي الرسوم')
    net_amount = Column(Float, default=0.0, comment='صافي المبلغ')

    # إحصائيات الحالات
    pending_count = Column(Integer, default=0, comment='عدد المعلقة')
    completed_count = Column(Integer, default=0, comment='عدد المكتملة')
    cancelled_count = Column(Integer, default=0, comment='عدد الملغية')

    # معلومات التقرير
    report_data = Column(Text, comment='بيانات التقرير (JSON)')
    file_path = Column(String(500), comment='مسار ملف التقرير')

    # حقول النظام
    generated_by = Column(Integer, ForeignKey('users.id'), nullable=False, comment='أنشئ بواسطة')
    generated_at = Column(DateTime, default=datetime.now, comment='تاريخ الإنشاء')
    created_at = Column(DateTime, default=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    generated_by_user = relationship("User")

# إضافة العلاقات العكسية للنماذج الموجودة
Remittance.documents = relationship("RemittanceDocument", back_populates="remittance", cascade="all, delete-orphan")
RemittanceDocument.remittance = relationship("Remittance", back_populates="documents")

# إضافة العلاقات العكسية للموردين
Supplier.accounts = relationship("SupplierAccount", back_populates="supplier", cascade="all, delete-orphan")
Supplier.remittances = relationship("Remittance", back_populates="supplier")

# إضافة العلاقات العكسية للعملات
Currency.supplier_accounts = relationship("SupplierAccount", back_populates="currency")
Currency.remittances = relationship("Remittance", back_populates="currency")
Currency.bank_accounts = relationship("BankAccount", back_populates="currency")

# إضافة العلاقات العكسية للشحنات الموجودة
Shipment.items = relationship("ShipmentItem", back_populates="shipment")
Shipment.containers = relationship("Container", back_populates="shipment")
Shipment.documents = relationship("ShipmentDocument", back_populates="shipment")

ShipmentItem.shipment = relationship("Shipment", back_populates="items")
Container.shipment = relationship("Shipment", back_populates="containers")
ShipmentDocument.shipment = relationship("Shipment", back_populates="documents")
