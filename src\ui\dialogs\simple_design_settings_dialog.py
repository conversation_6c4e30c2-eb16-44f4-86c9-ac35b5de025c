#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إعدادات التصميم المبسطة - ProShipment
Simple Design Settings Dialog
"""

import sys
from pathlib import Path

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QComboBox, QCheckBox, QGroupBox,
    QSlider, QSpinBox, QTabWidget, QWidget, QFrame,
    QMessageBox, QColorDialog, QFontDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

class SimpleDesignSettingsDialog(QDialog):
    """نافذة إعدادات التصميم المبسطة"""
    
    # إشارات
    settings_applied = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("إعدادات التصميم والمظهر")
        self.setModal(True)
        self.resize(600, 500)
        
        self.setup_ui()
        self.load_current_settings()
        self.connect_signals()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel("⚙️ إعدادات التصميم والمظهر")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # تبويب الثيم
        self.create_theme_tab()
        
        # تبويب التصميم المتجاوب
        self.create_responsive_tab()
        
        # تبويب الخطوط
        self.create_fonts_tab()
        
        # أزرار التحكم
        self.create_control_buttons(main_layout)
    
    def create_theme_tab(self):
        """إنشاء تبويب الثيم"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة الثيم
        theme_group = QGroupBox("🎨 الثيم العام")
        theme_layout = QFormLayout(theme_group)
        
        # اختيار الثيم
        self.theme_combo = QComboBox()
        self.theme_combo.addItem("فاتح", "light")
        self.theme_combo.addItem("مظلم", "dark")
        self.theme_combo.addItem("تلقائي", "auto")
        theme_layout.addRow("نوع الثيم:", self.theme_combo)
        
        # مجموعة مخطط الألوان
        colors_group = QGroupBox("🌈 مخطط الألوان")
        colors_layout = QFormLayout(colors_group)
        
        # اختيار مخطط الألوان
        self.color_scheme_combo = QComboBox()
        self.color_scheme_combo.addItem("أزرق", "blue")
        self.color_scheme_combo.addItem("أخضر", "green")
        self.color_scheme_combo.addItem("بنفسجي", "purple")
        self.color_scheme_combo.addItem("برتقالي", "orange")
        self.color_scheme_combo.addItem("أحمر", "red")
        self.color_scheme_combo.addItem("تركوازي", "teal")
        colors_layout.addRow("مخطط الألوان:", self.color_scheme_combo)
        
        layout.addWidget(theme_group)
        layout.addWidget(colors_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "الثيم والألوان")
    
    def create_responsive_tab(self):
        """إنشاء تبويب التصميم المتجاوب"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة التصميم المتجاوب
        responsive_group = QGroupBox("📱 التصميم المتجاوب")
        responsive_layout = QFormLayout(responsive_group)
        
        # تفعيل التصميم المتجاوب
        self.responsive_enabled = QCheckBox("تفعيل التصميم المتجاوب")
        self.responsive_enabled.setChecked(True)
        responsive_layout.addRow(self.responsive_enabled)
        
        # التوسيط التلقائي
        self.auto_center = QCheckBox("توسيط النوافذ تلقائياً")
        self.auto_center.setChecked(True)
        responsive_layout.addRow(self.auto_center)
        
        # معلومات الشاشة
        screen_group = QGroupBox("🖥️ معلومات الشاشة")
        screen_layout = QFormLayout(screen_group)
        
        try:
            from src.ui.responsive.responsive_manager import responsive_manager
            screen_info = responsive_manager.get_screen_info()
            
            self.screen_size_label = QLabel(screen_info.get("size", "غير معروف"))
            screen_layout.addRow("حجم الشاشة:", self.screen_size_label)
            
            self.resolution_label = QLabel(screen_info.get("resolution", "غير معروف"))
            screen_layout.addRow("الدقة:", self.resolution_label)
            
            self.scale_factor_label = QLabel(str(screen_info.get("scale_factor", 1.0)))
            screen_layout.addRow("معامل التكبير:", self.scale_factor_label)
        except:
            screen_layout.addRow("معلومات الشاشة:", QLabel("غير متاحة"))
        
        layout.addWidget(responsive_group)
        layout.addWidget(screen_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "التصميم المتجاوب")
    
    def create_fonts_tab(self):
        """إنشاء تبويب الخطوط"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة الخطوط
        fonts_group = QGroupBox("🔤 إعدادات الخطوط")
        fonts_layout = QFormLayout(fonts_group)
        
        # حجم الخط الأساسي
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(10)
        fonts_layout.addRow("حجم الخط الأساسي:", self.font_size_spin)
        
        # معامل تكبير الخط
        self.font_scale_slider = QSlider(Qt.Horizontal)
        self.font_scale_slider.setRange(50, 200)
        self.font_scale_slider.setValue(100)
        self.font_scale_label = QLabel("100%")
        
        font_scale_layout = QHBoxLayout()
        font_scale_layout.addWidget(self.font_scale_slider)
        font_scale_layout.addWidget(self.font_scale_label)
        
        fonts_layout.addRow("معامل تكبير الخط:", font_scale_layout)
        
        # اختيار خط مخصص
        self.custom_font_button = QPushButton("اختيار خط مخصص")
        self.custom_font_button.clicked.connect(self.choose_custom_font)
        fonts_layout.addRow("خط مخصص:", self.custom_font_button)
        
        # معاينة الخط
        self.font_preview = QLabel("نموذج نص للمعاينة - Sample Text Preview")
        self.font_preview.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                padding: 10px;
                background-color: white;
                border-radius: 5px;
            }
        """)
        fonts_layout.addRow("معاينة:", self.font_preview)
        
        layout.addWidget(fonts_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "الخطوط")
    
    def create_control_buttons(self, main_layout):
        """إنشاء أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.addStretch()
        
        # زر تطبيق
        apply_button = QPushButton("تطبيق")
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        apply_button.clicked.connect(self.apply_settings)
        buttons_layout.addWidget(apply_button)
        
        # زر معاينة
        preview_button = QPushButton("معاينة")
        preview_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        preview_button.clicked.connect(self.preview_settings)
        buttons_layout.addWidget(preview_button)
        
        # زر إعادة تعيين
        reset_button = QPushButton("إعادة تعيين")
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_button.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(reset_button)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.close)
        buttons_layout.addWidget(close_button)
        
        main_layout.addWidget(buttons_frame)
    
    def connect_signals(self):
        """ربط الإشارات"""
        # تحديث معاينة الخط
        self.font_size_spin.valueChanged.connect(self.update_font_preview)
        self.font_scale_slider.valueChanged.connect(self.update_font_scale_label)
        self.font_scale_slider.valueChanged.connect(self.update_font_preview)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            from src.ui.themes.theme_manager import theme_manager
            from src.ui.responsive.responsive_manager import responsive_manager
            
            # تحميل الثيم الحالي
            current_theme = theme_manager.current_theme
            for i in range(self.theme_combo.count()):
                if self.theme_combo.itemData(i) == current_theme.value:
                    self.theme_combo.setCurrentIndex(i)
                    break
            
            # تحميل مخطط الألوان الحالي
            current_scheme = theme_manager.current_color_scheme
            for i in range(self.color_scheme_combo.count()):
                if self.color_scheme_combo.itemData(i) == current_scheme.value:
                    self.color_scheme_combo.setCurrentIndex(i)
                    break
            
            # تحميل إعدادات الخط
            font_size = responsive_manager.get_font_size("base")
            if font_size:
                self.font_size_spin.setValue(font_size)
            
            font_scale = int(responsive_manager.font_scale * 100)
            self.font_scale_slider.setValue(font_scale)
            self.update_font_scale_label()
            
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def update_font_preview(self):
        """تحديث معاينة الخط"""
        font_size = self.font_size_spin.value()
        scale = self.font_scale_slider.value() / 100.0
        final_size = int(font_size * scale)
        
        font = self.font_preview.font()
        font.setPointSize(final_size)
        self.font_preview.setFont(font)
    
    def update_font_scale_label(self):
        """تحديث تسمية معامل تكبير الخط"""
        scale = self.font_scale_slider.value()
        self.font_scale_label.setText(f"{scale}%")
    
    def choose_custom_font(self):
        """اختيار خط مخصص"""
        font, ok = QFontDialog.getFont(self.font_preview.font(), self)
        if ok:
            self.font_preview.setFont(font)
            self.font_size_spin.setValue(font.pointSize())
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            from src.ui.themes.theme_manager import theme_manager, ThemeType, ColorScheme
            from src.ui.responsive.responsive_manager import responsive_manager
            
            # تطبيق الثيم
            theme_key = self.theme_combo.currentData()
            if theme_key:
                theme_manager.set_theme(ThemeType(theme_key))
            
            # تطبيق مخطط الألوان
            scheme_key = self.color_scheme_combo.currentData()
            if scheme_key:
                theme_manager.set_color_scheme(ColorScheme(scheme_key))
            
            # تطبيق إعدادات الخط
            responsive_manager.font_scale = self.font_scale_slider.value() / 100.0
            
            # إشارة التطبيق
            self.settings_applied.emit()
            
            QMessageBox.information(self, "نجح", "تم تطبيق الإعدادات بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تطبيق الإعدادات:\n{str(e)}")
    
    def preview_settings(self):
        """معاينة الإعدادات"""
        # تطبيق مؤقت للمعاينة
        self.apply_settings()
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إعادة تعيين إلى القيم الافتراضية
            self.theme_combo.setCurrentIndex(0)
            self.color_scheme_combo.setCurrentIndex(0)
            self.font_size_spin.setValue(10)
            self.font_scale_slider.setValue(100)
            self.responsive_enabled.setChecked(True)
            self.auto_center.setChecked(True)
            
            self.update_font_scale_label()
            self.update_font_preview()
