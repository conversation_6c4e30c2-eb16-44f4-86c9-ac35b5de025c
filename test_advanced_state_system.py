#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الحالات المتقدم - التحسين العملاق
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_window_initialization():
    """اختبار تهيئة النافذة في الوضع المعطل"""
    print("🚀 اختبار تهيئة النافذة في الوضع المعطل...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من الحالة الافتراضية
        print("🔍 فحص الحالة الافتراضية:")
        
        # التحقق من حالة النافذة
        if hasattr(window, 'window_mode'):
            mode = window.window_mode
            print(f"   حالة النافذة: {mode}")
            
            if mode == "DISABLED":
                print("   ✅ النافذة في الوضع المعطل افتراضياً")
            else:
                print("   ❌ النافذة ليست في الوضع المعطل")
                return False
        else:
            print("   ❌ متغير حالة النافذة غير موجود")
            return False
        
        # التحقق من مؤشر الحالة
        if hasattr(window, 'form_status_label'):
            status_text = window.form_status_label.text()
            print(f"   مؤشر الحالة: {status_text}")
            
            if "معطل" in status_text:
                print("   ✅ مؤشر الحالة يظهر التعطيل")
            else:
                print("   ❌ مؤشر الحالة لا يظهر التعطيل")
                return False
        else:
            print("   ❌ مؤشر الحالة غير موجود")
            return False
        
        # التحقق من حالة الأزرار
        button_states = {}
        buttons_to_check = [
            ('add_new_btn', 'زر الإضافة', True),
            ('edit_btn', 'زر التعديل', True),
            ('save_request_btn', 'زر الحفظ', False),
            ('cancel_btn', 'زر الإلغاء', False)
        ]
        
        for button_attr, button_desc, expected_enabled in buttons_to_check:
            if hasattr(window, button_attr):
                button = getattr(window, button_attr)
                is_enabled = button.isEnabled()
                button_states[button_desc] = is_enabled
                
                status = "مفعل" if is_enabled else "معطل"
                expected_status = "مفعل" if expected_enabled else "معطل"
                
                print(f"   {button_desc}: {status} (متوقع: {expected_status})")
                
                if is_enabled == expected_enabled:
                    print(f"      ✅ حالة {button_desc} صحيحة")
                else:
                    print(f"      ❌ حالة {button_desc} خاطئة")
                    return False
            else:
                print(f"   ❌ {button_desc} غير موجود")
                return False
        
        # التحقق من حالة الحقول
        if hasattr(window, 'form_fields') and window.form_fields:
            enabled_fields = sum(1 for field in window.form_fields if field and field.isEnabled())
            total_fields = len(window.form_fields)
            
            print(f"   الحقول المفعلة: {enabled_fields}/{total_fields}")
            
            if enabled_fields == 0:
                print("   ✅ جميع الحقول معطلة")
            else:
                print("   ❌ بعض الحقول مفعلة")
                return False
        else:
            print("   ❌ قائمة الحقول غير موجودة")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التهيئة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_new_mode_activation():
    """اختبار تفعيل وضع الإضافة"""
    print("\n➕ اختبار تفعيل وضع الإضافة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # تفعيل وضع الإضافة
        window.enable_new_mode()
        
        # التحقق من الحالة الجديدة
        print("🔍 فحص وضع الإضافة:")
        
        # التحقق من حالة النافذة
        if window.window_mode == "NEW":
            print("   ✅ النافذة في وضع الإضافة")
        else:
            print(f"   ❌ النافذة في وضع خاطئ: {window.window_mode}")
            return False
        
        # التحقق من مؤشر الحالة
        status_text = window.form_status_label.text()
        print(f"   مؤشر الحالة: {status_text}")
        
        if "إضافة" in status_text:
            print("   ✅ مؤشر الحالة يظهر وضع الإضافة")
        else:
            print("   ❌ مؤشر الحالة لا يظهر وضع الإضافة")
            return False
        
        # التحقق من حالة الأزرار
        expected_button_states = {
            'add_new_btn': False,      # معطل
            'edit_btn': False,         # معطل
            'save_request_btn': True,  # مفعل
            'cancel_btn': True         # مفعل
        }
        
        for button_attr, expected_enabled in expected_button_states.items():
            if hasattr(window, button_attr):
                button = getattr(window, button_attr)
                is_enabled = button.isEnabled()
                
                status = "مفعل" if is_enabled else "معطل"
                expected_status = "مفعل" if expected_enabled else "معطل"
                
                print(f"   {button_attr}: {status} (متوقع: {expected_status})")
                
                if is_enabled == expected_enabled:
                    print(f"      ✅ حالة الزر صحيحة")
                else:
                    print(f"      ❌ حالة الزر خاطئة")
                    return False
        
        # التحقق من حالة الحقول
        enabled_fields = sum(1 for field in window.form_fields if field and field.isEnabled())
        total_fields = len(window.form_fields)
        
        print(f"   الحقول المفعلة: {enabled_fields}/{total_fields}")
        
        if enabled_fields == total_fields:
            print("   ✅ جميع الحقول مفعلة")
        else:
            print("   ❌ بعض الحقول معطلة")
            return False
        
        # التحقق من نص زر الحفظ
        save_btn_text = window.save_request_btn.text()
        print(f"   نص زر الحفظ: {save_btn_text}")
        
        if "طلب جديد" in save_btn_text:
            print("   ✅ نص زر الحفظ صحيح")
        else:
            print("   ❌ نص زر الحفظ خاطئ")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وضع الإضافة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cancel_operation():
    """اختبار إلغاء العملية"""
    print("\n❌ اختبار إلغاء العملية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # تفعيل وضع الإضافة أولاً
        window.enable_new_mode()
        
        # التحقق من أن النافذة في وضع الإضافة
        if window.window_mode != "NEW":
            print("   ❌ فشل في تفعيل وضع الإضافة")
            return False
        
        print("   ✅ تم تفعيل وضع الإضافة")
        
        # محاكاة إلغاء العملية (بدون رسالة تأكيد)
        window.set_window_mode("DISABLED")
        
        # التحقق من العودة للوضع المعطل
        print("🔍 فحص العودة للوضع المعطل:")
        
        if window.window_mode == "DISABLED":
            print("   ✅ النافذة عادت للوضع المعطل")
        else:
            print(f"   ❌ النافذة في وضع خاطئ: {window.window_mode}")
            return False
        
        # التحقق من مؤشر الحالة
        status_text = window.form_status_label.text()
        if "معطل" in status_text:
            print("   ✅ مؤشر الحالة يظهر التعطيل")
        else:
            print("   ❌ مؤشر الحالة لا يظهر التعطيل")
            return False
        
        # التحقق من حالة الحقول
        enabled_fields = sum(1 for field in window.form_fields if field and field.isEnabled())
        
        if enabled_fields == 0:
            print("   ✅ جميع الحقول معطلة")
        else:
            print("   ❌ بعض الحقول ما زالت مفعلة")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإلغاء: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_fields_collection():
    """اختبار جمع حقول النموذج"""
    print("\n📋 اختبار جمع حقول النموذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من جمع الحقول
        if hasattr(window, 'form_fields'):
            field_count = len(window.form_fields)
            print(f"   عدد الحقول المجمعة: {field_count}")
            
            if field_count > 0:
                print("   ✅ تم جمع الحقول بنجاح")
                
                # التحقق من أنواع الحقول
                field_types = {}
                for field in window.form_fields:
                    if field:
                        field_type = type(field).__name__
                        field_types[field_type] = field_types.get(field_type, 0) + 1
                
                print("   أنواع الحقول:")
                for field_type, count in field_types.items():
                    print(f"      {field_type}: {count}")
                
                return True
            else:
                print("   ❌ لم يتم جمع أي حقول")
                return False
        else:
            print("   ❌ قائمة الحقول غير موجودة")
            return False
        
        window.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار جمع الحقول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام الحالات المتقدم - التحسين العملاق...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_window_initialization())
    results.append(test_form_fields_collection())
    results.append(test_new_mode_activation())
    results.append(test_cancel_operation())
    
    # عرض النتائج النهائية
    print("\n" + "="*80)
    print("🎯 ملخص اختبار نظام الحالات المتقدم:")
    print("="*80)
    
    test_names = [
        "تهيئة النافذة في الوضع المعطل",
        "جمع حقول النموذج",
        "تفعيل وضع الإضافة",
        "إلغاء العملية"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 التحسين العملاق يعمل بإحترافية شديدة!")
        print("✅ النافذة تبدأ في الوضع المعطل")
        print("✅ أزرار الإضافة والتعديل تعمل بشكل مثالي")
        print("✅ نظام الحالات يتحكم في جميع العناصر")
        print("✅ مؤشرات الحالة واضحة ومفيدة")
        print("✅ إلغاء العمليات يعمل بسلاسة")
        
        print("\n🌟 الميزات المحققة:")
        print("   🔒 تعطيل افتراضي للنافذة")
        print("   ➕ زر إضافة لتفعيل وضع الإدخال الجديد")
        print("   ✏️ زر تعديل لتفعيل وضع التعديل")
        print("   💾 زر حفظ ذكي يتغير حسب الوضع")
        print("   ❌ زر إلغاء للعودة للوضع المعطل")
        print("   📊 مؤشر حالة بصري واضح")
        print("   🎛️ تحكم كامل في جميع الحقول")
        
        print("\n🏆 مستوى الاحترافية:")
        print("   🎯 تصميم نظام حالات متقدم")
        print("   🔧 تحكم دقيق في واجهة المستخدم")
        print("   🛡️ حماية من الأخطاء والتداخل")
        print("   📱 تجربة مستخدم متميزة")
        print("   🚀 أداء محسن وموثوق")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم ميزات التحسين تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة ميزات فشلت. يرجى مراجعة:")
        print("- نظام الحالات")
        print("- أزرار التحكم")
        print("- تفعيل/تعطيل الحقول")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
