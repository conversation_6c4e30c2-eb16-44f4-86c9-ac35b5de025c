#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديثات حقل الفرع والغرض من التحويل
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_branch_updates():
    """اختبار تحديث حقل الفرع"""
    print("🏢 اختبار تحديث حقل الفرع...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من وجود حقل الفرع
        if hasattr(window, 'branch_combo'):
            print("✅ حقل الفرع موجود")
            
            # التحقق من عدد العناصر في القائمة
            branch_count = window.branch_combo.count()
            print(f"📊 عدد الفروع في القائمة: {branch_count}")
            
            # التحقق من الفروع الجديدة
            expected_branches = [
                "اختر الفرع...",
                "الادارة العامة صنعاء",
                "فرع عدن", 
                "فرع المكلا"
            ]
            
            found_branches = []
            for i in range(branch_count):
                branch_text = window.branch_combo.itemText(i)
                found_branches.append(branch_text)
                print(f"   {i+1}. {branch_text}")
            
            # التحقق من وجود الفروع المطلوبة
            missing_branches = []
            for expected_branch in expected_branches:
                if expected_branch not in found_branches:
                    missing_branches.append(expected_branch)
            
            if missing_branches:
                print(f"❌ الفروع التالية مفقودة: {', '.join(missing_branches)}")
                return False
            else:
                print("✅ جميع الفروع الجديدة موجودة")
            
            # اختبار اختيار فرع
            window.branch_combo.setCurrentIndex(1)  # الادارة العامة صنعاء
            selected_branch = window.branch_combo.currentText()
            
            if "الادارة العامة صنعاء" in selected_branch:
                print("✅ اختيار الفرع يعمل بشكل صحيح")
            else:
                print("❌ اختيار الفرع لا يعمل")
                return False
            
        else:
            print("❌ حقل الفرع غير موجود")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفرع: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transfer_purpose_default():
    """اختبار القيمة الافتراضية لحقل الغرض من التحويل"""
    print("\n📝 اختبار القيمة الافتراضية لحقل الغرض من التحويل...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # التحقق من وجود حقل الغرض من التحويل
        if hasattr(window, 'transfer_purpose_input'):
            print("✅ حقل الغرض من التحويل موجود")
            
            # التحقق من القيمة الافتراضية
            default_value = window.transfer_purpose_input.text()
            expected_value = "COST OF FOODSTUFF"
            
            print(f"📋 القيمة الحالية: '{default_value}'")
            print(f"📋 القيمة المتوقعة: '{expected_value}'")
            
            if default_value == expected_value:
                print("✅ القيمة الافتراضية صحيحة")
            else:
                print("❌ القيمة الافتراضية غير صحيحة")
                return False
            
            # اختبار تغيير القيمة
            test_value = "TEST PURPOSE"
            window.transfer_purpose_input.setText(test_value)
            
            if window.transfer_purpose_input.text() == test_value:
                print("✅ تغيير القيمة يعمل بشكل صحيح")
            else:
                print("❌ تغيير القيمة لا يعمل")
                return False
            
            # اختبار مسح النموذج (يجب أن يعيد القيمة الافتراضية)
            window.clear_new_request_form()
            
            cleared_value = window.transfer_purpose_input.text()
            print(f"📋 القيمة بعد المسح: '{cleared_value}'")
            
            if cleared_value == expected_value:
                print("✅ إعادة تعيين القيمة الافتراضية بعد المسح يعمل")
            else:
                print("❌ إعادة تعيين القيمة الافتراضية بعد المسح لا يعمل")
                return False
            
        else:
            print("❌ حقل الغرض من التحويل غير موجود")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الغرض من التحويل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_collection():
    """اختبار جمع البيانات مع التحديثات الجديدة"""
    print("\n📊 اختبار جمع البيانات مع التحديثات الجديدة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # تعيين قيم اختبارية
        window.branch_combo.setCurrentIndex(2)  # فرع عدن
        
        # جمع البيانات
        data = window.collect_new_request_data()
        
        # التحقق من بيانات الفرع
        if 'branch' in data:
            branch_value = data['branch']
            print(f"📋 قيمة الفرع في البيانات: '{branch_value}'")
            
            if "فرع عدن" in branch_value:
                print("✅ جمع بيانات الفرع يعمل")
            else:
                print("❌ جمع بيانات الفرع لا يعمل")
                return False
        else:
            print("❌ بيانات الفرع مفقودة")
            return False
        
        # التحقق من بيانات الغرض من التحويل
        if 'transfer_purpose' in data:
            purpose_value = data['transfer_purpose']
            print(f"📋 قيمة الغرض من التحويل: '{purpose_value}'")
            
            if purpose_value == "COST OF FOODSTUFF":
                print("✅ جمع بيانات الغرض من التحويل يعمل")
            else:
                print("❌ جمع بيانات الغرض من التحويل لا يعمل")
                return False
        else:
            print("❌ بيانات الغرض من التحويل مفقودة")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار جمع البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحديثات حقل الفرع والغرض من التحويل...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_branch_updates())
    results.append(test_transfer_purpose_default())
    results.append(test_data_collection())
    
    # عرض النتائج النهائية
    print("\n" + "="*70)
    print("🎯 ملخص اختبار التحديثات:")
    print("="*70)
    
    test_names = [
        "تحديث حقل الفرع",
        "القيمة الافتراضية للغرض من التحويل",
        "جمع البيانات مع التحديثات"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع التحديثات تعمل بنجاح!")
        print("✅ حقل الفرع يحتوي على البيانات الجديدة:")
        print("   • الادارة العامة صنعاء")
        print("   • فرع عدن")
        print("   • فرع المكلا")
        print("✅ حقل الغرض من التحويل له قيمة افتراضية:")
        print("   • COST OF FOODSTUFF")
        print("✅ جمع البيانات يعمل مع التحديثات الجديدة")
        print("✅ مسح النموذج يعيد القيم الافتراضية")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم التحديثات تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحديثات فشلت. يرجى مراجعة:")
        print("- قائمة الفروع")
        print("- القيمة الافتراضية للغرض من التحويل")
        print("- دوال جمع البيانات")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
