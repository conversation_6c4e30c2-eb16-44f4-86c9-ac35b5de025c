#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشاكل عرض المبلغ
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_num2words_library():
    """اختبار مكتبة num2words"""
    print("📚 اختبار مكتبة num2words...")
    
    try:
        from num2words import num2words
        
        # اختبار أرقام مختلفة
        test_numbers = [1000, 5000, 10000, 63500, 100000]
        
        print("   🔢 اختبار تحويل الأرقام:")
        for number in test_numbers:
            try:
                arabic_words = num2words(number, lang='ar')
                print(f"      {number}: {arabic_words}")
            except Exception as e:
                print(f"      ❌ خطأ في تحويل {number}: {e}")
                return False
        
        return True
        
    except ImportError:
        print("   ❌ مكتبة num2words غير مثبتة")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في اختبار المكتبة: {e}")
        return False

def test_amount_conversion_in_templates():
    """اختبار تحويل المبلغ في النماذج"""
    print("\n💰 اختبار تحويل المبلغ في النماذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # بيانات اختبار
        test_data = {
            'remittance_amount': '63500',
            'currency': 'USD'
        }
        
        templates_to_test = [
            ("src.ui.remittances.remittance_print_template", "RemittancePrintTemplate", "النموذج الأساسي"),
            ("src.ui.remittances.simple_print_template", "SimplePrintTemplate", "النموذج المبسط"),
            ("src.ui.remittances.professional_print_template", "ProfessionalPrintTemplate", "النموذج الاحترافي")
        ]
        
        for module_name, class_name, template_desc in templates_to_test:
            print(f"   📋 اختبار {template_desc}:")
            
            try:
                # استيراد النموذج
                module = __import__(module_name, fromlist=[class_name])
                template_class = getattr(module, class_name)
                
                # إنشاء النموذج
                template = template_class(test_data)
                
                # اختبار تحويل المبلغ
                amount_words = template.convert_amount_to_words('63500', 'USD')
                print(f"      تحويل 63500: {amount_words}")
                
                # التحقق من وجود كلمات عربية
                if any(char in amount_words for char in 'ثلاثة وستون ألف خمسمائة'):
                    print(f"      ✅ يحتوي على كلمات عربية")
                else:
                    print(f"      ❌ لا يحتوي على كلمات عربية صحيحة")
                    template.close()
                    return False
                
                # اختبار النص الكامل
                if hasattr(template, 'request_text'):
                    full_text = template.request_text.text()
                    print(f"      النص الكامل: {full_text}")
                    
                    # التحقق من عدم التكرار
                    amount_count = full_text.count('63500')
                    dollar_count = full_text.count('$')
                    
                    if amount_count <= 1 and dollar_count <= 1:
                        print(f"      ✅ لا يوجد تكرار في المبلغ")
                    else:
                        print(f"      ❌ يوجد تكرار: المبلغ {amount_count} مرات، $ {dollar_count} مرات")
                        template.close()
                        return False
                else:
                    print(f"      ⚠️ لا يحتوي على عنصر نص الطلب")
                
                template.close()
                
            except Exception as e:
                print(f"      ❌ خطأ في {template_desc}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النماذج: {e}")
        return False

def test_different_amounts():
    """اختبار مبالغ مختلفة"""
    print("\n🔢 اختبار مبالغ مختلفة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_print_template import RemittancePrintTemplate
        
        # مبالغ مختلفة للاختبار
        test_amounts = [
            {'amount': '1000', 'currency': 'USD'},
            {'amount': '5000', 'currency': 'SAR'},
            {'amount': '10000', 'currency': 'EUR'},
            {'amount': '100000', 'currency': 'YER'},
        ]
        
        template = RemittancePrintTemplate({})
        
        for test_case in test_amounts:
            amount = test_case['amount']
            currency = test_case['currency']
            
            print(f"   💰 اختبار {amount} {currency}:")
            
            # تحويل إلى كلمات
            words = template.convert_amount_to_words(amount, currency)
            print(f"      الكلمات: {words}")
            
            # تنسيق مع العملة
            formatted = template.format_amount_with_currency(amount, currency)
            print(f"      التنسيق: {formatted}")
            
            # التحقق من صحة التحويل
            if "لا غير" in words and len(words) > 10:
                print(f"      ✅ تحويل صحيح")
            else:
                print(f"      ❌ تحويل غير صحيح")
                template.close()
                return False
        
        template.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المبالغ المختلفة: {e}")
        return False

def test_pdf_generator():
    """اختبار مولد PDF"""
    print("\n📄 اختبار مولد PDF...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات اختبار
        test_data = {
            'remittance_amount': '25000',
            'currency': 'USD',
            'request_number': 'TEST-001',
            'receiver_name': 'MOHAMMED AHMED ALI'
        }
        
        generator = RemittancePDFGenerator()
        
        # اختبار تحويل المبلغ
        amount_words = generator.convert_amount_to_words('25000', 'USD')
        print(f"   تحويل 25000 USD: {amount_words}")
        
        # التحقق من صحة التحويل
        if any(char in amount_words for char in 'عشرون خمسة آلاف'):
            print(f"   ✅ تحويل صحيح في مولد PDF")
            return True
        else:
            print(f"   ❌ تحويل غير صحيح في مولد PDF")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مولد PDF: {e}")
        return False

def test_edge_cases():
    """اختبار حالات خاصة"""
    print("\n🔍 اختبار حالات خاصة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_print_template import RemittancePrintTemplate
        
        template = RemittancePrintTemplate({})
        
        # حالات خاصة
        edge_cases = [
            {'amount': '0', 'currency': 'USD', 'desc': 'صفر'},
            {'amount': '1', 'currency': 'USD', 'desc': 'واحد'},
            {'amount': '1000000', 'currency': 'USD', 'desc': 'مليون'},
            {'amount': '123.45', 'currency': 'USD', 'desc': 'رقم عشري'},
            {'amount': '1,000', 'currency': 'USD', 'desc': 'رقم بفاصلة'},
        ]
        
        for case in edge_cases:
            print(f"   🧪 اختبار {case['desc']} ({case['amount']}):")
            
            try:
                words = template.convert_amount_to_words(case['amount'], case['currency'])
                print(f"      النتيجة: {words}")
                
                if words and len(words) > 0:
                    print(f"      ✅ تم التعامل مع الحالة")
                else:
                    print(f"      ❌ فشل في التعامل مع الحالة")
                    template.close()
                    return False
                    
            except Exception as e:
                print(f"      ❌ خطأ في {case['desc']}: {e}")
                template.close()
                return False
        
        template.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحالات الخاصة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح مشاكل عرض المبلغ...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_num2words_library())
    results.append(test_amount_conversion_in_templates())
    results.append(test_different_amounts())
    results.append(test_pdf_generator())
    results.append(test_edge_cases())
    
    # عرض النتائج النهائية
    print("\n" + "="*80)
    print("🎯 ملخص اختبار إصلاح مشاكل عرض المبلغ:")
    print("="*80)
    
    test_names = [
        "مكتبة num2words",
        "تحويل المبلغ في النماذج",
        "مبالغ مختلفة",
        "مولد PDF",
        "حالات خاصة"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تم إصلاح جميع مشاكل عرض المبلغ!")
        print("✅ مكتبة num2words تعمل بشكل صحيح")
        print("✅ المبلغ يظهر كتابة بالعربية")
        print("✅ لا يوجد تكرار في عرض المبلغ")
        print("✅ جميع النماذج تعمل بشكل صحيح")
        print("✅ مولد PDF محدث ويعمل")
        
        print("\n🌟 الإصلاحات المطبقة:")
        print("   📚 تثبيت مكتبة num2words للتحويل الدقيق")
        print("   🔧 تحديث دوال التحويل في جميع النماذج")
        print("   🚫 إزالة أي تكرار في عرض المبلغ")
        print("   🛡️ معالجة الأخطاء والحالات الخاصة")
        print("   📄 تحديث مولد PDF")
        
    elif successful_tests >= len(results) * 0.8:
        print("\n✅ معظم المشاكل تم إصلاحها!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة مشاكل لم يتم إصلاحها. يرجى مراجعة:")
        print("- تثبيت مكتبة num2words")
        print("- دوال تحويل المبلغ")
        print("- التكرار في عرض المبلغ")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
