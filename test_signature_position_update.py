#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث موضع التوقيع وإخفاء الإطار
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_signature_position_change():
    """اختبار تغيير موضع التوقيع"""
    print("📍 اختبار تغيير موضع التوقيع...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن التغيير في الترتيب
            if "signature_layout.addWidget(manager_section)" in content and \
               "signature_layout.addWidget(thanks_label)" in content:
                
                # التحقق من الترتيب الجديد
                manager_pos = content.find("signature_layout.addWidget(manager_section)")
                thanks_pos = content.find("signature_layout.addWidget(thanks_label)")
                
                if manager_pos < thanks_pos:
                    print(f"      ✅ التوقيع في الجهة المقابلة (يسار)")
                else:
                    print(f"      ❌ التوقيع لا يزال في اليمين")
                    return False
            else:
                print(f"      ❌ لم يتم العثور على تخطيط التوقيع")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موضع التوقيع: {e}")
        return False

def test_signature_frame_removal():
    """اختبار إزالة إطار التوقيع"""
    print("\n🚫 اختبار إزالة إطار التوقيع...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن إزالة الإطارات
            frame_indicators = [
                "border: 1px solid",
                "border: 2px dashed",
                "border: 3px dashed",
                "background-color:",
                "border-radius:"
            ]
            
            # التحقق من وجود التوقيع بدون إطار
            if "بدون إطار" in content:
                print(f"      ✅ تم تحديث التوقيع ليكون بدون إطار")
                
                # التحقق من عدم وجود إطارات في التوقيع
                signature_section_start = content.find("# التوقيع في الأسفل (بدون إطار)")
                if signature_section_start != -1:
                    # البحث في قسم التوقيع فقط
                    signature_section = content[signature_section_start:signature_section_start + 500]
                    
                    has_border = any(indicator in signature_section for indicator in frame_indicators[:3])
                    
                    if not has_border:
                        print(f"      ✅ لا توجد إطارات في التوقيع")
                    else:
                        print(f"      ❌ لا تزال هناك إطارات في التوقيع")
                        return False
                else:
                    print(f"      ⚠️ لم يتم العثور على قسم التوقيع المحدث")
            else:
                print(f"      ❌ لم يتم تحديث التوقيع")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إزالة الإطار: {e}")
        return False

def test_pdf_signature_update():
    """اختبار تحديث التوقيع في PDF"""
    print("\n📄 اختبار تحديث التوقيع في PDF...")
    
    try:
        with open("src/ui/remittances/remittance_pdf_generator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن التحديثات
        updates_found = 0
        
        # التحقق من نقل التوقيع لليسار
        if "signature_x = self.margin + 10*mm" in content:
            print("   ✅ التوقيع منقول لليسار")
            updates_found += 1
        else:
            print("   ❌ التوقيع لم ينقل لليسار")
            return False
        
        # التحقق من إزالة الإطار
        if "c.rect(" not in content or "stroke=1, fill=0" not in content:
            print("   ✅ تم إزالة إطار التوقيع")
            updates_found += 1
        else:
            print("   ❌ لم يتم إزالة إطار التوقيع")
            return False
        
        # التحقق من نقل "وشكراً" لليمين
        if "c.drawRightString(self.page_width - self.margin - 10*mm" in content and "وشكراً" in content:
            print("   ✅ تم نقل 'وشكراً' لليمين")
            updates_found += 1
        else:
            print("   ❌ لم يتم نقل 'وشكراً' لليمين")
            return False
        
        if updates_found >= 3:
            print(f"   ✅ جميع تحديثات PDF مطبقة ({updates_found}/3)")
            return True
        else:
            print(f"   ❌ بعض تحديثات PDF مفقودة ({updates_found}/3)")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        return False

def test_pdf_generation():
    """اختبار إنشاء PDF مع التحديثات"""
    print("\n🧪 اختبار إنشاء PDF مع التحديثات...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        generator = RemittancePDFGenerator()
        print("   ✅ تم إنشاء مولد PDF بنجاح")
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'TEST-POSITION-001',
            'request_date': '2024/12/09',
            'remittance_amount': '7500',
            'currency': 'USD',
            'receiver_name': 'AHMED MOHAMMED ALI',
            'receiver_bank_name': 'TEST BANK',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        # مسار مؤقت للاختبار
        test_output = "test_signature_position.pdf"
        
        try:
            # محاولة إنشاء PDF
            result = generator.generate_pdf(test_data, test_output)
            
            if result and os.path.exists(test_output):
                print("   ✅ تم إنشاء PDF مع التحديثات بنجاح")
                
                # التحقق من حجم الملف
                file_size = os.path.getsize(test_output)
                if file_size > 1000:
                    print(f"   ✅ حجم الملف مناسب: {file_size} بايت")
                else:
                    print(f"   ⚠️ حجم الملف صغير: {file_size} بايت")
                
                # حذف الملف التجريبي
                os.remove(test_output)
                print("   ✅ تم حذف الملف التجريبي")
                
                return True
            else:
                print("   ❌ فشل في إنشاء PDF")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء PDF: {e}")
            
            # حذف الملف في حالة الخطأ
            if os.path.exists(test_output):
                os.remove(test_output)
            
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء PDF: {e}")
        return False

def test_visual_layout():
    """اختبار التخطيط المرئي"""
    print("\n👁️ اختبار التخطيط المرئي...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.simple_print_template import SimplePrintTemplate
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'VISUAL-TEST-001',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        template = SimplePrintTemplate(test_data)
        
        # البحث عن عناصر التوقيع
        signature_elements = []
        thanks_elements = []
        
        def find_elements(widget):
            for child in widget.findChildren(template.QLabel):
                text = child.text()
                if "نشأت رشاد قاسم الدبعي" in text:
                    signature_elements.append(child)
                elif "وشكراً" in text:
                    thanks_elements.append(child)
        
        find_elements(template)
        
        if signature_elements and thanks_elements:
            print("   ✅ وجد عناصر التوقيع و'وشكراً'")
            
            # التحقق من عدم وجود إطارات
            signature_element = signature_elements[0]
            style = signature_element.styleSheet()
            
            if "border:" not in style or "border: none" in style:
                print("   ✅ التوقيع بدون إطار")
            else:
                print("   ❌ التوقيع لا يزال له إطار")
                template.close()
                return False
        else:
            print("   ❌ لم يجد عناصر التوقيع")
            template.close()
            return False
        
        template.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التخطيط المرئي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحديث موضع التوقيع وإخفاء الإطار...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_signature_position_change())
    results.append(test_signature_frame_removal())
    results.append(test_pdf_signature_update())
    results.append(test_pdf_generation())
    results.append(test_visual_layout())
    
    # عرض النتائج النهائية
    print("\n" + "="*70)
    print("🎯 ملخص اختبار تحديث موضع التوقيع:")
    print("="*70)
    
    test_names = [
        "تغيير موضع التوقيع",
        "إزالة إطار التوقيع",
        "تحديث PDF",
        "إنشاء PDF",
        "التخطيط المرئي"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 تم تحديث موضع التوقيع وإخفاء الإطار بنجاح!")
        print("✅ التوقيع منقول للجهة المقابلة (يسار)")
        print("✅ إطار التوقيع مخفي في جميع النماذج")
        print("✅ PDF محدث مع الموضع الجديد")
        print("✅ 'وشكراً' منقول لليمين")
        print("✅ التخطيط المرئي محسن")
        
        print("\n🔄 التحديثات المطبقة:")
        print("   📍 نقل التوقيع من اليمين إلى اليسار")
        print("   🚫 إخفاء جميع إطارات التوقيع")
        print("   📄 تحديث مولد PDF")
        print("   🎨 تحسين التخطيط المرئي")
        print("   ↔️ تبديل مواضع التوقيع و'وشكراً'")
        
    elif successful_tests >= len(results) * 0.8:
        print("\n✅ معظم التحديثات تمت بنجاح!")
        print("بعض التحديثات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحديثات فشلت. يرجى مراجعة:")
        print("- موضع التوقيع")
        print("- إطارات التوقيع")
        print("- تحديثات PDF")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
