#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر للتطبيق
Direct App Test
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, Qt
from PySide6.QtGui import QFont

def test_app():
    """اختبار التطبيق مباشرة"""
    
    print("🧪 اختبار التطبيق مباشرة...")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 11)
        app.setFont(font)
        
        # إعداد تخطيط RTL
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # استيراد النافذة الرئيسية
        from src.ui.main_window import MainWindow
        
        print("✅ تم استيراد النافذة الرئيسية")
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # عرض النافذة
        window.show()
        
        print("✅ تم عرض النافذة")
        print("🚀 التطبيق جاهز للاستخدام!")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = test_app()
    print(f"انتهى التطبيق برمز الخروج: {exit_code}")
    sys.exit(exit_code)
