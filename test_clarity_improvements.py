#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات الوضوح والجودة البصرية
Test Clarity and Visual Quality Improvements
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QLabel, QPushButton, QLineEdit, 
                               QTextEdit, QComboBox, QTableWidget, QTableWidgetItem,
                               QGroupBox, QTabWidget, QFormLayout)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from src.utils.arabic_support import setup_arabic_support
from src.ui.styles.clarity_improvements import apply_clarity_improvements
from src.ui.styles.style_manager import style_manager

class ClarityTestWindow(QMainWindow):
    """نافذة اختبار تحسينات الوضوح"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("اختبار تحسينات الوضوح - ProShipment")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("اختبار تحسينات الوضوح والجودة البصرية")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء تبويبات للاختبار
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # تبويب النصوص والخطوط
        self.create_text_tab(tab_widget)
        
        # تبويب الأزرار والعناصر التفاعلية
        self.create_buttons_tab(tab_widget)
        
        # تبويب الجداول
        self.create_tables_tab(tab_widget)
        
        # أزرار التحكم في الثيم
        self.create_theme_controls(main_layout)
        
    def create_text_tab(self, parent):
        """إنشاء تبويب اختبار النصوص"""
        text_widget = QWidget()
        layout = QVBoxLayout(text_widget)
        layout.setSpacing(15)
        
        # مجموعة النصوص العربية
        arabic_group = QGroupBox("النصوص العربية")
        arabic_layout = QFormLayout(arabic_group)
        
        arabic_texts = [
            "هذا نص عربي للاختبار - حجم عادي",
            "نص عربي بخط كبير للاختبار",
            "نص عربي بخط صغير للاختبار",
            "نص مختلط: English + العربية + 123456"
        ]
        
        for i, text in enumerate(arabic_texts):
            label = QLabel(text)
            if i == 1:  # نص كبير
                label.setFont(QFont("Segoe UI", 14))
            elif i == 2:  # نص صغير
                label.setFont(QFont("Segoe UI", 9))
            arabic_layout.addRow(f"مثال {i+1}:", label)
            
        layout.addWidget(arabic_group)
        
        # مجموعة حقول الإدخال
        input_group = QGroupBox("حقول الإدخال")
        input_layout = QFormLayout(input_group)
        
        line_edit = QLineEdit("نص تجريبي في حقل الإدخال")
        input_layout.addRow("حقل نص:", line_edit)
        
        text_edit = QTextEdit()
        text_edit.setPlainText("هذا نص متعدد الأسطر للاختبار\nالسطر الثاني\nالسطر الثالث مع نص أطول للاختبار")
        text_edit.setMaximumHeight(100)
        input_layout.addRow("منطقة نص:", text_edit)
        
        combo = QComboBox()
        combo.addItems(["الخيار الأول", "الخيار الثاني", "الخيار الثالث"])
        input_layout.addRow("قائمة منسدلة:", combo)
        
        layout.addWidget(input_group)
        
        parent.addTab(text_widget, "النصوص والخطوط")
        
    def create_buttons_tab(self, parent):
        """إنشاء تبويب اختبار الأزرار"""
        buttons_widget = QWidget()
        layout = QVBoxLayout(buttons_widget)
        layout.setSpacing(15)
        
        # مجموعة الأزرار العادية
        normal_group = QGroupBox("الأزرار العادية")
        normal_layout = QHBoxLayout(normal_group)
        
        button_texts = ["حفظ", "إلغاء", "تعديل", "حذف", "إضافة جديد"]
        for text in button_texts:
            btn = QPushButton(text)
            btn.setMinimumHeight(40)
            normal_layout.addWidget(btn)
            
        layout.addWidget(normal_group)
        
        # مجموعة الأزرار الملونة
        colored_group = QGroupBox("الأزرار الملونة")
        colored_layout = QHBoxLayout(colored_group)
        
        # زر أخضر
        green_btn = QPushButton("نجح ✓")
        green_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        colored_layout.addWidget(green_btn)
        
        # زر أحمر
        red_btn = QPushButton("خطأ ✗")
        red_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        colored_layout.addWidget(red_btn)
        
        # زر أصفر
        yellow_btn = QPushButton("تحذير ⚠")
        yellow_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d35400);
            }
        """)
        colored_layout.addWidget(yellow_btn)
        
        layout.addWidget(colored_group)
        
        parent.addTab(buttons_widget, "الأزرار")
        
    def create_tables_tab(self, parent):
        """إنشاء تبويب اختبار الجداول"""
        tables_widget = QWidget()
        layout = QVBoxLayout(tables_widget)
        
        # جدول تجريبي
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحالة", "التاريخ"])
        
        # بيانات تجريبية
        sample_data = [
            ["شركة الأولى", "مورد", "نشط", "2025-01-01"],
            ["شركة الثانية", "عميل", "غير نشط", "2025-01-02"],
            ["شركة الثالثة", "مورد", "نشط", "2025-01-03"],
            ["شركة الرابعة", "شحن", "نشط", "2025-01-04"],
            ["شركة الخامسة", "مورد", "غير نشط", "2025-01-05"]
        ]
        
        for row, row_data in enumerate(sample_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                table.setItem(row, col, item)
                
        # تلوين الصفوف حسب الحالة
        for row in range(table.rowCount()):
            status_item = table.item(row, 2)
            if status_item and status_item.text() == "نشط":
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(Qt.green)
                        item.setForeground(Qt.white)
            elif status_item and status_item.text() == "غير نشط":
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(Qt.red)
                        item.setForeground(Qt.white)
        
        layout.addWidget(table)
        
        parent.addTab(tables_widget, "الجداول")
        
    def create_theme_controls(self, parent_layout):
        """إنشاء أزرار التحكم في الثيم"""
        controls_group = QGroupBox("التحكم في الثيم")
        controls_layout = QHBoxLayout(controls_group)
        
        # زر الثيم الحديث
        modern_btn = QPushButton("الثيم الحديث")
        modern_btn.clicked.connect(lambda: style_manager.load_theme("modern"))
        controls_layout.addWidget(modern_btn)
        
        # زر الثيم المظلم
        dark_btn = QPushButton("الثيم المظلم")
        dark_btn.clicked.connect(style_manager.apply_dark_theme)
        controls_layout.addWidget(dark_btn)
        
        # زر إعادة التعيين
        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(style_manager.reset_style)
        controls_layout.addWidget(reset_btn)
        
        # زر تطبيق تحسينات الوضوح
        clarity_btn = QPushButton("تحسينات الوضوح")
        clarity_btn.clicked.connect(apply_clarity_improvements)
        controls_layout.addWidget(clarity_btn)
        
        controls_layout.addStretch()
        
        parent_layout.addWidget(controls_group)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار تحسينات الوضوح والجودة البصرية")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد دعم العربية
        setup_arabic_support(app)
        
        # تطبيق تحسينات الوضوح
        apply_clarity_improvements()
        
        # تحميل الثيم الحديث
        style_manager.load_theme("modern")
        
        print("✅ تم إعداد التطبيق وتحسينات الوضوح")
        
        # إنشاء النافذة
        window = ClarityTestWindow()
        window.show()
        
        print("✅ تم عرض نافذة الاختبار")
        print("\n🎯 يمكنك الآن اختبار:")
        print("   • وضوح النصوص العربية")
        print("   • جودة الخطوط")
        print("   • تباين الألوان")
        print("   • وضوح العناصر التفاعلية")
        print("   • تبديل الثيمات")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏁 انتهى الاختبار برمز الخروج: {exit_code}")
    sys.exit(exit_code)
