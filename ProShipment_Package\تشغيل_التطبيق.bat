@echo off
chcp 65001 > nul
title ProShipment V2.0.0 - نظام إدارة الشحنات والحوالات

echo.
echo ================================================
echo   🚢 ProShipment V2.0.0 🚢
echo   نظام إدارة الشحنات والحوالات المتكامل
echo ================================================
echo.
echo 📋 معلومات النظام:
echo    ✅ الإصدار: 2.0.0
echo    📅 تاريخ البناء: 11 يوليو 2025
echo    💾 حجم قاعدة البيانات: متضمنة مع جميع البيانات
echo.
echo 🔍 فحص متطلبات النظام...

REM فحص نظام التشغيل
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo    🖥️ نظام التشغيل: Windows %VERSION%

REM فحص المساحة المتاحة
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set FREESPACE=%%a
echo    💽 المساحة المتاحة: %FREESPACE% bytes

echo.
echo 🚀 جاري تشغيل التطبيق...
echo    📁 مجلد التطبيق: %CD%
echo    🔧 الملف التنفيذي: ProShipment.exe
echo.

REM التحقق من وجود الملف التنفيذي
if not exist "ProShipment.exe" (
    echo ❌ خطأ: لم يتم العثور على ProShipment.exe
    echo 💡 تأكد من فك ضغط جميع الملفات بشكل صحيح
    echo.
    pause
    exit /b 1
)

REM التحقق من مجلد البيانات
if not exist "_internal\data" (
    echo ⚠️ تحذير: مجلد البيانات غير موجود
    echo 🔧 سيتم إنشاء قاعدة بيانات جديدة
)

echo ✅ جميع الملفات موجودة
echo 🎯 بدء تشغيل ProShipment...
echo.

REM تشغيل التطبيق
start "" "ProShipment.exe"

REM انتظار قصير للتأكد من بدء التطبيق
timeout /t 3 /nobreak > nul

echo 🎉 تم تشغيل التطبيق بنجاح!
echo.
echo 📋 نصائح للاستخدام:
echo    🎨 لتغيير المظهر: العرض → إعدادات التصميم
echo    ⚙️ لإعداد بيانات الشركة: الإعدادات → إعدادات عامة
echo    📞 للدعم الفني: راجع ملف دليل_التشغيل.md
echo.
echo 💡 يمكنك إغلاق هذه النافذة الآن
echo.
pause
