#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص Oracle المتقدمة - ProShipment V2.0.0
Advanced Oracle Diagnostics Tool
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.universal_database_manager import UniversalDatabaseManager
from src.database.oracle_config import DatabaseConfigManager, DatabaseType

class OracleDiagnostics:
    """أداة تشخيص Oracle المتقدمة"""
    
    def __init__(self):
        self.config_manager = DatabaseConfigManager()
        self.db_manager = None
        self.setup_diagnostics()
    
    def setup_diagnostics(self):
        """إعداد أداة التشخيص"""
        try:
            config = self.config_manager.load_config()
            
            if config.type == DatabaseType.ORACLE:
                self.db_manager = UniversalDatabaseManager(config)
                print("✅ تم إعداد أداة تشخيص Oracle")
            else:
                print("⚠️ نوع قاعدة البيانات ليس Oracle")
                
        except Exception as e:
            print(f"❌ خطأ في إعداد التشخيص: {e}")
    
    def diagnose_connection_issues(self) -> Dict[str, Any]:
        """تشخيص مشاكل الاتصال"""
        print("🔍 تشخيص مشاكل الاتصال...")
        
        diagnosis = {
            'timestamp': datetime.now().isoformat(),
            'tests': [],
            'recommendations': []
        }
        
        # اختبار 1: فحص إعدادات الاتصال
        config = self.config_manager.load_config()
        if config.type == DatabaseType.ORACLE:
            oracle_config = config.oracle_config
            
            test_result = {
                'test': 'إعدادات الاتصال',
                'status': 'pass',
                'details': {
                    'host': oracle_config.host,
                    'port': oracle_config.port,
                    'service_name': oracle_config.service_name,
                    'username': oracle_config.username
                }
            }
            diagnosis['tests'].append(test_result)
        
        # اختبار 2: فحص الشبكة
        network_test = self.test_network_connectivity(oracle_config.host, oracle_config.port)
        diagnosis['tests'].append(network_test)
        
        # اختبار 3: فحص Oracle Client
        client_test = self.test_oracle_client()
        diagnosis['tests'].append(client_test)
        
        # اختبار 4: فحص قاعدة البيانات
        db_test = self.test_database_connection()
        diagnosis['tests'].append(db_test)
        
        # إنشاء التوصيات
        diagnosis['recommendations'] = self.generate_connection_recommendations(diagnosis['tests'])
        
        return diagnosis
    
    def test_network_connectivity(self, host: str, port: int) -> Dict[str, Any]:
        """اختبار الاتصال بالشبكة"""
        import socket
        
        try:
            start_time = time.time()
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            response_time = time.time() - start_time
            
            if result == 0:
                return {
                    'test': 'اتصال الشبكة',
                    'status': 'pass',
                    'details': {
                        'host': host,
                        'port': port,
                        'response_time': response_time
                    }
                }
            else:
                return {
                    'test': 'اتصال الشبكة',
                    'status': 'fail',
                    'error': f'فشل الاتصال بـ {host}:{port}',
                    'details': {
                        'host': host,
                        'port': port,
                        'error_code': result
                    }
                }
                
        except Exception as e:
            return {
                'test': 'اتصال الشبكة',
                'status': 'fail',
                'error': str(e)
            }
    
    def test_oracle_client(self) -> Dict[str, Any]:
        """اختبار Oracle Client"""
        try:
            import cx_Oracle
            
            # الحصول على معلومات العميل
            client_version = cx_Oracle.clientversion()
            
            return {
                'test': 'Oracle Client',
                'status': 'pass',
                'details': {
                    'version': '.'.join(map(str, client_version)),
                    'cx_oracle_version': cx_Oracle.version
                }
            }
            
        except ImportError:
            return {
                'test': 'Oracle Client',
                'status': 'fail',
                'error': 'cx_Oracle غير مثبت'
            }
        except Exception as e:
            return {
                'test': 'Oracle Client',
                'status': 'fail',
                'error': str(e)
            }
    
    def test_database_connection(self) -> Dict[str, Any]:
        """اختبار اتصال قاعدة البيانات"""
        try:
            if not self.db_manager:
                return {
                    'test': 'اتصال قاعدة البيانات',
                    'status': 'fail',
                    'error': 'مدير قاعدة البيانات غير مهيأ'
                }
            
            start_time = time.time()
            is_connected = self.db_manager.test_connection()
            response_time = time.time() - start_time
            
            if is_connected:
                return {
                    'test': 'اتصال قاعدة البيانات',
                    'status': 'pass',
                    'details': {
                        'response_time': response_time,
                        'database_info': self.db_manager.get_database_info()
                    }
                }
            else:
                return {
                    'test': 'اتصال قاعدة البيانات',
                    'status': 'fail',
                    'error': 'فشل في الاتصال بقاعدة البيانات'
                }
                
        except Exception as e:
            return {
                'test': 'اتصال قاعدة البيانات',
                'status': 'fail',
                'error': str(e)
            }
    
    def generate_connection_recommendations(self, tests: List[Dict]) -> List[str]:
        """إنشاء توصيات حل مشاكل الاتصال"""
        recommendations = []
        
        for test in tests:
            if test['status'] == 'fail':
                test_name = test['test']
                error = test.get('error', '')
                
                if test_name == 'اتصال الشبكة':
                    recommendations.extend([
                        "تحقق من عنوان الخادم والمنفذ",
                        "تأكد من تشغيل Oracle Database",
                        "تحقق من إعدادات Firewall",
                        "تأكد من إعدادات الشبكة"
                    ])
                
                elif test_name == 'Oracle Client':
                    if 'cx_Oracle غير مثبت' in error:
                        recommendations.extend([
                            "ثبت cx_Oracle: pip install cx_Oracle",
                            "ثبت Oracle Instant Client",
                            "تأكد من إعدادات PATH"
                        ])
                    else:
                        recommendations.extend([
                            "تحقق من تثبيت Oracle Instant Client",
                            "تأكد من متغيرات البيئة ORACLE_HOME",
                            "تحقق من إصدار Oracle Client"
                        ])
                
                elif test_name == 'اتصال قاعدة البيانات':
                    if 'TNS' in error:
                        recommendations.extend([
                            "تحقق من ملف tnsnames.ora",
                            "تأكد من متغير TNS_ADMIN",
                            "تحقق من اسم الخدمة أو SID"
                        ])
                    elif 'ORA-01017' in error:
                        recommendations.extend([
                            "تحقق من اسم المستخدم وكلمة المرور",
                            "تأكد من وجود المستخدم في Oracle",
                            "تحقق من صلاحيات المستخدم"
                        ])
                    else:
                        recommendations.extend([
                            "تحقق من حالة Oracle Database",
                            "راجع سجلات Oracle للأخطاء",
                            "تأكد من إعدادات الاتصال"
                        ])
        
        if not recommendations:
            recommendations.append("جميع الاختبارات نجحت - الاتصال يعمل بشكل صحيح")
        
        return list(set(recommendations))  # إزالة التكرار
    
    def diagnose_performance_issues(self) -> Dict[str, Any]:
        """تشخيص مشاكل الأداء"""
        print("⚡ تشخيص مشاكل الأداء...")
        
        if not self.db_manager or not self.db_manager.is_connected:
            return {
                'error': 'غير متصل بقاعدة البيانات'
            }
        
        diagnosis = {
            'timestamp': datetime.now().isoformat(),
            'performance_checks': [],
            'recommendations': []
        }
        
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                # فحص 1: استخدام CPU
                cpu_check = self.check_cpu_usage(session)
                diagnosis['performance_checks'].append(cpu_check)
                
                # فحص 2: استخدام الذاكرة
                memory_check = self.check_memory_usage(session)
                diagnosis['performance_checks'].append(memory_check)
                
                # فحص 3: I/O Statistics
                io_check = self.check_io_statistics(session)
                diagnosis['performance_checks'].append(io_check)
                
                # فحص 4: Wait Events
                wait_check = self.check_wait_events(session)
                diagnosis['performance_checks'].append(wait_check)
                
                # فحص 5: Top SQL
                sql_check = self.check_top_sql(session)
                diagnosis['performance_checks'].append(sql_check)
            
            # إنشاء التوصيات
            diagnosis['recommendations'] = self.generate_performance_recommendations(
                diagnosis['performance_checks']
            )
            
        except Exception as e:
            diagnosis['error'] = str(e)
        
        return diagnosis
    
    def check_cpu_usage(self, session) -> Dict[str, Any]:
        """فحص استخدام CPU"""
        try:
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT value 
                FROM v$sysstat 
                WHERE name = 'CPU used by this session'
            """))
            
            cpu_value = result.scalar() or 0
            
            return {
                'check': 'استخدام CPU',
                'status': 'normal' if cpu_value < 1000000 else 'high',
                'value': cpu_value,
                'unit': 'centiseconds'
            }
            
        except Exception as e:
            return {
                'check': 'استخدام CPU',
                'status': 'error',
                'error': str(e)
            }
    
    def check_memory_usage(self, session) -> Dict[str, Any]:
        """فحص استخدام الذاكرة"""
        try:
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT 
                    SUM(bytes)/1024/1024 as total_mb
                FROM v$sgainfo
                WHERE name IN ('Database Buffers', 'Shared Pool Size', 'Large Pool Size')
            """))
            
            memory_mb = result.scalar() or 0
            
            return {
                'check': 'استخدام الذاكرة',
                'status': 'normal',
                'value': memory_mb,
                'unit': 'MB'
            }
            
        except Exception as e:
            return {
                'check': 'استخدام الذاكرة',
                'status': 'error',
                'error': str(e)
            }
    
    def check_io_statistics(self, session) -> Dict[str, Any]:
        """فحص إحصائيات I/O"""
        try:
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT 
                    SUM(phyrds) as physical_reads,
                    SUM(phywrts) as physical_writes
                FROM v$filestat
            """))
            
            row = result.fetchone()
            if row:
                reads, writes = row
                return {
                    'check': 'إحصائيات I/O',
                    'status': 'normal',
                    'physical_reads': reads,
                    'physical_writes': writes
                }
            else:
                return {
                    'check': 'إحصائيات I/O',
                    'status': 'no_data'
                }
                
        except Exception as e:
            return {
                'check': 'إحصائيات I/O',
                'status': 'error',
                'error': str(e)
            }
    
    def check_wait_events(self, session) -> Dict[str, Any]:
        """فحص أحداث الانتظار"""
        try:
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT 
                    event,
                    total_waits,
                    time_waited
                FROM v$system_event
                WHERE wait_class != 'Idle'
                ORDER BY time_waited DESC
                FETCH FIRST 5 ROWS ONLY
            """))
            
            wait_events = []
            for row in result:
                wait_events.append({
                    'event': row[0],
                    'total_waits': row[1],
                    'time_waited': row[2]
                })
            
            return {
                'check': 'أحداث الانتظار',
                'status': 'normal',
                'top_events': wait_events
            }
            
        except Exception as e:
            return {
                'check': 'أحداث الانتظار',
                'status': 'error',
                'error': str(e)
            }
    
    def check_top_sql(self, session) -> Dict[str, Any]:
        """فحص أهم الاستعلامات"""
        try:
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT 
                    sql_id,
                    executions,
                    elapsed_time,
                    cpu_time,
                    SUBSTR(sql_text, 1, 100) as sql_text
                FROM v$sql
                WHERE executions > 0
                ORDER BY elapsed_time DESC
                FETCH FIRST 5 ROWS ONLY
            """))
            
            top_sql = []
            for row in result:
                top_sql.append({
                    'sql_id': row[0],
                    'executions': row[1],
                    'elapsed_time': row[2],
                    'cpu_time': row[3],
                    'sql_text': row[4]
                })
            
            return {
                'check': 'أهم الاستعلامات',
                'status': 'normal',
                'top_sql': top_sql
            }
            
        except Exception as e:
            return {
                'check': 'أهم الاستعلامات',
                'status': 'error',
                'error': str(e)
            }
    
    def generate_performance_recommendations(self, checks: List[Dict]) -> List[str]:
        """إنشاء توصيات تحسين الأداء"""
        recommendations = []
        
        for check in checks:
            check_name = check['check']
            status = check['status']
            
            if status == 'high':
                if check_name == 'استخدام CPU':
                    recommendations.extend([
                        "استخدام CPU عالي - راجع الاستعلامات المعقدة",
                        "فكر في إضافة فهارس للجداول الكبيرة",
                        "راجع خطط التنفيذ للاستعلامات البطيئة"
                    ])
            
            elif status == 'error':
                recommendations.append(f"خطأ في فحص {check_name} - راجع الصلاحيات")
        
        # توصيات عامة
        if not any(check['status'] == 'high' for check in checks):
            recommendations.extend([
                "الأداء يبدو جيداً",
                "راقب النظام بانتظام",
                "حدث الإحصائيات دورياً"
            ])
        
        return recommendations
    
    def run_full_diagnostics(self) -> Dict[str, Any]:
        """تشغيل تشخيص شامل"""
        print("🔍 تشغيل تشخيص شامل لـ Oracle...")
        
        full_diagnosis = {
            'timestamp': datetime.now().isoformat(),
            'connection_diagnosis': self.diagnose_connection_issues(),
            'performance_diagnosis': self.diagnose_performance_issues()
        }
        
        return full_diagnosis
    
    def save_diagnosis_report(self, diagnosis: Dict[str, Any], filename: str = None):
        """حفظ تقرير التشخيص"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"oracle_diagnosis_{timestamp}.json"
        
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        report_path = reports_dir / filename
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(diagnosis, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ تم حفظ تقرير التشخيص: {report_path}")
            return str(report_path)
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return None

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة تشخيص Oracle المتقدمة - ProShipment V2.0.0")
    print("="*60)
    
    diagnostics = OracleDiagnostics()
    
    print("اختر نوع التشخيص:")
    print("1. تشخيص مشاكل الاتصال")
    print("2. تشخيص مشاكل الأداء")
    print("3. تشخيص شامل")
    
    choice = input("اختر رقم (1-3): ").strip()
    
    if choice == "1":
        diagnosis = diagnostics.diagnose_connection_issues()
        
        print("\n🔌 نتائج تشخيص الاتصال:")
        print("-" * 40)
        
        for test in diagnosis['tests']:
            status_icon = "✅" if test['status'] == 'pass' else "❌"
            print(f"{status_icon} {test['test']}")
            
            if test['status'] == 'fail' and 'error' in test:
                print(f"   خطأ: {test['error']}")
        
        print("\n💡 التوصيات:")
        for rec in diagnosis['recommendations']:
            print(f"   • {rec}")
    
    elif choice == "2":
        diagnosis = diagnostics.diagnose_performance_issues()
        
        if 'error' in diagnosis:
            print(f"❌ خطأ: {diagnosis['error']}")
        else:
            print("\n⚡ نتائج تشخيص الأداء:")
            print("-" * 40)
            
            for check in diagnosis['performance_checks']:
                status_icon = "✅" if check['status'] == 'normal' else "⚠️"
                print(f"{status_icon} {check['check']}")
                
                if 'value' in check:
                    unit = check.get('unit', '')
                    print(f"   القيمة: {check['value']} {unit}")
            
            print("\n💡 التوصيات:")
            for rec in diagnosis['recommendations']:
                print(f"   • {rec}")
    
    elif choice == "3":
        diagnosis = diagnostics.run_full_diagnostics()
        
        # حفظ التقرير
        report_path = diagnostics.save_diagnosis_report(diagnosis)
        
        print("\n📊 تم إنشاء تقرير التشخيص الشامل")
        if report_path:
            print(f"📄 مسار التقرير: {report_path}")
    
    else:
        print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()
