#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصحيح RTL في نموذج الطباعة
Test RTL Correction in Print Template
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_corrected_rtl():
    """اختبار التصحيح المطبق لـ RTL"""
    
    print("🔧 اختبار تصحيح RTL في نموذج الطباعة...")
    print("=" * 60)
    
    try:
        # قراءة ملف النموذج المبسط
        template_path = "src/ui/remittances/simple_print_template.py"
        with open(template_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        print("   📋 فحص التصحيحات المطبقة:")
        
        # فحص أن النصوص الإنجليزية فقط لها RTL
        english_rtl_count = 0
        
        # البحث عن التعليقات التي تشير للنصوص الإنجليزية
        if "# RTL للنص الإنجليزي" in code:
            english_rtl_count = code.count("# RTL للنص الإنجليزي")
            print(f"      ✅ النصوص الإنجليزية مع RTL: {english_rtl_count} موضع")
        
        # فحص أن النصوص العربية لا تحتوي على RTL إضافي
        arabic_rtl_count = code.count("# RTL للنص العربي")
        if arabic_rtl_count == 0:
            print("      ✅ النصوص العربية بدون RTL إضافي")
        else:
            print(f"      ❌ النصوص العربية لا تزال تحتوي RTL: {arabic_rtl_count}")
        
        # فحص الأقسام المحددة
        sections_check = [
            ("معلومات المستفيد (النص الإنجليزي - RTL)", "قسم معلومات المستفيد"),
            ("معلومات البنك (النص الإنجليزي - RTL)", "قسم معلومات البنك"),
            ("معلومات الشركة المرسلة (النص الإنجليزي - RTL)", "قسم معلومات المرسل"),
            ("النص الإنجليزي للغرض - RTL", "قسم الغرض من التحويل"),
            ("الاسم الإنجليزي - RTL", "قسم التوقيع")
        ]
        
        print("\n   📝 فحص الأقسام المصححة:")
        all_corrected = True
        
        for section_comment, description in sections_check:
            if section_comment in code:
                print(f"      ✅ {description} - مصحح")
            else:
                print(f"      ❌ {description} - غير مصحح")
                all_corrected = False
        
        return all_corrected and english_rtl_count > 0 and arabic_rtl_count == 0
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص التصحيح: {e}")
        return False

def display_correction_summary():
    """عرض ملخص التصحيح"""
    
    print("\n" + "=" * 80)
    print("✅ ملخص تصحيح RTL في نموذج الطباعة")
    print("=" * 80)
    
    print("\n🎯 المشكلة الأصلية:")
    print("   تم تطبيق RTL على النصوص العربية بدلاً من الإنجليزية")
    
    print("\n🔍 التحليل الصحيح للصورة:")
    print("   الأجزاء المحددة بالمستطيلات الحمراء هي:")
    print("   1️⃣ معلومات المستفيد (النص الإنجليزي)")
    print("   2️⃣ معلومات البنك (النص الإنجليزي)")
    print("   3️⃣ معلومات الشركة المرسلة (النص الإنجليزي)")
    print("   4️⃣ الغرض من التحويل (النص الإنجليزي)")
    print("   5️⃣ اسم المدير العام (النص العربي/الإنجليزي)")
    
    print("\n✅ التصحيحات المطبقة:")
    
    print("\n   📄 النموذج المبسط:")
    print("      - إزالة RTL من النصوص العربية")
    print("      - تطبيق RTL على النصوص الإنجليزية فقط")
    print("      - تصحيح محاذاة العناصر")
    print("      - إعادة ترتيب العناصر للوضع الطبيعي")
    
    print("\n   📄 النموذج المتقدم:")
    print("      - نفس التصحيحات المطبقة على النموذج المبسط")
    print("      - ضمان التوافق بين النموذجين")
    
    print("\n🎨 النتائج المتوقعة:")
    print("   ✅ النصوص العربية تظهر بالاتجاه الطبيعي (LTR)")
    print("   ✅ النصوص الإنجليزية في الأقسام المحددة تظهر بـ RTL")
    print("   ✅ ترتيب العناصر طبيعي ومألوف")
    print("   ✅ المحاذاة صحيحة لكل نوع نص")
    
    print("\n📋 الأقسام المصححة:")
    print("   1️⃣ معلومات المستفيد:")
    print("      - النص الإنجليزي: Beneficiary name, address, account")
    print("      - تطبيق RTL على هذا النص فقط")
    
    print("\n   2️⃣ معلومات البنك:")
    print("      - النص الإنجليزي: Bank name, branch, swift, country")
    print("      - تطبيق RTL على هذا النص فقط")
    
    print("\n   3️⃣ معلومات الشركة المرسلة:")
    print("      - النص الإنجليزي: ALFOGEHI FOR GENERAL TRADING...")
    print("      - تطبيق RTL على هذا النص فقط")
    
    print("\n   4️⃣ الغرض من التحويل:")
    print("      - النص الإنجليزي: COST OF FOODSTUFF")
    print("      - تطبيق RTL على هذا النص فقط")
    
    print("\n   5️⃣ التوقيع:")
    print("      - اسم المدير العام")
    print("      - تطبيق RTL حسب الحاجة")
    
    print("\n🚀 كيفية التحقق:")
    print("   1. فتح نافذة طلب الحوالة")
    print("   2. تعبئة البيانات وطباعة النموذج")
    print("   3. التحقق من أن النصوص الإنجليزية في الأقسام المحددة تظهر بـ RTL")
    print("   4. التحقق من أن النصوص العربية تظهر بالاتجاه الطبيعي")
    print("   5. مقارنة النتيجة مع الصورة المرفقة")

def run_correction_test():
    """تشغيل اختبار التصحيح"""
    
    print("🚀 بدء اختبار تصحيح RTL...")
    print("=" * 80)
    
    # اختبار التصحيح
    correction_ok = test_corrected_rtl()
    
    # عرض ملخص التصحيح
    display_correction_summary()
    
    # النتيجة النهائية
    if correction_ok:
        print("\n🏆 تم تصحيح RTL بنجاح!")
        print("✅ النصوص الإنجليزية في الأقسام المحددة لها RTL")
        print("✅ النصوص العربية بالاتجاه الطبيعي")
        print("✅ التصحيح مطبق على النموذجين")
        print("✅ التعليقات واضحة ومفهومة")
        
        print("\n🎉 نماذج الطباعة مصححة ومطابقة للمطلوب!")
        print("💡 جرب الآن طباعة نموذج وتحقق من النتيجة")
        
        return True
        
    else:
        print("\n❌ لا تزال هناك مشاكل في التصحيح")
        print("   - تحقق من تطبيق RTL على النصوص الصحيحة")
        print("   - تأكد من إزالة RTL من النصوص العربية")
        
        return False

if __name__ == "__main__":
    success = run_correction_test()
    print("=" * 80)
    sys.exit(0 if success else 1)
