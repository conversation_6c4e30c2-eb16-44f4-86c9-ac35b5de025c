#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد نماذج PDF لطلبات الحوالات مع دعم كامل للعربية
"""

import os
from datetime import datetime
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm, inch
from reportlab.lib.colors import black, blue, red
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.utils import ImageReader
from PIL import Image
from arabic_reshaper import arabic_reshaper
from bidi.algorithm import get_display

class RemittancePDFGenerator:
    """مولد نماذج PDF لطلبات الحوالات"""
    
    def __init__(self):
        self.page_width, self.page_height = A4
        self.margin = 20 * mm
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي من النظام
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    self.arabic_font = 'Arabic'
                    break
            else:
                # استخدام خط افتراضي
                self.arabic_font = 'Helvetica'
                
        except Exception as e:
            print(f"تحذير: لم يتم تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def reshape_arabic_text(self, text):
        """تشكيل النص العربي للعرض الصحيح"""
        try:
            if not text:
                return ""
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def draw_header(self, c, request_data):
        """رسم رأس النموذج مع الشعار والمعلومات"""
        # رسم رأس الشركة
        self.draw_company_header(c)

        # خط فاصل أفقي تحت رأس الشركة
        c.setLineWidth(1)
        c.line(self.margin + 5*mm, self.page_height - 45*mm,
               self.page_width - self.margin - 5*mm, self.page_height - 45*mm)

        # الرقم والتاريخ تحت الخط الفاصل
        ref_number = request_data.get('request_number', '2025-01')
        request_date = request_data.get('request_date', '2024/01/03')

        # التاريخ (يسار) - استخدام الخط العربي
        c.setFont(self.arabic_font, 11)
        date_text = self.reshape_arabic_text(f"التاريخ: {request_date}")
        c.drawString(self.margin + 10*mm, self.page_height - 55*mm, date_text)

        # الرقم (يمين) - استخدام الخط العربي
        ref_text = self.reshape_arabic_text(f"الرقم: {ref_number}")
        c.drawRightString(self.page_width - self.margin - 10*mm, self.page_height - 55*mm, ref_text)

    def draw_company_header(self, c):
        """رسم رأس الشركة مع الشعار والمعلومات"""
        header_y = self.page_height - 15*mm

        # محاولة إدراج صورة الرأس إذا كانت متوفرة
        header_image_path = self.get_header_image_path()
        if header_image_path and os.path.exists(header_image_path):
            self.draw_header_image(c, header_image_path)
        else:
            # رسم الرأس النصي كبديل
            self.draw_text_header(c, header_y)

    def get_header_image_path(self):
        """الحصول على مسار صورة الرأس"""
        # البحث عن صورة الرأس في مجلد assets
        possible_paths = [
            "assets/header.png",
            "assets/header.jpg",
            "assets/header.jpeg",
            "assets/company_header.png",
            "assets/company_header.jpg",
            "assets/logo_header.png",
            "assets/logo_header.jpg"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None

    def draw_header_image(self, c, image_path):
        """رسم صورة الرأس"""
        try:
            # فتح الصورة وتحديد أبعادها
            with Image.open(image_path) as img:
                img_width, img_height = img.size

            # حساب أبعاد الصورة في PDF (تناسب عرض الصفحة)
            max_width = self.page_width - 2 * self.margin
            max_height = 40*mm  # ارتفاع مناسب للرأس

            # حساب النسبة للحفاظ على تناسق الصورة
            width_ratio = max_width / img_width
            height_ratio = max_height / img_height
            scale_ratio = min(width_ratio, height_ratio)

            # الأبعاد النهائية
            final_width = img_width * scale_ratio
            final_height = img_height * scale_ratio

            # موضع الصورة (وسط الصفحة)
            x_pos = (self.page_width - final_width) / 2
            y_pos = self.page_height - 15*mm - final_height

            # رسم الصورة
            c.drawImage(image_path, x_pos, y_pos, width=final_width, height=final_height)

            print(f"✅ تم إدراج صورة الرأس: {image_path}")

        except Exception as e:
            print(f"⚠️ خطأ في إدراج صورة الرأس: {e}")
            # رسم الرأس النصي كبديل
            self.draw_text_header(c, self.page_height - 15*mm)

    def draw_text_header(self, c, header_y):
        """رسم الرأس النصي كبديل للصورة"""
        # المعلومات العربية (يمين)
        c.setFont(self.arabic_font, 12)
        arabic_company = self.reshape_arabic_text("شركة الفقيهي للتجارة والتموينات المحدودة")
        c.drawRightString(self.page_width - self.margin - 10*mm, header_y, arabic_company)

        arabic_location = self.reshape_arabic_text("صنعاء - الجردة - شارع 24")
        c.setFont(self.arabic_font, 10)
        c.drawRightString(self.page_width - self.margin - 10*mm, header_y - 8*mm, arabic_location)

        arabic_contact = self.reshape_arabic_text("تلفون: 616109 فاكس: 615909")
        c.drawRightString(self.page_width - self.margin - 10*mm, header_y - 16*mm, arabic_contact)

        # المعلومات الإنجليزية (يسار)
        c.setFont('Helvetica-Bold', 12)
        c.drawString(self.margin + 10*mm, header_y, "AL FOGEHI FOR TRADING AND CATERING LTD, CO")

        c.setFont('Helvetica', 10)
        c.drawString(self.margin + 10*mm, header_y - 8*mm, "Sana'a -Algarda'a -24st.")
        c.drawString(self.margin + 10*mm, header_y - 16*mm, "Tel: 616109   Fax: 615909")

        # رسم الشعار في الوسط (دائرة بسيطة كبديل للشعار)
        center_x = self.page_width / 2
        center_y = header_y - 10*mm

        # دائرة خارجية
        c.setLineWidth(2)
        c.circle(center_x, center_y, 12*mm, fill=0)

        # دائرة داخلية
        c.setLineWidth(1)
        c.circle(center_x, center_y, 8*mm, fill=0)

        # نص في الوسط (بديل للشعار)
        c.setFont('Helvetica-Bold', 8)
        c.drawCentredString(center_x, center_y + 2*mm, "AL FOGEHI")
        c.drawCentredString(center_x, center_y - 2*mm, "TRADING")
    
    def draw_title_section(self, c, request_data):
        """رسم قسم العنوان حسب المتطلبات الجديدة"""
        y_pos = self.page_height - 70*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # السطر الأول: المحترمون (يسار) - الأخوة/ اسم الصراف (يمين)
        c.setFont(self.arabic_font, 12)

        # المحترمون (يسار)
        title_text = self.reshape_arabic_text("المحترمون")
        c.drawString(self.margin + 10*mm, y_pos, title_text)

        # الأخوة/ اسم الصراف (يمين)
        exchanger_name = request_data.get('exchanger', 'اسم الصراف')
        if exchanger_name and exchanger_name != 'اختر الصراف...':
            # استخراج اسم الصراف فقط (إزالة التفاصيل الإضافية)
            exchanger_clean = exchanger_name.split(' (')[0].split(' - ')[0]
            # تكوين النص: الأخوة/ اسم الصراف
            brothers_exchanger_text = f"الأخوة/ {exchanger_clean}"
            brothers_exchanger_arabic = self.reshape_arabic_text(brothers_exchanger_text)
            c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, brothers_exchanger_arabic)
        else:
            # في حالة عدم وجود اسم صراف
            company_text = self.reshape_arabic_text("الأخوة/ شركة")
            c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, company_text)
    
    def draw_greeting_section(self, c, request_data):
        """رسم قسم التحية والطلب مع تقليل المسافات"""
        y_pos = self.page_height - 90*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # نص التحية (وسط الصفحة)
        c.setFont(self.arabic_font, 12)
        greeting_text = self.reshape_arabic_text("تحية طيبة وبعد")
        text_width = c.stringWidth(greeting_text, self.arabic_font, 12)
        x_center = (self.page_width - text_width) / 2
        c.drawString(x_center, y_pos, greeting_text)

        # نص الطلب (محاذاة يمين) - حسب الصورة
        amount = request_data.get('remittance_amount', '63,500')
        currency = request_data.get('currency', 'USD')

        # تحويل المبلغ إلى كلمات (مبسط)
        amount_words = self.convert_amount_to_words(amount, currency)
        request_text = f"يرجى تحويل مبلغ {amount_words} (${amount})"
        request_text_arabic = self.reshape_arabic_text(request_text)

        c.setFont(self.arabic_font, 11)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 12*mm, request_text_arabic)

        # وذلك إلى البلد على العنوان التالي
        country = request_data.get('receiver_country', 'الصين')
        address_text = f"وذلك إلى {country} على العنوان التالي: -"
        address_text_arabic = self.reshape_arabic_text(address_text)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 20*mm, address_text_arabic)

    def convert_amount_to_words(self, amount, currency):
        """تحويل المبلغ إلى كلمات (مبسط)"""
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        if amount == '63,500' and currency == 'USD':
            return "ثلاثة وستون ألف وخمسمائة دولار أمريكي لا غير"
        else:
            return f"{amount} {currency}"
    
    def draw_beneficiary_section(self, c, request_data):
        """رسم قسم بيانات المستفيد مع تقليل المسافات"""
        y_pos = self.page_height - 120*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم المستفيد والعنوان ورقم الحساب: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات المستفيد بالإنجليزية - تقليل المسافات
        c.setFont('Helvetica', 10)
        beneficiary_name = request_data.get('receiver_name', 'BENEFICIARY NAME')
        beneficiary_address = request_data.get('receiver_address', 'BENEFICIARY ADDRESS')
        account_number = request_data.get('receiver_account', 'ACCOUNT NUMBER')
        receiver_phone = request_data.get('receiver_phone', '')
        receiver_city = request_data.get('receiver_city', '')

        line_spacing = 4*mm  # تقليل المسافة بين الأسطر أكثر
        current_y = y_pos - 6*mm

        c.drawString(self.margin + 10*mm, current_y, f"Beneficiary name: {beneficiary_name}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Beneficiary address: {beneficiary_address}")
        current_y -= line_spacing
        if receiver_city:
            c.drawString(self.margin + 10*mm, current_y, f"City: {receiver_city}")
            current_y -= line_spacing
        if receiver_phone:
            c.drawString(self.margin + 10*mm, current_y, f"Phone: {receiver_phone}")
            current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Account number: {account_number}")
    
    def draw_bank_section(self, c, request_data):
        """رسم قسم بيانات البنك مع تقليل المسافات وإصلاح البلد"""
        y_pos = self.page_height - 150*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم البنك المستفيد والعنوان والسويفت كود: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات البنك بالإنجليزية - تقليل المسافات
        c.setFont('Helvetica', 10)
        bank_name = request_data.get('receiver_bank', 'BANK NAME')
        bank_branch = request_data.get('receiver_bank_branch', 'BANK BRANCH')
        swift_code = request_data.get('receiver_swift', 'SWIFT CODE')
        bank_address = request_data.get('receiver_bank_address', '')

        # إصلاح مشكلة البلد - استخدام البلد من معلومات المستقبل بالإنجليزية وبحروف كبيرة
        receiver_country = request_data.get('receiver_country', 'CHINA')
        # تحويل إلى أحرف كبيرة وإنجليزية
        bank_country = receiver_country.upper() if receiver_country else 'CHINA'

        line_spacing = 4*mm  # تقليل المسافة بين الأسطر أكثر
        current_y = y_pos - 6*mm

        c.drawString(self.margin + 10*mm, current_y, f"Bank: {bank_name}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Branch: {bank_branch}")
        current_y -= line_spacing
        if bank_address:
            c.drawString(self.margin + 10*mm, current_y, f"Address: {bank_address}")
            current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Swift: {swift_code}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Bank country: {bank_country}")
    
    def draw_sender_section(self, c, request_data):
        """رسم قسم بيانات الشركة المرسلة مع تقليل المسافات"""
        y_pos = self.page_height - 180*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم الشركة المرسلة والعنوان: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات الشركة بالإنجليزية - تقليل المسافات
        c.setFont('Helvetica-Bold', 10)
        sender_name = request_data.get('sender_name', 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD')
        sender_address = request_data.get('sender_address', 'TAIZ STREET, ORPHAN BUILDING, NEAR ALBRKA STORES – SANA\'A, YEMEN')
        sender_entity = request_data.get('sender_entity', 'G.M: - NASHA\'AT RASHAD QASIM ALDUBAEE')

        line_spacing = 4*mm  # تقليل المسافة بين الأسطر أكثر
        current_y = y_pos - 6*mm

        c.drawString(self.margin + 10*mm, current_y, sender_name)
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, sender_address)
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, sender_entity)
        current_y -= line_spacing

        # معلومات الاتصال
        c.setFont('Helvetica', 9)
        phone = request_data.get('sender_phone', '+967 1 616109')
        fax = request_data.get('sender_fax', '+967 1 615909')
        mobile = request_data.get('sender_mobile', '+967 777161609')
        email = request_data.get('sender_email', '<EMAIL>, <EMAIL>')
        box_number = request_data.get('sender_box', '1903')

        c.drawString(self.margin + 10*mm, current_y, f"BOX: {box_number}    TEL: {phone}    FAX: {fax}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"MOBILE: {mobile}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"EMAIL: {email}")
    
    def draw_purpose_section(self, c, request_data):
        """رسم قسم الغرض من التحويل مع تقليل المسافات"""
        y_pos = self.page_height - 210*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("الغرض من التحويل: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # الغرض بالإنجليزية
        c.setFont('Helvetica', 10)
        purpose = request_data.get('transfer_purpose', 'COST OF FOODSTUFF')
        c.drawString(self.margin + 10*mm, y_pos - 6*mm, purpose)

    def draw_footer_section(self, c, request_data):
        """رسم قسم التوقيع والختم مع تقليل المسافات"""
        y_pos = self.page_height - 230*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # نص الشكر والتقدير - تقليل المسافات
        c.setFont(self.arabic_font, 11)
        thanks_text = self.reshape_arabic_text("مرفق لكم صورة التحويل من البنك كمرجع")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, thanks_text)

        thanks_text2 = self.reshape_arabic_text("وذلك بقيد التحويل على حسابنا لديكم .....")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 8*mm, thanks_text2)

        thanks_text3 = self.reshape_arabic_text("وشكراً")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 16*mm, thanks_text3)

        # التوقيع والختم
        c.setFont(self.arabic_font, 11)
        signature_text = self.reshape_arabic_text("المدير العام")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 28*mm, signature_text)

        # اسم المدير العام
        manager_name = request_data.get('manager_name', 'نشأت رشاد قاسم الدبعي')
        signature_name = self.reshape_arabic_text(manager_name)
        c.setFont(self.arabic_font, 10)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 36*mm, signature_name)

        # مكان للختم
        c.setFont('Helvetica', 8)
        c.drawString(self.margin + 10*mm, y_pos - 28*mm, "STAMP")
        c.rect(self.margin + 10*mm, y_pos - 40*mm, 40*mm, 18*mm)  # مربع للختم
    
    def generate_pdf(self, request_data, output_path=None):
        """إنشاء ملف PDF للحوالة"""
        if not output_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f"remittance_request_{timestamp}.pdf"
        
        # إنشاء ملف PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        
        # رسم أقسام النموذج
        self.draw_header(c, request_data)
        self.draw_title_section(c, request_data)
        self.draw_greeting_section(c, request_data)
        self.draw_beneficiary_section(c, request_data)
        self.draw_bank_section(c, request_data)
        self.draw_sender_section(c, request_data)
        self.draw_purpose_section(c, request_data)
        self.draw_footer_section(c, request_data)
        
        # رسم إطار حول الصفحة
        c.rect(self.margin/2, self.margin/2, 
               self.page_width - self.margin, 
               self.page_height - self.margin)
        
        # حفظ الملف
        c.save()
        
        return output_path
