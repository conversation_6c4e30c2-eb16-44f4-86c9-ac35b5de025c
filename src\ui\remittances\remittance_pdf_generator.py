#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد نماذج PDF لطلبات الحوالات مع دعم كامل للعربية
"""

import os
from datetime import datetime
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm, inch
from reportlab.lib.colors import black, blue, red
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.utils import ImageReader
from PIL import Image
from arabic_reshaper import arabic_reshaper
from bidi.algorithm import get_display
from ...database.database_manager import DatabaseManager
from ...database.models import Company

class RemittancePDFGenerator:
    """مولد نماذج PDF لطلبات الحوالات"""

    def __init__(self):
        self.page_width, self.page_height = A4
        self.margin = 20 * mm
        self.arabic_font = None
        self.db_manager = DatabaseManager()
        self.company_data = None
        self.setup_fonts()
        self.load_company_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي من النظام
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    self.arabic_font = 'Arabic'
                    break
            else:
                # استخدام خط افتراضي
                self.arabic_font = 'Helvetica'
                
        except Exception as e:
            print(f"تحذير: لم يتم تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def reshape_arabic_text(self, text):
        """تشكيل النص العربي للعرض الصحيح"""
        try:
            if not text:
                return ""
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text

    def load_company_data(self):
        """تحميل بيانات الشركة من قاعدة البيانات"""
        try:
            session = self.db_manager.get_session()
            try:
                # البحث عن الشركة النشطة
                company = session.query(Company).filter(Company.is_active == True).first()

                if company:
                    self.company_data = {
                        'name': company.name or "شركة الشحنات المتقدمة",
                        'name_en': company.name_en or "Advanced Shipping Company",
                        'address': company.address or "الرياض، المملكة العربية السعودية",
                        'phone': company.phone or "+966-11-1234567",
                        'email': company.email or "<EMAIL>",
                        'tax_number': company.tax_number or "",
                        'commercial_register': company.commercial_register or "",
                        'logo_path': company.logo_path
                    }
                    print(f"✅ تم تحميل بيانات الشركة: {company.name}")
                else:
                    # استخدام بيانات افتراضية
                    self.company_data = self.get_default_company_data()
                    print("⚠️ لم يتم العثور على بيانات شركة، استخدام البيانات الافتراضية")

            finally:
                session.close()

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الشركة: {e}")
            self.company_data = self.get_default_company_data()

    def get_default_company_data(self):
        """الحصول على بيانات الشركة الافتراضية"""
        return {
            'name': "شركة الشحنات المتقدمة",
            'name_en': "Advanced Shipping Company",
            'address': "الرياض، المملكة العربية السعودية",
            'phone': "+966-11-1234567",
            'email': "<EMAIL>",
            'tax_number': "",
            'commercial_register': "",
            'logo_path': None
        }
    
    def draw_header(self, c, request_data):
        """رسم رأس النموذج مع الشعار والمعلومات"""
        # رسم رأس الشركة
        self.draw_company_header(c)

        # خط فاصل أفقي تحت رأس الشركة
        c.setLineWidth(1)
        c.line(self.margin + 5*mm, self.page_height - 45*mm,
               self.page_width - self.margin - 5*mm, self.page_height - 45*mm)

        # الرقم والتاريخ تحت الخط الفاصل
        ref_number = request_data.get('request_number', '2025-01')
        request_date = request_data.get('request_date', '2024/01/03')

        # التاريخ (يسار) - استخدام الخط العربي
        c.setFont(self.arabic_font, 11)
        date_text = self.reshape_arabic_text(f"التاريخ: {request_date}")
        c.drawString(self.margin + 10*mm, self.page_height - 55*mm, date_text)

        # الرقم (يمين) - استخدام الخط العربي
        ref_text = self.reshape_arabic_text(f"الرقم: {ref_number}")
        c.drawRightString(self.page_width - self.margin - 10*mm, self.page_height - 55*mm, ref_text)

    def draw_company_header(self, c):
        """رسم رأس الشركة مع الشعار والمعلومات من قاعدة البيانات"""
        header_y = self.page_height - 15*mm

        # محاولة إدراج صورة الرأس من قاعدة البيانات أو مجلد assets
        header_image_path = self.get_company_logo_path()
        if header_image_path and os.path.exists(header_image_path):
            self.draw_header_image(c, header_image_path)
        else:
            # رسم الرأس النصي كبديل باستخدام بيانات الشركة
            self.draw_text_header(c, header_y)

    def get_company_logo_path(self):
        """الحصول على مسار شعار الشركة من قاعدة البيانات أو مجلد assets"""
        # أولاً: البحث في بيانات الشركة من قاعدة البيانات
        if self.company_data and self.company_data.get('logo_path'):
            logo_path = self.company_data['logo_path']
            if os.path.exists(logo_path):
                return logo_path

        # ثانياً: البحث عن صورة الرأس في مجلد assets
        possible_paths = [
            "assets/header.png",
            "assets/header.jpg",
            "assets/header.jpeg",
            "assets/company_header.png",
            "assets/company_header.jpg",
            "assets/logo_header.png",
            "assets/logo_header.jpg",
            "assets/logo.png",
            "assets/logo.jpg"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None

    def get_header_image_path(self):
        """الحصول على مسار صورة الرأس (للتوافق مع الكود القديم)"""
        return self.get_company_logo_path()

    def draw_header_image(self, c, image_path):
        """رسم صورة الرأس مع بيانات الشركة"""
        try:
            # فتح الصورة وتحديد أبعادها
            with Image.open(image_path) as img:
                img_width, img_height = img.size

            # حساب أبعاد الصورة المصغرة (شعار فقط، ليس رأس كامل)
            max_logo_width = 60*mm   # عرض أقصى للشعار
            max_logo_height = 25*mm  # ارتفاع أقصى للشعار (لا يتجاوز الخط الفاصل)

            # حساب النسبة للحفاظ على تناسق الصورة
            width_ratio = max_logo_width / img_width
            height_ratio = max_logo_height / img_height
            scale_ratio = min(width_ratio, height_ratio)

            # الأبعاد النهائية للشعار
            final_width = img_width * scale_ratio
            final_height = img_height * scale_ratio

            # موضع الشعار (وسط الصفحة، أعلى)
            logo_x = (self.page_width - final_width) / 2
            logo_y = self.page_height - 15*mm - final_height

            # رسم الشعار
            c.drawImage(image_path, logo_x, logo_y, width=final_width, height=final_height)

            # رسم بيانات الشركة حول الشعار
            self.draw_company_text_around_logo(c, logo_x, logo_y, final_width, final_height)

            print(f"✅ تم إدراج شعار الشركة: {image_path}")
            print(f"📏 أبعاد الشعار: {final_width/mm:.1f}x{final_height/mm:.1f}mm")

        except Exception as e:
            print(f"⚠️ خطأ في إدراج صورة الرأس: {e}")
            # رسم الرأس النصي كبديل
            self.draw_text_header(c, self.page_height - 15*mm)

    def draw_company_text_around_logo(self, c, logo_x, logo_y, logo_width, logo_height):
        """رسم بيانات الشركة حول الشعار"""
        if not self.company_data:
            self.load_company_data()

        # حساب المواضع
        header_y = self.page_height - 15*mm

        # المعلومات العربية (يمين الشعار)
        arabic_x = logo_x + logo_width + 10*mm
        if arabic_x < self.page_width - self.margin - 10*mm:
            # اسم الشركة العربي
            c.setFont(self.arabic_font, 12)  # حجم موحد مع الإنجليزية
            arabic_company = self.reshape_arabic_text(self.company_data['name'])
            c.drawRightString(self.page_width - self.margin - 5*mm, header_y, arabic_company)

            # العنوان العربي
            c.setFont(self.arabic_font, 9)
            arabic_address = self.reshape_arabic_text(self.company_data['address'])
            c.drawRightString(self.page_width - self.margin - 5*mm, header_y - 7*mm, arabic_address)

            # معلومات الاتصال العربية
            phone_text = f"تلفون: {self.company_data['phone']}"
            arabic_contact = self.reshape_arabic_text(phone_text)
            c.setFont(self.arabic_font, 8)
            c.drawRightString(self.page_width - self.margin - 5*mm, header_y - 14*mm, arabic_contact)

        # المعلومات الإنجليزية (يسار الشعار)
        english_x = logo_x - 10*mm
        if english_x > self.margin + 10*mm:
            # اسم الشركة الإنجليزي - حجم مصغر لتجنب التداخل
            c.setFont('Helvetica-Bold', 10)  # حجم مصغر لتجنب التداخل مع العربي
            c.drawString(self.margin + 5*mm, header_y, self.company_data['name_en'])

            # العنوان الإنجليزي
            c.setFont('Helvetica', 9)
            # تقسيم العنوان إذا كان طويلاً
            address_en = self.company_data['address']
            if len(address_en) > 40:
                words = address_en.split()
                line1 = " ".join(words[:len(words)//2])
                line2 = " ".join(words[len(words)//2:])
                c.drawString(self.margin + 5*mm, header_y - 5*mm, line1)
                c.drawString(self.margin + 5*mm, header_y - 10*mm, line2)
                contact_y = header_y - 15*mm
            else:
                c.drawString(self.margin + 5*mm, header_y - 7*mm, address_en)
                contact_y = header_y - 14*mm

            # معلومات الاتصال الإنجليزية
            c.setFont('Helvetica', 8)
            contact_en = f"Tel: {self.company_data['phone']}"
            c.drawString(self.margin + 5*mm, contact_y, contact_en)

    def draw_text_header(self, c, header_y):
        """رسم الرأس النصي كبديل للصورة باستخدام بيانات الشركة"""
        if not self.company_data:
            self.load_company_data()

        print("📝 رسم الرأس النصي مع بيانات الشركة...")

        # المعلومات العربية (يمين)
        c.setFont(self.arabic_font, 12)  # حجم أساسي للاسم العربي
        arabic_company = self.reshape_arabic_text(self.company_data['name'])
        c.drawRightString(self.page_width - self.margin - 5*mm, header_y, arabic_company)
        print(f"   ✅ اسم الشركة العربي: {self.company_data['name']} (حجم خط 12 - أساسي)")

        # العنوان العربي
        arabic_address = self.reshape_arabic_text(self.company_data['address'])
        c.setFont(self.arabic_font, 10)
        c.drawRightString(self.page_width - self.margin - 5*mm, header_y - 8*mm, arabic_address)
        print(f"   ✅ العنوان العربي: {self.company_data['address']}")

        # معلومات الاتصال العربية
        phone_text = f"تلفون: {self.company_data['phone']}"
        arabic_contact = self.reshape_arabic_text(phone_text)
        c.setFont(self.arabic_font, 9)
        c.drawRightString(self.page_width - self.margin - 5*mm, header_y - 16*mm, arabic_contact)

        # البريد الإلكتروني العربي (سطر منفصل)
        if self.company_data['email']:
            email_text = f"البريد: {self.company_data['email']}"
            arabic_email = self.reshape_arabic_text(email_text)
            c.setFont(self.arabic_font, 8)
            c.drawRightString(self.page_width - self.margin - 5*mm, header_y - 23*mm, arabic_email)

        # المعلومات الإنجليزية (يسار) - حجم مصغر لتجنب التداخل
        c.setFont('Helvetica-Bold', 10)  # حجم مصغر لتجنب التداخل مع العربي
        c.drawString(self.margin + 5*mm, header_y, self.company_data['name_en'])
        print(f"   ✅ اسم الشركة الإنجليزي: {self.company_data['name_en']} (حجم خط 10 - مصغر)")

        # العنوان الإنجليزي
        c.setFont('Helvetica', 10)
        address_en = self.company_data['address']
        # تقسيم العنوان إذا كان طويلاً
        if len(address_en) > 45:
            words = address_en.split()
            mid_point = len(words) // 2
            line1 = " ".join(words[:mid_point])
            line2 = " ".join(words[mid_point:])
            c.drawString(self.margin + 5*mm, header_y - 6*mm, line1)
            c.drawString(self.margin + 5*mm, header_y - 12*mm, line2)
            contact_y = header_y - 18*mm
        else:
            c.drawString(self.margin + 5*mm, header_y - 8*mm, address_en)
            contact_y = header_y - 16*mm

        print(f"   ✅ العنوان الإنجليزي: {address_en}")

        # معلومات الاتصال الإنجليزية
        c.setFont('Helvetica', 9)
        contact_en = f"Tel: {self.company_data['phone']}"
        c.drawString(self.margin + 5*mm, contact_y, contact_en)

        # البريد الإلكتروني الإنجليزي (سطر منفصل)
        if self.company_data['email']:
            c.setFont('Helvetica', 8)
            email_en = f"Email: {self.company_data['email']}"
            c.drawString(self.margin + 5*mm, contact_y - 7*mm, email_en)

        # رسم شعار بسيط في الوسط
        self.draw_simple_logo(c, header_y)

    def draw_simple_logo(self, c, header_y):
        """رسم شعار بسيط في وسط الرأس (حجم مصغر)"""
        center_x = self.page_width / 2
        center_y = header_y - 15*mm

        # دائرة خارجية (حجم أصغر)
        c.setLineWidth(1.5)
        c.circle(center_x, center_y, 8*mm, fill=0)

        # دائرة داخلية (حجم أصغر)
        c.setLineWidth(1)
        c.circle(center_x, center_y, 5*mm, fill=0)

        # نص الشركة في الوسط (أول حرفين من الاسم الإنجليزي)
        c.setFont('Helvetica-Bold', 8)
        company_initials = self.get_company_initials()
        c.drawCentredString(center_x, center_y + 0.5*mm, company_initials)

        # خط فاصل صغير
        c.setLineWidth(0.5)
        c.line(center_x - 3*mm, center_y - 1.5*mm, center_x + 3*mm, center_y - 1.5*mm)

        # نص "TRADING" أو مختصر
        c.setFont('Helvetica', 5)
        c.drawCentredString(center_x, center_y - 4*mm, "TRADING")

    def get_company_initials(self):
        """الحصول على الأحرف الأولى من اسم الشركة"""
        if not self.company_data:
            return "SC"  # Shipping Company

        name_en = self.company_data['name_en']
        words = name_en.split()

        if len(words) >= 2:
            return f"{words[0][0]}{words[1][0]}"
        elif len(words) == 1:
            return words[0][:2].upper()
        else:
            return "SC"
    
    def draw_title_section(self, c, request_data):
        """رسم قسم العنوان حسب المتطلبات الجديدة"""
        y_pos = self.page_height - 70*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # السطر الأول: المحترمون (يسار) - الأخوة/ اسم الصراف (يمين)
        c.setFont(self.arabic_font, 12)

        # المحترمون (يسار)
        title_text = self.reshape_arabic_text("المحترمون")
        c.drawString(self.margin + 10*mm, y_pos, title_text)

        # الأخوة/ اسم الصراف (يمين)
        exchanger_name = request_data.get('exchanger', 'اسم الصراف')
        if exchanger_name and exchanger_name != 'اختر الصراف...':
            # استخراج اسم الصراف فقط (إزالة التفاصيل الإضافية)
            exchanger_clean = exchanger_name.split(' (')[0].split(' - ')[0]
            # تكوين النص: الأخوة/ اسم الصراف
            brothers_exchanger_text = f"الأخوة/ {exchanger_clean}"
            brothers_exchanger_arabic = self.reshape_arabic_text(brothers_exchanger_text)
            c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, brothers_exchanger_arabic)
        else:
            # في حالة عدم وجود اسم صراف
            company_text = self.reshape_arabic_text("الأخوة/ شركة")
            c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, company_text)
    
    def draw_greeting_section(self, c, request_data):
        """رسم قسم التحية والطلب مع تقليل المسافات"""
        y_pos = self.page_height - 90*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # نص التحية (وسط الصفحة)
        c.setFont(self.arabic_font, 12)
        greeting_text = self.reshape_arabic_text("تحية طيبة وبعد")
        text_width = c.stringWidth(greeting_text, self.arabic_font, 12)
        x_center = (self.page_width - text_width) / 2
        c.drawString(x_center, y_pos, greeting_text)

        # نص الطلب (محاذاة يمين) - حسب الصورة
        amount = request_data.get('remittance_amount', '63,500')
        currency = request_data.get('currency', 'USD')

        # تحويل المبلغ إلى كلمات (مبسط)
        amount_words = self.convert_amount_to_words(amount, currency)
        request_text = f"يرجى تحويل مبلغ {amount_words} (${amount})"
        request_text_arabic = self.reshape_arabic_text(request_text)

        c.setFont(self.arabic_font, 11)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 12*mm, request_text_arabic)

        # وذلك إلى البلد على العنوان التالي
        country = request_data.get('receiver_country', 'الصين')
        address_text = f"وذلك إلى {country} على العنوان التالي: -"
        address_text_arabic = self.reshape_arabic_text(address_text)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 20*mm, address_text_arabic)

    def convert_amount_to_words(self, amount, currency):
        """تحويل المبلغ إلى كلمات (مبسط)"""
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        if amount == '63,500' and currency == 'USD':
            return "ثلاثة وستون ألف وخمسمائة دولار أمريكي لا غير"
        else:
            return f"{amount} {currency}"
    
    def draw_beneficiary_section(self, c, request_data):
        """رسم قسم بيانات المستفيد مع تقليل المسافات"""
        y_pos = self.page_height - 120*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم المستفيد والعنوان ورقم الحساب: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات المستفيد بالإنجليزية - تقليل المسافات
        c.setFont('Helvetica', 10)
        beneficiary_name = request_data.get('receiver_name', 'BENEFICIARY NAME')
        beneficiary_address = request_data.get('receiver_address', 'BENEFICIARY ADDRESS')
        account_number = request_data.get('receiver_account', 'ACCOUNT NUMBER')
        receiver_phone = request_data.get('receiver_phone', '')
        receiver_city = request_data.get('receiver_city', '')

        line_spacing = 4*mm  # تقليل المسافة بين الأسطر أكثر
        current_y = y_pos - 6*mm

        c.drawString(self.margin + 10*mm, current_y, f"Beneficiary name: {beneficiary_name}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Beneficiary address: {beneficiary_address}")
        current_y -= line_spacing
        if receiver_city:
            c.drawString(self.margin + 10*mm, current_y, f"City: {receiver_city}")
            current_y -= line_spacing
        if receiver_phone:
            c.drawString(self.margin + 10*mm, current_y, f"Phone: {receiver_phone}")
            current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Account number: {account_number}")
    
    def draw_bank_section(self, c, request_data):
        """رسم قسم بيانات البنك مع تقليل المسافات وإصلاح البلد"""
        y_pos = self.page_height - 150*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم البنك المستفيد والعنوان والسويفت كود: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات البنك بالإنجليزية - تقليل المسافات
        c.setFont('Helvetica', 10)
        bank_name = request_data.get('receiver_bank', 'BANK NAME')
        bank_branch = request_data.get('receiver_bank_branch', 'BANK BRANCH')
        swift_code = request_data.get('receiver_swift', 'SWIFT CODE')
        bank_address = request_data.get('receiver_bank_address', '')

        # إصلاح مشكلة البلد - استخدام البلد من معلومات المستقبل بالإنجليزية وبحروف كبيرة
        receiver_country = request_data.get('receiver_country', 'CHINA')
        # تحويل إلى أحرف كبيرة وإنجليزية
        bank_country = receiver_country.upper() if receiver_country else 'CHINA'

        line_spacing = 4*mm  # تقليل المسافة بين الأسطر أكثر
        current_y = y_pos - 6*mm

        c.drawString(self.margin + 10*mm, current_y, f"Bank: {bank_name}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Branch: {bank_branch}")
        current_y -= line_spacing
        if bank_address:
            c.drawString(self.margin + 10*mm, current_y, f"Address: {bank_address}")
            current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Swift: {swift_code}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"Bank country: {bank_country}")
    
    def draw_sender_section(self, c, request_data):
        """رسم قسم بيانات الشركة المرسلة مع تقليل المسافات"""
        y_pos = self.page_height - 180*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم الشركة المرسلة والعنوان: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات الشركة بالإنجليزية - تقليل المسافات
        c.setFont('Helvetica-Bold', 10)
        sender_name = request_data.get('sender_name', 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD')
        sender_address = request_data.get('sender_address', 'TAIZ STREET, ORPHAN BUILDING, NEAR ALBRKA STORES – SANA\'A, YEMEN')
        sender_entity = request_data.get('sender_entity', 'G.M: - NASHA\'AT RASHAD QASIM ALDUBAEE')

        line_spacing = 4*mm  # تقليل المسافة بين الأسطر أكثر
        current_y = y_pos - 6*mm

        c.drawString(self.margin + 10*mm, current_y, sender_name)
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, sender_address)
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, sender_entity)
        current_y -= line_spacing

        # معلومات الاتصال
        c.setFont('Helvetica', 9)
        phone = request_data.get('sender_phone', '+967 1 616109')
        fax = request_data.get('sender_fax', '+967 1 615909')
        mobile = request_data.get('sender_mobile', '+967 777161609')
        email = request_data.get('sender_email', '<EMAIL>, <EMAIL>')
        box_number = request_data.get('sender_box', '1903')

        c.drawString(self.margin + 10*mm, current_y, f"BOX: {box_number}    TEL: {phone}    FAX: {fax}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"MOBILE: {mobile}")
        current_y -= line_spacing
        c.drawString(self.margin + 10*mm, current_y, f"EMAIL: {email}")
    
    def draw_purpose_section(self, c, request_data):
        """رسم قسم الغرض من التحويل مع تقليل المسافات"""
        y_pos = self.page_height - 210*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("الغرض من التحويل: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # الغرض بالإنجليزية
        c.setFont('Helvetica', 10)
        purpose = request_data.get('transfer_purpose', 'COST OF FOODSTUFF')
        c.drawString(self.margin + 10*mm, y_pos - 6*mm, purpose)

    def draw_footer_section(self, c, request_data):
        """رسم قسم التوقيع والختم مع تقليل المسافات"""
        y_pos = self.page_height - 230*mm  # تحديث الموضع ليتناسب مع الرأس الجديد

        # نص الشكر والتقدير - تقليل المسافات
        c.setFont(self.arabic_font, 11)
        thanks_text = self.reshape_arabic_text("مرفق لكم صورة التحويل من البنك كمرجع")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, thanks_text)

        thanks_text2 = self.reshape_arabic_text("وذلك بقيد التحويل على حسابنا لديكم .....")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 8*mm, thanks_text2)

        thanks_text3 = self.reshape_arabic_text("وشكراً")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 16*mm, thanks_text3)

        # التوقيع والختم
        c.setFont(self.arabic_font, 11)
        signature_text = self.reshape_arabic_text("المدير العام")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 28*mm, signature_text)

        # اسم المدير العام
        manager_name = request_data.get('manager_name', 'نشأت رشاد قاسم الدبعي')
        signature_name = self.reshape_arabic_text(manager_name)
        c.setFont(self.arabic_font, 10)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 36*mm, signature_name)

        # مكان للختم
        c.setFont('Helvetica', 8)
        c.drawString(self.margin + 10*mm, y_pos - 28*mm, "STAMP")
        c.rect(self.margin + 10*mm, y_pos - 40*mm, 40*mm, 18*mm)  # مربع للختم
    
    def generate_pdf(self, request_data, output_path=None):
        """إنشاء ملف PDF للحوالة"""
        if not output_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f"remittance_request_{timestamp}.pdf"
        
        # إنشاء ملف PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        
        # رسم أقسام النموذج
        self.draw_header(c, request_data)
        self.draw_title_section(c, request_data)
        self.draw_greeting_section(c, request_data)
        self.draw_beneficiary_section(c, request_data)
        self.draw_bank_section(c, request_data)
        self.draw_sender_section(c, request_data)
        self.draw_purpose_section(c, request_data)
        self.draw_footer_section(c, request_data)
        
        # رسم إطار حول الصفحة
        c.rect(self.margin/2, self.margin/2, 
               self.page_width - self.margin, 
               self.page_height - self.margin)
        
        # حفظ الملف
        c.save()
        
        return output_path
