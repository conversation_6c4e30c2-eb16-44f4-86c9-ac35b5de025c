#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد نماذج PDF لطلبات الحوالات مع دعم كامل للعربية
"""

import os
from datetime import datetime
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm, inch
from reportlab.lib.colors import black, blue, red
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from arabic_reshaper import arabic_reshaper
from bidi.algorithm import get_display

class RemittancePDFGenerator:
    """مولد نماذج PDF لطلبات الحوالات"""
    
    def __init__(self):
        self.page_width, self.page_height = A4
        self.margin = 20 * mm
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي من النظام
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    self.arabic_font = 'Arabic'
                    break
            else:
                # استخدام خط افتراضي
                self.arabic_font = 'Helvetica'
                
        except Exception as e:
            print(f"تحذير: لم يتم تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def reshape_arabic_text(self, text):
        """تشكيل النص العربي للعرض الصحيح"""
        try:
            if not text:
                return ""
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def draw_header(self, c, request_data):
        """رسم رأس النموذج"""
        # خط فاصل أفقي في الأعلى (ترك رأس فارغ)
        c.setLineWidth(1)
        c.line(self.margin + 5*mm, self.page_height - 25*mm,
               self.page_width - self.margin - 5*mm, self.page_height - 25*mm)

        # الرقم والتاريخ تحت الخط الفاصل
        ref_number = request_data.get('request_number', '2025-01')
        request_date = request_data.get('request_date', '2024/01/03')

        # التاريخ (يسار)
        c.setFont('Helvetica', 11)
        date_text = f"التاريخ: {request_date}"
        c.drawString(self.margin + 10*mm, self.page_height - 35*mm, date_text)

        # الرقم (يمين)
        ref_text = f"الرقم: {ref_number}"
        c.drawRightString(self.page_width - self.margin - 10*mm, self.page_height - 35*mm, ref_text)
    
    def draw_title_section(self, c, request_data):
        """رسم قسم العنوان"""
        y_pos = self.page_height - 50*mm

        # المحترمون (يسار)
        c.setFont(self.arabic_font, 12)
        title_text = self.reshape_arabic_text("المحترمون")
        c.drawString(self.margin + 10*mm, y_pos, title_text)

        # للصرافة (يمين)
        c.setFont(self.arabic_font, 12)
        subtitle_text = self.reshape_arabic_text("للصرافة")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, subtitle_text)

        # الأخوة - شركة (يمين في السطر التالي)
        c.setFont(self.arabic_font, 12)
        company_text = self.reshape_arabic_text("الأخوة - شركة")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 12*mm, company_text)

        # اسم الصراف (بعد نص الأخوة شركة)
        exchanger_name = request_data.get('exchanger', 'اسم الصراف')
        if exchanger_name and exchanger_name != 'اختر الصراف...':
            # استخراج اسم الصراف فقط (إزالة التفاصيل الإضافية)
            exchanger_clean = exchanger_name.split(' (')[0].split(' - ')[0]
            exchanger_text = self.reshape_arabic_text(exchanger_clean)
            c.setFont(self.arabic_font, 11)
            c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 24*mm, exchanger_text)
    
    def draw_greeting_section(self, c, request_data):
        """رسم قسم التحية والطلب"""
        y_pos = self.page_height - 85*mm

        # نص التحية (وسط الصفحة)
        c.setFont(self.arabic_font, 12)
        greeting_text = self.reshape_arabic_text("تحية طيبة وبعد")
        text_width = c.stringWidth(greeting_text, self.arabic_font, 12)
        x_center = (self.page_width - text_width) / 2
        c.drawString(x_center, y_pos, greeting_text)

        # نص الطلب (محاذاة يمين)
        amount = request_data.get('remittance_amount', '63,500')
        currency = request_data.get('currency', 'USD')

        # تحويل المبلغ إلى كلمات (مبسط)
        amount_words = self.convert_amount_to_words(amount, currency)
        request_text = f"يرجى تحويل مبلغ {amount_words} ({amount} {currency})"
        request_text_arabic = self.reshape_arabic_text(request_text)

        c.setFont(self.arabic_font, 11)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 15*mm, request_text_arabic)

        # وذلك إلى البلد على العنوان التالي
        country = request_data.get('receiver_country', 'الصين')
        address_text = f"وذلك إلى {country} على العنوان التالي: -"
        address_text_arabic = self.reshape_arabic_text(address_text)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 25*mm, address_text_arabic)

    def convert_amount_to_words(self, amount, currency):
        """تحويل المبلغ إلى كلمات (مبسط)"""
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        if amount == '63,500' and currency == 'USD':
            return "ثلاثة وستون ألف وخمسمائة دولار أمريكي لا غير"
        else:
            return f"{amount} {currency}"
    
    def draw_beneficiary_section(self, c, request_data):
        """رسم قسم بيانات المستفيد"""
        y_pos = self.page_height - 125*mm

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم المستفيد والعنوان ورقم الحساب: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات المستفيد بالإنجليزية
        c.setFont('Helvetica', 10)
        beneficiary_name = request_data.get('receiver_name', 'BENEFICIARY NAME')
        beneficiary_address = request_data.get('receiver_address', 'BENEFICIARY ADDRESS')
        account_number = request_data.get('receiver_account', 'ACCOUNT NUMBER')
        receiver_phone = request_data.get('receiver_phone', '')
        receiver_city = request_data.get('receiver_city', '')

        c.drawString(self.margin + 10*mm, y_pos - 10*mm, f"Beneficiary name: {beneficiary_name}")
        c.drawString(self.margin + 10*mm, y_pos - 18*mm, f"Beneficiary address: {beneficiary_address}")
        if receiver_city:
            c.drawString(self.margin + 10*mm, y_pos - 26*mm, f"City: {receiver_city}")
        if receiver_phone:
            c.drawString(self.margin + 10*mm, y_pos - 34*mm, f"Phone: {receiver_phone}")
        c.drawString(self.margin + 10*mm, y_pos - 42*mm, f"Account number: {account_number}")
    
    def draw_bank_section(self, c, request_data):
        """رسم قسم بيانات البنك"""
        y_pos = self.page_height - 180*mm

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم البنك المستفيد والعنوان والسويفت كود: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات البنك بالإنجليزية
        c.setFont('Helvetica', 10)
        bank_name = request_data.get('receiver_bank', 'BANK NAME')
        bank_branch = request_data.get('receiver_bank_branch', 'BANK BRANCH')
        swift_code = request_data.get('receiver_swift', 'SWIFT CODE')
        bank_country = request_data.get('receiver_country', 'BANK COUNTRY')
        bank_address = request_data.get('receiver_bank_address', '')

        c.drawString(self.margin + 10*mm, y_pos - 10*mm, f"Bank: {bank_name}")
        c.drawString(self.margin + 10*mm, y_pos - 18*mm, f"Branch: {bank_branch}")
        if bank_address:
            c.drawString(self.margin + 10*mm, y_pos - 26*mm, f"Address: {bank_address}")
        c.drawString(self.margin + 10*mm, y_pos - 34*mm, f"Swift: {swift_code}")
        c.drawString(self.margin + 10*mm, y_pos - 42*mm, f"Bank country: {bank_country}")
    
    def draw_sender_section(self, c, request_data):
        """رسم قسم بيانات الشركة المرسلة"""
        y_pos = self.page_height - 235*mm

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("اسم الشركة المرسلة والعنوان: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # بيانات الشركة بالإنجليزية
        c.setFont('Helvetica-Bold', 10)
        sender_name = request_data.get('sender_name', 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD')
        sender_address = request_data.get('sender_address', 'TAIZ STREET, ORPHAN BUILDING, NEAR ALBRKA STORES – SANA\'A, YEMEN')
        sender_entity = request_data.get('sender_entity', 'G.M: - NASHA\'AT RASHAD QASIM ALDUBAEE')

        c.drawString(self.margin + 10*mm, y_pos - 10*mm, sender_name)
        c.drawString(self.margin + 10*mm, y_pos - 18*mm, sender_address)
        c.drawString(self.margin + 10*mm, y_pos - 26*mm, sender_entity)

        # معلومات الاتصال
        c.setFont('Helvetica', 9)
        phone = request_data.get('sender_phone', '+967 1 616109')
        fax = request_data.get('sender_fax', '+967 1 615909')
        mobile = request_data.get('sender_mobile', '+967 777161609')
        email = request_data.get('sender_email', '<EMAIL>, <EMAIL>')
        box_number = request_data.get('sender_box', '1903')

        c.drawString(self.margin + 10*mm, y_pos - 36*mm, f"BOX: {box_number}    TEL: {phone}    FAX: {fax}")
        c.drawString(self.margin + 10*mm, y_pos - 44*mm, f"MOBILE: {mobile}")
        c.drawString(self.margin + 10*mm, y_pos - 52*mm, f"EMAIL: {email}")
    
    def draw_purpose_section(self, c, request_data):
        """رسم قسم الغرض من التحويل"""
        y_pos = self.page_height - 300*mm

        # عنوان القسم بالعربية
        c.setFont(self.arabic_font, 11)
        section_title = self.reshape_arabic_text("الغرض من التحويل: -")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, section_title)

        # الغرض بالإنجليزية
        c.setFont('Helvetica', 10)
        purpose = request_data.get('transfer_purpose', 'COST OF FOODSTUFF')
        c.drawString(self.margin + 10*mm, y_pos - 10*mm, purpose)
    
    def draw_footer_section(self, c, request_data):
        """رسم قسم التوقيع والختم"""
        y_pos = self.page_height - 330*mm

        # نص الشكر والتقدير
        c.setFont(self.arabic_font, 11)
        thanks_text = self.reshape_arabic_text("مرفق لكم صورة التحويل من البنك كمرجع")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos, thanks_text)

        thanks_text2 = self.reshape_arabic_text("وذلك بقيد التحويل على حسابنا لديكم .....")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 12*mm, thanks_text2)

        thanks_text3 = self.reshape_arabic_text("وشكراً")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 24*mm, thanks_text3)

        # التوقيع والختم
        c.setFont(self.arabic_font, 11)
        signature_text = self.reshape_arabic_text("المدير العام")
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 40*mm, signature_text)

        # اسم المدير العام
        manager_name = request_data.get('manager_name', 'نشأت رشاد قاسم الدبعي')
        signature_name = self.reshape_arabic_text(manager_name)
        c.setFont(self.arabic_font, 10)
        c.drawRightString(self.page_width - self.margin - 10*mm, y_pos - 52*mm, signature_name)

        # مكان للختم
        c.setFont('Helvetica', 8)
        c.drawString(self.margin + 10*mm, y_pos - 40*mm, "STAMP")
        c.rect(self.margin + 10*mm, y_pos - 60*mm, 40*mm, 25*mm)  # مربع للختم
    
    def generate_pdf(self, request_data, output_path=None):
        """إنشاء ملف PDF للحوالة"""
        if not output_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f"remittance_request_{timestamp}.pdf"
        
        # إنشاء ملف PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        
        # رسم أقسام النموذج
        self.draw_header(c, request_data)
        self.draw_title_section(c, request_data)
        self.draw_greeting_section(c, request_data)
        self.draw_beneficiary_section(c, request_data)
        self.draw_bank_section(c, request_data)
        self.draw_sender_section(c, request_data)
        self.draw_purpose_section(c, request_data)
        self.draw_footer_section(c, request_data)
        
        # رسم إطار حول الصفحة
        c.rect(self.margin/2, self.margin/2, 
               self.page_width - self.margin, 
               self.page_height - self.margin)
        
        # حفظ الملف
        c.save()
        
        return output_path
