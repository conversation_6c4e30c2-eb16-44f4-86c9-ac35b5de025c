#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لإضافة فرع
Simple Branch Test
"""

import sqlite3
from pathlib import Path

def simple_test():
    """اختبار مبسط"""
    
    print("🧪 اختبار مبسط لإضافة فرع...")
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عد الفروع قبل الإضافة
        cursor.execute("SELECT COUNT(*) FROM branches WHERE is_active = 1")
        count_before = cursor.fetchone()[0]
        print(f"📊 عدد الفروع قبل الإضافة: {count_before}")
        
        # الحصول على بنك للربط
        cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 LIMIT 1")
        bank = cursor.fetchone()
        
        if bank:
            print(f"🏦 سيتم ربط الفرع بالبنك: {bank[1]} (ID: {bank[0]})")
            
            # إضافة فرع جديد
            cursor.execute("""
                INSERT INTO branches (
                    name, code, type, parent_type, parent_id, bank_id, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                'فرع اختبار مبسط',
                'SIMPLE_TEST',
                'فرع اختبار',
                'بنك',
                bank[0],
                bank[0],
                1
            ))
            
            new_id = cursor.lastrowid
            print(f"✅ تم إضافة فرع جديد (ID: {new_id})")
            
            # عد الفروع بعد الإضافة
            cursor.execute("SELECT COUNT(*) FROM branches WHERE is_active = 1")
            count_after = cursor.fetchone()[0]
            print(f"📊 عدد الفروع بعد الإضافة: {count_after}")
            
            if count_after > count_before:
                print("🎉 الإضافة نجحت!")
            else:
                print("❌ الإضافة فشلت!")
            
            # حذف الفرع التجريبي
            cursor.execute("DELETE FROM branches WHERE id = ?", (new_id,))
            print("🗑️ تم حذف الفرع التجريبي")
            
        else:
            print("❌ لا توجد بنوك للربط!")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    simple_test()
