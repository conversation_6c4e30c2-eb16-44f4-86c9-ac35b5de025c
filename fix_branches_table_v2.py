#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جدول الفروع النسخة الثانية - بناءً على الهيكل الموجود
Fix Branches Table V2 - Based on Existing Structure
"""

import sqlite3
import os
from pathlib import Path

def fix_branches_table_v2():
    """إصلاح جدول الفروع بناءً على الهيكل الموجود"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إصلاح جدول الفروع بناءً على الهيكل الموجود...")
        
        # 1. التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(branches)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print("📋 الأعمدة الموجودة في جدول الفروع:")
        for col in column_names:
            print(f"   • {col}")
        
        # 2. إضافة الأعمدة المفقودة المطلوبة
        required_columns = {
            'type': 'TEXT DEFAULT "فرع"',
            'country': 'TEXT DEFAULT "السعودية"',
            'city': 'TEXT',
            'email': 'TEXT',
            'website': 'TEXT'
        }
        
        for column_name, column_type in required_columns.items():
            if column_name not in column_names:
                try:
                    cursor.execute(f"ALTER TABLE branches ADD COLUMN {column_name} {column_type}")
                    print(f"✅ تم إضافة عمود {column_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e).lower():
                        print(f"⚠️ خطأ في إضافة عمود {column_name}: {e}")
            else:
                print(f"ℹ️ عمود {column_name} موجود بالفعل")
        
        # 3. إضافة بيانات تجريبية للفروع
        cursor.execute("SELECT COUNT(*) FROM branches")
        branches_count = cursor.fetchone()[0]
        
        if branches_count == 0:
            print("\n📋 إضافة فروع تجريبية...")
            
            # الحصول على البنوك والصرافات الموجودة
            cursor.execute("SELECT id, name FROM banks LIMIT 2")
            banks = cursor.fetchall()
            
            cursor.execute("SELECT id, name FROM exchanges LIMIT 2")
            exchanges = cursor.fetchall()
            
            sample_branches = []
            
            # فروع البنوك
            if banks:
                for i, (bank_id, bank_name) in enumerate(banks):
                    branch_data = (
                        1,  # company_id (افتراضي)
                        f"فرع {bank_name} الرئيسي",  # name
                        f"{bank_name} Main Branch",  # name_en
                        f"BR{bank_id:03d}",  # code
                        f'شارع الملك فهد، {bank_name}',  # address
                        f'011-{1000000 + i}',  # phone
                        f'011-{2000000 + i}',  # fax
                        f'مدير فرع {bank_name}',  # manager_name
                        f'050-{1000000 + i}',  # manager_phone
                        'بنك',  # type
                        'السعودية',  # country
                        'الرياض' if i == 0 else 'جدة',  # city
                        f'info@{bank_name.replace(" ", "").lower()}.com',  # email
                        f'www.{bank_name.replace(" ", "").lower()}.com',  # website
                        'الأحد - الخميس: 8:00 - 17:00',  # working_hours
                        'خدمات مصرفية شاملة، تحويلات، قروض',  # services
                        f'الفرع الرئيسي لـ {bank_name}',  # notes
                        'bank',  # parent_type
                        bank_id,  # parent_id
                        'الوسط',  # region
                        '12345',  # postal_code
                        '08:00',  # start_time
                        '17:00',  # end_time
                        'الأحد-الخميس',  # working_days
                        1,  # cash_service
                        1,  # transfer_service
                        0,  # exchange_service
                        1,  # atm_service
                        1  # is_active
                    )
                    sample_branches.append(branch_data)
            
            # فروع الصرافات
            if exchanges:
                for i, (exchange_id, exchange_name) in enumerate(exchanges):
                    branch_data = (
                        1,  # company_id (افتراضي)
                        f"فرع {exchange_name} الرئيسي",  # name
                        f"{exchange_name} Main Branch",  # name_en
                        f"EX{exchange_id:03d}",  # code
                        f'شارع العروبة، {exchange_name}',  # address
                        f'013-{3000000 + i}',  # phone
                        f'013-{4000000 + i}',  # fax
                        f'مدير فرع {exchange_name}',  # manager_name
                        f'055-{2000000 + i}',  # manager_phone
                        'صرافة',  # type
                        'السعودية',  # country
                        'الدمام' if i == 0 else 'المدينة',  # city
                        f'info@{exchange_name.replace(" ", "").lower()}.com',  # email
                        f'www.{exchange_name.replace(" ", "").lower()}.com',  # website
                        'السبت - الخميس: 9:00 - 22:00',  # working_hours
                        'تحويلات نقدية، صرف عملات، خدمات سريعة',  # services
                        f'الفرع الرئيسي لـ {exchange_name}',  # notes
                        'exchange',  # parent_type
                        exchange_id,  # parent_id
                        'الشرق',  # region
                        '54321',  # postal_code
                        '09:00',  # start_time
                        '22:00',  # end_time
                        'السبت-الخميس',  # working_days
                        1,  # cash_service
                        1,  # transfer_service
                        1,  # exchange_service
                        0,  # atm_service
                        1  # is_active
                    )
                    sample_branches.append(branch_data)
            
            # إدراج البيانات
            for branch_data in sample_branches:
                cursor.execute("""
                    INSERT INTO branches (
                        company_id, name, name_en, code, address, phone, fax, manager_name, manager_phone,
                        type, country, city, email, website, working_hours, services, notes,
                        parent_type, parent_id, region, postal_code, start_time, end_time, working_days,
                        cash_service, transfer_service, exchange_service, atm_service, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, branch_data)
            
            print(f"✅ تم إضافة {len(sample_branches)} فرع تجريبي")
        else:
            print(f"ℹ️ يوجد {branches_count} فرع في النظام")
        
        # 4. التحقق النهائي من الجدول
        print("\n🔍 التحقق النهائي من جدول الفروع...")
        cursor.execute("PRAGMA table_info(branches)")
        columns = cursor.fetchall()
        print(f"📊 جدول الفروع يحتوي على {len(columns)} عمود")
        
        # التحقق من الأعمدة المطلوبة للنوافذ
        critical_columns = ['name', 'name_en', 'code', 'address', 'phone', 'fax', 'email', 'type', 'country', 'city']
        column_names = [col[1] for col in columns]
        missing_critical = [col for col in critical_columns if col not in column_names]
        
        if missing_critical:
            print(f"⚠️ أعمدة مهمة مفقودة: {missing_critical}")
        else:
            print("✅ جدول الفروع يحتوي على جميع الأعمدة المهمة")
        
        # عرض عينة من البيانات
        cursor.execute("SELECT id, name, type, city FROM branches LIMIT 3")
        sample_data = cursor.fetchall()
        if sample_data:
            print("\n📋 عينة من بيانات الفروع:")
            for row in sample_data:
                print(f"   ID: {row[0]}, الاسم: {row[1]}, النوع: {row[2]}, المدينة: {row[3]}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح جدول الفروع بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول الفروع: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_branch_insertion_v2():
    """اختبار إضافة فرع جديد بناءً على الهيكل الموجود"""
    
    print("\n🧪 اختبار إضافة فرع جديد...")
    
    db_path = Path("data/proshipment.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # بيانات فرع تجريبي
        test_branch_data = (
            1,  # company_id
            'فرع الاختبار',  # name
            'Test Branch',  # name_en
            'TEST001',  # code
            'شارع الاختبار',  # address
            '011-1234567',  # phone
            '011-1234568',  # fax
            'مدير الاختبار',  # manager_name
            '050-1234567',  # manager_phone
            'اختبار',  # type
            'السعودية',  # country
            'الرياض',  # city
            '<EMAIL>',  # email
            'www.testbranch.com',  # website
            '24/7',  # working_hours
            'خدمات اختبار',  # services
            'فرع تجريبي للاختبار',  # notes
            'test',  # parent_type
            1,  # parent_id
            'الوسط',  # region
            '12345',  # postal_code
            '08:00',  # start_time
            '17:00',  # end_time
            'يومياً',  # working_days
            1,  # cash_service
            1,  # transfer_service
            1,  # exchange_service
            1,  # atm_service
            1  # is_active
        )
        
        cursor.execute("""
            INSERT INTO branches (
                company_id, name, name_en, code, address, phone, fax, manager_name, manager_phone,
                type, country, city, email, website, working_hours, services, notes,
                parent_type, parent_id, region, postal_code, start_time, end_time, working_days,
                cash_service, transfer_service, exchange_service, atm_service, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, test_branch_data)
        
        branch_id = cursor.lastrowid
        print(f"✅ تم إضافة فرع جديد بنجاح (ID: {branch_id})")
        
        # التحقق من البيانات المضافة
        cursor.execute("SELECT name, fax, manager_name, email FROM branches WHERE id = ?", (branch_id,))
        result = cursor.fetchone()
        if result:
            print(f"   الاسم: {result[0]}")
            print(f"   الفاكس: {result[1]}")
            print(f"   المدير: {result[2]}")
            print(f"   البريد: {result[3]}")
        
        # حذف البيانات التجريبية
        cursor.execute("DELETE FROM branches WHERE code = 'TEST001'")
        print("✅ تم حذف البيانات التجريبية")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إضافة الفرع: {str(e)}")
        conn.rollback()
        conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح جدول الفروع النسخة الثانية...")
    print("=" * 60)
    
    success = fix_branches_table_v2()
    
    if success:
        # اختبار إضافة فرع جديد
        test_success = test_branch_insertion_v2()
        
        if test_success:
            print("\n✅ تم إنجاز الإصلاح والاختبار بنجاح!")
            print("📊 الآن يمكنك:")
            print("   • إضافة فروع جديدة بدون أخطاء")
            print("   • استخدام جميع الحقول المطلوبة")
            print("   • ربط الفروع بالبنوك والصرافات")
            print("   • حفظ معلومات الفاكس والبريد الإلكتروني")
        else:
            print("\n⚠️ تم الإصلاح ولكن فشل الاختبار")
    else:
        print("\n❌ فشل في إنجاز الإصلاح!")
    
    print("=" * 60)
