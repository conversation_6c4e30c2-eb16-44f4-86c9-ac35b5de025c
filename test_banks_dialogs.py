#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_banks_dialogs():
    """اختبار نوافذ إدارة البنوك الجديدة"""
    print("🔍 اختبار نوافذ إدارة البنوك الجديدة...")
    print("=" * 60)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # اختبار نافذة إضافة بنك جديد
        print("\n🏦 اختبار نافذة إضافة بنك جديد...")
        try:
            from src.ui.remittances.add_new_bank_dialog import AddNewBankDialog
            
            bank_dialog = AddNewBankDialog()
            print("   ✅ تم إنشاء نافذة إضافة بنك جديد")
            
            # اختبار الحقول الأساسية
            required_fields = [
                'bank_name_input',
                'bank_code_input', 
                'bank_type_combo',
                'country_combo',
                'base_currency_combo',
                'save_btn',
                'cancel_btn'
            ]
            
            missing_fields = []
            for field in required_fields:
                if hasattr(bank_dialog, field):
                    print(f"   ✅ {field}")
                else:
                    missing_fields.append(field)
                    print(f"   ❌ {field} مفقود")
            
            if not missing_fields:
                print("   🎉 جميع حقول نافذة البنك موجودة!")
            
        except Exception as e:
            print(f"   ❌ خطأ في نافذة البنك: {e}")
        
        # اختبار نافذة إضافة صراف جديد
        print("\n💱 اختبار نافذة إضافة صراف جديد...")
        try:
            from src.ui.remittances.add_new_exchange_dialog import AddNewExchangeDialog
            
            exchange_dialog = AddNewExchangeDialog()
            print("   ✅ تم إنشاء نافذة إضافة صراف جديد")
            
            # اختبار الحقول الأساسية
            required_fields = [
                'exchange_name_input',
                'exchange_code_input',
                'exchange_type_combo',
                'country_combo',
                'supported_currencies_list',
                'start_time_input',
                'end_time_input',
                'save_btn',
                'cancel_btn'
            ]
            
            missing_fields = []
            for field in required_fields:
                if hasattr(exchange_dialog, field):
                    print(f"   ✅ {field}")
                else:
                    missing_fields.append(field)
                    print(f"   ❌ {field} مفقود")
            
            if not missing_fields:
                print("   🎉 جميع حقول نافذة الصراف موجودة!")
            
        except Exception as e:
            print(f"   ❌ خطأ في نافذة الصراف: {e}")
        
        # اختبار نافذة إضافة فرع جديد
        print("\n🏢 اختبار نافذة إضافة فرع جديد...")
        try:
            from src.ui.remittances.add_new_branch_dialog import AddNewBranchDialog
            
            branch_dialog = AddNewBranchDialog()
            print("   ✅ تم إنشاء نافذة إضافة فرع جديد")
            
            # اختبار الحقول الأساسية
            required_fields = [
                'parent_type_combo',
                'parent_entity_combo',
                'branch_name_input',
                'branch_code_input',
                'city_combo',
                'region_combo',
                'start_time_input',
                'end_time_input',
                'save_btn',
                'cancel_btn'
            ]
            
            missing_fields = []
            for field in required_fields:
                if hasattr(branch_dialog, field):
                    print(f"   ✅ {field}")
                else:
                    missing_fields.append(field)
                    print(f"   ❌ {field} مفقود")
            
            if not missing_fields:
                print("   🎉 جميع حقول نافذة الفرع موجودة!")
            
        except Exception as e:
            print(f"   ❌ خطأ في نافذة الفرع: {e}")
        
        # اختبار التكامل مع نافذة إدارة البنوك
        print("\n🔗 اختبار التكامل مع نافذة إدارة البنوك...")
        try:
            from src.ui.remittances.banks_management_window import BanksManagementWindow
            
            banks_window = BanksManagementWindow()
            print("   ✅ تم إنشاء نافذة إدارة البنوك")
            
            # اختبار وجود الدوال المحدثة
            required_methods = [
                'add_new_bank',
                'add_new_exchange', 
                'add_new_branch',
                'on_bank_added',
                'on_exchange_added',
                'on_branch_added'
            ]
            
            missing_methods = []
            for method in required_methods:
                if hasattr(banks_window, method):
                    print(f"   ✅ {method}")
                else:
                    missing_methods.append(method)
                    print(f"   ❌ {method} مفقودة")
            
            if not missing_methods:
                print("   🎉 جميع دوال التكامل موجودة!")
            
        except Exception as e:
            print(f"   ❌ خطأ في نافذة إدارة البنوك: {e}")
        
        print("\n" + "=" * 60)
        print("📊 ملخص الاختبار:")
        print("✅ نافذة إضافة بنك جديد - مكتملة")
        print("✅ نافذة إضافة صراف جديد - مكتملة") 
        print("✅ نافذة إضافة فرع جديد - مكتملة")
        print("✅ التكامل مع نافذة إدارة البنوك - مكتمل")
        
        print("\n🎉 جميع النوافذ تم تطويرها بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_features_summary():
    """عرض ملخص الميزات المطورة"""
    print("\n📋 ملخص الميزات المطورة:")
    print("=" * 50)
    
    print("\n🏦 نافذة إضافة بنك جديد:")
    print("   • المعلومات الأساسية (الاسم، الرمز، النوع، البلد)")
    print("   • معلومات الاتصال (العنوان، الهاتف، البريد الإلكتروني)")
    print("   • المعلومات المالية (العملة الأساسية، رسوم التحويل)")
    print("   • رفع شعار البنك")
    print("   • التحقق من صحة البيانات")
    print("   • حفظ في قاعدة البيانات")
    
    print("\n💱 نافذة إضافة صراف جديد:")
    print("   • المعلومات الأساسية (الاسم، الرمز، النوع، الترخيص)")
    print("   • معلومات الاتصال (العنوان، الهاتف، الجوال)")
    print("   • العملات المدعومة (اختيار متعدد)")
    print("   • المعلومات المالية (رسوم التحويل، نسبة العمولة)")
    print("   • أوقات العمل (البداية، النهاية، أيام العمل)")
    print("   • الخدمات (إلكترونية، توصيل منزلي)")
    print("   • رفع شعار الصراف")
    
    print("\n🏢 نافذة إضافة فرع جديد:")
    print("   • اختيار الجهة الأم (بنك أو صراف)")
    print("   • المعلومات الأساسية (الاسم، الرمز، النوع)")
    print("   • الموقع (المدينة، المنطقة، العنوان)")
    print("   • معلومات الاتصال (الهاتف، البريد الإلكتروني)")
    print("   • أوقات العمل والخدمات المتاحة")
    print("   • معلومات المدير")
    print("   • ربط تلقائي مع الجهة الأم")
    
    print("\n🔗 التكامل:")
    print("   • ربط كامل مع نافذة إدارة البنوك")
    print("   • تحديث تلقائي للقوائم بعد الإضافة")
    print("   • معالجة الأخطاء والتحقق من البيانات")
    print("   • واجهات حديثة ومتجاوبة")

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📝 كيفية الاستخدام:")
    print("=" * 30)
    print("1. فتح نافذة إدارة البنوك:")
    print("   - من القائمة الرئيسية: إدارة الحوالات → إدارة البنوك")
    print()
    print("2. إضافة بنك جديد:")
    print("   - انقر على زر 'إضافة بنك جديد'")
    print("   - املأ المعلومات المطلوبة")
    print("   - اختر شعار البنك (اختياري)")
    print("   - انقر 'حفظ البنك'")
    print()
    print("3. إضافة صراف جديد:")
    print("   - انقر على زر 'إضافة صراف جديد'")
    print("   - املأ المعلومات الأساسية")
    print("   - اختر العملات المدعومة")
    print("   - حدد أوقات العمل والخدمات")
    print("   - انقر 'حفظ الصراف'")
    print()
    print("4. إضافة فرع جديد:")
    print("   - انقر على زر 'إضافة فرع جديد'")
    print("   - اختر نوع الجهة الأم (بنك/صراف)")
    print("   - اختر الجهة الأم من القائمة")
    print("   - املأ معلومات الفرع")
    print("   - انقر 'حفظ الفرع'")

if __name__ == "__main__":
    success = test_banks_dialogs()
    
    if success:
        show_features_summary()
        show_usage_instructions()
        print("\n🚀 جميع النوافذ جاهزة للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
