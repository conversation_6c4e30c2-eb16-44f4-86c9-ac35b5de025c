{"ab-testing": "0xf01c9", "abacus": "0xf16e0", "abjad-arabic": "0xf1328", "abjad-hebrew": "0xf1329", "abugida-devanagari": "0xf132a", "abugida-thai": "0xf132b", "access-point": "0xf0003", "access-point-check": "0xf1538", "access-point-minus": "0xf1539", "access-point-network": "0xf0002", "access-point-network-off": "0xf0be1", "access-point-off": "0xf1511", "access-point-plus": "0xf153a", "access-point-remove": "0xf153b", "account": "0xf0004", "account-alert": "0xf0005", "account-alert-outline": "0xf0b50", "account-arrow-left": "0xf0b51", "account-arrow-left-outline": "0xf0b52", "account-arrow-right": "0xf0b53", "account-arrow-right-outline": "0xf0b54", "account-box": "0xf0006", "account-box-multiple": "0xf0934", "account-box-multiple-outline": "0xf100a", "account-box-outline": "0xf0007", "account-cancel": "0xf12df", "account-cancel-outline": "0xf12e0", "account-cash": "0xf1097", "account-cash-outline": "0xf1098", "account-check": "0xf0008", "account-check-outline": "0xf0be2", "account-child": "0xf0a89", "account-child-circle": "0xf0a8a", "account-child-outline": "0xf10c8", "account-circle": "0xf0009", "account-circle-outline": "0xf0b55", "account-clock": "0xf0b56", "account-clock-outline": "0xf0b57", "account-cog": "0xf1370", "account-cog-outline": "0xf1371", "account-convert": "0xf000a", "account-convert-outline": "0xf1301", "account-cowboy-hat": "0xf0e9b", "account-details": "0xf0631", "account-details-outline": "0xf1372", "account-edit": "0xf06bc", "account-edit-outline": "0xf0ffb", "account-group": "0xf0849", "account-group-outline": "0xf0b58", "account-hard-hat": "0xf05b5", "account-heart": "0xf0899", "account-heart-outline": "0xf0be3", "account-key": "0xf000b", "account-key-outline": "0xf0be4", "account-lock": "0xf115e", "account-lock-outline": "0xf115f", "account-minus": "0xf000d", "account-minus-outline": "0xf0aec", "account-multiple": "0xf000e", "account-multiple-check": "0xf08c5", "account-multiple-check-outline": "0xf11fe", "account-multiple-minus": "0xf05d3", "account-multiple-minus-outline": "0xf0be5", "account-multiple-outline": "0xf000f", "account-multiple-plus": "0xf0010", "account-multiple-plus-outline": "0xf0800", "account-multiple-remove": "0xf120a", "account-multiple-remove-outline": "0xf120b", "account-music": "0xf0803", "account-music-outline": "0xf0ce9", "account-network": "0xf0011", "account-network-outline": "0xf0be6", "account-off": "0xf0012", "account-off-outline": "0xf0be7", "account-outline": "0xf0013", "account-plus": "0xf0014", "account-plus-outline": "0xf0801", "account-question": "0xf0b59", "account-question-outline": "0xf0b5a", "account-reactivate": "0xf152b", "account-reactivate-outline": "0xf152c", "account-remove": "0xf0015", "account-remove-outline": "0xf0aed", "account-search": "0xf0016", "account-search-outline": "0xf0935", "account-settings": "0xf0630", "account-settings-outline": "0xf10c9", "account-star": "0xf0017", "account-star-outline": "0xf0be8", "account-supervisor": "0xf0a8b", "account-supervisor-circle": "0xf0a8c", "account-supervisor-circle-outline": "0xf14ec", "account-supervisor-outline": "0xf112d", "account-switch": "0xf0019", "account-switch-outline": "0xf04cb", "account-tie": "0xf0ce3", "account-tie-outline": "0xf10ca", "account-tie-voice": "0xf1308", "account-tie-voice-off": "0xf130a", "account-tie-voice-off-outline": "0xf130b", "account-tie-voice-outline": "0xf1309", "account-voice": "0xf05cb", "adjust": "0xf001a", "adobe": "0xf0936", "adobe-acrobat": "0xf0f9d", "air-conditioner": "0xf001b", "air-filter": "0xf0d43", "air-horn": "0xf0dac", "air-humidifier": "0xf1099", "air-humidifier-off": "0xf1466", "air-purifier": "0xf0d44", "airbag": "0xf0be9", "airballoon": "0xf001c", "airballoon-outline": "0xf100b", "airplane": "0xf001d", "airplane-landing": "0xf05d4", "airplane-off": "0xf001e", "airplane-takeoff": "0xf05d5", "airport": "0xf084b", "alarm": "0xf0020", "alarm-bell": "0xf078e", "alarm-check": "0xf0021", "alarm-light": "0xf078f", "alarm-light-off": "0xf171e", "alarm-light-off-outline": "0xf171f", "alarm-light-outline": "0xf0bea", "alarm-multiple": "0xf0022", "alarm-note": "0xf0e71", "alarm-note-off": "0xf0e72", "alarm-off": "0xf0023", "alarm-panel": "0xf15c4", "alarm-panel-outline": "0xf15c5", "alarm-plus": "0xf0024", "alarm-snooze": "0xf068e", "album": "0xf0025", "alert": "0xf0026", "alert-box": "0xf0027", "alert-box-outline": "0xf0ce4", "alert-circle": "0xf0028", "alert-circle-check": "0xf11ed", "alert-circle-check-outline": "0xf11ee", "alert-circle-outline": "0xf05d6", "alert-decagram": "0xf06bd", "alert-decagram-outline": "0xf0ce5", "alert-minus": "0xf14bb", "alert-minus-outline": "0xf14be", "alert-octagon": "0xf0029", "alert-octagon-outline": "0xf0ce6", "alert-octagram": "0xf0767", "alert-octagram-outline": "0xf0ce7", "alert-outline": "0xf002a", "alert-plus": "0xf14ba", "alert-plus-outline": "0xf14bd", "alert-remove": "0xf14bc", "alert-remove-outline": "0xf14bf", "alert-rhombus": "0xf11ce", "alert-rhombus-outline": "0xf11cf", "alien": "0xf089a", "alien-outline": "0xf10cb", "align-horizontal-center": "0xf11c3", "align-horizontal-left": "0xf11c2", "align-horizontal-right": "0xf11c4", "align-vertical-bottom": "0xf11c5", "align-vertical-center": "0xf11c6", "align-vertical-top": "0xf11c7", "all-inclusive": "0xf06be", "allergy": "0xf1258", "alpha": "0xf002b", "alpha-a": "0xf0aee", "alpha-a-box": "0xf0b08", "alpha-a-box-outline": "0xf0beb", "alpha-a-circle": "0xf0bec", "alpha-a-circle-outline": "0xf0bed", "alpha-b": "0xf0aef", "alpha-b-box": "0xf0b09", "alpha-b-box-outline": "0xf0bee", "alpha-b-circle": "0xf0bef", "alpha-b-circle-outline": "0xf0bf0", "alpha-c": "0xf0af0", "alpha-c-box": "0xf0b0a", "alpha-c-box-outline": "0xf0bf1", "alpha-c-circle": "0xf0bf2", "alpha-c-circle-outline": "0xf0bf3", "alpha-d": "0xf0af1", "alpha-d-box": "0xf0b0b", "alpha-d-box-outline": "0xf0bf4", "alpha-d-circle": "0xf0bf5", "alpha-d-circle-outline": "0xf0bf6", "alpha-e": "0xf0af2", "alpha-e-box": "0xf0b0c", "alpha-e-box-outline": "0xf0bf7", "alpha-e-circle": "0xf0bf8", "alpha-e-circle-outline": "0xf0bf9", "alpha-f": "0xf0af3", "alpha-f-box": "0xf0b0d", "alpha-f-box-outline": "0xf0bfa", "alpha-f-circle": "0xf0bfb", "alpha-f-circle-outline": "0xf0bfc", "alpha-g": "0xf0af4", "alpha-g-box": "0xf0b0e", "alpha-g-box-outline": "0xf0bfd", "alpha-g-circle": "0xf0bfe", "alpha-g-circle-outline": "0xf0bff", "alpha-h": "0xf0af5", "alpha-h-box": "0xf0b0f", "alpha-h-box-outline": "0xf0c00", "alpha-h-circle": "0xf0c01", "alpha-h-circle-outline": "0xf0c02", "alpha-i": "0xf0af6", "alpha-i-box": "0xf0b10", "alpha-i-box-outline": "0xf0c03", "alpha-i-circle": "0xf0c04", "alpha-i-circle-outline": "0xf0c05", "alpha-j": "0xf0af7", "alpha-j-box": "0xf0b11", "alpha-j-box-outline": "0xf0c06", "alpha-j-circle": "0xf0c07", "alpha-j-circle-outline": "0xf0c08", "alpha-k": "0xf0af8", "alpha-k-box": "0xf0b12", "alpha-k-box-outline": "0xf0c09", "alpha-k-circle": "0xf0c0a", "alpha-k-circle-outline": "0xf0c0b", "alpha-l": "0xf0af9", "alpha-l-box": "0xf0b13", "alpha-l-box-outline": "0xf0c0c", "alpha-l-circle": "0xf0c0d", "alpha-l-circle-outline": "0xf0c0e", "alpha-m": "0xf0afa", "alpha-m-box": "0xf0b14", "alpha-m-box-outline": "0xf0c0f", "alpha-m-circle": "0xf0c10", "alpha-m-circle-outline": "0xf0c11", "alpha-n": "0xf0afb", "alpha-n-box": "0xf0b15", "alpha-n-box-outline": "0xf0c12", "alpha-n-circle": "0xf0c13", "alpha-n-circle-outline": "0xf0c14", "alpha-o": "0xf0afc", "alpha-o-box": "0xf0b16", "alpha-o-box-outline": "0xf0c15", "alpha-o-circle": "0xf0c16", "alpha-o-circle-outline": "0xf0c17", "alpha-p": "0xf0afd", "alpha-p-box": "0xf0b17", "alpha-p-box-outline": "0xf0c18", "alpha-p-circle": "0xf0c19", "alpha-p-circle-outline": "0xf0c1a", "alpha-q": "0xf0afe", "alpha-q-box": "0xf0b18", "alpha-q-box-outline": "0xf0c1b", "alpha-q-circle": "0xf0c1c", "alpha-q-circle-outline": "0xf0c1d", "alpha-r": "0xf0aff", "alpha-r-box": "0xf0b19", "alpha-r-box-outline": "0xf0c1e", "alpha-r-circle": "0xf0c1f", "alpha-r-circle-outline": "0xf0c20", "alpha-s": "0xf0b00", "alpha-s-box": "0xf0b1a", "alpha-s-box-outline": "0xf0c21", "alpha-s-circle": "0xf0c22", "alpha-s-circle-outline": "0xf0c23", "alpha-t": "0xf0b01", "alpha-t-box": "0xf0b1b", "alpha-t-box-outline": "0xf0c24", "alpha-t-circle": "0xf0c25", "alpha-t-circle-outline": "0xf0c26", "alpha-u": "0xf0b02", "alpha-u-box": "0xf0b1c", "alpha-u-box-outline": "0xf0c27", "alpha-u-circle": "0xf0c28", "alpha-u-circle-outline": "0xf0c29", "alpha-v": "0xf0b03", "alpha-v-box": "0xf0b1d", "alpha-v-box-outline": "0xf0c2a", "alpha-v-circle": "0xf0c2b", "alpha-v-circle-outline": "0xf0c2c", "alpha-w": "0xf0b04", "alpha-w-box": "0xf0b1e", "alpha-w-box-outline": "0xf0c2d", "alpha-w-circle": "0xf0c2e", "alpha-w-circle-outline": "0xf0c2f", "alpha-x": "0xf0b05", "alpha-x-box": "0xf0b1f", "alpha-x-box-outline": "0xf0c30", "alpha-x-circle": "0xf0c31", "alpha-x-circle-outline": "0xf0c32", "alpha-y": "0xf0b06", "alpha-y-box": "0xf0b20", "alpha-y-box-outline": "0xf0c33", "alpha-y-circle": "0xf0c34", "alpha-y-circle-outline": "0xf0c35", "alpha-z": "0xf0b07", "alpha-z-box": "0xf0b21", "alpha-z-box-outline": "0xf0c36", "alpha-z-circle": "0xf0c37", "alpha-z-circle-outline": "0xf0c38", "alphabet-aurebesh": "0xf132c", "alphabet-cyrillic": "0xf132d", "alphabet-greek": "0xf132e", "alphabet-latin": "0xf132f", "alphabet-piqad": "0xf1330", "alphabet-tengwar": "0xf1337", "alphabetical": "0xf002c", "alphabetical-off": "0xf100c", "alphabetical-variant": "0xf100d", "alphabetical-variant-off": "0xf100e", "altimeter": "0xf05d7", "amazon": "0xf002d", "amazon-alexa": "0xf08c6", "ambulance": "0xf002f", "ammunition": "0xf0ce8", "ampersand": "0xf0a8d", "amplifier": "0xf0030", "amplifier-off": "0xf11b5", "anchor": "0xf0031", "android": "0xf0032", "android-auto": "0xf0a8e", "android-debug-bridge": "0xf0033", "android-messages": "0xf0d45", "android-studio": "0xf0034", "angle-acute": "0xf0937", "angle-obtuse": "0xf0938", "angle-right": "0xf0939", "angular": "0xf06b2", "angularjs": "0xf06bf", "animation": "0xf05d8", "animation-outline": "0xf0a8f", "animation-play": "0xf093a", "animation-play-outline": "0xf0a90", "ansible": "0xf109a", "antenna": "0xf1119", "anvil": "0xf089b", "apache-kafka": "0xf100f", "api": "0xf109b", "api-off": "0xf1257", "apple": "0xf0035", "apple-airplay": "0xf001f", "apple-finder": "0xf0036", "apple-icloud": "0xf0038", "apple-ios": "0xf0037", "apple-keyboard-caps": "0xf0632", "apple-keyboard-command": "0xf0633", "apple-keyboard-control": "0xf0634", "apple-keyboard-option": "0xf0635", "apple-keyboard-shift": "0xf0636", "apple-safari": "0xf0039", "application": "0xf0614", "application-cog": "0xf1577", "application-export": "0xf0dad", "application-import": "0xf0dae", "application-settings": "0xf1555", "approximately-equal": "0xf0f9e", "approximately-equal-box": "0xf0f9f", "apps": "0xf003b", "apps-box": "0xf0d46", "arch": "0xf08c7", "archive": "0xf003c", "archive-alert": "0xf14fd", "archive-alert-outline": "0xf14fe", "archive-arrow-down": "0xf1259", "archive-arrow-down-outline": "0xf125a", "archive-arrow-up": "0xf125b", "archive-arrow-up-outline": "0xf125c", "archive-outline": "0xf120e", "arm-flex": "0xf0fd7", "arm-flex-outline": "0xf0fd6", "arrange-bring-forward": "0xf003d", "arrange-bring-to-front": "0xf003e", "arrange-send-backward": "0xf003f", "arrange-send-to-back": "0xf0040", "arrow-all": "0xf0041", "arrow-bottom-left": "0xf0042", "arrow-bottom-left-bold-outline": "0xf09b7", "arrow-bottom-left-thick": "0xf09b8", "arrow-bottom-left-thin-circle-outline": "0xf1596", "arrow-bottom-right": "0xf0043", "arrow-bottom-right-bold-outline": "0xf09b9", "arrow-bottom-right-thick": "0xf09ba", "arrow-bottom-right-thin-circle-outline": "0xf1595", "arrow-collapse": "0xf0615", "arrow-collapse-all": "0xf0044", "arrow-collapse-down": "0xf0792", "arrow-collapse-horizontal": "0xf084c", "arrow-collapse-left": "0xf0793", "arrow-collapse-right": "0xf0794", "arrow-collapse-up": "0xf0795", "arrow-collapse-vertical": "0xf084d", "arrow-decision": "0xf09bb", "arrow-decision-auto": "0xf09bc", "arrow-decision-auto-outline": "0xf09bd", "arrow-decision-outline": "0xf09be", "arrow-down": "0xf0045", "arrow-down-bold": "0xf072e", "arrow-down-bold-box": "0xf072f", "arrow-down-bold-box-outline": "0xf0730", "arrow-down-bold-circle": "0xf0047", "arrow-down-bold-circle-outline": "0xf0048", "arrow-down-bold-hexagon-outline": "0xf0049", "arrow-down-bold-outline": "0xf09bf", "arrow-down-box": "0xf06c0", "arrow-down-circle": "0xf0cdb", "arrow-down-circle-outline": "0xf0cdc", "arrow-down-drop-circle": "0xf004a", "arrow-down-drop-circle-outline": "0xf004b", "arrow-down-thick": "0xf0046", "arrow-down-thin-circle-outline": "0xf1599", "arrow-expand": "0xf0616", "arrow-expand-all": "0xf004c", "arrow-expand-down": "0xf0796", "arrow-expand-horizontal": "0xf084e", "arrow-expand-left": "0xf0797", "arrow-expand-right": "0xf0798", "arrow-expand-up": "0xf0799", "arrow-expand-vertical": "0xf084f", "arrow-horizontal-lock": "0xf115b", "arrow-left": "0xf004d", "arrow-left-bold": "0xf0731", "arrow-left-bold-box": "0xf0732", "arrow-left-bold-box-outline": "0xf0733", "arrow-left-bold-circle": "0xf004f", "arrow-left-bold-circle-outline": "0xf0050", "arrow-left-bold-hexagon-outline": "0xf0051", "arrow-left-bold-outline": "0xf09c0", "arrow-left-box": "0xf06c1", "arrow-left-circle": "0xf0cdd", "arrow-left-circle-outline": "0xf0cde", "arrow-left-drop-circle": "0xf0052", "arrow-left-drop-circle-outline": "0xf0053", "arrow-left-right": "0xf0e73", "arrow-left-right-bold": "0xf0e74", "arrow-left-right-bold-outline": "0xf09c1", "arrow-left-thick": "0xf004e", "arrow-left-thin-circle-outline": "0xf159a", "arrow-right": "0xf0054", "arrow-right-bold": "0xf0734", "arrow-right-bold-box": "0xf0735", "arrow-right-bold-box-outline": "0xf0736", "arrow-right-bold-circle": "0xf0056", "arrow-right-bold-circle-outline": "0xf0057", "arrow-right-bold-hexagon-outline": "0xf0058", "arrow-right-bold-outline": "0xf09c2", "arrow-right-box": "0xf06c2", "arrow-right-circle": "0xf0cdf", "arrow-right-circle-outline": "0xf0ce0", "arrow-right-drop-circle": "0xf0059", "arrow-right-drop-circle-outline": "0xf005a", "arrow-right-thick": "0xf0055", "arrow-right-thin-circle-outline": "0xf1598", "arrow-split-horizontal": "0xf093b", "arrow-split-vertical": "0xf093c", "arrow-top-left": "0xf005b", "arrow-top-left-bold-outline": "0xf09c3", "arrow-top-left-bottom-right": "0xf0e75", "arrow-top-left-bottom-right-bold": "0xf0e76", "arrow-top-left-thick": "0xf09c4", "arrow-top-left-thin-circle-outline": "0xf1593", "arrow-top-right": "0xf005c", "arrow-top-right-bold-outline": "0xf09c5", "arrow-top-right-bottom-left": "0xf0e77", "arrow-top-right-bottom-left-bold": "0xf0e78", "arrow-top-right-thick": "0xf09c6", "arrow-top-right-thin-circle-outline": "0xf1594", "arrow-up": "0xf005d", "arrow-up-bold": "0xf0737", "arrow-up-bold-box": "0xf0738", "arrow-up-bold-box-outline": "0xf0739", "arrow-up-bold-circle": "0xf005f", "arrow-up-bold-circle-outline": "0xf0060", "arrow-up-bold-hexagon-outline": "0xf0061", "arrow-up-bold-outline": "0xf09c7", "arrow-up-box": "0xf06c3", "arrow-up-circle": "0xf0ce1", "arrow-up-circle-outline": "0xf0ce2", "arrow-up-down": "0xf0e79", "arrow-up-down-bold": "0xf0e7a", "arrow-up-down-bold-outline": "0xf09c8", "arrow-up-drop-circle": "0xf0062", "arrow-up-drop-circle-outline": "0xf0063", "arrow-up-thick": "0xf005e", "arrow-up-thin-circle-outline": "0xf1597", "arrow-vertical-lock": "0xf115c", "artstation": "0xf0b5b", "aspect-ratio": "0xf0a24", "assistant": "0xf0064", "asterisk": "0xf06c4", "at": "0xf0065", "atlassian": "0xf0804", "atm": "0xf0d47", "atom": "0xf0768", "atom-variant": "0xf0e7b", "attachment": "0xf0066", "audio-video": "0xf093d", "audio-video-off": "0xf11b6", "augmented-reality": "0xf0850", "auto-download": "0xf137e", "auto-fix": "0xf0068", "auto-upload": "0xf0069", "autorenew": "0xf006a", "av-timer": "0xf006b", "aws": "0xf0e0f", "axe": "0xf08c8", "axis": "0xf0d48", "axis-arrow": "0xf0d49", "axis-arrow-info": "0xf140e", "axis-arrow-lock": "0xf0d4a", "axis-lock": "0xf0d4b", "axis-x-arrow": "0xf0d4c", "axis-x-arrow-lock": "0xf0d4d", "axis-x-rotate-clockwise": "0xf0d4e", "axis-x-rotate-counterclockwise": "0xf0d4f", "axis-x-y-arrow-lock": "0xf0d50", "axis-y-arrow": "0xf0d51", "axis-y-arrow-lock": "0xf0d52", "axis-y-rotate-clockwise": "0xf0d53", "axis-y-rotate-counterclockwise": "0xf0d54", "axis-z-arrow": "0xf0d55", "axis-z-arrow-lock": "0xf0d56", "axis-z-rotate-clockwise": "0xf0d57", "axis-z-rotate-counterclockwise": "0xf0d58", "babel": "0xf0a25", "baby": "0xf006c", "baby-bottle": "0xf0f39", "baby-bottle-outline": "0xf0f3a", "baby-buggy": "0xf13e0", "baby-carriage": "0xf068f", "baby-carriage-off": "0xf0fa0", "baby-face": "0xf0e7c", "baby-face-outline": "0xf0e7d", "backburger": "0xf006d", "backspace": "0xf006e", "backspace-outline": "0xf0b5c", "backspace-reverse": "0xf0e7e", "backspace-reverse-outline": "0xf0e7f", "backup-restore": "0xf006f", "bacteria": "0xf0ed5", "bacteria-outline": "0xf0ed6", "badge-account": "0xf0da7", "badge-account-alert": "0xf0da8", "badge-account-alert-outline": "0xf0da9", "badge-account-horizontal": "0xf0e0d", "badge-account-horizontal-outline": "0xf0e0e", "badge-account-outline": "0xf0daa", "badminton": "0xf0851", "bag-carry-on": "0xf0f3b", "bag-carry-on-check": "0xf0d65", "bag-carry-on-off": "0xf0f3c", "bag-checked": "0xf0f3d", "bag-personal": "0xf0e10", "bag-personal-off": "0xf0e11", "bag-personal-off-outline": "0xf0e12", "bag-personal-outline": "0xf0e13", "bag-suitcase": "0xf158b", "bag-suitcase-off": "0xf158d", "bag-suitcase-off-outline": "0xf158e", "bag-suitcase-outline": "0xf158c", "baguette": "0xf0f3e", "balloon": "0xf0a26", "ballot": "0xf09c9", "ballot-outline": "0xf09ca", "ballot-recount": "0xf0c39", "ballot-recount-outline": "0xf0c3a", "bandage": "0xf0daf", "bandcamp": "0xf0675", "bank": "0xf0070", "bank-check": "0xf1655", "bank-minus": "0xf0db0", "bank-off": "0xf1656", "bank-off-outline": "0xf1657", "bank-outline": "0xf0e80", "bank-plus": "0xf0db1", "bank-remove": "0xf0db2", "bank-transfer": "0xf0a27", "bank-transfer-in": "0xf0a28", "bank-transfer-out": "0xf0a29", "barcode": "0xf0071", "barcode-off": "0xf1236", "barcode-scan": "0xf0072", "barley": "0xf0073", "barley-off": "0xf0b5d", "barn": "0xf0b5e", "barrel": "0xf0074", "baseball": "0xf0852", "baseball-bat": "0xf0853", "baseball-diamond": "0xf15ec", "baseball-diamond-outline": "0xf15ed", "bash": "0xf1183", "basket": "0xf0076", "basket-fill": "0xf0077", "basket-minus": "0xf1523", "basket-minus-outline": "0xf1524", "basket-off": "0xf1525", "basket-off-outline": "0xf1526", "basket-outline": "0xf1181", "basket-plus": "0xf1527", "basket-plus-outline": "0xf1528", "basket-remove": "0xf1529", "basket-remove-outline": "0xf152a", "basket-unfill": "0xf0078", "basketball": "0xf0806", "basketball-hoop": "0xf0c3b", "basketball-hoop-outline": "0xf0c3c", "bat": "0xf0b5f", "battery": "0xf0079", "battery-10": "0xf007a", "battery-10-bluetooth": "0xf093e", "battery-20": "0xf007b", "battery-20-bluetooth": "0xf093f", "battery-30": "0xf007c", "battery-30-bluetooth": "0xf0940", "battery-40": "0xf007d", "battery-40-bluetooth": "0xf0941", "battery-50": "0xf007e", "battery-50-bluetooth": "0xf0942", "battery-60": "0xf007f", "battery-60-bluetooth": "0xf0943", "battery-70": "0xf0080", "battery-70-bluetooth": "0xf0944", "battery-80": "0xf0081", "battery-80-bluetooth": "0xf0945", "battery-90": "0xf0082", "battery-90-bluetooth": "0xf0946", "battery-alert": "0xf0083", "battery-alert-bluetooth": "0xf0947", "battery-alert-variant": "0xf10cc", "battery-alert-variant-outline": "0xf10cd", "battery-bluetooth": "0xf0948", "battery-bluetooth-variant": "0xf0949", "battery-charging": "0xf0084", "battery-charging-10": "0xf089c", "battery-charging-100": "0xf0085", "battery-charging-20": "0xf0086", "battery-charging-30": "0xf0087", "battery-charging-40": "0xf0088", "battery-charging-50": "0xf089d", "battery-charging-60": "0xf0089", "battery-charging-70": "0xf089e", "battery-charging-80": "0xf008a", "battery-charging-90": "0xf008b", "battery-charging-high": "0xf12a6", "battery-charging-low": "0xf12a4", "battery-charging-medium": "0xf12a5", "battery-charging-outline": "0xf089f", "battery-charging-wireless": "0xf0807", "battery-charging-wireless-10": "0xf0808", "battery-charging-wireless-20": "0xf0809", "battery-charging-wireless-30": "0xf080a", "battery-charging-wireless-40": "0xf080b", "battery-charging-wireless-50": "0xf080c", "battery-charging-wireless-60": "0xf080d", "battery-charging-wireless-70": "0xf080e", "battery-charging-wireless-80": "0xf080f", "battery-charging-wireless-90": "0xf0810", "battery-charging-wireless-alert": "0xf0811", "battery-charging-wireless-outline": "0xf0812", "battery-heart": "0xf120f", "battery-heart-outline": "0xf1210", "battery-heart-variant": "0xf1211", "battery-high": "0xf12a3", "battery-low": "0xf12a1", "battery-medium": "0xf12a2", "battery-minus": "0xf008c", "battery-negative": "0xf008d", "battery-off": "0xf125d", "battery-off-outline": "0xf125e", "battery-outline": "0xf008e", "battery-plus": "0xf008f", "battery-positive": "0xf0090", "battery-unknown": "0xf0091", "battery-unknown-bluetooth": "0xf094a", "battlenet": "0xf0b60", "beach": "0xf0092", "beaker": "0xf0cea", "beaker-alert": "0xf1229", "beaker-alert-outline": "0xf122a", "beaker-check": "0xf122b", "beaker-check-outline": "0xf122c", "beaker-minus": "0xf122d", "beaker-minus-outline": "0xf122e", "beaker-outline": "0xf0690", "beaker-plus": "0xf122f", "beaker-plus-outline": "0xf1230", "beaker-question": "0xf1231", "beaker-question-outline": "0xf1232", "beaker-remove": "0xf1233", "beaker-remove-outline": "0xf1234", "bed": "0xf02e3", "bed-double": "0xf0fd4", "bed-double-outline": "0xf0fd3", "bed-empty": "0xf08a0", "bed-king": "0xf0fd2", "bed-king-outline": "0xf0fd1", "bed-outline": "0xf0099", "bed-queen": "0xf0fd0", "bed-queen-outline": "0xf0fdb", "bed-single": "0xf106d", "bed-single-outline": "0xf106e", "bee": "0xf0fa1", "bee-flower": "0xf0fa2", "beehive-off-outline": "0xf13ed", "beehive-outline": "0xf10ce", "beekeeper": "0xf14e2", "beer": "0xf0098", "beer-outline": "0xf130c", "bell": "0xf009a", "bell-alert": "0xf0d59", "bell-alert-outline": "0xf0e81", "bell-cancel": "0xf13e7", "bell-cancel-outline": "0xf13e8", "bell-check": "0xf11e5", "bell-check-outline": "0xf11e6", "bell-circle": "0xf0d5a", "bell-circle-outline": "0xf0d5b", "bell-minus": "0xf13e9", "bell-minus-outline": "0xf13ea", "bell-off": "0xf009b", "bell-off-outline": "0xf0a91", "bell-outline": "0xf009c", "bell-plus": "0xf009d", "bell-plus-outline": "0xf0a92", "bell-remove": "0xf13eb", "bell-remove-outline": "0xf13ec", "bell-ring": "0xf009e", "bell-ring-outline": "0xf009f", "bell-sleep": "0xf00a0", "bell-sleep-outline": "0xf0a93", "beta": "0xf00a1", "betamax": "0xf09cb", "biathlon": "0xf0e14", "bicycle": "0xf109c", "bicycle-basket": "0xf1235", "bicycle-electric": "0xf15b4", "bicycle-penny-farthing": "0xf15e9", "bike": "0xf00a3", "bike-fast": "0xf111f", "billboard": "0xf1010", "billiards": "0xf0b61", "billiards-rack": "0xf0b62", "binoculars": "0xf00a5", "bio": "0xf00a6", "biohazard": "0xf00a7", "bird": "0xf15c6", "bitbucket": "0xf00a8", "bitcoin": "0xf0813", "black-mesa": "0xf00a9", "blender": "0xf0ceb", "blender-software": "0xf00ab", "blinds": "0xf00ac", "blinds-open": "0xf1011", "block-helper": "0xf00ad", "blogger": "0xf00ae", "blood-bag": "0xf0cec", "bluetooth": "0xf00af", "bluetooth-audio": "0xf00b0", "bluetooth-connect": "0xf00b1", "bluetooth-off": "0xf00b2", "bluetooth-settings": "0xf00b3", "bluetooth-transfer": "0xf00b4", "blur": "0xf00b5", "blur-linear": "0xf00b6", "blur-off": "0xf00b7", "blur-radial": "0xf00b8", "bolnisi-cross": "0xf0ced", "bolt": "0xf0db3", "bomb": "0xf0691", "bomb-off": "0xf06c5", "bone": "0xf00b9", "book": "0xf00ba", "book-account": "0xf13ad", "book-account-outline": "0xf13ae", "book-alert": "0xf167c", "book-alert-outline": "0xf167d", "book-alphabet": "0xf061d", "book-arrow-down": "0xf167e", "book-arrow-down-outline": "0xf167f", "book-arrow-left": "0xf1680", "book-arrow-left-outline": "0xf1681", "book-arrow-right": "0xf1682", "book-arrow-right-outline": "0xf1683", "book-arrow-up": "0xf1684", "book-arrow-up-outline": "0xf1685", "book-cancel": "0xf1686", "book-cancel-outline": "0xf1687", "book-check": "0xf14f3", "book-check-outline": "0xf14f4", "book-clock": "0xf1688", "book-clock-outline": "0xf1689", "book-cog": "0xf168a", "book-cog-outline": "0xf168b", "book-cross": "0xf00a2", "book-edit": "0xf168c", "book-edit-outline": "0xf168d", "book-education": "0xf16c9", "book-education-outline": "0xf16ca", "book-information-variant": "0xf106f", "book-lock": "0xf079a", "book-lock-open": "0xf079b", "book-lock-open-outline": "0xf168e", "book-lock-outline": "0xf168f", "book-marker": "0xf1690", "book-marker-outline": "0xf1691", "book-minus": "0xf05d9", "book-minus-multiple": "0xf0a94", "book-minus-multiple-outline": "0xf090b", "book-minus-outline": "0xf1692", "book-multiple": "0xf00bb", "book-multiple-outline": "0xf0436", "book-music": "0xf0067", "book-music-outline": "0xf1693", "book-off": "0xf1694", "book-off-outline": "0xf1695", "book-open": "0xf00bd", "book-open-blank-variant": "0xf00be", "book-open-outline": "0xf0b63", "book-open-page-variant": "0xf05da", "book-open-page-variant-outline": "0xf15d6", "book-open-variant": "0xf14f7", "book-outline": "0xf0b64", "book-play": "0xf0e82", "book-play-outline": "0xf0e83", "book-plus": "0xf05db", "book-plus-multiple": "0xf0a95", "book-plus-multiple-outline": "0xf0ade", "book-plus-outline": "0xf1696", "book-refresh": "0xf1697", "book-refresh-outline": "0xf1698", "book-remove": "0xf0a97", "book-remove-multiple": "0xf0a96", "book-remove-multiple-outline": "0xf04ca", "book-remove-outline": "0xf1699", "book-search": "0xf0e84", "book-search-outline": "0xf0e85", "book-settings": "0xf169a", "book-settings-outline": "0xf169b", "book-sync": "0xf169c", "book-sync-outline": "0xf16c8", "book-variant": "0xf00bf", "book-variant-multiple": "0xf00bc", "bookmark": "0xf00c0", "bookmark-check": "0xf00c1", "bookmark-check-outline": "0xf137b", "bookmark-minus": "0xf09cc", "bookmark-minus-outline": "0xf09cd", "bookmark-multiple": "0xf0e15", "bookmark-multiple-outline": "0xf0e16", "bookmark-music": "0xf00c2", "bookmark-music-outline": "0xf1379", "bookmark-off": "0xf09ce", "bookmark-off-outline": "0xf09cf", "bookmark-outline": "0xf00c3", "bookmark-plus": "0xf00c5", "bookmark-plus-outline": "0xf00c4", "bookmark-remove": "0xf00c6", "bookmark-remove-outline": "0xf137a", "bookshelf": "0xf125f", "boom-gate": "0xf0e86", "boom-gate-alert": "0xf0e87", "boom-gate-alert-outline": "0xf0e88", "boom-gate-down": "0xf0e89", "boom-gate-down-outline": "0xf0e8a", "boom-gate-outline": "0xf0e8b", "boom-gate-up": "0xf0e8c", "boom-gate-up-outline": "0xf0e8d", "boombox": "0xf05dc", "boomerang": "0xf10cf", "bootstrap": "0xf06c6", "border-all": "0xf00c7", "border-all-variant": "0xf08a1", "border-bottom": "0xf00c8", "border-bottom-variant": "0xf08a2", "border-color": "0xf00c9", "border-horizontal": "0xf00ca", "border-inside": "0xf00cb", "border-left": "0xf00cc", "border-left-variant": "0xf08a3", "border-none": "0xf00cd", "border-none-variant": "0xf08a4", "border-outside": "0xf00ce", "border-right": "0xf00cf", "border-right-variant": "0xf08a5", "border-style": "0xf00d0", "border-top": "0xf00d1", "border-top-variant": "0xf08a6", "border-vertical": "0xf00d2", "bottle-soda": "0xf1070", "bottle-soda-classic": "0xf1071", "bottle-soda-classic-outline": "0xf1363", "bottle-soda-outline": "0xf1072", "bottle-tonic": "0xf112e", "bottle-tonic-outline": "0xf112f", "bottle-tonic-plus": "0xf1130", "bottle-tonic-plus-outline": "0xf1131", "bottle-tonic-skull": "0xf1132", "bottle-tonic-skull-outline": "0xf1133", "bottle-wine": "0xf0854", "bottle-wine-outline": "0xf1310", "bow-tie": "0xf0678", "bowl": "0xf028e", "bowl-mix": "0xf0617", "bowl-mix-outline": "0xf02e4", "bowl-outline": "0xf02a9", "bowling": "0xf00d3", "box": "0xf00d4", "box-cutter": "0xf00d5", "box-cutter-off": "0xf0b4a", "box-shadow": "0xf0637", "boxing-glove": "0xf0b65", "braille": "0xf09d0", "brain": "0xf09d1", "bread-slice": "0xf0cee", "bread-slice-outline": "0xf0cef", "bridge": "0xf0618", "briefcase": "0xf00d6", "briefcase-account": "0xf0cf0", "briefcase-account-outline": "0xf0cf1", "briefcase-check": "0xf00d7", "briefcase-check-outline": "0xf131e", "briefcase-clock": "0xf10d0", "briefcase-clock-outline": "0xf10d1", "briefcase-download": "0xf00d8", "briefcase-download-outline": "0xf0c3d", "briefcase-edit": "0xf0a98", "briefcase-edit-outline": "0xf0c3e", "briefcase-minus": "0xf0a2a", "briefcase-minus-outline": "0xf0c3f", "briefcase-off": "0xf1658", "briefcase-off-outline": "0xf1659", "briefcase-outline": "0xf0814", "briefcase-plus": "0xf0a2b", "briefcase-plus-outline": "0xf0c40", "briefcase-remove": "0xf0a2c", "briefcase-remove-outline": "0xf0c41", "briefcase-search": "0xf0a2d", "briefcase-search-outline": "0xf0c42", "briefcase-upload": "0xf00d9", "briefcase-upload-outline": "0xf0c43", "briefcase-variant": "0xf1494", "briefcase-variant-off": "0xf165a", "briefcase-variant-off-outline": "0xf165b", "briefcase-variant-outline": "0xf1495", "brightness-1": "0xf00da", "brightness-2": "0xf00db", "brightness-3": "0xf00dc", "brightness-4": "0xf00dd", "brightness-5": "0xf00de", "brightness-6": "0xf00df", "brightness-7": "0xf00e0", "brightness-auto": "0xf00e1", "brightness-percent": "0xf0cf2", "broadcast": "0xf1720", "broadcast-off": "0xf1721", "broom": "0xf00e2", "brush": "0xf00e3", "bucket": "0xf1415", "bucket-outline": "0xf1416", "buddhism": "0xf094b", "buffer": "0xf0619", "buffet": "0xf0578", "bug": "0xf00e4", "bug-check": "0xf0a2e", "bug-check-outline": "0xf0a2f", "bug-outline": "0xf0a30", "bugle": "0xf0db4", "bulldozer": "0xf0b22", "bullet": "0xf0cf3", "bulletin-board": "0xf00e5", "bullhorn": "0xf00e6", "bullhorn-outline": "0xf0b23", "bullseye": "0xf05dd", "bullseye-arrow": "0xf08c9", "bulma": "0xf12e7", "bunk-bed": "0xf1302", "bunk-bed-outline": "0xf0097", "bus": "0xf00e7", "bus-alert": "0xf0a99", "bus-articulated-end": "0xf079c", "bus-articulated-front": "0xf079d", "bus-clock": "0xf08ca", "bus-double-decker": "0xf079e", "bus-marker": "0xf1212", "bus-multiple": "0xf0f3f", "bus-school": "0xf079f", "bus-side": "0xf07a0", "bus-stop": "0xf1012", "bus-stop-covered": "0xf1013", "bus-stop-uncovered": "0xf1014", "butterfly": "0xf1589", "butterfly-outline": "0xf158a", "cable-data": "0xf1394", "cached": "0xf00e8", "cactus": "0xf0db5", "cake": "0xf00e9", "cake-layered": "0xf00ea", "cake-variant": "0xf00eb", "calculator": "0xf00ec", "calculator-variant": "0xf0a9a", "calculator-variant-outline": "0xf15a6", "calendar": "0xf00ed", "calendar-account": "0xf0ed7", "calendar-account-outline": "0xf0ed8", "calendar-alert": "0xf0a31", "calendar-arrow-left": "0xf1134", "calendar-arrow-right": "0xf1135", "calendar-blank": "0xf00ee", "calendar-blank-multiple": "0xf1073", "calendar-blank-outline": "0xf0b66", "calendar-check": "0xf00ef", "calendar-check-outline": "0xf0c44", "calendar-clock": "0xf00f0", "calendar-clock-outline": "0xf16e1", "calendar-cursor": "0xf157b", "calendar-edit": "0xf08a7", "calendar-end": "0xf166c", "calendar-export": "0xf0b24", "calendar-heart": "0xf09d2", "calendar-import": "0xf0b25", "calendar-lock": "0xf1641", "calendar-lock-outline": "0xf1642", "calendar-minus": "0xf0d5c", "calendar-month": "0xf0e17", "calendar-month-outline": "0xf0e18", "calendar-multiple": "0xf00f1", "calendar-multiple-check": "0xf00f2", "calendar-multiselect": "0xf0a32", "calendar-outline": "0xf0b67", "calendar-plus": "0xf00f3", "calendar-question": "0xf0692", "calendar-range": "0xf0679", "calendar-range-outline": "0xf0b68", "calendar-refresh": "0xf01e1", "calendar-refresh-outline": "0xf0203", "calendar-remove": "0xf00f4", "calendar-remove-outline": "0xf0c45", "calendar-search": "0xf094c", "calendar-star": "0xf09d3", "calendar-start": "0xf166d", "calendar-sync": "0xf0e8e", "calendar-sync-outline": "0xf0e8f", "calendar-text": "0xf00f5", "calendar-text-outline": "0xf0c46", "calendar-today": "0xf00f6", "calendar-week": "0xf0a33", "calendar-week-begin": "0xf0a34", "calendar-weekend": "0xf0ed9", "calendar-weekend-outline": "0xf0eda", "call-made": "0xf00f7", "call-merge": "0xf00f8", "call-missed": "0xf00f9", "call-received": "0xf00fa", "call-split": "0xf00fb", "camcorder": "0xf00fc", "camcorder-off": "0xf00ff", "camera": "0xf0100", "camera-account": "0xf08cb", "camera-burst": "0xf0693", "camera-control": "0xf0b69", "camera-enhance": "0xf0101", "camera-enhance-outline": "0xf0b6a", "camera-flip": "0xf15d9", "camera-flip-outline": "0xf15da", "camera-front": "0xf0102", "camera-front-variant": "0xf0103", "camera-gopro": "0xf07a1", "camera-image": "0xf08cc", "camera-iris": "0xf0104", "camera-metering-center": "0xf07a2", "camera-metering-matrix": "0xf07a3", "camera-metering-partial": "0xf07a4", "camera-metering-spot": "0xf07a5", "camera-off": "0xf05df", "camera-outline": "0xf0d5d", "camera-party-mode": "0xf0105", "camera-plus": "0xf0edb", "camera-plus-outline": "0xf0edc", "camera-rear": "0xf0106", "camera-rear-variant": "0xf0107", "camera-retake": "0xf0e19", "camera-retake-outline": "0xf0e1a", "camera-switch": "0xf0108", "camera-switch-outline": "0xf084a", "camera-timer": "0xf0109", "camera-wireless": "0xf0db6", "camera-wireless-outline": "0xf0db7", "campfire": "0xf0edd", "cancel": "0xf073a", "candle": "0xf05e2", "candycane": "0xf010a", "cannabis": "0xf07a6", "cannabis-off": "0xf166e", "caps-lock": "0xf0a9b", "car": "0xf010b", "car-2-plus": "0xf1015", "car-3-plus": "0xf1016", "car-arrow-left": "0xf13b2", "car-arrow-right": "0xf13b3", "car-back": "0xf0e1b", "car-battery": "0xf010c", "car-brake-abs": "0xf0c47", "car-brake-alert": "0xf0c48", "car-brake-hold": "0xf0d5e", "car-brake-parking": "0xf0d5f", "car-brake-retarder": "0xf1017", "car-child-seat": "0xf0fa3", "car-clutch": "0xf1018", "car-cog": "0xf13cc", "car-connected": "0xf010d", "car-convertible": "0xf07a7", "car-coolant-level": "0xf1019", "car-cruise-control": "0xf0d60", "car-defrost-front": "0xf0d61", "car-defrost-rear": "0xf0d62", "car-door": "0xf0b6b", "car-door-lock": "0xf109d", "car-electric": "0xf0b6c", "car-electric-outline": "0xf15b5", "car-emergency": "0xf160f", "car-esp": "0xf0c49", "car-estate": "0xf07a8", "car-hatchback": "0xf07a9", "car-info": "0xf11be", "car-key": "0xf0b6d", "car-lifted-pickup": "0xf152d", "car-light-dimmed": "0xf0c4a", "car-light-fog": "0xf0c4b", "car-light-high": "0xf0c4c", "car-limousine": "0xf08cd", "car-multiple": "0xf0b6e", "car-off": "0xf0e1c", "car-outline": "0xf14ed", "car-parking-lights": "0xf0d63", "car-pickup": "0xf07aa", "car-seat": "0xf0fa4", "car-seat-cooler": "0xf0fa5", "car-seat-heater": "0xf0fa6", "car-settings": "0xf13cd", "car-shift-pattern": "0xf0f40", "car-side": "0xf07ab", "car-sports": "0xf07ac", "car-tire-alert": "0xf0c4d", "car-traction-control": "0xf0d64", "car-turbocharger": "0xf101a", "car-wash": "0xf010e", "car-windshield": "0xf101b", "car-windshield-outline": "0xf101c", "carabiner": "0xf14c0", "caravan": "0xf07ad", "card": "0xf0b6f", "card-account-details": "0xf05d2", "card-account-details-outline": "0xf0dab", "card-account-details-star": "0xf02a3", "card-account-details-star-outline": "0xf06db", "card-account-mail": "0xf018e", "card-account-mail-outline": "0xf0e98", "card-account-phone": "0xf0e99", "card-account-phone-outline": "0xf0e9a", "card-bulleted": "0xf0b70", "card-bulleted-off": "0xf0b71", "card-bulleted-off-outline": "0xf0b72", "card-bulleted-outline": "0xf0b73", "card-bulleted-settings": "0xf0b74", "card-bulleted-settings-outline": "0xf0b75", "card-minus": "0xf1600", "card-minus-outline": "0xf1601", "card-off": "0xf1602", "card-off-outline": "0xf1603", "card-outline": "0xf0b76", "card-plus": "0xf11ff", "card-plus-outline": "0xf1200", "card-remove": "0xf1604", "card-remove-outline": "0xf1605", "card-search": "0xf1074", "card-search-outline": "0xf1075", "card-text": "0xf0b77", "card-text-outline": "0xf0b78", "cards": "0xf0638", "cards-club": "0xf08ce", "cards-diamond": "0xf08cf", "cards-diamond-outline": "0xf101d", "cards-heart": "0xf08d0", "cards-outline": "0xf0639", "cards-playing-outline": "0xf063a", "cards-spade": "0xf08d1", "cards-variant": "0xf06c7", "carrot": "0xf010f", "cart": "0xf0110", "cart-arrow-down": "0xf0d66", "cart-arrow-right": "0xf0c4e", "cart-arrow-up": "0xf0d67", "cart-check": "0xf15ea", "cart-minus": "0xf0d68", "cart-off": "0xf066b", "cart-outline": "0xf0111", "cart-plus": "0xf0112", "cart-remove": "0xf0d69", "cart-variant": "0xf15eb", "case-sensitive-alt": "0xf0113", "cash": "0xf0114", "cash-100": "0xf0115", "cash-check": "0xf14ee", "cash-lock": "0xf14ea", "cash-lock-open": "0xf14eb", "cash-marker": "0xf0db8", "cash-minus": "0xf1260", "cash-multiple": "0xf0116", "cash-plus": "0xf1261", "cash-refund": "0xf0a9c", "cash-register": "0xf0cf4", "cash-remove": "0xf1262", "cash-usd": "0xf1176", "cash-usd-outline": "0xf0117", "cassette": "0xf09d4", "cast": "0xf0118", "cast-audio": "0xf101e", "cast-connected": "0xf0119", "cast-education": "0xf0e1d", "cast-off": "0xf078a", "castle": "0xf011a", "cat": "0xf011b", "cctv": "0xf07ae", "ceiling-light": "0xf0769", "cellphone": "0xf011c", "cellphone-android": "0xf011d", "cellphone-arrow-down": "0xf09d5", "cellphone-basic": "0xf011e", "cellphone-charging": "0xf1397", "cellphone-cog": "0xf0951", "cellphone-dock": "0xf011f", "cellphone-erase": "0xf094d", "cellphone-information": "0xf0f41", "cellphone-iphone": "0xf0120", "cellphone-key": "0xf094e", "cellphone-link": "0xf0121", "cellphone-link-off": "0xf0122", "cellphone-lock": "0xf094f", "cellphone-message": "0xf08d3", "cellphone-message-off": "0xf10d2", "cellphone-nfc": "0xf0e90", "cellphone-nfc-off": "0xf12d8", "cellphone-off": "0xf0950", "cellphone-play": "0xf101f", "cellphone-screenshot": "0xf0a35", "cellphone-settings": "0xf0123", "cellphone-sound": "0xf0952", "cellphone-text": "0xf08d2", "cellphone-wireless": "0xf0815", "celtic-cross": "0xf0cf5", "centos": "0xf111a", "certificate": "0xf0124", "certificate-outline": "0xf1188", "chair-rolling": "0xf0f48", "chair-school": "0xf0125", "charity": "0xf0c4f", "chart-arc": "0xf0126", "chart-areaspline": "0xf0127", "chart-areaspline-variant": "0xf0e91", "chart-bar": "0xf0128", "chart-bar-stacked": "0xf076a", "chart-bell-curve": "0xf0c50", "chart-bell-curve-cumulative": "0xf0fa7", "chart-box": "0xf154d", "chart-box-outline": "0xf154e", "chart-box-plus-outline": "0xf154f", "chart-bubble": "0xf05e3", "chart-donut": "0xf07af", "chart-donut-variant": "0xf07b0", "chart-gantt": "0xf066c", "chart-histogram": "0xf0129", "chart-line": "0xf012a", "chart-line-stacked": "0xf076b", "chart-line-variant": "0xf07b1", "chart-multiline": "0xf08d4", "chart-multiple": "0xf1213", "chart-pie": "0xf012b", "chart-ppf": "0xf1380", "chart-sankey": "0xf11df", "chart-sankey-variant": "0xf11e0", "chart-scatter-plot": "0xf0e92", "chart-scatter-plot-hexbin": "0xf066d", "chart-timeline": "0xf066e", "chart-timeline-variant": "0xf0e93", "chart-timeline-variant-shimmer": "0xf15b6", "chart-tree": "0xf0e94", "chat": "0xf0b79", "chat-alert": "0xf0b7a", "chat-alert-outline": "0xf12c9", "chat-minus": "0xf1410", "chat-minus-outline": "0xf1413", "chat-outline": "0xf0ede", "chat-plus": "0xf140f", "chat-plus-outline": "0xf1412", "chat-processing": "0xf0b7b", "chat-processing-outline": "0xf12ca", "chat-question": "0xf1738", "chat-question-outline": "0xf1739", "chat-remove": "0xf1411", "chat-remove-outline": "0xf1414", "chat-sleep": "0xf12d1", "chat-sleep-outline": "0xf12d2", "check": "0xf012c", "check-all": "0xf012d", "check-bold": "0xf0e1e", "check-box-multiple-outline": "0xf0c51", "check-box-outline": "0xf0c52", "check-circle": "0xf05e0", "check-circle-outline": "0xf05e1", "check-decagram": "0xf0791", "check-decagram-outline": "0xf1740", "check-network": "0xf0c53", "check-network-outline": "0xf0c54", "check-outline": "0xf0855", "check-underline": "0xf0e1f", "check-underline-circle": "0xf0e20", "check-underline-circle-outline": "0xf0e21", "checkbook": "0xf0a9d", "checkbox-blank": "0xf012e", "checkbox-blank-circle": "0xf012f", "checkbox-blank-circle-outline": "0xf0130", "checkbox-blank-off": "0xf12ec", "checkbox-blank-off-outline": "0xf12ed", "checkbox-blank-outline": "0xf0131", "checkbox-intermediate": "0xf0856", "checkbox-marked": "0xf0132", "checkbox-marked-circle": "0xf0133", "checkbox-marked-circle-outline": "0xf0134", "checkbox-marked-outline": "0xf0135", "checkbox-multiple-blank": "0xf0136", "checkbox-multiple-blank-circle": "0xf063b", "checkbox-multiple-blank-circle-outline": "0xf063c", "checkbox-multiple-blank-outline": "0xf0137", "checkbox-multiple-marked": "0xf0138", "checkbox-multiple-marked-circle": "0xf063d", "checkbox-multiple-marked-circle-outline": "0xf063e", "checkbox-multiple-marked-outline": "0xf0139", "checkerboard": "0xf013a", "checkerboard-minus": "0xf1202", "checkerboard-plus": "0xf1201", "checkerboard-remove": "0xf1203", "cheese": "0xf12b9", "cheese-off": "0xf13ee", "chef-hat": "0xf0b7c", "chemical-weapon": "0xf013b", "chess-bishop": "0xf085c", "chess-king": "0xf0857", "chess-knight": "0xf0858", "chess-pawn": "0xf0859", "chess-queen": "0xf085a", "chess-rook": "0xf085b", "chevron-double-down": "0xf013c", "chevron-double-left": "0xf013d", "chevron-double-right": "0xf013e", "chevron-double-up": "0xf013f", "chevron-down": "0xf0140", "chevron-down-box": "0xf09d6", "chevron-down-box-outline": "0xf09d7", "chevron-down-circle": "0xf0b26", "chevron-down-circle-outline": "0xf0b27", "chevron-left": "0xf0141", "chevron-left-box": "0xf09d8", "chevron-left-box-outline": "0xf09d9", "chevron-left-circle": "0xf0b28", "chevron-left-circle-outline": "0xf0b29", "chevron-right": "0xf0142", "chevron-right-box": "0xf09da", "chevron-right-box-outline": "0xf09db", "chevron-right-circle": "0xf0b2a", "chevron-right-circle-outline": "0xf0b2b", "chevron-triple-down": "0xf0db9", "chevron-triple-left": "0xf0dba", "chevron-triple-right": "0xf0dbb", "chevron-triple-up": "0xf0dbc", "chevron-up": "0xf0143", "chevron-up-box": "0xf09dc", "chevron-up-box-outline": "0xf09dd", "chevron-up-circle": "0xf0b2c", "chevron-up-circle-outline": "0xf0b2d", "chili-hot": "0xf07b2", "chili-medium": "0xf07b3", "chili-mild": "0xf07b4", "chili-off": "0xf1467", "chip": "0xf061a", "christianity": "0xf0953", "christianity-outline": "0xf0cf6", "church": "0xf0144", "cigar": "0xf1189", "cigar-off": "0xf141b", "circle": "0xf0765", "circle-box": "0xf15dc", "circle-box-outline": "0xf15dd", "circle-double": "0xf0e95", "circle-edit-outline": "0xf08d5", "circle-expand": "0xf0e96", "circle-half": "0xf1395", "circle-half-full": "0xf1396", "circle-medium": "0xf09de", "circle-multiple": "0xf0b38", "circle-multiple-outline": "0xf0695", "circle-off-outline": "0xf10d3", "circle-outline": "0xf0766", "circle-slice-1": "0xf0a9e", "circle-slice-2": "0xf0a9f", "circle-slice-3": "0xf0aa0", "circle-slice-4": "0xf0aa1", "circle-slice-5": "0xf0aa2", "circle-slice-6": "0xf0aa3", "circle-slice-7": "0xf0aa4", "circle-slice-8": "0xf0aa5", "circle-small": "0xf09df", "circular-saw": "0xf0e22", "city": "0xf0146", "city-variant": "0xf0a36", "city-variant-outline": "0xf0a37", "clipboard": "0xf0147", "clipboard-account": "0xf0148", "clipboard-account-outline": "0xf0c55", "clipboard-alert": "0xf0149", "clipboard-alert-outline": "0xf0cf7", "clipboard-arrow-down": "0xf014a", "clipboard-arrow-down-outline": "0xf0c56", "clipboard-arrow-left": "0xf014b", "clipboard-arrow-left-outline": "0xf0cf8", "clipboard-arrow-right": "0xf0cf9", "clipboard-arrow-right-outline": "0xf0cfa", "clipboard-arrow-up": "0xf0c57", "clipboard-arrow-up-outline": "0xf0c58", "clipboard-check": "0xf014e", "clipboard-check-multiple": "0xf1263", "clipboard-check-multiple-outline": "0xf1264", "clipboard-check-outline": "0xf08a8", "clipboard-clock": "0xf16e2", "clipboard-clock-outline": "0xf16e3", "clipboard-edit": "0xf14e5", "clipboard-edit-outline": "0xf14e6", "clipboard-file": "0xf1265", "clipboard-file-outline": "0xf1266", "clipboard-flow": "0xf06c8", "clipboard-flow-outline": "0xf1117", "clipboard-list": "0xf10d4", "clipboard-list-outline": "0xf10d5", "clipboard-minus": "0xf1618", "clipboard-minus-outline": "0xf1619", "clipboard-multiple": "0xf1267", "clipboard-multiple-outline": "0xf1268", "clipboard-off": "0xf161a", "clipboard-off-outline": "0xf161b", "clipboard-outline": "0xf014c", "clipboard-play": "0xf0c59", "clipboard-play-multiple": "0xf1269", "clipboard-play-multiple-outline": "0xf126a", "clipboard-play-outline": "0xf0c5a", "clipboard-plus": "0xf0751", "clipboard-plus-outline": "0xf131f", "clipboard-pulse": "0xf085d", "clipboard-pulse-outline": "0xf085e", "clipboard-remove": "0xf161c", "clipboard-remove-outline": "0xf161d", "clipboard-search": "0xf161e", "clipboard-search-outline": "0xf161f", "clipboard-text": "0xf014d", "clipboard-text-multiple": "0xf126b", "clipboard-text-multiple-outline": "0xf126c", "clipboard-text-off": "0xf1620", "clipboard-text-off-outline": "0xf1621", "clipboard-text-outline": "0xf0a38", "clipboard-text-play": "0xf0c5b", "clipboard-text-play-outline": "0xf0c5c", "clipboard-text-search": "0xf1622", "clipboard-text-search-outline": "0xf1623", "clippy": "0xf014f", "clock": "0xf0954", "clock-alert": "0xf0955", "clock-alert-outline": "0xf05ce", "clock-check": "0xf0fa8", "clock-check-outline": "0xf0fa9", "clock-digital": "0xf0e97", "clock-end": "0xf0151", "clock-fast": "0xf0152", "clock-in": "0xf0153", "clock-out": "0xf0154", "clock-outline": "0xf0150", "clock-start": "0xf0155", "clock-time-eight": "0xf1446", "clock-time-eight-outline": "0xf1452", "clock-time-eleven": "0xf1449", "clock-time-eleven-outline": "0xf1455", "clock-time-five": "0xf1443", "clock-time-five-outline": "0xf144f", "clock-time-four": "0xf1442", "clock-time-four-outline": "0xf144e", "clock-time-nine": "0xf1447", "clock-time-nine-outline": "0xf1453", "clock-time-one": "0xf143f", "clock-time-one-outline": "0xf144b", "clock-time-seven": "0xf1445", "clock-time-seven-outline": "0xf1451", "clock-time-six": "0xf1444", "clock-time-six-outline": "0xf1450", "clock-time-ten": "0xf1448", "clock-time-ten-outline": "0xf1454", "clock-time-three": "0xf1441", "clock-time-three-outline": "0xf144d", "clock-time-twelve": "0xf144a", "clock-time-twelve-outline": "0xf1456", "clock-time-two": "0xf1440", "clock-time-two-outline": "0xf144c", "close": "0xf0156", "close-box": "0xf0157", "close-box-multiple": "0xf0c5d", "close-box-multiple-outline": "0xf0c5e", "close-box-outline": "0xf0158", "close-circle": "0xf0159", "close-circle-multiple": "0xf062a", "close-circle-multiple-outline": "0xf0883", "close-circle-outline": "0xf015a", "close-network": "0xf015b", "close-network-outline": "0xf0c5f", "close-octagon": "0xf015c", "close-octagon-outline": "0xf015d", "close-outline": "0xf06c9", "close-thick": "0xf1398", "closed-caption": "0xf015e", "closed-caption-outline": "0xf0dbd", "cloud": "0xf015f", "cloud-alert": "0xf09e0", "cloud-braces": "0xf07b5", "cloud-check": "0xf0160", "cloud-check-outline": "0xf12cc", "cloud-circle": "0xf0161", "cloud-download": "0xf0162", "cloud-download-outline": "0xf0b7d", "cloud-lock": "0xf11f1", "cloud-lock-outline": "0xf11f2", "cloud-off-outline": "0xf0164", "cloud-outline": "0xf0163", "cloud-print": "0xf0165", "cloud-print-outline": "0xf0166", "cloud-question": "0xf0a39", "cloud-refresh": "0xf052a", "cloud-search": "0xf0956", "cloud-search-outline": "0xf0957", "cloud-sync": "0xf063f", "cloud-sync-outline": "0xf12d6", "cloud-tags": "0xf07b6", "cloud-upload": "0xf0167", "cloud-upload-outline": "0xf0b7e", "clover": "0xf0816", "coach-lamp": "0xf1020", "coat-rack": "0xf109e", "code-array": "0xf0168", "code-braces": "0xf0169", "code-braces-box": "0xf10d6", "code-brackets": "0xf016a", "code-equal": "0xf016b", "code-greater-than": "0xf016c", "code-greater-than-or-equal": "0xf016d", "json": "0xf0626", "code-json": "0xf0626", "code-less-than": "0xf016e", "code-less-than-or-equal": "0xf016f", "code-not-equal": "0xf0170", "code-not-equal-variant": "0xf0171", "code-parentheses": "0xf0172", "code-parentheses-box": "0xf10d7", "code-string": "0xf0173", "code-tags": "0xf0174", "code-tags-check": "0xf0694", "codepen": "0xf0175", "coffee": "0xf0176", "coffee-maker": "0xf109f", "coffee-off": "0xf0faa", "coffee-off-outline": "0xf0fab", "coffee-outline": "0xf06ca", "coffee-to-go": "0xf0177", "coffee-to-go-outline": "0xf130e", "coffin": "0xf0b7f", "cog": "0xf0493", "cog-box": "0xf0494", "cog-clockwise": "0xf11dd", "cog-counterclockwise": "0xf11de", "cog-off": "0xf13ce", "cog-off-outline": "0xf13cf", "cog-outline": "0xf08bb", "cog-refresh": "0xf145e", "cog-refresh-outline": "0xf145f", "cog-sync": "0xf1460", "cog-sync-outline": "0xf1461", "cog-transfer": "0xf105b", "cog-transfer-outline": "0xf105c", "cogs": "0xf08d6", "collage": "0xf0640", "collapse-all": "0xf0aa6", "collapse-all-outline": "0xf0aa7", "color-helper": "0xf0179", "comma": "0xf0e23", "comma-box": "0xf0e2b", "comma-box-outline": "0xf0e24", "comma-circle": "0xf0e25", "comma-circle-outline": "0xf0e26", "comment": "0xf017a", "comment-account": "0xf017b", "comment-account-outline": "0xf017c", "comment-alert": "0xf017d", "comment-alert-outline": "0xf017e", "comment-arrow-left": "0xf09e1", "comment-arrow-left-outline": "0xf09e2", "comment-arrow-right": "0xf09e3", "comment-arrow-right-outline": "0xf09e4", "comment-bookmark": "0xf15ae", "comment-bookmark-outline": "0xf15af", "comment-check": "0xf017f", "comment-check-outline": "0xf0180", "comment-edit": "0xf11bf", "comment-edit-outline": "0xf12c4", "comment-eye": "0xf0a3a", "comment-eye-outline": "0xf0a3b", "comment-flash": "0xf15b0", "comment-flash-outline": "0xf15b1", "comment-minus": "0xf15df", "comment-minus-outline": "0xf15e0", "comment-multiple": "0xf085f", "comment-multiple-outline": "0xf0181", "comment-off": "0xf15e1", "comment-off-outline": "0xf15e2", "comment-outline": "0xf0182", "comment-plus": "0xf09e5", "comment-plus-outline": "0xf0183", "comment-processing": "0xf0184", "comment-processing-outline": "0xf0185", "comment-question": "0xf0817", "comment-question-outline": "0xf0186", "comment-quote": "0xf1021", "comment-quote-outline": "0xf1022", "comment-remove": "0xf05de", "comment-remove-outline": "0xf0187", "comment-search": "0xf0a3c", "comment-search-outline": "0xf0a3d", "comment-text": "0xf0188", "comment-text-multiple": "0xf0860", "comment-text-multiple-outline": "0xf0861", "comment-text-outline": "0xf0189", "compare": "0xf018a", "compare-horizontal": "0xf1492", "compare-vertical": "0xf1493", "compass": "0xf018b", "compass-off": "0xf0b80", "compass-off-outline": "0xf0b81", "compass-outline": "0xf018c", "compass-rose": "0xf1382", "concourse-ci": "0xf10a0", "connection": "0xf1616", "console": "0xf018d", "console-line": "0xf07b7", "console-network": "0xf08a9", "console-network-outline": "0xf0c60", "consolidate": "0xf10d8", "contactless-payment": "0xf0d6a", "contactless-payment-circle": "0xf0321", "contactless-payment-circle-outline": "0xf0408", "contacts": "0xf06cb", "contacts-outline": "0xf05b8", "contain": "0xf0a3e", "contain-end": "0xf0a3f", "contain-start": "0xf0a40", "content-copy": "0xf018f", "content-cut": "0xf0190", "content-duplicate": "0xf0191", "content-paste": "0xf0192", "content-save": "0xf0193", "content-save-alert": "0xf0f42", "content-save-alert-outline": "0xf0f43", "content-save-all": "0xf0194", "content-save-all-outline": "0xf0f44", "content-save-cog": "0xf145b", "content-save-cog-outline": "0xf145c", "content-save-edit": "0xf0cfb", "content-save-edit-outline": "0xf0cfc", "content-save-move": "0xf0e27", "content-save-move-outline": "0xf0e28", "content-save-off": "0xf1643", "content-save-off-outline": "0xf1644", "content-save-outline": "0xf0818", "content-save-settings": "0xf061b", "content-save-settings-outline": "0xf0b2e", "contrast": "0xf0195", "contrast-box": "0xf0196", "contrast-circle": "0xf0197", "controller-classic": "0xf0b82", "controller-classic-outline": "0xf0b83", "cookie": "0xf0198", "cookie-alert": "0xf16d0", "cookie-alert-outline": "0xf16d1", "cookie-check": "0xf16d2", "cookie-check-outline": "0xf16d3", "cookie-clock": "0xf16e4", "cookie-clock-outline": "0xf16e5", "cookie-cog": "0xf16d4", "cookie-cog-outline": "0xf16d5", "cookie-edit": "0xf16e6", "cookie-edit-outline": "0xf16e7", "cookie-lock": "0xf16e8", "cookie-lock-outline": "0xf16e9", "cookie-minus": "0xf16da", "cookie-minus-outline": "0xf16db", "cookie-off": "0xf16ea", "cookie-off-outline": "0xf16eb", "cookie-outline": "0xf16de", "cookie-plus": "0xf16d6", "cookie-plus-outline": "0xf16d7", "cookie-refresh": "0xf16ec", "cookie-refresh-outline": "0xf16ed", "cookie-remove": "0xf16d8", "cookie-remove-outline": "0xf16d9", "cookie-settings": "0xf16dc", "cookie-settings-outline": "0xf16dd", "coolant-temperature": "0xf03c8", "copyright": "0xf05e6", "cordova": "0xf0958", "corn": "0xf07b8", "corn-off": "0xf13ef", "cosine-wave": "0xf1479", "counter": "0xf0199", "cow": "0xf019a", "cpu-32-bit": "0xf0edf", "cpu-64-bit": "0xf0ee0", "crane": "0xf0862", "creation": "0xf0674", "creative-commons": "0xf0d6b", "credit-card": "0xf0fef", "credit-card-check": "0xf13d0", "credit-card-check-outline": "0xf13d1", "credit-card-clock": "0xf0ee1", "credit-card-clock-outline": "0xf0ee2", "credit-card-marker": "0xf06a8", "credit-card-marker-outline": "0xf0dbe", "credit-card-minus": "0xf0fac", "credit-card-minus-outline": "0xf0fad", "credit-card-multiple": "0xf0ff0", "credit-card-multiple-outline": "0xf019c", "credit-card-off": "0xf0ff1", "credit-card-off-outline": "0xf05e4", "credit-card-outline": "0xf019b", "credit-card-plus": "0xf0ff2", "credit-card-plus-outline": "0xf0676", "credit-card-refresh": "0xf1645", "credit-card-refresh-outline": "0xf1646", "credit-card-refund": "0xf0ff3", "credit-card-refund-outline": "0xf0aa8", "credit-card-remove": "0xf0fae", "credit-card-remove-outline": "0xf0faf", "credit-card-scan": "0xf0ff4", "credit-card-scan-outline": "0xf019d", "credit-card-search": "0xf1647", "credit-card-search-outline": "0xf1648", "credit-card-settings": "0xf0ff5", "credit-card-settings-outline": "0xf08d7", "credit-card-sync": "0xf1649", "credit-card-sync-outline": "0xf164a", "credit-card-wireless": "0xf0802", "credit-card-wireless-off": "0xf057a", "credit-card-wireless-off-outline": "0xf057b", "credit-card-wireless-outline": "0xf0d6c", "cricket": "0xf0d6d", "crop": "0xf019e", "crop-free": "0xf019f", "crop-landscape": "0xf01a0", "crop-portrait": "0xf01a1", "crop-rotate": "0xf0696", "crop-square": "0xf01a2", "crosshairs": "0xf01a3", "crosshairs-gps": "0xf01a4", "crosshairs-off": "0xf0f45", "crosshairs-question": "0xf1136", "crown": "0xf01a5", "crown-outline": "0xf11d0", "cryengine": "0xf0959", "crystal-ball": "0xf0b2f", "cube": "0xf01a6", "cube-off": "0xf141c", "cube-off-outline": "0xf141d", "cube-outline": "0xf01a7", "cube-scan": "0xf0b84", "cube-send": "0xf01a8", "cube-unfolded": "0xf01a9", "cup": "0xf01aa", "cup-off": "0xf05e5", "cup-off-outline": "0xf137d", "cup-outline": "0xf130f", "cup-water": "0xf01ab", "cupboard": "0xf0f46", "cupboard-outline": "0xf0f47", "cupcake": "0xf095a", "curling": "0xf0863", "currency-bdt": "0xf0864", "currency-brl": "0xf0b85", "currency-btc": "0xf01ac", "currency-cny": "0xf07ba", "currency-eth": "0xf07bb", "currency-eur": "0xf01ad", "currency-eur-off": "0xf1315", "currency-gbp": "0xf01ae", "currency-ils": "0xf0c61", "currency-inr": "0xf01af", "currency-jpy": "0xf07bc", "currency-krw": "0xf07bd", "currency-kzt": "0xf0865", "currency-mnt": "0xf1512", "currency-ngn": "0xf01b0", "currency-php": "0xf09e6", "currency-rial": "0xf0e9c", "currency-rub": "0xf01b1", "currency-sign": "0xf07be", "currency-try": "0xf01b2", "currency-twd": "0xf07bf", "currency-usd": "0xf01c1", "currency-usd-circle": "0xf116b", "currency-usd-circle-outline": "0xf0178", "currency-usd-off": "0xf067a", "current-ac": "0xf1480", "current-dc": "0xf095c", "cursor-default": "0xf01c0", "cursor-default-click": "0xf0cfd", "cursor-default-click-outline": "0xf0cfe", "cursor-default-gesture": "0xf1127", "cursor-default-gesture-outline": "0xf1128", "cursor-default-outline": "0xf01bf", "cursor-move": "0xf01be", "cursor-pointer": "0xf01bd", "cursor-text": "0xf05e7", "dance-ballroom": "0xf15fb", "dance-pole": "0xf1578", "data-matrix": "0xf153c", "data-matrix-edit": "0xf153d", "data-matrix-minus": "0xf153e", "data-matrix-plus": "0xf153f", "data-matrix-remove": "0xf1540", "data-matrix-scan": "0xf1541", "database": "0xf01bc", "database-alert": "0xf163a", "database-alert-outline": "0xf1624", "database-arrow-down": "0xf163b", "database-arrow-down-outline": "0xf1625", "database-arrow-left": "0xf163c", "database-arrow-left-outline": "0xf1626", "database-arrow-right": "0xf163d", "database-arrow-right-outline": "0xf1627", "database-arrow-up": "0xf163e", "database-arrow-up-outline": "0xf1628", "database-check": "0xf0aa9", "database-check-outline": "0xf1629", "database-clock": "0xf163f", "database-clock-outline": "0xf162a", "database-cog": "0xf164b", "database-cog-outline": "0xf164c", "database-edit": "0xf0b86", "database-edit-outline": "0xf162b", "database-export": "0xf095e", "database-export-outline": "0xf162c", "database-import": "0xf095d", "database-import-outline": "0xf162d", "database-lock": "0xf0aaa", "database-lock-outline": "0xf162e", "database-marker": "0xf12f6", "database-marker-outline": "0xf162f", "database-minus": "0xf01bb", "database-minus-outline": "0xf1630", "database-off": "0xf1640", "database-off-outline": "0xf1631", "database-outline": "0xf1632", "database-plus": "0xf01ba", "database-plus-outline": "0xf1633", "database-refresh": "0xf05c2", "database-refresh-outline": "0xf1634", "database-remove": "0xf0d00", "database-remove-outline": "0xf1635", "database-search": "0xf0866", "database-search-outline": "0xf1636", "database-settings": "0xf0d01", "database-settings-outline": "0xf1637", "database-sync": "0xf0cff", "database-sync-outline": "0xf1638", "death-star": "0xf08d8", "death-star-variant": "0xf08d9", "deathly-hallows": "0xf0b87", "debian": "0xf08da", "debug-step-into": "0xf01b9", "debug-step-out": "0xf01b8", "debug-step-over": "0xf01b7", "decagram": "0xf076c", "decagram-outline": "0xf076d", "decimal": "0xf10a1", "decimal-comma": "0xf10a2", "decimal-comma-decrease": "0xf10a3", "decimal-comma-increase": "0xf10a4", "decimal-decrease": "0xf01b6", "decimal-increase": "0xf01b5", "delete": "0xf01b4", "delete-alert": "0xf10a5", "delete-alert-outline": "0xf10a6", "delete-circle": "0xf0683", "delete-circle-outline": "0xf0b88", "delete-clock": "0xf1556", "delete-clock-outline": "0xf1557", "delete-empty": "0xf06cc", "delete-empty-outline": "0xf0e9d", "delete-forever": "0xf05e8", "delete-forever-outline": "0xf0b89", "delete-off": "0xf10a7", "delete-off-outline": "0xf10a8", "delete-outline": "0xf09e7", "delete-restore": "0xf0819", "delete-sweep": "0xf05e9", "delete-sweep-outline": "0xf0c62", "delete-variant": "0xf01b3", "delta": "0xf01c2", "desk": "0xf1239", "desk-lamp": "0xf095f", "deskphone": "0xf01c3", "desktop-classic": "0xf07c0", "desktop-mac": "0xf01c4", "desktop-mac-dashboard": "0xf09e8", "desktop-tower": "0xf01c5", "desktop-tower-monitor": "0xf0aab", "details": "0xf01c6", "dev-to": "0xf0d6e", "developer-board": "0xf0697", "deviantart": "0xf01c7", "devices": "0xf0fb0", "diabetes": "0xf1126", "dialpad": "0xf061c", "diameter": "0xf0c63", "diameter-outline": "0xf0c64", "diameter-variant": "0xf0c65", "diamond": "0xf0b8a", "diamond-outline": "0xf0b8b", "diamond-stone": "0xf01c8", "dice-1": "0xf01ca", "dice-1-outline": "0xf114a", "dice-2": "0xf01cb", "dice-2-outline": "0xf114b", "dice-3": "0xf01cc", "dice-3-outline": "0xf114c", "dice-4": "0xf01cd", "dice-4-outline": "0xf114d", "dice-5": "0xf01ce", "dice-5-outline": "0xf114e", "dice-6": "0xf01cf", "dice-6-outline": "0xf114f", "dice-d10": "0xf1153", "dice-d10-outline": "0xf076f", "dice-d12": "0xf1154", "dice-d12-outline": "0xf0867", "dice-d20": "0xf1155", "dice-d20-outline": "0xf05ea", "dice-d4": "0xf1150", "dice-d4-outline": "0xf05eb", "dice-d6": "0xf1151", "dice-d6-outline": "0xf05ed", "dice-d8": "0xf1152", "dice-d8-outline": "0xf05ec", "dice-multiple": "0xf076e", "dice-multiple-outline": "0xf1156", "digital-ocean": "0xf1237", "dip-switch": "0xf07c1", "directions": "0xf01d0", "directions-fork": "0xf0641", "disc": "0xf05ee", "disc-alert": "0xf01d1", "disc-player": "0xf0960", "discord": "0xf066f", "dishwasher": "0xf0aac", "dishwasher-alert": "0xf11b8", "dishwasher-off": "0xf11b9", "disqus": "0xf01d2", "distribute-horizontal-center": "0xf11c9", "distribute-horizontal-left": "0xf11c8", "distribute-horizontal-right": "0xf11ca", "distribute-vertical-bottom": "0xf11cb", "distribute-vertical-center": "0xf11cc", "distribute-vertical-top": "0xf11cd", "diving-flippers": "0xf0dbf", "diving-helmet": "0xf0dc0", "diving-scuba": "0xf0dc1", "diving-scuba-flag": "0xf0dc2", "diving-scuba-tank": "0xf0dc3", "diving-scuba-tank-multiple": "0xf0dc4", "diving-snorkel": "0xf0dc5", "division": "0xf01d4", "division-box": "0xf01d5", "dlna": "0xf0a41", "dna": "0xf0684", "dns": "0xf01d6", "dns-outline": "0xf0b8c", "do-not-disturb": "0xf0698", "do-not-disturb-off": "0xf0699", "dock-bottom": "0xf10a9", "dock-left": "0xf10aa", "dock-right": "0xf10ab", "dock-top": "0xf1513", "dock-window": "0xf10ac", "docker": "0xf0868", "doctor": "0xf0a42", "dog": "0xf0a43", "dog-service": "0xf0aad", "dog-side": "0xf0a44", "dog-side-off": "0xf16ee", "dolby": "0xf06b3", "dolly": "0xf0e9e", "domain": "0xf01d7", "domain-off": "0xf0d6f", "domain-plus": "0xf10ad", "domain-remove": "0xf10ae", "dome-light": "0xf141e", "domino-mask": "0xf1023", "donkey": "0xf07c2", "door": "0xf081a", "door-closed": "0xf081b", "door-closed-lock": "0xf10af", "door-open": "0xf081c", "doorbell": "0xf12e6", "doorbell-video": "0xf0869", "dot-net": "0xf0aae", "dots-grid": "0xf15fc", "dots-hexagon": "0xf15ff", "dots-horizontal": "0xf01d8", "dots-horizontal-circle": "0xf07c3", "dots-horizontal-circle-outline": "0xf0b8d", "dots-square": "0xf15fd", "dots-triangle": "0xf15fe", "dots-vertical": "0xf01d9", "dots-vertical-circle": "0xf07c4", "dots-vertical-circle-outline": "0xf0b8e", "douban": "0xf069a", "download": "0xf01da", "download-box": "0xf1462", "download-box-outline": "0xf1463", "download-circle": "0xf1464", "download-circle-outline": "0xf1465", "download-lock": "0xf1320", "download-lock-outline": "0xf1321", "download-multiple": "0xf09e9", "download-network": "0xf06f4", "download-network-outline": "0xf0c66", "download-off": "0xf10b0", "download-off-outline": "0xf10b1", "download-outline": "0xf0b8f", "drag": "0xf01db", "drag-horizontal": "0xf01dc", "drag-horizontal-variant": "0xf12f0", "drag-variant": "0xf0b90", "drag-vertical": "0xf01dd", "drag-vertical-variant": "0xf12f1", "drama-masks": "0xf0d02", "draw": "0xf0f49", "drawing": "0xf01de", "drawing-box": "0xf01df", "dresser": "0xf0f4a", "dresser-outline": "0xf0f4b", "drone": "0xf01e2", "dropbox": "0xf01e3", "drupal": "0xf01e4", "duck": "0xf01e5", "dumbbell": "0xf01e6", "dump-truck": "0xf0c67", "ear-hearing": "0xf07c5", "ear-hearing-off": "0xf0a45", "earth": "0xf01e7", "earth-arrow-right": "0xf1311", "earth-box": "0xf06cd", "earth-box-minus": "0xf1407", "earth-box-off": "0xf06ce", "earth-box-plus": "0xf1406", "earth-box-remove": "0xf1408", "earth-minus": "0xf1404", "earth-off": "0xf01e8", "earth-plus": "0xf1403", "earth-remove": "0xf1405", "egg": "0xf0aaf", "egg-easter": "0xf0ab0", "egg-off": "0xf13f0", "egg-off-outline": "0xf13f1", "egg-outline": "0xf13f2", "eiffel-tower": "0xf156b", "eight-track": "0xf09ea", "eject": "0xf01ea", "eject-outline": "0xf0b91", "electric-switch": "0xf0e9f", "electric-switch-closed": "0xf10d9", "electron-framework": "0xf1024", "elephant": "0xf07c6", "elevation-decline": "0xf01eb", "elevation-rise": "0xf01ec", "elevator": "0xf01ed", "elevator-down": "0xf12c2", "elevator-passenger": "0xf1381", "elevator-up": "0xf12c1", "ellipse": "0xf0ea0", "ellipse-outline": "0xf0ea1", "email": "0xf01ee", "email-alert": "0xf06cf", "email-alert-outline": "0xf0d42", "email-box": "0xf0d03", "email-check": "0xf0ab1", "email-check-outline": "0xf0ab2", "email-edit": "0xf0ee3", "email-edit-outline": "0xf0ee4", "email-lock": "0xf01f1", "email-mark-as-unread": "0xf0b92", "email-minus": "0xf0ee5", "email-minus-outline": "0xf0ee6", "email-multiple": "0xf0ee7", "email-multiple-outline": "0xf0ee8", "email-newsletter": "0xf0fb1", "email-off": "0xf13e3", "email-off-outline": "0xf13e4", "email-open": "0xf01ef", "email-open-multiple": "0xf0ee9", "email-open-multiple-outline": "0xf0eea", "email-open-outline": "0xf05ef", "email-outline": "0xf01f0", "email-plus": "0xf09eb", "email-plus-outline": "0xf09ec", "email-receive": "0xf10da", "email-receive-outline": "0xf10db", "email-remove": "0xf1661", "email-remove-outline": "0xf1662", "email-search": "0xf0961", "email-search-outline": "0xf0962", "email-send": "0xf10dc", "email-send-outline": "0xf10dd", "email-sync": "0xf12c7", "email-sync-outline": "0xf12c8", "email-variant": "0xf05f0", "ember": "0xf0b30", "emby": "0xf06b4", "emoticon": "0xf0c68", "emoticon-angry": "0xf0c69", "emoticon-angry-outline": "0xf0c6a", "emoticon-confused": "0xf10de", "emoticon-confused-outline": "0xf10df", "emoticon-cool": "0xf0c6b", "emoticon-cool-outline": "0xf01f3", "emoticon-cry": "0xf0c6c", "emoticon-cry-outline": "0xf0c6d", "emoticon-dead": "0xf0c6e", "emoticon-dead-outline": "0xf069b", "emoticon-devil": "0xf0c6f", "emoticon-devil-outline": "0xf01f4", "emoticon-excited": "0xf0c70", "emoticon-excited-outline": "0xf069c", "emoticon-frown": "0xf0f4c", "emoticon-frown-outline": "0xf0f4d", "emoticon-happy": "0xf0c71", "emoticon-happy-outline": "0xf01f5", "emoticon-kiss": "0xf0c72", "emoticon-kiss-outline": "0xf0c73", "emoticon-lol": "0xf1214", "emoticon-lol-outline": "0xf1215", "emoticon-neutral": "0xf0c74", "emoticon-neutral-outline": "0xf01f6", "emoticon-outline": "0xf01f2", "emoticon-poop": "0xf01f7", "emoticon-poop-outline": "0xf0c75", "emoticon-sad": "0xf0c76", "emoticon-sad-outline": "0xf01f8", "emoticon-sick": "0xf157c", "emoticon-sick-outline": "0xf157d", "emoticon-tongue": "0xf01f9", "emoticon-tongue-outline": "0xf0c77", "emoticon-wink": "0xf0c78", "emoticon-wink-outline": "0xf0c79", "engine": "0xf01fa", "engine-off": "0xf0a46", "engine-off-outline": "0xf0a47", "engine-outline": "0xf01fb", "epsilon": "0xf10e0", "equal": "0xf01fc", "equal-box": "0xf01fd", "equalizer": "0xf0ea2", "equalizer-outline": "0xf0ea3", "eraser": "0xf01fe", "eraser-variant": "0xf0642", "escalator": "0xf01ff", "escalator-box": "0xf1399", "escalator-down": "0xf12c0", "escalator-up": "0xf12bf", "eslint": "0xf0c7a", "et": "0xf0ab3", "ethereum": "0xf086a", "ethernet": "0xf0200", "ethernet-cable": "0xf0201", "ethernet-cable-off": "0xf0202", "ev-plug-ccs1": "0xf1519", "ev-plug-ccs2": "0xf151a", "ev-plug-chademo": "0xf151b", "ev-plug-tesla": "0xf151c", "ev-plug-type1": "0xf151d", "ev-plug-type2": "0xf151e", "ev-station": "0xf05f1", "evernote": "0xf0204", "excavator": "0xf1025", "exclamation": "0xf0205", "exclamation-thick": "0xf1238", "exit-run": "0xf0a48", "exit-to-app": "0xf0206", "expand-all": "0xf0ab4", "expand-all-outline": "0xf0ab5", "expansion-card": "0xf08ae", "expansion-card-variant": "0xf0fb2", "exponent": "0xf0963", "exponent-box": "0xf0964", "export": "0xf0207", "export-variant": "0xf0b93", "eye": "0xf0208", "eye-check": "0xf0d04", "eye-check-outline": "0xf0d05", "eye-circle": "0xf0b94", "eye-circle-outline": "0xf0b95", "eye-minus": "0xf1026", "eye-minus-outline": "0xf1027", "eye-off": "0xf0209", "eye-off-outline": "0xf06d1", "eye-outline": "0xf06d0", "eye-plus": "0xf086b", "eye-plus-outline": "0xf086c", "eye-remove": "0xf15e3", "eye-remove-outline": "0xf15e4", "eye-settings": "0xf086d", "eye-settings-outline": "0xf086e", "eyedropper": "0xf020a", "eyedropper-minus": "0xf13dd", "eyedropper-off": "0xf13df", "eyedropper-plus": "0xf13dc", "eyedropper-remove": "0xf13de", "eyedropper-variant": "0xf020b", "face": "0xf0643", "face-agent": "0xf0d70", "face-mask": "0xf1586", "face-mask-outline": "0xf1587", "face-outline": "0xf0b96", "face-profile": "0xf0644", "face-profile-woman": "0xf1076", "face-recognition": "0xf0c7b", "face-shimmer": "0xf15cc", "face-shimmer-outline": "0xf15cd", "face-woman": "0xf1077", "face-woman-outline": "0xf1078", "face-woman-shimmer": "0xf15ce", "face-woman-shimmer-outline": "0xf15cf", "facebook": "0xf020c", "facebook-gaming": "0xf07dd", "facebook-messenger": "0xf020e", "facebook-workplace": "0xf0b31", "factory": "0xf020f", "family-tree": "0xf160e", "fan": "0xf0210", "fan-alert": "0xf146c", "fan-auto": "0xf171d", "fan-chevron-down": "0xf146d", "fan-chevron-up": "0xf146e", "fan-minus": "0xf1470", "fan-off": "0xf081d", "fan-plus": "0xf146f", "fan-remove": "0xf1471", "fan-speed-1": "0xf1472", "fan-speed-2": "0xf1473", "fan-speed-3": "0xf1474", "fast-forward": "0xf0211", "fast-forward-10": "0xf0d71", "fast-forward-30": "0xf0d06", "fast-forward-5": "0xf11f8", "fast-forward-60": "0xf160b", "fast-forward-outline": "0xf06d2", "fax": "0xf0212", "feather": "0xf06d3", "feature-search": "0xf0a49", "feature-search-outline": "0xf0a4a", "fedora": "0xf08db", "fencing": "0xf14c1", "ferris-wheel": "0xf0ea4", "ferry": "0xf0213", "file": "0xf0214", "file-account": "0xf073b", "file-account-outline": "0xf1028", "file-alert": "0xf0a4b", "file-alert-outline": "0xf0a4c", "file-cabinet": "0xf0ab6", "file-cad": "0xf0eeb", "file-cad-box": "0xf0eec", "file-cancel": "0xf0dc6", "file-cancel-outline": "0xf0dc7", "file-certificate": "0xf1186", "file-certificate-outline": "0xf1187", "file-chart": "0xf0215", "file-chart-outline": "0xf1029", "file-check": "0xf0216", "file-check-outline": "0xf0e29", "file-clock": "0xf12e1", "file-clock-outline": "0xf12e2", "file-cloud": "0xf0217", "file-cloud-outline": "0xf102a", "file-code": "0xf022e", "file-code-outline": "0xf102b", "file-cog": "0xf107b", "file-cog-outline": "0xf107c", "file-compare": "0xf08aa", "file-delimited": "0xf0218", "file-delimited-outline": "0xf0ea5", "file-document": "0xf0219", "file-document-edit": "0xf0dc8", "file-document-edit-outline": "0xf0dc9", "file-document-multiple": "0xf1517", "file-document-multiple-outline": "0xf1518", "file-document-outline": "0xf09ee", "file-download": "0xf0965", "file-download-outline": "0xf0966", "file-edit": "0xf11e7", "file-edit-outline": "0xf11e8", "file-excel": "0xf021b", "file-excel-box": "0xf021c", "file-excel-box-outline": "0xf102c", "file-excel-outline": "0xf102d", "file-export": "0xf021d", "file-export-outline": "0xf102e", "file-eye": "0xf0dca", "file-eye-outline": "0xf0dcb", "file-find": "0xf021e", "file-find-outline": "0xf0b97", "file-hidden": "0xf0613", "file-image": "0xf021f", "file-image-outline": "0xf0eb0", "file-import": "0xf0220", "file-import-outline": "0xf102f", "file-key": "0xf1184", "file-key-outline": "0xf1185", "file-link": "0xf1177", "file-link-outline": "0xf1178", "file-lock": "0xf0221", "file-lock-outline": "0xf1030", "file-move": "0xf0ab9", "file-move-outline": "0xf1031", "file-multiple": "0xf0222", "file-multiple-outline": "0xf1032", "file-music": "0xf0223", "file-music-outline": "0xf0e2a", "file-outline": "0xf0224", "file-pdf": "0xf0225", "file-pdf-box": "0xf0226", "file-pdf-box-outline": "0xf0fb3", "file-pdf-outline": "0xf0e2d", "file-percent": "0xf081e", "file-percent-outline": "0xf1033", "file-phone": "0xf1179", "file-phone-outline": "0xf117a", "file-plus": "0xf0752", "file-plus-outline": "0xf0eed", "file-powerpoint": "0xf0227", "file-powerpoint-box": "0xf0228", "file-powerpoint-box-outline": "0xf1034", "file-powerpoint-outline": "0xf1035", "file-presentation-box": "0xf0229", "file-question": "0xf086f", "file-question-outline": "0xf1036", "file-refresh": "0xf0918", "file-refresh-outline": "0xf0541", "file-remove": "0xf0b98", "file-remove-outline": "0xf1037", "file-replace": "0xf0b32", "file-replace-outline": "0xf0b33", "file-restore": "0xf0670", "file-restore-outline": "0xf1038", "file-search": "0xf0c7c", "file-search-outline": "0xf0c7d", "file-send": "0xf022a", "file-send-outline": "0xf1039", "file-settings": "0xf1079", "file-settings-outline": "0xf107a", "file-star": "0xf103a", "file-star-outline": "0xf103b", "file-swap": "0xf0fb4", "file-swap-outline": "0xf0fb5", "file-sync": "0xf1216", "file-sync-outline": "0xf1217", "file-table": "0xf0c7e", "file-table-box": "0xf10e1", "file-table-box-multiple": "0xf10e2", "file-table-box-multiple-outline": "0xf10e3", "file-table-box-outline": "0xf10e4", "file-table-outline": "0xf0c7f", "file-tree": "0xf0645", "file-tree-outline": "0xf13d2", "file-undo": "0xf08dc", "file-undo-outline": "0xf103c", "file-upload": "0xf0a4d", "file-upload-outline": "0xf0a4e", "file-video": "0xf022b", "file-video-outline": "0xf0e2c", "file-word": "0xf022c", "file-word-box": "0xf022d", "file-word-box-outline": "0xf103d", "file-word-outline": "0xf103e", "film": "0xf022f", "filmstrip": "0xf0230", "filmstrip-box": "0xf0332", "filmstrip-box-multiple": "0xf0d18", "filmstrip-off": "0xf0231", "filter": "0xf0232", "filter-menu": "0xf10e5", "filter-menu-outline": "0xf10e6", "filter-minus": "0xf0eee", "filter-minus-outline": "0xf0eef", "filter-off": "0xf14ef", "filter-off-outline": "0xf14f0", "filter-outline": "0xf0233", "filter-plus": "0xf0ef0", "filter-plus-outline": "0xf0ef1", "filter-remove": "0xf0234", "filter-remove-outline": "0xf0235", "filter-variant": "0xf0236", "filter-variant-minus": "0xf1112", "filter-variant-plus": "0xf1113", "filter-variant-remove": "0xf103f", "finance": "0xf081f", "find-replace": "0xf06d4", "fingerprint": "0xf0237", "fingerprint-off": "0xf0eb1", "fire": "0xf0238", "fire-alert": "0xf15d7", "fire-extinguisher": "0xf0ef2", "fire-hydrant": "0xf1137", "fire-hydrant-alert": "0xf1138", "fire-hydrant-off": "0xf1139", "fire-off": "0xf1722", "fire-truck": "0xf08ab", "firebase": "0xf0967", "firefox": "0xf0239", "fireplace": "0xf0e2e", "fireplace-off": "0xf0e2f", "firework": "0xf0e30", "firework-off": "0xf1723", "fish": "0xf023a", "fish-off": "0xf13f3", "fishbowl": "0xf0ef3", "fishbowl-outline": "0xf0ef4", "fit-to-page": "0xf0ef5", "fit-to-page-outline": "0xf0ef6", "flag": "0xf023b", "flag-checkered": "0xf023c", "flag-minus": "0xf0b99", "flag-minus-outline": "0xf10b2", "flag-outline": "0xf023d", "flag-plus": "0xf0b9a", "flag-plus-outline": "0xf10b3", "flag-remove": "0xf0b9b", "flag-remove-outline": "0xf10b4", "flag-triangle": "0xf023f", "flag-variant": "0xf0240", "flag-variant-outline": "0xf023e", "flare": "0xf0d72", "flash": "0xf0241", "flash-alert": "0xf0ef7", "flash-alert-outline": "0xf0ef8", "flash-auto": "0xf0242", "flash-circle": "0xf0820", "flash-off": "0xf0243", "flash-outline": "0xf06d5", "flash-red-eye": "0xf067b", "flashlight": "0xf0244", "flashlight-off": "0xf0245", "flask": "0xf0093", "flask-empty": "0xf0094", "flask-empty-minus": "0xf123a", "flask-empty-minus-outline": "0xf123b", "flask-empty-off": "0xf13f4", "flask-empty-off-outline": "0xf13f5", "flask-empty-outline": "0xf0095", "flask-empty-plus": "0xf123c", "flask-empty-plus-outline": "0xf123d", "flask-empty-remove": "0xf123e", "flask-empty-remove-outline": "0xf123f", "flask-minus": "0xf1240", "flask-minus-outline": "0xf1241", "flask-off": "0xf13f6", "flask-off-outline": "0xf13f7", "flask-outline": "0xf0096", "flask-plus": "0xf1242", "flask-plus-outline": "0xf1243", "flask-remove": "0xf1244", "flask-remove-outline": "0xf1245", "flask-round-bottom": "0xf124b", "flask-round-bottom-empty": "0xf124c", "flask-round-bottom-empty-outline": "0xf124d", "flask-round-bottom-outline": "0xf124e", "fleur-de-lis": "0xf1303", "flip-horizontal": "0xf10e7", "flip-to-back": "0xf0247", "flip-to-front": "0xf0248", "flip-vertical": "0xf10e8", "floor-lamp": "0xf08dd", "floor-lamp-dual": "0xf1040", "floor-lamp-variant": "0xf1041", "floor-plan": "0xf0821", "floppy": "0xf0249", "floppy-variant": "0xf09ef", "flower": "0xf024a", "flower-outline": "0xf09f0", "flower-poppy": "0xf0d08", "flower-tulip": "0xf09f1", "flower-tulip-outline": "0xf09f2", "focus-auto": "0xf0f4e", "focus-field": "0xf0f4f", "focus-field-horizontal": "0xf0f50", "focus-field-vertical": "0xf0f51", "folder": "0xf024b", "folder-account": "0xf024c", "folder-account-outline": "0xf0b9c", "folder-alert": "0xf0dcc", "folder-alert-outline": "0xf0dcd", "folder-clock": "0xf0aba", "folder-clock-outline": "0xf0abb", "folder-cog": "0xf107f", "folder-cog-outline": "0xf1080", "folder-download": "0xf024d", "folder-download-outline": "0xf10e9", "folder-edit": "0xf08de", "folder-edit-outline": "0xf0dce", "folder-google-drive": "0xf024e", "folder-heart": "0xf10ea", "folder-heart-outline": "0xf10eb", "folder-home": "0xf10b5", "folder-home-outline": "0xf10b6", "folder-image": "0xf024f", "folder-information": "0xf10b7", "folder-information-outline": "0xf10b8", "folder-key": "0xf08ac", "folder-key-network": "0xf08ad", "folder-key-network-outline": "0xf0c80", "folder-key-outline": "0xf10ec", "folder-lock": "0xf0250", "folder-lock-open": "0xf0251", "folder-marker": "0xf126d", "folder-marker-outline": "0xf126e", "folder-move": "0xf0252", "folder-move-outline": "0xf1246", "folder-multiple": "0xf0253", "folder-multiple-image": "0xf0254", "folder-multiple-outline": "0xf0255", "folder-multiple-plus": "0xf147e", "folder-multiple-plus-outline": "0xf147f", "folder-music": "0xf1359", "folder-music-outline": "0xf135a", "folder-network": "0xf0870", "folder-network-outline": "0xf0c81", "folder-open": "0xf0770", "folder-open-outline": "0xf0dcf", "folder-outline": "0xf0256", "folder-plus": "0xf0257", "folder-plus-outline": "0xf0b9d", "folder-pound": "0xf0d09", "folder-pound-outline": "0xf0d0a", "folder-refresh": "0xf0749", "folder-refresh-outline": "0xf0542", "folder-remove": "0xf0258", "folder-remove-outline": "0xf0b9e", "folder-search": "0xf0968", "folder-search-outline": "0xf0969", "folder-settings": "0xf107d", "folder-settings-outline": "0xf107e", "folder-star": "0xf069d", "folder-star-multiple": "0xf13d3", "folder-star-multiple-outline": "0xf13d4", "folder-star-outline": "0xf0b9f", "folder-swap": "0xf0fb6", "folder-swap-outline": "0xf0fb7", "folder-sync": "0xf0d0b", "folder-sync-outline": "0xf0d0c", "folder-table": "0xf12e3", "folder-table-outline": "0xf12e4", "folder-text": "0xf0c82", "folder-text-outline": "0xf0c83", "folder-upload": "0xf0259", "folder-upload-outline": "0xf10ed", "folder-zip": "0xf06eb", "folder-zip-outline": "0xf07b9", "font-awesome": "0xf003a", "food": "0xf025a", "food-apple": "0xf025b", "food-apple-outline": "0xf0c84", "food-croissant": "0xf07c8", "food-drumstick": "0xf141f", "food-drumstick-off": "0xf1468", "food-drumstick-off-outline": "0xf1469", "food-drumstick-outline": "0xf1420", "food-fork-drink": "0xf05f2", "food-halal": "0xf1572", "food-kosher": "0xf1573", "food-off": "0xf05f3", "food-steak": "0xf146a", "food-steak-off": "0xf146b", "food-turkey": "0xf171c", "food-variant": "0xf025c", "food-variant-off": "0xf13e5", "foot-print": "0xf0f52", "football": "0xf025d", "football-australian": "0xf025e", "football-helmet": "0xf025f", "forklift": "0xf07c9", "form-dropdown": "0xf1400", "form-select": "0xf1401", "form-textarea": "0xf1095", "form-textbox": "0xf060e", "form-textbox-lock": "0xf135d", "form-textbox-password": "0xf07f5", "format-align-bottom": "0xf0753", "format-align-center": "0xf0260", "format-align-justify": "0xf0261", "format-align-left": "0xf0262", "format-align-middle": "0xf0754", "format-align-right": "0xf0263", "format-align-top": "0xf0755", "format-annotation-minus": "0xf0abc", "format-annotation-plus": "0xf0646", "format-bold": "0xf0264", "format-clear": "0xf0265", "format-color-fill": "0xf0266", "format-color-highlight": "0xf0e31", "format-color-marker-cancel": "0xf1313", "format-color-text": "0xf069e", "format-columns": "0xf08df", "format-float-center": "0xf0267", "format-float-left": "0xf0268", "format-float-none": "0xf0269", "format-float-right": "0xf026a", "format-font": "0xf06d6", "format-font-size-decrease": "0xf09f3", "format-font-size-increase": "0xf09f4", "format-header-1": "0xf026b", "format-header-2": "0xf026c", "format-header-3": "0xf026d", "format-header-4": "0xf026e", "format-header-5": "0xf026f", "format-header-6": "0xf0270", "format-header-decrease": "0xf0271", "format-header-equal": "0xf0272", "format-header-increase": "0xf0273", "format-header-pound": "0xf0274", "format-horizontal-align-center": "0xf061e", "format-horizontal-align-left": "0xf061f", "format-horizontal-align-right": "0xf0620", "format-indent-decrease": "0xf0275", "format-indent-increase": "0xf0276", "format-italic": "0xf0277", "format-letter-case": "0xf0b34", "format-letter-case-lower": "0xf0b35", "format-letter-case-upper": "0xf0b36", "format-letter-ends-with": "0xf0fb8", "format-letter-matches": "0xf0fb9", "format-letter-starts-with": "0xf0fba", "format-line-spacing": "0xf0278", "format-line-style": "0xf05c8", "format-line-weight": "0xf05c9", "format-list-bulleted": "0xf0279", "format-list-bulleted-square": "0xf0dd0", "format-list-bulleted-triangle": "0xf0eb2", "format-list-bulleted-type": "0xf027a", "format-list-checkbox": "0xf096a", "format-list-checks": "0xf0756", "format-list-numbered": "0xf027b", "format-list-numbered-rtl": "0xf0d0d", "format-list-text": "0xf126f", "format-overline": "0xf0eb3", "format-page-break": "0xf06d7", "format-paint": "0xf027c", "format-paragraph": "0xf027d", "format-pilcrow": "0xf06d8", "format-quote-close": "0xf027e", "format-quote-close-outline": "0xf11a8", "format-quote-open": "0xf0757", "format-quote-open-outline": "0xf11a7", "format-rotate-90": "0xf06aa", "format-section": "0xf069f", "format-size": "0xf027f", "format-strikethrough": "0xf0280", "format-strikethrough-variant": "0xf0281", "format-subscript": "0xf0282", "format-superscript": "0xf0283", "format-text": "0xf0284", "format-text-rotation-angle-down": "0xf0fbb", "format-text-rotation-angle-up": "0xf0fbc", "format-text-rotation-down": "0xf0d73", "format-text-rotation-down-vertical": "0xf0fbd", "format-text-rotation-none": "0xf0d74", "format-text-rotation-up": "0xf0fbe", "format-text-rotation-vertical": "0xf0fbf", "format-text-variant": "0xf0e32", "format-text-variant-outline": "0xf150f", "format-text-wrapping-clip": "0xf0d0e", "format-text-wrapping-overflow": "0xf0d0f", "format-text-wrapping-wrap": "0xf0d10", "format-textbox": "0xf0d11", "format-textdirection-l-to-r": "0xf0285", "format-textdirection-r-to-l": "0xf0286", "format-title": "0xf05f4", "format-underline": "0xf0287", "format-vertical-align-bottom": "0xf0621", "format-vertical-align-center": "0xf0622", "format-vertical-align-top": "0xf0623", "format-wrap-inline": "0xf0288", "format-wrap-square": "0xf0289", "format-wrap-tight": "0xf028a", "format-wrap-top-bottom": "0xf028b", "forum": "0xf028c", "forum-outline": "0xf0822", "forward": "0xf028d", "forwardburger": "0xf0d75", "fountain": "0xf096b", "fountain-pen": "0xf0d12", "fountain-pen-tip": "0xf0d13", "freebsd": "0xf08e0", "frequently-asked-questions": "0xf0eb4", "fridge": "0xf0290", "fridge-alert": "0xf11b1", "fridge-alert-outline": "0xf11b2", "fridge-bottom": "0xf0292", "fridge-industrial": "0xf15ee", "fridge-industrial-alert": "0xf15ef", "fridge-industrial-alert-outline": "0xf15f0", "fridge-industrial-off": "0xf15f1", "fridge-industrial-off-outline": "0xf15f2", "fridge-industrial-outline": "0xf15f3", "fridge-off": "0xf11af", "fridge-off-outline": "0xf11b0", "fridge-outline": "0xf028f", "fridge-top": "0xf0291", "fridge-variant": "0xf15f4", "fridge-variant-alert": "0xf15f5", "fridge-variant-alert-outline": "0xf15f6", "fridge-variant-off": "0xf15f7", "fridge-variant-off-outline": "0xf15f8", "fridge-variant-outline": "0xf15f9", "fruit-cherries": "0xf1042", "fruit-cherries-off": "0xf13f8", "fruit-citrus": "0xf1043", "fruit-citrus-off": "0xf13f9", "fruit-grapes": "0xf1044", "fruit-grapes-outline": "0xf1045", "fruit-pineapple": "0xf1046", "fruit-watermelon": "0xf1047", "fuel": "0xf07ca", "fullscreen": "0xf0293", "fullscreen-exit": "0xf0294", "function": "0xf0295", "function-variant": "0xf0871", "furigana-horizontal": "0xf1081", "furigana-vertical": "0xf1082", "fuse": "0xf0c85", "fuse-alert": "0xf142d", "fuse-blade": "0xf0c86", "fuse-off": "0xf142c", "gamepad": "0xf0296", "gamepad-circle": "0xf0e33", "gamepad-circle-down": "0xf0e34", "gamepad-circle-left": "0xf0e35", "gamepad-circle-outline": "0xf0e36", "gamepad-circle-right": "0xf0e37", "gamepad-circle-up": "0xf0e38", "gamepad-down": "0xf0e39", "gamepad-left": "0xf0e3a", "gamepad-right": "0xf0e3b", "gamepad-round": "0xf0e3c", "gamepad-round-down": "0xf0e3d", "gamepad-round-left": "0xf0e3e", "gamepad-round-outline": "0xf0e3f", "gamepad-round-right": "0xf0e40", "gamepad-round-up": "0xf0e41", "gamepad-square": "0xf0eb5", "gamepad-square-outline": "0xf0eb6", "gamepad-up": "0xf0e42", "gamepad-variant": "0xf0297", "gamepad-variant-outline": "0xf0eb7", "gamma": "0xf10ee", "gantry-crane": "0xf0dd1", "garage": "0xf06d9", "garage-alert": "0xf0872", "garage-alert-variant": "0xf12d5", "garage-open": "0xf06da", "garage-open-variant": "0xf12d4", "garage-variant": "0xf12d3", "gas-cylinder": "0xf0647", "gas-station": "0xf0298", "gas-station-off": "0xf1409", "gas-station-off-outline": "0xf140a", "gas-station-outline": "0xf0eb8", "gate": "0xf0299", "gate-and": "0xf08e1", "gate-arrow-right": "0xf1169", "gate-nand": "0xf08e2", "gate-nor": "0xf08e3", "gate-not": "0xf08e4", "gate-open": "0xf116a", "gate-or": "0xf08e5", "gate-xnor": "0xf08e6", "gate-xor": "0xf08e7", "gatsby": "0xf0e43", "gauge": "0xf029a", "gauge-empty": "0xf0873", "gauge-full": "0xf0874", "gauge-low": "0xf0875", "gavel": "0xf029b", "gender-female": "0xf029c", "gender-male": "0xf029d", "gender-male-female": "0xf029e", "gender-male-female-variant": "0xf113f", "gender-non-binary": "0xf1140", "gender-transgender": "0xf029f", "gentoo": "0xf08e8", "gesture": "0xf07cb", "gesture-double-tap": "0xf073c", "gesture-pinch": "0xf0abd", "gesture-spread": "0xf0abe", "gesture-swipe": "0xf0d76", "gesture-swipe-down": "0xf073d", "gesture-swipe-horizontal": "0xf0abf", "gesture-swipe-left": "0xf073e", "gesture-swipe-right": "0xf073f", "gesture-swipe-up": "0xf0740", "gesture-swipe-vertical": "0xf0ac0", "gesture-tap": "0xf0741", "gesture-tap-box": "0xf12a9", "gesture-tap-button": "0xf12a8", "gesture-tap-hold": "0xf0d77", "gesture-two-double-tap": "0xf0742", "gesture-two-tap": "0xf0743", "ghost": "0xf02a0", "ghost-off": "0xf09f5", "ghost-off-outline": "0xf165c", "ghost-outline": "0xf165d", "gif": "0xf0d78", "gift": "0xf0e44", "gift-off": "0xf16ef", "gift-off-outline": "0xf16f0", "gift-open": "0xf16f1", "gift-open-outline": "0xf16f2", "gift-outline": "0xf02a1", "git": "0xf02a2", "github": "0xf02a4", "gitlab": "0xf0ba0", "glass-cocktail": "0xf0356", "glass-cocktail-off": "0xf15e6", "glass-flute": "0xf02a5", "glass-mug": "0xf02a6", "glass-mug-off": "0xf15e7", "glass-mug-variant": "0xf1116", "glass-mug-variant-off": "0xf15e8", "glass-pint-outline": "0xf130d", "glass-stange": "0xf02a7", "glass-tulip": "0xf02a8", "glass-wine": "0xf0876", "glasses": "0xf02aa", "globe-light": "0xf12d7", "globe-model": "0xf08e9", "gmail": "0xf02ab", "gnome": "0xf02ac", "go-kart": "0xf0d79", "go-kart-track": "0xf0d7a", "gog": "0xf0ba1", "gold": "0xf124f", "golf": "0xf0823", "golf-cart": "0xf11a4", "golf-tee": "0xf1083", "gondola": "0xf0686", "goodreads": "0xf0d7b", "google": "0xf02ad", "google-ads": "0xf0c87", "google-analytics": "0xf07cc", "google-assistant": "0xf07cd", "google-cardboard": "0xf02ae", "google-chrome": "0xf02af", "google-circles": "0xf02b0", "google-circles-communities": "0xf02b1", "google-circles-extended": "0xf02b2", "google-circles-group": "0xf02b3", "google-classroom": "0xf02c0", "google-cloud": "0xf11f6", "google-controller": "0xf02b4", "google-controller-off": "0xf02b5", "google-downasaur": "0xf1362", "google-drive": "0xf02b6", "google-earth": "0xf02b7", "google-fit": "0xf096c", "google-glass": "0xf02b8", "google-hangouts": "0xf02c9", "google-home": "0xf0824", "google-keep": "0xf06dc", "google-lens": "0xf09f6", "google-maps": "0xf05f5", "google-my-business": "0xf1048", "google-nearby": "0xf02b9", "google-photos": "0xf06dd", "google-play": "0xf02bc", "google-plus": "0xf02bd", "google-podcast": "0xf0eb9", "google-spreadsheet": "0xf09f7", "google-street-view": "0xf0c88", "google-translate": "0xf02bf", "gradient": "0xf06a0", "grain": "0xf0d7c", "graph": "0xf1049", "graph-outline": "0xf104a", "graphql": "0xf0877", "grass": "0xf1510", "grave-stone": "0xf0ba2", "grease-pencil": "0xf0648", "greater-than": "0xf096d", "greater-than-or-equal": "0xf096e", "grid": "0xf02c1", "grid-large": "0xf0758", "grid-off": "0xf02c2", "grill": "0xf0e45", "grill-outline": "0xf118a", "group": "0xf02c3", "guitar-acoustic": "0xf0771", "guitar-electric": "0xf02c4", "guitar-pick": "0xf02c5", "guitar-pick-outline": "0xf02c6", "guy-fawkes-mask": "0xf0825", "hail": "0xf0ac1", "hair-dryer": "0xf10ef", "hair-dryer-outline": "0xf10f0", "halloween": "0xf0ba3", "hamburger": "0xf0685", "hammer": "0xf08ea", "hammer-screwdriver": "0xf1322", "hammer-wrench": "0xf1323", "hand": "0xf0a4f", "hand-heart": "0xf10f1", "hand-heart-outline": "0xf157e", "hand-left": "0xf0e46", "hand-okay": "0xf0a50", "hand-peace": "0xf0a51", "hand-peace-variant": "0xf0a52", "hand-pointing-down": "0xf0a53", "hand-pointing-left": "0xf0a54", "hand-pointing-right": "0xf02c7", "hand-pointing-up": "0xf0a55", "hand-right": "0xf0e47", "hand-saw": "0xf0e48", "hand-wash": "0xf157f", "hand-wash-outline": "0xf1580", "hand-water": "0xf139f", "handball": "0xf0f53", "handcuffs": "0xf113e", "handshake": "0xf1218", "handshake-outline": "0xf15a1", "hanger": "0xf02c8", "hard-hat": "0xf096f", "harddisk": "0xf02ca", "harddisk-plus": "0xf104b", "harddisk-remove": "0xf104c", "hat-fedora": "0xf0ba4", "hazard-lights": "0xf0c89", "hdr": "0xf0d7d", "hdr-off": "0xf0d7e", "head": "0xf135e", "head-alert": "0xf1338", "head-alert-outline": "0xf1339", "head-check": "0xf133a", "head-check-outline": "0xf133b", "head-cog": "0xf133c", "head-cog-outline": "0xf133d", "head-dots-horizontal": "0xf133e", "head-dots-horizontal-outline": "0xf133f", "head-flash": "0xf1340", "head-flash-outline": "0xf1341", "head-heart": "0xf1342", "head-heart-outline": "0xf1343", "head-lightbulb": "0xf1344", "head-lightbulb-outline": "0xf1345", "head-minus": "0xf1346", "head-minus-outline": "0xf1347", "head-outline": "0xf135f", "head-plus": "0xf1348", "head-plus-outline": "0xf1349", "head-question": "0xf134a", "head-question-outline": "0xf134b", "head-remove": "0xf134c", "head-remove-outline": "0xf134d", "head-snowflake": "0xf134e", "head-snowflake-outline": "0xf134f", "head-sync": "0xf1350", "head-sync-outline": "0xf1351", "headphones": "0xf02cb", "headphones-bluetooth": "0xf0970", "headphones-box": "0xf02cc", "headphones-off": "0xf07ce", "headphones-settings": "0xf02cd", "headset": "0xf02ce", "headset-dock": "0xf02cf", "headset-off": "0xf02d0", "heart": "0xf02d1", "heart-box": "0xf02d2", "heart-box-outline": "0xf02d3", "heart-broken": "0xf02d4", "heart-broken-outline": "0xf0d14", "heart-circle": "0xf0971", "heart-circle-outline": "0xf0972", "heart-cog": "0xf1663", "heart-cog-outline": "0xf1664", "heart-flash": "0xf0ef9", "heart-half": "0xf06df", "heart-half-full": "0xf06de", "heart-half-outline": "0xf06e0", "heart-minus": "0xf142f", "heart-minus-outline": "0xf1432", "heart-multiple": "0xf0a56", "heart-multiple-outline": "0xf0a57", "heart-off": "0xf0759", "heart-off-outline": "0xf1434", "heart-outline": "0xf02d5", "heart-plus": "0xf142e", "heart-plus-outline": "0xf1431", "heart-pulse": "0xf05f6", "heart-remove": "0xf1430", "heart-remove-outline": "0xf1433", "heart-settings": "0xf1665", "heart-settings-outline": "0xf1666", "helicopter": "0xf0ac2", "help": "0xf02d6", "help-box": "0xf078b", "help-circle": "0xf02d7", "help-circle-outline": "0xf0625", "help-network": "0xf06f5", "help-network-outline": "0xf0c8a", "help-rhombus": "0xf0ba5", "help-rhombus-outline": "0xf0ba6", "hexadecimal": "0xf12a7", "hexagon": "0xf02d8", "hexagon-multiple": "0xf06e1", "hexagon-multiple-outline": "0xf10f2", "hexagon-outline": "0xf02d9", "hexagon-slice-1": "0xf0ac3", "hexagon-slice-2": "0xf0ac4", "hexagon-slice-3": "0xf0ac5", "hexagon-slice-4": "0xf0ac6", "hexagon-slice-5": "0xf0ac7", "hexagon-slice-6": "0xf0ac8", "hexagram": "0xf0ac9", "hexagram-outline": "0xf0aca", "high-definition": "0xf07cf", "high-definition-box": "0xf0878", "highway": "0xf05f7", "hiking": "0xf0d7f", "hinduism": "0xf0973", "history": "0xf02da", "hockey-puck": "0xf0879", "hockey-sticks": "0xf087a", "hololens": "0xf02db", "home": "0xf02dc", "home-account": "0xf0826", "home-alert": "0xf087b", "home-alert-outline": "0xf15d0", "home-analytics": "0xf0eba", "home-assistant": "0xf07d0", "home-automation": "0xf07d1", "home-circle": "0xf07d2", "home-circle-outline": "0xf104d", "home-city": "0xf0d15", "home-city-outline": "0xf0d16", "home-currency-usd": "0xf08af", "home-edit": "0xf1159", "home-edit-outline": "0xf115a", "home-export-outline": "0xf0f9b", "home-flood": "0xf0efa", "home-floor-0": "0xf0dd2", "home-floor-1": "0xf0d80", "home-floor-2": "0xf0d81", "home-floor-3": "0xf0d82", "home-floor-a": "0xf0d83", "home-floor-b": "0xf0d84", "home-floor-g": "0xf0d85", "home-floor-l": "0xf0d86", "home-floor-negative-1": "0xf0dd3", "home-group": "0xf0dd4", "home-heart": "0xf0827", "home-import-outline": "0xf0f9c", "home-lightbulb": "0xf1251", "home-lightbulb-outline": "0xf1252", "home-lock": "0xf08eb", "home-lock-open": "0xf08ec", "home-map-marker": "0xf05f8", "home-minus": "0xf0974", "home-minus-outline": "0xf13d5", "home-modern": "0xf02dd", "home-outline": "0xf06a1", "home-plus": "0xf0975", "home-plus-outline": "0xf13d6", "home-remove": "0xf1247", "home-remove-outline": "0xf13d7", "home-roof": "0xf112b", "home-search": "0xf13b0", "home-search-outline": "0xf13b1", "home-thermometer": "0xf0f54", "home-thermometer-outline": "0xf0f55", "home-variant": "0xf02de", "home-variant-outline": "0xf0ba7", "hook": "0xf06e2", "hook-off": "0xf06e3", "hops": "0xf02df", "horizontal-rotate-clockwise": "0xf10f3", "horizontal-rotate-counterclockwise": "0xf10f4", "horse": "0xf15bf", "horse-human": "0xf15c0", "horse-variant": "0xf15c1", "horseshoe": "0xf0a58", "hospital": "0xf0ff6", "hospital-box": "0xf02e0", "hospital-box-outline": "0xf0ff7", "hospital-building": "0xf02e1", "hospital-marker": "0xf02e2", "hot-tub": "0xf0828", "hours-24": "0xf1478", "hubspot": "0xf0d17", "hulu": "0xf0829", "human": "0xf02e6", "human-baby-changing-table": "0xf138b", "human-cane": "0xf1581", "human-capacity-decrease": "0xf159b", "human-capacity-increase": "0xf159c", "human-child": "0xf02e7", "human-edit": "0xf14e8", "human-female": "0xf0649", "human-female-boy": "0xf0a59", "human-female-dance": "0xf15c9", "human-female-female": "0xf0a5a", "human-female-girl": "0xf0a5b", "human-greeting": "0xf064a", "human-greeting-proximity": "0xf159d", "human-handsdown": "0xf064b", "human-handsup": "0xf064c", "human-male": "0xf064d", "human-male-boy": "0xf0a5c", "human-male-child": "0xf138c", "human-male-female": "0xf02e8", "human-male-girl": "0xf0a5d", "human-male-height": "0xf0efb", "human-male-height-variant": "0xf0efc", "human-male-male": "0xf0a5e", "human-pregnant": "0xf05cf", "human-queue": "0xf1571", "human-scooter": "0xf11e9", "human-wheelchair": "0xf138d", "humble-bundle": "0xf0744", "hvac": "0xf1352", "hvac-off": "0xf159e", "hydraulic-oil-level": "0xf1324", "hydraulic-oil-temperature": "0xf1325", "hydro-power": "0xf12e5", "ice-cream": "0xf082a", "ice-cream-off": "0xf0e52", "ice-pop": "0xf0efd", "id-card": "0xf0fc0", "identifier": "0xf0efe", "ideogram-cjk": "0xf1331", "ideogram-cjk-variant": "0xf1332", "iframe": "0xf0c8b", "iframe-array": "0xf10f5", "iframe-array-outline": "0xf10f6", "iframe-braces": "0xf10f7", "iframe-braces-outline": "0xf10f8", "iframe-outline": "0xf0c8c", "iframe-parentheses": "0xf10f9", "iframe-parentheses-outline": "0xf10fa", "iframe-variable": "0xf10fb", "iframe-variable-outline": "0xf10fc", "image": "0xf02e9", "image-album": "0xf02ea", "image-area": "0xf02eb", "image-area-close": "0xf02ec", "image-auto-adjust": "0xf0fc1", "image-broken": "0xf02ed", "image-broken-variant": "0xf02ee", "image-edit": "0xf11e3", "image-edit-outline": "0xf11e4", "image-filter-black-white": "0xf02f0", "image-filter-center-focus": "0xf02f1", "image-filter-center-focus-strong": "0xf0eff", "image-filter-center-focus-strong-outline": "0xf0f00", "image-filter-center-focus-weak": "0xf02f2", "image-filter-drama": "0xf02f3", "image-filter-frames": "0xf02f4", "image-filter-hdr": "0xf02f5", "image-filter-none": "0xf02f6", "image-filter-tilt-shift": "0xf02f7", "image-filter-vintage": "0xf02f8", "image-frame": "0xf0e49", "image-minus": "0xf1419", "image-move": "0xf09f8", "image-multiple": "0xf02f9", "image-multiple-outline": "0xf02ef", "image-off": "0xf082b", "image-off-outline": "0xf11d1", "image-outline": "0xf0976", "image-plus": "0xf087c", "image-remove": "0xf1418", "image-search": "0xf0977", "image-search-outline": "0xf0978", "image-size-select-actual": "0xf0c8d", "image-size-select-large": "0xf0c8e", "image-size-select-small": "0xf0c8f", "image-text": "0xf160d", "import": "0xf02fa", "inbox": "0xf0687", "inbox-arrow-down": "0xf02fb", "inbox-arrow-down-outline": "0xf1270", "inbox-arrow-up": "0xf03d1", "inbox-arrow-up-outline": "0xf1271", "inbox-full": "0xf1272", "inbox-full-outline": "0xf1273", "inbox-multiple": "0xf08b0", "inbox-multiple-outline": "0xf0ba8", "inbox-outline": "0xf1274", "inbox-remove": "0xf159f", "inbox-remove-outline": "0xf15a0", "incognito": "0xf05f9", "incognito-circle": "0xf1421", "incognito-circle-off": "0xf1422", "incognito-off": "0xf0075", "infinity": "0xf06e4", "information": "0xf02fc", "information-outline": "0xf02fd", "information-variant": "0xf064e", "instagram": "0xf02fe", "instrument-triangle": "0xf104e", "invert-colors": "0xf0301", "invert-colors-off": "0xf0e4a", "iobroker": "0xf12e8", "ip": "0xf0a5f", "ip-network": "0xf0a60", "ip-network-outline": "0xf0c90", "ipod": "0xf0c91", "islam": "0xf0979", "island": "0xf104f", "iv-bag": "0xf10b9", "jabber": "0xf0dd5", "jeepney": "0xf0302", "jellyfish": "0xf0f01", "jellyfish-outline": "0xf0f02", "jira": "0xf0303", "jquery": "0xf087d", "jsfiddle": "0xf0304", "judaism": "0xf097a", "jump-rope": "0xf12ff", "kabaddi": "0xf0d87", "kangaroo": "0xf1558", "karate": "0xf082c", "keg": "0xf0305", "kettle": "0xf05fa", "kettle-alert": "0xf1317", "kettle-alert-outline": "0xf1318", "kettle-off": "0xf131b", "kettle-off-outline": "0xf131c", "kettle-outline": "0xf0f56", "kettle-pour-over": "0xf173c", "kettle-steam": "0xf1319", "kettle-steam-outline": "0xf131a", "kettlebell": "0xf1300", "key": "0xf0306", "key-arrow-right": "0xf1312", "key-chain": "0xf1574", "key-chain-variant": "0xf1575", "key-change": "0xf0307", "key-link": "0xf119f", "key-minus": "0xf0308", "key-outline": "0xf0dd6", "key-plus": "0xf0309", "key-remove": "0xf030a", "key-star": "0xf119e", "key-variant": "0xf030b", "key-wireless": "0xf0fc2", "keyboard": "0xf030c", "keyboard-backspace": "0xf030d", "keyboard-caps": "0xf030e", "keyboard-close": "0xf030f", "keyboard-esc": "0xf12b7", "keyboard-f1": "0xf12ab", "keyboard-f10": "0xf12b4", "keyboard-f11": "0xf12b5", "keyboard-f12": "0xf12b6", "keyboard-f2": "0xf12ac", "keyboard-f3": "0xf12ad", "keyboard-f4": "0xf12ae", "keyboard-f5": "0xf12af", "keyboard-f6": "0xf12b0", "keyboard-f7": "0xf12b1", "keyboard-f8": "0xf12b2", "keyboard-f9": "0xf12b3", "keyboard-off": "0xf0310", "keyboard-off-outline": "0xf0e4b", "keyboard-outline": "0xf097b", "keyboard-return": "0xf0311", "keyboard-settings": "0xf09f9", "keyboard-settings-outline": "0xf09fa", "keyboard-space": "0xf1050", "keyboard-tab": "0xf0312", "keyboard-variant": "0xf0313", "khanda": "0xf10fd", "kickstarter": "0xf0745", "klingon": "0xf135b", "knife": "0xf09fb", "knife-military": "0xf09fc", "koala": "0xf173f", "kodi": "0xf0314", "kubernetes": "0xf10fe", "label": "0xf0315", "label-multiple": "0xf1375", "label-multiple-outline": "0xf1376", "label-off": "0xf0acb", "label-off-outline": "0xf0acc", "label-outline": "0xf0316", "label-percent": "0xf12ea", "label-percent-outline": "0xf12eb", "label-variant": "0xf0acd", "label-variant-outline": "0xf0ace", "ladder": "0xf15a2", "ladybug": "0xf082d", "lambda": "0xf0627", "lamp": "0xf06b5", "lamps": "0xf1576", "lan": "0xf0317", "lan-check": "0xf12aa", "lan-connect": "0xf0318", "lan-disconnect": "0xf0319", "lan-pending": "0xf031a", "language-c": "0xf0671", "language-cpp": "0xf0672", "language-csharp": "0xf031b", "language-css3": "0xf031c", "language-fortran": "0xf121a", "language-go": "0xf07d3", "language-haskell": "0xf0c92", "language-html5": "0xf031d", "language-java": "0xf0b37", "language-javascript": "0xf031e", "language-kotlin": "0xf1219", "language-lua": "0xf08b1", "markdown": "0xf0354", "language-markdown": "0xf0354", "language-markdown-outline": "0xf0f5b", "language-php": "0xf031f", "language-python": "0xf0320", "language-r": "0xf07d4", "language-ruby": "0xf0d2d", "language-ruby-on-rails": "0xf0acf", "language-rust": "0xf1617", "language-swift": "0xf06e5", "language-typescript": "0xf06e6", "language-xaml": "0xf0673", "laptop": "0xf0322", "laptop-chromebook": "0xf0323", "laptop-mac": "0xf0324", "laptop-off": "0xf06e7", "laptop-windows": "0xf0325", "laravel": "0xf0ad0", "laser-pointer": "0xf1484", "lasso": "0xf0f03", "lastpass": "0xf0446", "latitude": "0xf0f57", "launch": "0xf0327", "lava-lamp": "0xf07d5", "layers": "0xf0328", "layers-minus": "0xf0e4c", "layers-off": "0xf0329", "layers-off-outline": "0xf09fd", "layers-outline": "0xf09fe", "layers-plus": "0xf0e4d", "layers-remove": "0xf0e4e", "layers-search": "0xf1206", "layers-search-outline": "0xf1207", "layers-triple": "0xf0f58", "layers-triple-outline": "0xf0f59", "lead-pencil": "0xf064f", "leaf": "0xf032a", "leaf-maple": "0xf0c93", "leaf-maple-off": "0xf12da", "leaf-off": "0xf12d9", "leak": "0xf0dd7", "leak-off": "0xf0dd8", "led-off": "0xf032b", "led-on": "0xf032c", "led-outline": "0xf032d", "led-strip": "0xf07d6", "led-strip-variant": "0xf1051", "led-variant-off": "0xf032e", "led-variant-on": "0xf032f", "led-variant-outline": "0xf0330", "leek": "0xf117d", "less-than": "0xf097c", "less-than-or-equal": "0xf097d", "library": "0xf0331", "library-shelves": "0xf0ba9", "license": "0xf0fc3", "lifebuoy": "0xf087e", "light-switch": "0xf097e", "lightbulb": "0xf0335", "lightbulb-cfl": "0xf1208", "lightbulb-cfl-off": "0xf1209", "lightbulb-cfl-spiral": "0xf1275", "lightbulb-cfl-spiral-off": "0xf12c3", "lightbulb-group": "0xf1253", "lightbulb-group-off": "0xf12cd", "lightbulb-group-off-outline": "0xf12ce", "lightbulb-group-outline": "0xf1254", "lightbulb-multiple": "0xf1255", "lightbulb-multiple-off": "0xf12cf", "lightbulb-multiple-off-outline": "0xf12d0", "lightbulb-multiple-outline": "0xf1256", "lightbulb-off": "0xf0e4f", "lightbulb-off-outline": "0xf0e50", "lightbulb-on": "0xf06e8", "lightbulb-on-outline": "0xf06e9", "lightbulb-outline": "0xf0336", "lighthouse": "0xf09ff", "lighthouse-on": "0xf0a00", "lightning-bolt": "0xf140b", "lightning-bolt-outline": "0xf140c", "lingerie": "0xf1476", "link": "0xf0337", "link-box": "0xf0d1a", "link-box-outline": "0xf0d1b", "link-box-variant": "0xf0d1c", "link-box-variant-outline": "0xf0d1d", "link-lock": "0xf10ba", "link-off": "0xf0338", "link-plus": "0xf0c94", "link-variant": "0xf0339", "link-variant-minus": "0xf10ff", "link-variant-off": "0xf033a", "link-variant-plus": "0xf1100", "link-variant-remove": "0xf1101", "linkedin": "0xf033b", "linux": "0xf033d", "linux-mint": "0xf08ed", "lipstick": "0xf13b5", "list-status": "0xf15ab", "litecoin": "0xf0a61", "loading": "0xf0772", "location-enter": "0xf0fc4", "location-exit": "0xf0fc5", "lock": "0xf033e", "lock-alert": "0xf08ee", "lock-alert-outline": "0xf15d1", "lock-check": "0xf139a", "lock-check-outline": "0xf16a8", "lock-clock": "0xf097f", "lock-minus": "0xf16a9", "lock-minus-outline": "0xf16aa", "lock-off": "0xf1671", "lock-off-outline": "0xf1672", "lock-open": "0xf033f", "lock-open-alert": "0xf139b", "lock-open-alert-outline": "0xf15d2", "lock-open-check": "0xf139c", "lock-open-check-outline": "0xf16ab", "lock-open-minus": "0xf16ac", "lock-open-minus-outline": "0xf16ad", "lock-open-outline": "0xf0340", "lock-open-plus": "0xf16ae", "lock-open-plus-outline": "0xf16af", "lock-open-remove": "0xf16b0", "lock-open-remove-outline": "0xf16b1", "lock-open-variant": "0xf0fc6", "lock-open-variant-outline": "0xf0fc7", "lock-outline": "0xf0341", "lock-pattern": "0xf06ea", "lock-plus": "0xf05fb", "lock-plus-outline": "0xf16b2", "lock-question": "0xf08ef", "lock-remove": "0xf16b3", "lock-remove-outline": "0xf16b4", "lock-reset": "0xf0773", "lock-smart": "0xf08b2", "locker": "0xf07d7", "locker-multiple": "0xf07d8", "login": "0xf0342", "login-variant": "0xf05fc", "logout": "0xf0343", "logout-variant": "0xf05fd", "longitude": "0xf0f5a", "looks": "0xf0344", "lotion": "0xf1582", "lotion-outline": "0xf1583", "lotion-plus": "0xf1584", "lotion-plus-outline": "0xf1585", "loupe": "0xf0345", "lumx": "0xf0346", "lungs": "0xf1084", "magnet": "0xf0347", "magnet-on": "0xf0348", "magnify": "0xf0349", "magnify-close": "0xf0980", "magnify-minus": "0xf034a", "magnify-minus-cursor": "0xf0a62", "magnify-minus-outline": "0xf06ec", "magnify-plus": "0xf034b", "magnify-plus-cursor": "0xf0a63", "magnify-plus-outline": "0xf06ed", "magnify-remove-cursor": "0xf120c", "magnify-remove-outline": "0xf120d", "magnify-scan": "0xf1276", "mail": "0xf0ebb", "mailbox": "0xf06ee", "mailbox-open": "0xf0d88", "mailbox-open-outline": "0xf0d89", "mailbox-open-up": "0xf0d8a", "mailbox-open-up-outline": "0xf0d8b", "mailbox-outline": "0xf0d8c", "mailbox-up": "0xf0d8d", "mailbox-up-outline": "0xf0d8e", "manjaro": "0xf160a", "map": "0xf034d", "map-check": "0xf0ebc", "map-check-outline": "0xf0ebd", "map-clock": "0xf0d1e", "map-clock-outline": "0xf0d1f", "map-legend": "0xf0a01", "map-marker": "0xf034e", "map-marker-alert": "0xf0f05", "map-marker-alert-outline": "0xf0f06", "map-marker-check": "0xf0c95", "map-marker-check-outline": "0xf12fb", "map-marker-circle": "0xf034f", "map-marker-distance": "0xf08f0", "map-marker-down": "0xf1102", "map-marker-left": "0xf12db", "map-marker-left-outline": "0xf12dd", "map-marker-minus": "0xf0650", "map-marker-minus-outline": "0xf12f9", "map-marker-multiple": "0xf0350", "map-marker-multiple-outline": "0xf1277", "map-marker-off": "0xf0351", "map-marker-off-outline": "0xf12fd", "map-marker-outline": "0xf07d9", "map-marker-path": "0xf0d20", "map-marker-plus": "0xf0651", "map-marker-plus-outline": "0xf12f8", "map-marker-question": "0xf0f07", "map-marker-question-outline": "0xf0f08", "map-marker-radius": "0xf0352", "map-marker-radius-outline": "0xf12fc", "map-marker-remove": "0xf0f09", "map-marker-remove-outline": "0xf12fa", "map-marker-remove-variant": "0xf0f0a", "map-marker-right": "0xf12dc", "map-marker-right-outline": "0xf12de", "map-marker-star": "0xf1608", "map-marker-star-outline": "0xf1609", "map-marker-up": "0xf1103", "map-minus": "0xf0981", "map-outline": "0xf0982", "map-plus": "0xf0983", "map-search": "0xf0984", "map-search-outline": "0xf0985", "mapbox": "0xf0baa", "margin": "0xf0353", "marker": "0xf0652", "marker-cancel": "0xf0dd9", "marker-check": "0xf0355", "mastodon": "0xf0ad1", "material-design": "0xf0986", "material-ui": "0xf0357", "math-compass": "0xf0358", "math-cos": "0xf0c96", "math-integral": "0xf0fc8", "math-integral-box": "0xf0fc9", "math-log": "0xf1085", "math-norm": "0xf0fca", "math-norm-box": "0xf0fcb", "math-sin": "0xf0c97", "math-tan": "0xf0c98", "matrix": "0xf0628", "medal": "0xf0987", "medal-outline": "0xf1326", "medical-bag": "0xf06ef", "meditation": "0xf117b", "memory": "0xf035b", "menu": "0xf035c", "menu-down": "0xf035d", "menu-down-outline": "0xf06b6", "menu-left": "0xf035e", "menu-left-outline": "0xf0a02", "menu-open": "0xf0bab", "menu-right": "0xf035f", "menu-right-outline": "0xf0a03", "menu-swap": "0xf0a64", "menu-swap-outline": "0xf0a65", "menu-up": "0xf0360", "menu-up-outline": "0xf06b7", "merge": "0xf0f5c", "message": "0xf0361", "message-alert": "0xf0362", "message-alert-outline": "0xf0a04", "message-arrow-left": "0xf12f2", "message-arrow-left-outline": "0xf12f3", "message-arrow-right": "0xf12f4", "message-arrow-right-outline": "0xf12f5", "message-bookmark": "0xf15ac", "message-bookmark-outline": "0xf15ad", "message-bulleted": "0xf06a2", "message-bulleted-off": "0xf06a3", "message-cog": "0xf06f1", "message-cog-outline": "0xf1172", "message-draw": "0xf0363", "message-flash": "0xf15a9", "message-flash-outline": "0xf15aa", "message-image": "0xf0364", "message-image-outline": "0xf116c", "message-lock": "0xf0fcc", "message-lock-outline": "0xf116d", "message-minus": "0xf116e", "message-minus-outline": "0xf116f", "message-off": "0xf164d", "message-off-outline": "0xf164e", "message-outline": "0xf0365", "message-plus": "0xf0653", "message-plus-outline": "0xf10bb", "message-processing": "0xf0366", "message-processing-outline": "0xf1170", "message-question": "0xf173a", "message-question-outline": "0xf173b", "message-reply": "0xf0367", "message-reply-outline": "0xf173d", "message-reply-text": "0xf0368", "message-reply-text-outline": "0xf173e", "message-settings": "0xf06f0", "message-settings-outline": "0xf1171", "message-text": "0xf0369", "message-text-clock": "0xf1173", "message-text-clock-outline": "0xf1174", "message-text-lock": "0xf0fcd", "message-text-lock-outline": "0xf1175", "message-text-outline": "0xf036a", "message-video": "0xf036b", "meteor": "0xf0629", "metronome": "0xf07da", "metronome-tick": "0xf07db", "micro-sd": "0xf07dc", "microphone": "0xf036c", "microphone-minus": "0xf08b3", "microphone-off": "0xf036d", "microphone-outline": "0xf036e", "microphone-plus": "0xf08b4", "microphone-settings": "0xf036f", "microphone-variant": "0xf0370", "microphone-variant-off": "0xf0371", "microscope": "0xf0654", "microsoft": "0xf0372", "microsoft-access": "0xf138e", "microsoft-azure": "0xf0805", "microsoft-azure-devops": "0xf0fd5", "microsoft-bing": "0xf00a4", "microsoft-dynamics-365": "0xf0988", "microsoft-edge": "0xf01e9", "microsoft-edge-legacy": "0xf1250", "microsoft-excel": "0xf138f", "microsoft-internet-explorer": "0xf0300", "microsoft-office": "0xf03c6", "microsoft-onedrive": "0xf03ca", "microsoft-onenote": "0xf0747", "microsoft-outlook": "0xf0d22", "microsoft-powerpoint": "0xf1390", "microsoft-sharepoint": "0xf1391", "microsoft-teams": "0xf02bb", "microsoft-visual-studio": "0xf0610", "microsoft-visual-studio-code": "0xf0a1e", "windows": "0xf05b3", "microsoft-windows": "0xf05b3", "microsoft-windows-classic": "0xf0a21", "microsoft-word": "0xf1392", "microsoft-xbox": "0xf05b9", "microsoft-xbox-controller": "0xf05ba", "microsoft-xbox-controller-battery-alert": "0xf074b", "microsoft-xbox-controller-battery-charging": "0xf0a22", "microsoft-xbox-controller-battery-empty": "0xf074c", "microsoft-xbox-controller-battery-full": "0xf074d", "microsoft-xbox-controller-battery-low": "0xf074e", "microsoft-xbox-controller-battery-medium": "0xf074f", "microsoft-xbox-controller-battery-unknown": "0xf0750", "microsoft-xbox-controller-menu": "0xf0e6f", "microsoft-xbox-controller-off": "0xf05bb", "microsoft-xbox-controller-view": "0xf0e70", "microsoft-yammer": "0xf0789", "microwave": "0xf0c99", "microwave-off": "0xf1423", "middleware": "0xf0f5d", "middleware-outline": "0xf0f5e", "midi": "0xf08f1", "midi-port": "0xf08f2", "mine": "0xf0dda", "minecraft": "0xf0373", "mini-sd": "0xf0a05", "minidisc": "0xf0a06", "minus": "0xf0374", "minus-box": "0xf0375", "minus-box-multiple": "0xf1141", "minus-box-multiple-outline": "0xf1142", "minus-box-outline": "0xf06f2", "minus-circle": "0xf0376", "minus-circle-multiple": "0xf035a", "minus-circle-multiple-outline": "0xf0ad3", "minus-circle-off": "0xf1459", "minus-circle-off-outline": "0xf145a", "minus-circle-outline": "0xf0377", "minus-network": "0xf0378", "minus-network-outline": "0xf0c9a", "minus-thick": "0xf1639", "mirror": "0xf11fd", "mixed-martial-arts": "0xf0d8f", "mixed-reality": "0xf087f", "molecule": "0xf0bac", "molecule-co": "0xf12fe", "molecule-co2": "0xf07e4", "monitor": "0xf0379", "monitor-cellphone": "0xf0989", "monitor-cellphone-star": "0xf098a", "monitor-clean": "0xf1104", "monitor-dashboard": "0xf0a07", "monitor-edit": "0xf12c6", "monitor-eye": "0xf13b4", "monitor-lock": "0xf0ddb", "monitor-multiple": "0xf037a", "monitor-off": "0xf0d90", "monitor-screenshot": "0xf0e51", "monitor-share": "0xf1483", "monitor-speaker": "0xf0f5f", "monitor-speaker-off": "0xf0f60", "monitor-star": "0xf0ddc", "moon-first-quarter": "0xf0f61", "moon-full": "0xf0f62", "moon-last-quarter": "0xf0f63", "moon-new": "0xf0f64", "moon-waning-crescent": "0xf0f65", "moon-waning-gibbous": "0xf0f66", "moon-waxing-crescent": "0xf0f67", "moon-waxing-gibbous": "0xf0f68", "moped": "0xf1086", "moped-electric": "0xf15b7", "moped-electric-outline": "0xf15b8", "moped-outline": "0xf15b9", "more": "0xf037b", "mother-heart": "0xf1314", "mother-nurse": "0xf0d21", "motion": "0xf15b2", "motion-outline": "0xf15b3", "motion-pause": "0xf1590", "motion-pause-outline": "0xf1592", "motion-play": "0xf158f", "motion-play-outline": "0xf1591", "motion-sensor": "0xf0d91", "motion-sensor-off": "0xf1435", "motorbike": "0xf037c", "motorbike-electric": "0xf15ba", "mouse": "0xf037d", "mouse-bluetooth": "0xf098b", "mouse-move-down": "0xf1550", "mouse-move-up": "0xf1551", "mouse-move-vertical": "0xf1552", "mouse-off": "0xf037e", "mouse-variant": "0xf037f", "mouse-variant-off": "0xf0380", "move-resize": "0xf0655", "move-resize-variant": "0xf0656", "movie": "0xf0381", "movie-check": "0xf16f3", "movie-check-outline": "0xf16f4", "movie-cog": "0xf16f5", "movie-cog-outline": "0xf16f6", "movie-edit": "0xf1122", "movie-edit-outline": "0xf1123", "movie-filter": "0xf1124", "movie-filter-outline": "0xf1125", "movie-minus": "0xf16f7", "movie-minus-outline": "0xf16f8", "movie-off": "0xf16f9", "movie-off-outline": "0xf16fa", "movie-open": "0xf0fce", "movie-open-check": "0xf16fb", "movie-open-check-outline": "0xf16fc", "movie-open-cog": "0xf16fd", "movie-open-cog-outline": "0xf16fe", "movie-open-edit": "0xf16ff", "movie-open-edit-outline": "0xf1700", "movie-open-minus": "0xf1701", "movie-open-minus-outline": "0xf1702", "movie-open-off": "0xf1703", "movie-open-off-outline": "0xf1704", "movie-open-outline": "0xf0fcf", "movie-open-play": "0xf1705", "movie-open-play-outline": "0xf1706", "movie-open-plus": "0xf1707", "movie-open-plus-outline": "0xf1708", "movie-open-remove": "0xf1709", "movie-open-remove-outline": "0xf170a", "movie-open-settings": "0xf170b", "movie-open-settings-outline": "0xf170c", "movie-open-star": "0xf170d", "movie-open-star-outline": "0xf170e", "movie-outline": "0xf0ddd", "movie-play": "0xf170f", "movie-play-outline": "0xf1710", "movie-plus": "0xf1711", "movie-plus-outline": "0xf1712", "movie-remove": "0xf1713", "movie-remove-outline": "0xf1714", "movie-roll": "0xf07de", "movie-search": "0xf11d2", "movie-search-outline": "0xf11d3", "movie-settings": "0xf1715", "movie-settings-outline": "0xf1716", "movie-star": "0xf1717", "movie-star-outline": "0xf1718", "mower": "0xf166f", "mower-bag": "0xf1670", "muffin": "0xf098c", "multiplication": "0xf0382", "multiplication-box": "0xf0383", "mushroom": "0xf07df", "mushroom-off": "0xf13fa", "mushroom-off-outline": "0xf13fb", "mushroom-outline": "0xf07e0", "music": "0xf075a", "music-accidental-double-flat": "0xf0f69", "music-accidental-double-sharp": "0xf0f6a", "music-accidental-flat": "0xf0f6b", "music-accidental-natural": "0xf0f6c", "music-accidental-sharp": "0xf0f6d", "music-box": "0xf0384", "music-box-multiple": "0xf0333", "music-box-multiple-outline": "0xf0f04", "music-box-outline": "0xf0385", "music-circle": "0xf0386", "music-circle-outline": "0xf0ad4", "music-clef-alto": "0xf0f6e", "music-clef-bass": "0xf0f6f", "music-clef-treble": "0xf0f70", "music-note": "0xf0387", "music-note-bluetooth": "0xf05fe", "music-note-bluetooth-off": "0xf05ff", "music-note-eighth": "0xf0388", "music-note-eighth-dotted": "0xf0f71", "music-note-half": "0xf0389", "music-note-half-dotted": "0xf0f72", "music-note-off": "0xf038a", "music-note-off-outline": "0xf0f73", "music-note-outline": "0xf0f74", "music-note-plus": "0xf0dde", "music-note-quarter": "0xf038b", "music-note-quarter-dotted": "0xf0f75", "music-note-sixteenth": "0xf038c", "music-note-sixteenth-dotted": "0xf0f76", "music-note-whole": "0xf038d", "music-note-whole-dotted": "0xf0f77", "music-off": "0xf075b", "music-rest-eighth": "0xf0f78", "music-rest-half": "0xf0f79", "music-rest-quarter": "0xf0f7a", "music-rest-sixteenth": "0xf0f7b", "music-rest-whole": "0xf0f7c", "mustache": "0xf15de", "nail": "0xf0ddf", "nas": "0xf08f3", "nativescript": "0xf0880", "nature": "0xf038e", "nature-people": "0xf038f", "navigation": "0xf0390", "navigation-outline": "0xf1607", "near-me": "0xf05cd", "necklace": "0xf0f0b", "needle": "0xf0391", "netflix": "0xf0746", "network": "0xf06f3", "network-off": "0xf0c9b", "network-off-outline": "0xf0c9c", "network-outline": "0xf0c9d", "network-strength-1": "0xf08f4", "network-strength-1-alert": "0xf08f5", "network-strength-2": "0xf08f6", "network-strength-2-alert": "0xf08f7", "network-strength-3": "0xf08f8", "network-strength-3-alert": "0xf08f9", "network-strength-4": "0xf08fa", "network-strength-4-alert": "0xf08fb", "network-strength-off": "0xf08fc", "network-strength-off-outline": "0xf08fd", "network-strength-outline": "0xf08fe", "new-box": "0xf0394", "newspaper": "0xf0395", "newspaper-minus": "0xf0f0c", "newspaper-plus": "0xf0f0d", "newspaper-variant": "0xf1001", "newspaper-variant-multiple": "0xf1002", "newspaper-variant-multiple-outline": "0xf1003", "newspaper-variant-outline": "0xf1004", "nfc": "0xf0396", "nfc-search-variant": "0xf0e53", "nfc-tap": "0xf0397", "nfc-variant": "0xf0398", "nfc-variant-off": "0xf0e54", "ninja": "0xf0774", "nintendo-game-boy": "0xf1393", "nintendo-switch": "0xf07e1", "nintendo-wii": "0xf05ab", "nintendo-wiiu": "0xf072d", "nix": "0xf1105", "nodejs": "0xf0399", "noodles": "0xf117e", "not-equal": "0xf098d", "not-equal-variant": "0xf098e", "note": "0xf039a", "note-minus": "0xf164f", "note-minus-outline": "0xf1650", "note-multiple": "0xf06b8", "note-multiple-outline": "0xf06b9", "note-outline": "0xf039b", "note-plus": "0xf039c", "note-plus-outline": "0xf039d", "note-remove": "0xf1651", "note-remove-outline": "0xf1652", "note-search": "0xf1653", "note-search-outline": "0xf1654", "note-text": "0xf039e", "note-text-outline": "0xf11d7", "notebook": "0xf082e", "notebook-check": "0xf14f5", "notebook-check-outline": "0xf14f6", "notebook-edit": "0xf14e7", "notebook-edit-outline": "0xf14e9", "notebook-minus": "0xf1610", "notebook-minus-outline": "0xf1611", "notebook-multiple": "0xf0e55", "notebook-outline": "0xf0ebf", "notebook-plus": "0xf1612", "notebook-plus-outline": "0xf1613", "notebook-remove": "0xf1614", "notebook-remove-outline": "0xf1615", "notification-clear-all": "0xf039f", "npm": "0xf06f7", "nuke": "0xf06a4", "null": "0xf07e2", "numeric": "0xf03a0", "numeric-0": "0xf0b39", "numeric-0-box": "0xf03a1", "numeric-0-box-multiple": "0xf0f0e", "numeric-0-box-multiple-outline": "0xf03a2", "numeric-0-box-outline": "0xf03a3", "numeric-0-circle": "0xf0c9e", "numeric-0-circle-outline": "0xf0c9f", "numeric-1": "0xf0b3a", "numeric-1-box": "0xf03a4", "numeric-1-box-multiple": "0xf0f0f", "numeric-1-box-multiple-outline": "0xf03a5", "numeric-1-box-outline": "0xf03a6", "numeric-1-circle": "0xf0ca0", "numeric-1-circle-outline": "0xf0ca1", "numeric-10": "0xf0fe9", "numeric-10-box": "0xf0f7d", "numeric-10-box-multiple": "0xf0fea", "numeric-10-box-multiple-outline": "0xf0feb", "numeric-10-box-outline": "0xf0f7e", "numeric-10-circle": "0xf0fec", "numeric-10-circle-outline": "0xf0fed", "numeric-2": "0xf0b3b", "numeric-2-box": "0xf03a7", "numeric-2-box-multiple": "0xf0f10", "numeric-2-box-multiple-outline": "0xf03a8", "numeric-2-box-outline": "0xf03a9", "numeric-2-circle": "0xf0ca2", "numeric-2-circle-outline": "0xf0ca3", "numeric-3": "0xf0b3c", "numeric-3-box": "0xf03aa", "numeric-3-box-multiple": "0xf0f11", "numeric-3-box-multiple-outline": "0xf03ab", "numeric-3-box-outline": "0xf03ac", "numeric-3-circle": "0xf0ca4", "numeric-3-circle-outline": "0xf0ca5", "numeric-4": "0xf0b3d", "numeric-4-box": "0xf03ad", "numeric-4-box-multiple": "0xf0f12", "numeric-4-box-multiple-outline": "0xf03b2", "numeric-4-box-outline": "0xf03ae", "numeric-4-circle": "0xf0ca6", "numeric-4-circle-outline": "0xf0ca7", "numeric-5": "0xf0b3e", "numeric-5-box": "0xf03b1", "numeric-5-box-multiple": "0xf0f13", "numeric-5-box-multiple-outline": "0xf03af", "numeric-5-box-outline": "0xf03b0", "numeric-5-circle": "0xf0ca8", "numeric-5-circle-outline": "0xf0ca9", "numeric-6": "0xf0b3f", "numeric-6-box": "0xf03b3", "numeric-6-box-multiple": "0xf0f14", "numeric-6-box-multiple-outline": "0xf03b4", "numeric-6-box-outline": "0xf03b5", "numeric-6-circle": "0xf0caa", "numeric-6-circle-outline": "0xf0cab", "numeric-7": "0xf0b40", "numeric-7-box": "0xf03b6", "numeric-7-box-multiple": "0xf0f15", "numeric-7-box-multiple-outline": "0xf03b7", "numeric-7-box-outline": "0xf03b8", "numeric-7-circle": "0xf0cac", "numeric-7-circle-outline": "0xf0cad", "numeric-8": "0xf0b41", "numeric-8-box": "0xf03b9", "numeric-8-box-multiple": "0xf0f16", "numeric-8-box-multiple-outline": "0xf03ba", "numeric-8-box-outline": "0xf03bb", "numeric-8-circle": "0xf0cae", "numeric-8-circle-outline": "0xf0caf", "numeric-9": "0xf0b42", "numeric-9-box": "0xf03bc", "numeric-9-box-multiple": "0xf0f17", "numeric-9-box-multiple-outline": "0xf03bd", "numeric-9-box-outline": "0xf03be", "numeric-9-circle": "0xf0cb0", "numeric-9-circle-outline": "0xf0cb1", "numeric-9-plus": "0xf0fee", "numeric-9-plus-box": "0xf03bf", "numeric-9-plus-box-multiple": "0xf0f18", "numeric-9-plus-box-multiple-outline": "0xf03c0", "numeric-9-plus-box-outline": "0xf03c1", "numeric-9-plus-circle": "0xf0cb2", "numeric-9-plus-circle-outline": "0xf0cb3", "numeric-negative-1": "0xf1052", "numeric-positive-1": "0xf15cb", "nut": "0xf06f8", "nutrition": "0xf03c2", "nuxt": "0xf1106", "oar": "0xf067c", "ocarina": "0xf0de0", "oci": "0xf12e9", "ocr": "0xf113a", "octagon": "0xf03c3", "octagon-outline": "0xf03c4", "octagram": "0xf06f9", "octagram-outline": "0xf0775", "odnoklassniki": "0xf03c5", "offer": "0xf121b", "office-building": "0xf0991", "office-building-marker": "0xf1520", "office-building-marker-outline": "0xf1521", "office-building-outline": "0xf151f", "oil": "0xf03c7", "oil-lamp": "0xf0f19", "oil-level": "0xf1053", "oil-temperature": "0xf0ff8", "omega": "0xf03c9", "one-up": "0xf0bad", "onepassword": "0xf0881", "opacity": "0xf05cc", "open-in-app": "0xf03cb", "open-in-new": "0xf03cc", "open-source-initiative": "0xf0bae", "openid": "0xf03cd", "opera": "0xf03ce", "orbit": "0xf0018", "orbit-variant": "0xf15db", "order-alphabetical-ascending": "0xf020d", "order-alphabetical-descending": "0xf0d07", "order-bool-ascending": "0xf02be", "order-bool-ascending-variant": "0xf098f", "order-bool-descending": "0xf1384", "order-bool-descending-variant": "0xf0990", "order-numeric-ascending": "0xf0545", "order-numeric-descending": "0xf0546", "origin": "0xf0b43", "ornament": "0xf03cf", "ornament-variant": "0xf03d0", "outdoor-lamp": "0xf1054", "overscan": "0xf1005", "owl": "0xf03d2", "pac-man": "0xf0baf", "package": "0xf03d3", "package-down": "0xf03d4", "package-up": "0xf03d5", "package-variant": "0xf03d6", "package-variant-closed": "0xf03d7", "page-first": "0xf0600", "page-last": "0xf0601", "page-layout-body": "0xf06fa", "page-layout-footer": "0xf06fb", "page-layout-header": "0xf06fc", "page-layout-header-footer": "0xf0f7f", "page-layout-sidebar-left": "0xf06fd", "page-layout-sidebar-right": "0xf06fe", "page-next": "0xf0bb0", "page-next-outline": "0xf0bb1", "page-previous": "0xf0bb2", "page-previous-outline": "0xf0bb3", "pail": "0xf1417", "pail-minus": "0xf1437", "pail-minus-outline": "0xf143c", "pail-off": "0xf1439", "pail-off-outline": "0xf143e", "pail-outline": "0xf143a", "pail-plus": "0xf1436", "pail-plus-outline": "0xf143b", "pail-remove": "0xf1438", "pail-remove-outline": "0xf143d", "palette": "0xf03d8", "palette-advanced": "0xf03d9", "palette-outline": "0xf0e0c", "palette-swatch": "0xf08b5", "palette-swatch-outline": "0xf135c", "palm-tree": "0xf1055", "pan": "0xf0bb4", "pan-bottom-left": "0xf0bb5", "pan-bottom-right": "0xf0bb6", "pan-down": "0xf0bb7", "pan-horizontal": "0xf0bb8", "pan-left": "0xf0bb9", "pan-right": "0xf0bba", "pan-top-left": "0xf0bbb", "pan-top-right": "0xf0bbc", "pan-up": "0xf0bbd", "pan-vertical": "0xf0bbe", "panda": "0xf03da", "pandora": "0xf03db", "panorama": "0xf03dc", "panorama-fisheye": "0xf03dd", "panorama-horizontal": "0xf03de", "panorama-vertical": "0xf03df", "panorama-wide-angle": "0xf03e0", "paper-cut-vertical": "0xf03e1", "paper-roll": "0xf1157", "paper-roll-outline": "0xf1158", "paperclip": "0xf03e2", "parachute": "0xf0cb4", "parachute-outline": "0xf0cb5", "parking": "0xf03e3", "party-popper": "0xf1056", "passport": "0xf07e3", "passport-biometric": "0xf0de1", "pasta": "0xf1160", "patio-heater": "0xf0f80", "patreon": "0xf0882", "pause": "0xf03e4", "pause-circle": "0xf03e5", "pause-circle-outline": "0xf03e6", "pause-octagon": "0xf03e7", "pause-octagon-outline": "0xf03e8", "paw": "0xf03e9", "paw-off": "0xf0657", "paw-off-outline": "0xf1676", "paw-outline": "0xf1675", "pdf-box": "0xf0e56", "peace": "0xf0884", "peanut": "0xf0ffc", "peanut-off": "0xf0ffd", "peanut-off-outline": "0xf0fff", "peanut-outline": "0xf0ffe", "pen": "0xf03ea", "pen-lock": "0xf0de2", "pen-minus": "0xf0de3", "pen-off": "0xf0de4", "pen-plus": "0xf0de5", "pen-remove": "0xf0de6", "pencil": "0xf03eb", "pencil-box": "0xf03ec", "pencil-box-multiple": "0xf1144", "pencil-box-multiple-outline": "0xf1145", "pencil-box-outline": "0xf03ed", "pencil-circle": "0xf06ff", "pencil-circle-outline": "0xf0776", "pencil-lock": "0xf03ee", "pencil-lock-outline": "0xf0de7", "pencil-minus": "0xf0de8", "pencil-minus-outline": "0xf0de9", "pencil-off": "0xf03ef", "pencil-off-outline": "0xf0dea", "pencil-outline": "0xf0cb6", "pencil-plus": "0xf0deb", "pencil-plus-outline": "0xf0dec", "pencil-remove": "0xf0ded", "pencil-remove-outline": "0xf0dee", "pencil-ruler": "0xf1353", "penguin": "0xf0ec0", "pentagon": "0xf0701", "pentagon-outline": "0xf0700", "pentagram": "0xf1667", "percent": "0xf03f0", "percent-outline": "0xf1278", "periodic-table": "0xf08b6", "perspective-less": "0xf0d23", "perspective-more": "0xf0d24", "pharmacy": "0xf03f1", "phone": "0xf03f2", "phone-alert": "0xf0f1a", "phone-alert-outline": "0xf118e", "phone-bluetooth": "0xf03f3", "phone-bluetooth-outline": "0xf118f", "phone-cancel": "0xf10bc", "phone-cancel-outline": "0xf1190", "phone-check": "0xf11a9", "phone-check-outline": "0xf11aa", "phone-classic": "0xf0602", "phone-classic-off": "0xf1279", "phone-dial": "0xf1559", "phone-dial-outline": "0xf155a", "phone-forward": "0xf03f4", "phone-forward-outline": "0xf1191", "phone-hangup": "0xf03f5", "phone-hangup-outline": "0xf1192", "phone-in-talk": "0xf03f6", "phone-in-talk-outline": "0xf1182", "phone-incoming": "0xf03f7", "phone-incoming-outline": "0xf1193", "phone-lock": "0xf03f8", "phone-lock-outline": "0xf1194", "phone-log": "0xf03f9", "phone-log-outline": "0xf1195", "phone-message": "0xf1196", "phone-message-outline": "0xf1197", "phone-minus": "0xf0658", "phone-minus-outline": "0xf1198", "phone-missed": "0xf03fa", "phone-missed-outline": "0xf11a5", "phone-off": "0xf0def", "phone-off-outline": "0xf11a6", "phone-outgoing": "0xf03fb", "phone-outgoing-outline": "0xf1199", "phone-outline": "0xf0df0", "phone-paused": "0xf03fc", "phone-paused-outline": "0xf119a", "phone-plus": "0xf0659", "phone-plus-outline": "0xf119b", "phone-remove": "0xf152f", "phone-remove-outline": "0xf1530", "phone-return": "0xf082f", "phone-return-outline": "0xf119c", "phone-ring": "0xf11ab", "phone-ring-outline": "0xf11ac", "phone-rotate-landscape": "0xf0885", "phone-rotate-portrait": "0xf0886", "phone-settings": "0xf03fd", "phone-settings-outline": "0xf119d", "phone-voip": "0xf03fe", "pi": "0xf03ff", "pi-box": "0xf0400", "pi-hole": "0xf0df1", "piano": "0xf067d", "pickaxe": "0xf08b7", "picture-in-picture-bottom-right": "0xf0e57", "picture-in-picture-bottom-right-outline": "0xf0e58", "picture-in-picture-top-right": "0xf0e59", "picture-in-picture-top-right-outline": "0xf0e5a", "pier": "0xf0887", "pier-crane": "0xf0888", "pig": "0xf0401", "pig-variant": "0xf1006", "pig-variant-outline": "0xf1678", "piggy-bank": "0xf1007", "piggy-bank-outline": "0xf1679", "pill": "0xf0402", "pillar": "0xf0702", "pin": "0xf0403", "pin-off": "0xf0404", "pin-off-outline": "0xf0930", "pin-outline": "0xf0931", "pine-tree": "0xf0405", "pine-tree-box": "0xf0406", "pine-tree-fire": "0xf141a", "pinterest": "0xf0407", "pinwheel": "0xf0ad5", "pinwheel-outline": "0xf0ad6", "pipe": "0xf07e5", "pipe-disconnected": "0xf07e6", "pipe-leak": "0xf0889", "pipe-wrench": "0xf1354", "pirate": "0xf0a08", "pistol": "0xf0703", "piston": "0xf088a", "pitchfork": "0xf1553", "pizza": "0xf0409", "play": "0xf040a", "play-box": "0xf127a", "play-box-multiple": "0xf0d19", "play-box-multiple-outline": "0xf13e6", "play-box-outline": "0xf040b", "play-circle": "0xf040c", "play-circle-outline": "0xf040d", "play-network": "0xf088b", "play-network-outline": "0xf0cb7", "play-outline": "0xf0f1b", "play-pause": "0xf040e", "play-protected-content": "0xf040f", "play-speed": "0xf08ff", "playlist-check": "0xf05c7", "playlist-edit": "0xf0900", "playlist-minus": "0xf0410", "playlist-music": "0xf0cb8", "playlist-music-outline": "0xf0cb9", "playlist-play": "0xf0411", "playlist-plus": "0xf0412", "playlist-remove": "0xf0413", "playlist-star": "0xf0df2", "plex": "0xf06ba", "plus": "0xf0415", "plus-box": "0xf0416", "plus-box-multiple": "0xf0334", "plus-box-multiple-outline": "0xf1143", "plus-box-outline": "0xf0704", "plus-circle": "0xf0417", "plus-circle-multiple": "0xf034c", "plus-circle-multiple-outline": "0xf0418", "plus-circle-outline": "0xf0419", "plus-minus": "0xf0992", "plus-minus-box": "0xf0993", "plus-minus-variant": "0xf14c9", "plus-network": "0xf041a", "plus-network-outline": "0xf0cba", "plus-one": "0xf041b", "plus-outline": "0xf0705", "plus-thick": "0xf11ec", "podcast": "0xf0994", "podium": "0xf0d25", "podium-bronze": "0xf0d26", "podium-gold": "0xf0d27", "podium-silver": "0xf0d28", "point-of-sale": "0xf0d92", "pokeball": "0xf041d", "pokemon-go": "0xf0a09", "poker-chip": "0xf0830", "polaroid": "0xf041e", "police-badge": "0xf1167", "police-badge-outline": "0xf1168", "poll": "0xf041f", "poll-box": "0xf0420", "poll-box-outline": "0xf127b", "polo": "0xf14c3", "polymer": "0xf0421", "pool": "0xf0606", "popcorn": "0xf0422", "post": "0xf1008", "post-outline": "0xf1009", "postage-stamp": "0xf0cbb", "pot": "0xf02e5", "pot-mix": "0xf065b", "pot-mix-outline": "0xf0677", "pot-outline": "0xf02ff", "pot-steam": "0xf065a", "pot-steam-outline": "0xf0326", "pound": "0xf0423", "pound-box": "0xf0424", "pound-box-outline": "0xf117f", "power": "0xf0425", "power-cycle": "0xf0901", "power-off": "0xf0902", "power-on": "0xf0903", "power-plug": "0xf06a5", "power-plug-off": "0xf06a6", "power-plug-off-outline": "0xf1424", "power-plug-outline": "0xf1425", "power-settings": "0xf0426", "power-sleep": "0xf0904", "power-socket": "0xf0427", "power-socket-au": "0xf0905", "power-socket-de": "0xf1107", "power-socket-eu": "0xf07e7", "power-socket-fr": "0xf1108", "power-socket-it": "0xf14ff", "power-socket-jp": "0xf1109", "power-socket-uk": "0xf07e8", "power-socket-us": "0xf07e9", "power-standby": "0xf0906", "powershell": "0xf0a0a", "prescription": "0xf0706", "presentation": "0xf0428", "presentation-play": "0xf0429", "pretzel": "0xf1562", "printer": "0xf042a", "printer-3d": "0xf042b", "printer-3d-nozzle": "0xf0e5b", "printer-3d-nozzle-alert": "0xf11c0", "printer-3d-nozzle-alert-outline": "0xf11c1", "printer-3d-nozzle-outline": "0xf0e5c", "printer-alert": "0xf042c", "printer-check": "0xf1146", "printer-eye": "0xf1458", "printer-off": "0xf0e5d", "printer-pos": "0xf1057", "printer-search": "0xf1457", "printer-settings": "0xf0707", "printer-wireless": "0xf0a0b", "priority-high": "0xf0603", "priority-low": "0xf0604", "professional-hexagon": "0xf042d", "progress-alert": "0xf0cbc", "progress-check": "0xf0995", "progress-clock": "0xf0996", "progress-close": "0xf110a", "progress-download": "0xf0997", "progress-question": "0xf1522", "progress-upload": "0xf0998", "progress-wrench": "0xf0cbd", "projector": "0xf042e", "projector-screen": "0xf042f", "projector-screen-outline": "0xf1724", "propane-tank": "0xf1357", "propane-tank-outline": "0xf1358", "protocol": "0xf0fd8", "publish": "0xf06a7", "pulse": "0xf0430", "pump": "0xf1402", "pumpkin": "0xf0bbf", "purse": "0xf0f1c", "purse-outline": "0xf0f1d", "puzzle": "0xf0431", "puzzle-check": "0xf1426", "puzzle-check-outline": "0xf1427", "puzzle-edit": "0xf14d3", "puzzle-edit-outline": "0xf14d9", "puzzle-heart": "0xf14d4", "puzzle-heart-outline": "0xf14da", "puzzle-minus": "0xf14d1", "puzzle-minus-outline": "0xf14d7", "puzzle-outline": "0xf0a66", "puzzle-plus": "0xf14d0", "puzzle-plus-outline": "0xf14d6", "puzzle-remove": "0xf14d2", "puzzle-remove-outline": "0xf14d8", "puzzle-star": "0xf14d5", "puzzle-star-outline": "0xf14db", "qi": "0xf0999", "qqchat": "0xf0605", "qrcode": "0xf0432", "qrcode-edit": "0xf08b8", "qrcode-minus": "0xf118c", "qrcode-plus": "0xf118b", "qrcode-remove": "0xf118d", "qrcode-scan": "0xf0433", "quadcopter": "0xf0434", "quality-high": "0xf0435", "quality-low": "0xf0a0c", "quality-medium": "0xf0a0d", "quora": "0xf0d29", "rabbit": "0xf0907", "racing-helmet": "0xf0d93", "racquetball": "0xf0d94", "radar": "0xf0437", "radiator": "0xf0438", "radiator-disabled": "0xf0ad7", "radiator-off": "0xf0ad8", "radio": "0xf0439", "radio-am": "0xf0cbe", "radio-fm": "0xf0cbf", "radio-handheld": "0xf043a", "radio-off": "0xf121c", "radio-tower": "0xf043b", "radioactive": "0xf043c", "radioactive-off": "0xf0ec1", "radiobox-blank": "0xf043d", "radiobox-marked": "0xf043e", "radiology-box": "0xf14c5", "radiology-box-outline": "0xf14c6", "radius": "0xf0cc0", "radius-outline": "0xf0cc1", "railroad-light": "0xf0f1e", "rake": "0xf1544", "raspberry-pi": "0xf043f", "ray-end": "0xf0440", "ray-end-arrow": "0xf0441", "ray-start": "0xf0442", "ray-start-arrow": "0xf0443", "ray-start-end": "0xf0444", "ray-start-vertex-end": "0xf15d8", "ray-vertex": "0xf0445", "react": "0xf0708", "read": "0xf0447", "receipt": "0xf0449", "record": "0xf044a", "record-circle": "0xf0ec2", "record-circle-outline": "0xf0ec3", "record-player": "0xf099a", "record-rec": "0xf044b", "rectangle": "0xf0e5e", "rectangle-outline": "0xf0e5f", "recycle": "0xf044c", "recycle-variant": "0xf139d", "reddit": "0xf044d", "redhat": "0xf111b", "redo": "0xf044e", "redo-variant": "0xf044f", "reflect-horizontal": "0xf0a0e", "reflect-vertical": "0xf0a0f", "refresh": "0xf0450", "refresh-circle": "0xf1377", "regex": "0xf0451", "registered-trademark": "0xf0a67", "reiterate": "0xf1588", "relation-many-to-many": "0xf1496", "relation-many-to-one": "0xf1497", "relation-many-to-one-or-many": "0xf1498", "relation-many-to-only-one": "0xf1499", "relation-many-to-zero-or-many": "0xf149a", "relation-many-to-zero-or-one": "0xf149b", "relation-one-or-many-to-many": "0xf149c", "relation-one-or-many-to-one": "0xf149d", "relation-one-or-many-to-one-or-many": "0xf149e", "relation-one-or-many-to-only-one": "0xf149f", "relation-one-or-many-to-zero-or-many": "0xf14a0", "relation-one-or-many-to-zero-or-one": "0xf14a1", "relation-one-to-many": "0xf14a2", "relation-one-to-one": "0xf14a3", "relation-one-to-one-or-many": "0xf14a4", "relation-one-to-only-one": "0xf14a5", "relation-one-to-zero-or-many": "0xf14a6", "relation-one-to-zero-or-one": "0xf14a7", "relation-only-one-to-many": "0xf14a8", "relation-only-one-to-one": "0xf14a9", "relation-only-one-to-one-or-many": "0xf14aa", "relation-only-one-to-only-one": "0xf14ab", "relation-only-one-to-zero-or-many": "0xf14ac", "relation-only-one-to-zero-or-one": "0xf14ad", "relation-zero-or-many-to-many": "0xf14ae", "relation-zero-or-many-to-one": "0xf14af", "relation-zero-or-many-to-one-or-many": "0xf14b0", "relation-zero-or-many-to-only-one": "0xf14b1", "relation-zero-or-many-to-zero-or-many": "0xf14b2", "relation-zero-or-many-to-zero-or-one": "0xf14b3", "relation-zero-or-one-to-many": "0xf14b4", "relation-zero-or-one-to-one": "0xf14b5", "relation-zero-or-one-to-one-or-many": "0xf14b6", "relation-zero-or-one-to-only-one": "0xf14b7", "relation-zero-or-one-to-zero-or-many": "0xf14b8", "relation-zero-or-one-to-zero-or-one": "0xf14b9", "relative-scale": "0xf0452", "reload": "0xf0453", "reload-alert": "0xf110b", "reminder": "0xf088c", "remote": "0xf0454", "remote-desktop": "0xf08b9", "remote-off": "0xf0ec4", "remote-tv": "0xf0ec5", "remote-tv-off": "0xf0ec6", "rename-box": "0xf0455", "reorder-horizontal": "0xf0688", "reorder-vertical": "0xf0689", "repeat": "0xf0456", "repeat-off": "0xf0457", "repeat-once": "0xf0458", "replay": "0xf0459", "reply": "0xf045a", "reply-all": "0xf045b", "reply-all-outline": "0xf0f1f", "reply-circle": "0xf11ae", "reply-outline": "0xf0f20", "reproduction": "0xf045c", "resistor": "0xf0b44", "resistor-nodes": "0xf0b45", "resize": "0xf0a68", "resize-bottom-right": "0xf045d", "responsive": "0xf045e", "restart": "0xf0709", "restart-alert": "0xf110c", "restart-off": "0xf0d95", "restore": "0xf099b", "restore-alert": "0xf110d", "rewind": "0xf045f", "rewind-10": "0xf0d2a", "rewind-30": "0xf0d96", "rewind-5": "0xf11f9", "rewind-60": "0xf160c", "rewind-outline": "0xf070a", "rhombus": "0xf070b", "rhombus-medium": "0xf0a10", "rhombus-medium-outline": "0xf14dc", "rhombus-outline": "0xf070c", "rhombus-split": "0xf0a11", "rhombus-split-outline": "0xf14dd", "ribbon": "0xf0460", "rice": "0xf07ea", "rickshaw": "0xf15bb", "rickshaw-electric": "0xf15bc", "ring": "0xf07eb", "rivet": "0xf0e60", "road": "0xf0461", "road-variant": "0xf0462", "robber": "0xf1058", "robot": "0xf06a9", "robot-angry": "0xf169d", "robot-angry-outline": "0xf169e", "robot-confused": "0xf169f", "robot-confused-outline": "0xf16a0", "robot-dead": "0xf16a1", "robot-dead-outline": "0xf16a2", "robot-excited": "0xf16a3", "robot-excited-outline": "0xf16a4", "robot-happy": "0xf1719", "robot-happy-outline": "0xf171a", "robot-industrial": "0xf0b46", "robot-love": "0xf16a5", "robot-love-outline": "0xf16a6", "robot-mower": "0xf11f7", "robot-mower-outline": "0xf11f3", "robot-off": "0xf16a7", "robot-off-outline": "0xf167b", "robot-outline": "0xf167a", "robot-vacuum": "0xf070d", "robot-vacuum-variant": "0xf0908", "rocket": "0xf0463", "rocket-launch": "0xf14de", "rocket-launch-outline": "0xf14df", "rocket-outline": "0xf13af", "rodent": "0xf1327", "roller-skate": "0xf0d2b", "roller-skate-off": "0xf0145", "rollerblade": "0xf0d2c", "rollerblade-off": "0xf002e", "rollupjs": "0xf0bc0", "roman-numeral-1": "0xf1088", "roman-numeral-10": "0xf1091", "roman-numeral-2": "0xf1089", "roman-numeral-3": "0xf108a", "roman-numeral-4": "0xf108b", "roman-numeral-5": "0xf108c", "roman-numeral-6": "0xf108d", "roman-numeral-7": "0xf108e", "roman-numeral-8": "0xf108f", "roman-numeral-9": "0xf1090", "room-service": "0xf088d", "room-service-outline": "0xf0d97", "rotate-3d": "0xf0ec7", "rotate-3d-variant": "0xf0464", "rotate-left": "0xf0465", "rotate-left-variant": "0xf0466", "rotate-orbit": "0xf0d98", "rotate-right": "0xf0467", "rotate-right-variant": "0xf0468", "rounded-corner": "0xf0607", "router": "0xf11e2", "router-network": "0xf1087", "router-wireless": "0xf0469", "router-wireless-off": "0xf15a3", "router-wireless-settings": "0xf0a69", "routes": "0xf046a", "routes-clock": "0xf1059", "rowing": "0xf0608", "rss": "0xf046b", "rss-box": "0xf046c", "rss-off": "0xf0f21", "rug": "0xf1475", "rugby": "0xf0d99", "ruler": "0xf046d", "ruler-square": "0xf0cc2", "ruler-square-compass": "0xf0ebe", "run": "0xf070e", "run-fast": "0xf046e", "rv-truck": "0xf11d4", "sack": "0xf0d2e", "sack-percent": "0xf0d2f", "safe": "0xf0a6a", "safe-square": "0xf127c", "safe-square-outline": "0xf127d", "safety-goggles": "0xf0d30", "sail-boat": "0xf0ec8", "sale": "0xf046f", "salesforce": "0xf088e", "sass": "0xf07ec", "satellite": "0xf0470", "satellite-uplink": "0xf0909", "satellite-variant": "0xf0471", "sausage": "0xf08ba", "saw-blade": "0xf0e61", "sawtooth-wave": "0xf147a", "saxophone": "0xf0609", "scale": "0xf0472", "scale-balance": "0xf05d1", "scale-bathroom": "0xf0473", "scale-off": "0xf105a", "scan-helper": "0xf13d8", "scanner": "0xf06ab", "scanner-off": "0xf090a", "scatter-plot": "0xf0ec9", "scatter-plot-outline": "0xf0eca", "school": "0xf0474", "school-outline": "0xf1180", "scissors-cutting": "0xf0a6b", "scooter": "0xf15bd", "scooter-electric": "0xf15be", "scoreboard": "0xf127e", "scoreboard-outline": "0xf127f", "screen-rotation": "0xf0475", "screen-rotation-lock": "0xf0478", "screw-flat-top": "0xf0df3", "screw-lag": "0xf0df4", "screw-machine-flat-top": "0xf0df5", "screw-machine-round-top": "0xf0df6", "screw-round-top": "0xf0df7", "screwdriver": "0xf0476", "script": "0xf0bc1", "script-outline": "0xf0477", "script-text": "0xf0bc2", "script-text-key": "0xf1725", "script-text-key-outline": "0xf1726", "script-text-outline": "0xf0bc3", "script-text-play": "0xf1727", "script-text-play-outline": "0xf1728", "sd": "0xf0479", "seal": "0xf047a", "seal-variant": "0xf0fd9", "search-web": "0xf070f", "seat": "0xf0cc3", "seat-flat": "0xf047b", "seat-flat-angled": "0xf047c", "seat-individual-suite": "0xf047d", "seat-legroom-extra": "0xf047e", "seat-legroom-normal": "0xf047f", "seat-legroom-reduced": "0xf0480", "seat-outline": "0xf0cc4", "seat-passenger": "0xf1249", "seat-recline-extra": "0xf0481", "seat-recline-normal": "0xf0482", "seatbelt": "0xf0cc5", "security": "0xf0483", "security-network": "0xf0484", "seed": "0xf0e62", "seed-off": "0xf13fd", "seed-off-outline": "0xf13fe", "seed-outline": "0xf0e63", "seesaw": "0xf15a4", "segment": "0xf0ecb", "select": "0xf0485", "select-all": "0xf0486", "select-color": "0xf0d31", "select-compare": "0xf0ad9", "select-drag": "0xf0a6c", "select-group": "0xf0f82", "select-inverse": "0xf0487", "select-marker": "0xf1280", "select-multiple": "0xf1281", "select-multiple-marker": "0xf1282", "select-off": "0xf0488", "select-place": "0xf0fda", "select-search": "0xf1204", "selection": "0xf0489", "selection-drag": "0xf0a6d", "selection-ellipse": "0xf0d32", "selection-ellipse-arrow-inside": "0xf0f22", "selection-marker": "0xf1283", "selection-multiple": "0xf1285", "selection-multiple-marker": "0xf1284", "selection-off": "0xf0777", "selection-search": "0xf1205", "semantic-web": "0xf1316", "send": "0xf048a", "send-check": "0xf1161", "send-check-outline": "0xf1162", "send-circle": "0xf0df8", "send-circle-outline": "0xf0df9", "send-clock": "0xf1163", "send-clock-outline": "0xf1164", "send-lock": "0xf07ed", "send-lock-outline": "0xf1166", "send-outline": "0xf1165", "serial-port": "0xf065c", "server": "0xf048b", "server-minus": "0xf048c", "server-network": "0xf048d", "server-network-off": "0xf048e", "server-off": "0xf048f", "server-plus": "0xf0490", "server-remove": "0xf0491", "server-security": "0xf0492", "set-all": "0xf0778", "set-center": "0xf0779", "set-center-right": "0xf077a", "set-left": "0xf077b", "set-left-center": "0xf077c", "set-left-right": "0xf077d", "set-merge": "0xf14e0", "set-none": "0xf077e", "set-right": "0xf077f", "set-split": "0xf14e1", "set-square": "0xf145d", "set-top-box": "0xf099f", "settings-helper": "0xf0a6e", "shaker": "0xf110e", "shaker-outline": "0xf110f", "shape": "0xf0831", "shape-circle-plus": "0xf065d", "shape-outline": "0xf0832", "shape-oval-plus": "0xf11fa", "shape-plus": "0xf0495", "shape-polygon-plus": "0xf065e", "shape-rectangle-plus": "0xf065f", "shape-square-plus": "0xf0660", "shape-square-rounded-plus": "0xf14fa", "share": "0xf0496", "share-all": "0xf11f4", "share-all-outline": "0xf11f5", "share-circle": "0xf11ad", "share-off": "0xf0f23", "share-off-outline": "0xf0f24", "share-outline": "0xf0932", "share-variant": "0xf0497", "share-variant-outline": "0xf1514", "shark-fin": "0xf1673", "shark-fin-outline": "0xf1674", "sheep": "0xf0cc6", "shield": "0xf0498", "shield-account": "0xf088f", "shield-account-outline": "0xf0a12", "shield-account-variant": "0xf15a7", "shield-account-variant-outline": "0xf15a8", "shield-airplane": "0xf06bb", "shield-airplane-outline": "0xf0cc7", "shield-alert": "0xf0ecc", "shield-alert-outline": "0xf0ecd", "shield-bug": "0xf13da", "shield-bug-outline": "0xf13db", "shield-car": "0xf0f83", "shield-check": "0xf0565", "shield-check-outline": "0xf0cc8", "shield-cross": "0xf0cc9", "shield-cross-outline": "0xf0cca", "shield-edit": "0xf11a0", "shield-edit-outline": "0xf11a1", "shield-half": "0xf1360", "shield-half-full": "0xf0780", "shield-home": "0xf068a", "shield-home-outline": "0xf0ccb", "shield-key": "0xf0bc4", "shield-key-outline": "0xf0bc5", "shield-link-variant": "0xf0d33", "shield-link-variant-outline": "0xf0d34", "shield-lock": "0xf099d", "shield-lock-outline": "0xf0ccc", "shield-off": "0xf099e", "shield-off-outline": "0xf099c", "shield-outline": "0xf0499", "shield-plus": "0xf0ada", "shield-plus-outline": "0xf0adb", "shield-refresh": "0xf00aa", "shield-refresh-outline": "0xf01e0", "shield-remove": "0xf0adc", "shield-remove-outline": "0xf0add", "shield-search": "0xf0d9a", "shield-star": "0xf113b", "shield-star-outline": "0xf113c", "shield-sun": "0xf105d", "shield-sun-outline": "0xf105e", "shield-sync": "0xf11a2", "shield-sync-outline": "0xf11a3", "ship-wheel": "0xf0833", "shoe-ballet": "0xf15ca", "shoe-cleat": "0xf15c7", "shoe-formal": "0xf0b47", "shoe-heel": "0xf0b48", "shoe-print": "0xf0dfa", "shoe-sneaker": "0xf15c8", "shopping": "0xf049a", "shopping-music": "0xf049b", "shopping-outline": "0xf11d5", "shopping-search": "0xf0f84", "shore": "0xf14f9", "shovel": "0xf0710", "shovel-off": "0xf0711", "shower": "0xf09a0", "shower-head": "0xf09a1", "shredder": "0xf049c", "shuffle": "0xf049d", "shuffle-disabled": "0xf049e", "shuffle-variant": "0xf049f", "shuriken": "0xf137f", "sigma": "0xf04a0", "sigma-lower": "0xf062b", "sign-caution": "0xf04a1", "sign-direction": "0xf0781", "sign-direction-minus": "0xf1000", "sign-direction-plus": "0xf0fdc", "sign-direction-remove": "0xf0fdd", "sign-pole": "0xf14f8", "sign-real-estate": "0xf1118", "sign-text": "0xf0782", "signal": "0xf04a2", "signal-2g": "0xf0712", "signal-3g": "0xf0713", "signal-4g": "0xf0714", "signal-5g": "0xf0a6f", "signal-cellular-1": "0xf08bc", "signal-cellular-2": "0xf08bd", "signal-cellular-3": "0xf08be", "signal-cellular-outline": "0xf08bf", "signal-distance-variant": "0xf0e64", "signal-hspa": "0xf0715", "signal-hspa-plus": "0xf0716", "signal-off": "0xf0783", "signal-variant": "0xf060a", "signature": "0xf0dfb", "signature-freehand": "0xf0dfc", "signature-image": "0xf0dfd", "signature-text": "0xf0dfe", "silo": "0xf0b49", "silverware": "0xf04a3", "silverware-clean": "0xf0fde", "silverware-fork": "0xf04a4", "silverware-fork-knife": "0xf0a70", "silverware-spoon": "0xf04a5", "silverware-variant": "0xf04a6", "sim": "0xf04a7", "sim-alert": "0xf04a8", "sim-alert-outline": "0xf15d3", "sim-off": "0xf04a9", "sim-off-outline": "0xf15d4", "sim-outline": "0xf15d5", "simple-icons": "0xf131d", "sina-weibo": "0xf0adf", "sine-wave": "0xf095b", "sitemap": "0xf04aa", "size-l": "0xf13a6", "size-m": "0xf13a5", "size-s": "0xf13a4", "size-xl": "0xf13a7", "size-xs": "0xf13a3", "size-xxl": "0xf13a8", "size-xxs": "0xf13a2", "size-xxxl": "0xf13a9", "skate": "0xf0d35", "skateboard": "0xf14c2", "skew-less": "0xf0d36", "skew-more": "0xf0d37", "ski": "0xf1304", "ski-cross-country": "0xf1305", "ski-water": "0xf1306", "skip-backward": "0xf04ab", "skip-backward-outline": "0xf0f25", "skip-forward": "0xf04ac", "skip-forward-outline": "0xf0f26", "skip-next": "0xf04ad", "skip-next-circle": "0xf0661", "skip-next-circle-outline": "0xf0662", "skip-next-outline": "0xf0f27", "skip-previous": "0xf04ae", "skip-previous-circle": "0xf0663", "skip-previous-circle-outline": "0xf0664", "skip-previous-outline": "0xf0f28", "skull": "0xf068c", "skull-crossbones": "0xf0bc6", "skull-crossbones-outline": "0xf0bc7", "skull-outline": "0xf0bc8", "skull-scan": "0xf14c7", "skull-scan-outline": "0xf14c8", "skype": "0xf04af", "skype-business": "0xf04b0", "slack": "0xf04b1", "slash-forward": "0xf0fdf", "slash-forward-box": "0xf0fe0", "sleep": "0xf04b2", "sleep-off": "0xf04b3", "slide": "0xf15a5", "slope-downhill": "0xf0dff", "slope-uphill": "0xf0e00", "slot-machine": "0xf1114", "slot-machine-outline": "0xf1115", "smart-card": "0xf10bd", "smart-card-outline": "0xf10be", "smart-card-reader": "0xf10bf", "smart-card-reader-outline": "0xf10c0", "smog": "0xf0a71", "smoke-detector": "0xf0392", "smoking": "0xf04b4", "smoking-off": "0xf04b5", "smoking-pipe": "0xf140d", "smoking-pipe-off": "0xf1428", "snail": "0xf1677", "snake": "0xf150e", "snapchat": "0xf04b6", "snowboard": "0xf1307", "snowflake": "0xf0717", "snowflake-alert": "0xf0f29", "snowflake-melt": "0xf12cb", "snowflake-off": "0xf14e3", "snowflake-variant": "0xf0f2a", "snowman": "0xf04b7", "soccer": "0xf04b8", "soccer-field": "0xf0834", "social-distance-2-meters": "0xf1579", "social-distance-6-feet": "0xf157a", "sofa": "0xf04b9", "sofa-outline": "0xf156d", "sofa-single": "0xf156e", "sofa-single-outline": "0xf156f", "solar-panel": "0xf0d9b", "solar-panel-large": "0xf0d9c", "solar-power": "0xf0a72", "soldering-iron": "0xf1092", "solid": "0xf068d", "sony-playstation": "0xf0414", "sort": "0xf04ba", "sort-alphabetical-ascending": "0xf05bd", "sort-alphabetical-ascending-variant": "0xf1148", "sort-alphabetical-descending": "0xf05bf", "sort-alphabetical-descending-variant": "0xf1149", "sort-alphabetical-variant": "0xf04bb", "sort-ascending": "0xf04bc", "sort-bool-ascending": "0xf1385", "sort-bool-ascending-variant": "0xf1386", "sort-bool-descending": "0xf1387", "sort-bool-descending-variant": "0xf1388", "sort-calendar-ascending": "0xf1547", "sort-calendar-descending": "0xf1548", "sort-clock-ascending": "0xf1549", "sort-clock-ascending-outline": "0xf154a", "sort-clock-descending": "0xf154b", "sort-clock-descending-outline": "0xf154c", "sort-descending": "0xf04bd", "sort-numeric-ascending": "0xf1389", "sort-numeric-ascending-variant": "0xf090d", "sort-numeric-descending": "0xf138a", "sort-numeric-descending-variant": "0xf0ad2", "sort-numeric-variant": "0xf04be", "sort-reverse-variant": "0xf033c", "sort-variant": "0xf04bf", "sort-variant-lock": "0xf0ccd", "sort-variant-lock-open": "0xf0cce", "sort-variant-remove": "0xf1147", "soundcloud": "0xf04c0", "source-branch": "0xf062c", "source-branch-check": "0xf14cf", "source-branch-minus": "0xf14cb", "source-branch-plus": "0xf14ca", "source-branch-refresh": "0xf14cd", "source-branch-remove": "0xf14cc", "source-branch-sync": "0xf14ce", "source-commit": "0xf0718", "source-commit-end": "0xf0719", "source-commit-end-local": "0xf071a", "source-commit-local": "0xf071b", "source-commit-next-local": "0xf071c", "source-commit-start": "0xf071d", "source-commit-start-next-local": "0xf071e", "source-fork": "0xf04c1", "source-merge": "0xf062d", "source-pull": "0xf04c2", "source-repository": "0xf0ccf", "source-repository-multiple": "0xf0cd0", "soy-sauce": "0xf07ee", "soy-sauce-off": "0xf13fc", "spa": "0xf0cd1", "spa-outline": "0xf0cd2", "space-invaders": "0xf0bc9", "space-station": "0xf1383", "spade": "0xf0e65", "sparkles": "0xf1545", "speaker": "0xf04c3", "speaker-bluetooth": "0xf09a2", "speaker-multiple": "0xf0d38", "speaker-off": "0xf04c4", "speaker-wireless": "0xf071f", "speedometer": "0xf04c5", "speedometer-medium": "0xf0f85", "speedometer-slow": "0xf0f86", "spellcheck": "0xf04c6", "spider": "0xf11ea", "spider-thread": "0xf11eb", "spider-web": "0xf0bca", "spirit-level": "0xf14f1", "spoon-sugar": "0xf1429", "spotify": "0xf04c7", "spotlight": "0xf04c8", "spotlight-beam": "0xf04c9", "spray": "0xf0665", "spray-bottle": "0xf0ae0", "sprinkler": "0xf105f", "sprinkler-variant": "0xf1060", "sprout": "0xf0e66", "sprout-outline": "0xf0e67", "square": "0xf0764", "square-circle": "0xf1500", "square-edit-outline": "0xf090c", "square-medium": "0xf0a13", "square-medium-outline": "0xf0a14", "square-off": "0xf12ee", "square-off-outline": "0xf12ef", "square-outline": "0xf0763", "square-root": "0xf0784", "square-root-box": "0xf09a3", "square-rounded": "0xf14fb", "square-rounded-outline": "0xf14fc", "square-small": "0xf0a15", "square-wave": "0xf147b", "squeegee": "0xf0ae1", "ssh": "0xf08c0", "stack-exchange": "0xf060b", "stack-overflow": "0xf04cc", "stackpath": "0xf0359", "stadium": "0xf0ff9", "stadium-variant": "0xf0720", "stairs": "0xf04cd", "stairs-box": "0xf139e", "stairs-down": "0xf12be", "stairs-up": "0xf12bd", "stamper": "0xf0d39", "standard-definition": "0xf07ef", "star": "0xf04ce", "star-box": "0xf0a73", "star-box-multiple": "0xf1286", "star-box-multiple-outline": "0xf1287", "star-box-outline": "0xf0a74", "star-check": "0xf1566", "star-check-outline": "0xf156a", "star-circle": "0xf04cf", "star-circle-outline": "0xf09a4", "star-cog": "0xf1668", "star-cog-outline": "0xf1669", "star-face": "0xf09a5", "star-four-points": "0xf0ae2", "star-four-points-outline": "0xf0ae3", "star-half": "0xf0246", "star-half-full": "0xf04d0", "star-minus": "0xf1564", "star-minus-outline": "0xf1568", "star-off": "0xf04d1", "star-off-outline": "0xf155b", "star-outline": "0xf04d2", "star-plus": "0xf1563", "star-plus-outline": "0xf1567", "star-remove": "0xf1565", "star-remove-outline": "0xf1569", "star-settings": "0xf166a", "star-settings-outline": "0xf166b", "star-shooting": "0xf1741", "star-shooting-outline": "0xf1742", "star-three-points": "0xf0ae4", "star-three-points-outline": "0xf0ae5", "state-machine": "0xf11ef", "steam": "0xf04d3", "steering": "0xf04d4", "steering-off": "0xf090e", "step-backward": "0xf04d5", "step-backward-2": "0xf04d6", "step-forward": "0xf04d7", "step-forward-2": "0xf04d8", "stethoscope": "0xf04d9", "sticker": "0xf1364", "sticker-alert": "0xf1365", "sticker-alert-outline": "0xf1366", "sticker-check": "0xf1367", "sticker-check-outline": "0xf1368", "sticker-circle-outline": "0xf05d0", "sticker-emoji": "0xf0785", "sticker-minus": "0xf1369", "sticker-minus-outline": "0xf136a", "sticker-outline": "0xf136b", "sticker-plus": "0xf136c", "sticker-plus-outline": "0xf136d", "sticker-remove": "0xf136e", "sticker-remove-outline": "0xf136f", "stocking": "0xf04da", "stomach": "0xf1093", "stop": "0xf04db", "stop-circle": "0xf0666", "stop-circle-outline": "0xf0667", "store": "0xf04dc", "store-24-hour": "0xf04dd", "store-minus": "0xf165e", "store-outline": "0xf1361", "store-plus": "0xf165f", "store-remove": "0xf1660", "storefront": "0xf07c7", "storefront-outline": "0xf10c1", "stove": "0xf04de", "strategy": "0xf11d6", "stretch-to-page": "0xf0f2b", "stretch-to-page-outline": "0xf0f2c", "string-lights": "0xf12ba", "string-lights-off": "0xf12bb", "subdirectory-arrow-left": "0xf060c", "subdirectory-arrow-right": "0xf060d", "submarine": "0xf156c", "subtitles": "0xf0a16", "subtitles-outline": "0xf0a17", "subway": "0xf06ac", "subway-alert-variant": "0xf0d9d", "subway-variant": "0xf04df", "summit": "0xf0786", "sunglasses": "0xf04e0", "surround-sound": "0xf05c5", "surround-sound-2-0": "0xf07f0", "surround-sound-2-1": "0xf1729", "surround-sound-3-1": "0xf07f1", "surround-sound-5-1": "0xf07f2", "surround-sound-5-1-2": "0xf172a", "surround-sound-7-1": "0xf07f3", "svg": "0xf0721", "swap-horizontal": "0xf04e1", "swap-horizontal-bold": "0xf0bcd", "swap-horizontal-circle": "0xf0fe1", "swap-horizontal-circle-outline": "0xf0fe2", "swap-horizontal-variant": "0xf08c1", "swap-vertical": "0xf04e2", "swap-vertical-bold": "0xf0bce", "swap-vertical-circle": "0xf0fe3", "swap-vertical-circle-outline": "0xf0fe4", "swap-vertical-variant": "0xf08c2", "swim": "0xf04e3", "switch": "0xf04e4", "sword": "0xf04e5", "sword-cross": "0xf0787", "syllabary-hangul": "0xf1333", "syllabary-hiragana": "0xf1334", "syllabary-katakana": "0xf1335", "syllabary-katakana-halfwidth": "0xf1336", "symbol": "0xf1501", "symfony": "0xf0ae6", "sync": "0xf04e6", "sync-alert": "0xf04e7", "sync-circle": "0xf1378", "sync-off": "0xf04e8", "tab": "0xf04e9", "tab-minus": "0xf0b4b", "tab-plus": "0xf075c", "tab-remove": "0xf0b4c", "tab-unselected": "0xf04ea", "table": "0xf04eb", "table-account": "0xf13b9", "table-alert": "0xf13ba", "table-arrow-down": "0xf13bb", "table-arrow-left": "0xf13bc", "table-arrow-right": "0xf13bd", "table-arrow-up": "0xf13be", "table-border": "0xf0a18", "table-cancel": "0xf13bf", "table-chair": "0xf1061", "table-check": "0xf13c0", "table-clock": "0xf13c1", "table-cog": "0xf13c2", "table-column": "0xf0835", "table-column-plus-after": "0xf04ec", "table-column-plus-before": "0xf04ed", "table-column-remove": "0xf04ee", "table-column-width": "0xf04ef", "table-edit": "0xf04f0", "table-eye": "0xf1094", "table-eye-off": "0xf13c3", "table-furniture": "0xf05bc", "table-headers-eye": "0xf121d", "table-headers-eye-off": "0xf121e", "table-heart": "0xf13c4", "table-key": "0xf13c5", "table-large": "0xf04f1", "table-large-plus": "0xf0f87", "table-large-remove": "0xf0f88", "table-lock": "0xf13c6", "table-merge-cells": "0xf09a6", "table-minus": "0xf13c7", "table-multiple": "0xf13c8", "table-network": "0xf13c9", "table-of-contents": "0xf0836", "table-off": "0xf13ca", "table-picnic": "0xf1743", "table-plus": "0xf0a75", "table-refresh": "0xf13a0", "table-remove": "0xf0a76", "table-row": "0xf0837", "table-row-height": "0xf04f2", "table-row-plus-after": "0xf04f3", "table-row-plus-before": "0xf04f4", "table-row-remove": "0xf04f5", "table-search": "0xf090f", "table-settings": "0xf0838", "table-split-cell": "0xf142a", "table-star": "0xf13cb", "table-sync": "0xf13a1", "table-tennis": "0xf0e68", "tablet": "0xf04f6", "tablet-android": "0xf04f7", "tablet-cellphone": "0xf09a7", "tablet-dashboard": "0xf0ece", "tablet-ipad": "0xf04f8", "taco": "0xf0762", "tag": "0xf04f9", "tag-arrow-down": "0xf172b", "tag-arrow-down-outline": "0xf172c", "tag-arrow-left": "0xf172d", "tag-arrow-left-outline": "0xf172e", "tag-arrow-right": "0xf172f", "tag-arrow-right-outline": "0xf1730", "tag-arrow-up": "0xf1731", "tag-arrow-up-outline": "0xf1732", "tag-faces": "0xf04fa", "tag-heart": "0xf068b", "tag-heart-outline": "0xf0bcf", "tag-minus": "0xf0910", "tag-minus-outline": "0xf121f", "tag-multiple": "0xf04fb", "tag-multiple-outline": "0xf12f7", "tag-off": "0xf1220", "tag-off-outline": "0xf1221", "tag-outline": "0xf04fc", "tag-plus": "0xf0722", "tag-plus-outline": "0xf1222", "tag-remove": "0xf0723", "tag-remove-outline": "0xf1223", "tag-text": "0xf1224", "tag-text-outline": "0xf04fd", "tailwind": "0xf13ff", "tank": "0xf0d3a", "tanker-truck": "0xf0fe5", "tape-drive": "0xf16df", "tape-measure": "0xf0b4d", "target": "0xf04fe", "target-account": "0xf0bd0", "target-variant": "0xf0a77", "taxi": "0xf04ff", "tea": "0xf0d9e", "tea-outline": "0xf0d9f", "teach": "0xf0890", "teamviewer": "0xf0500", "telegram": "0xf0501", "telescope": "0xf0b4e", "television": "0xf0502", "television-ambient-light": "0xf1356", "television-box": "0xf0839", "television-classic": "0xf07f4", "television-classic-off": "0xf083a", "television-clean": "0xf1110", "television-guide": "0xf0503", "television-off": "0xf083b", "television-pause": "0xf0f89", "television-play": "0xf0ecf", "television-stop": "0xf0f8a", "temperature-celsius": "0xf0504", "temperature-fahrenheit": "0xf0505", "temperature-kelvin": "0xf0506", "tennis": "0xf0da0", "tennis-ball": "0xf0507", "tent": "0xf0508", "terraform": "0xf1062", "terrain": "0xf0509", "test-tube": "0xf0668", "test-tube-empty": "0xf0911", "test-tube-off": "0xf0912", "text": "0xf09a8", "text-account": "0xf1570", "text-box": "0xf021a", "text-box-check": "0xf0ea6", "text-box-check-outline": "0xf0ea7", "text-box-minus": "0xf0ea8", "text-box-minus-outline": "0xf0ea9", "text-box-multiple": "0xf0ab7", "text-box-multiple-outline": "0xf0ab8", "text-box-outline": "0xf09ed", "text-box-plus": "0xf0eaa", "text-box-plus-outline": "0xf0eab", "text-box-remove": "0xf0eac", "text-box-remove-outline": "0xf0ead", "text-box-search": "0xf0eae", "text-box-search-outline": "0xf0eaf", "text-recognition": "0xf113d", "text-search": "0xf13b8", "text-shadow": "0xf0669", "text-short": "0xf09a9", "text-subject": "0xf09aa", "text-to-speech": "0xf050a", "text-to-speech-off": "0xf050b", "texture": "0xf050c", "texture-box": "0xf0fe6", "theater": "0xf050d", "theme-light-dark": "0xf050e", "thermometer": "0xf050f", "thermometer-alert": "0xf0e01", "thermometer-chevron-down": "0xf0e02", "thermometer-chevron-up": "0xf0e03", "thermometer-high": "0xf10c2", "thermometer-lines": "0xf0510", "thermometer-low": "0xf10c3", "thermometer-minus": "0xf0e04", "thermometer-off": "0xf1531", "thermometer-plus": "0xf0e05", "thermostat": "0xf0393", "thermostat-box": "0xf0891", "thought-bubble": "0xf07f6", "thought-bubble-outline": "0xf07f7", "thumb-down": "0xf0511", "thumb-down-outline": "0xf0512", "thumb-up": "0xf0513", "thumb-up-outline": "0xf0514", "thumbs-up-down": "0xf0515", "ticket": "0xf0516", "ticket-account": "0xf0517", "ticket-confirmation": "0xf0518", "ticket-confirmation-outline": "0xf13aa", "ticket-outline": "0xf0913", "ticket-percent": "0xf0724", "ticket-percent-outline": "0xf142b", "tie": "0xf0519", "tilde": "0xf0725", "timelapse": "0xf051a", "timeline": "0xf0bd1", "timeline-alert": "0xf0f95", "timeline-alert-outline": "0xf0f98", "timeline-check": "0xf1532", "timeline-check-outline": "0xf1533", "timeline-clock": "0xf11fb", "timeline-clock-outline": "0xf11fc", "timeline-help": "0xf0f99", "timeline-help-outline": "0xf0f9a", "timeline-minus": "0xf1534", "timeline-minus-outline": "0xf1535", "timeline-outline": "0xf0bd2", "timeline-plus": "0xf0f96", "timeline-plus-outline": "0xf0f97", "timeline-remove": "0xf1536", "timeline-remove-outline": "0xf1537", "timeline-text": "0xf0bd3", "timeline-text-outline": "0xf0bd4", "timer": "0xf13ab", "timer-10": "0xf051c", "timer-3": "0xf051d", "timer-off": "0xf13ac", "timer-off-outline": "0xf051e", "timer-outline": "0xf051b", "timer-sand": "0xf051f", "timer-sand-empty": "0xf06ad", "timer-sand-full": "0xf078c", "timetable": "0xf0520", "toaster": "0xf1063", "toaster-off": "0xf11b7", "toaster-oven": "0xf0cd3", "toggle-switch": "0xf0521", "toggle-switch-off": "0xf0522", "toggle-switch-off-outline": "0xf0a19", "toggle-switch-outline": "0xf0a1a", "toilet": "0xf09ab", "toolbox": "0xf09ac", "toolbox-outline": "0xf09ad", "tools": "0xf1064", "tooltip": "0xf0523", "tooltip-account": "0xf000c", "tooltip-check": "0xf155c", "tooltip-check-outline": "0xf155d", "tooltip-edit": "0xf0524", "tooltip-edit-outline": "0xf12c5", "tooltip-image": "0xf0525", "tooltip-image-outline": "0xf0bd5", "tooltip-minus": "0xf155e", "tooltip-minus-outline": "0xf155f", "tooltip-outline": "0xf0526", "tooltip-plus": "0xf0bd6", "tooltip-plus-outline": "0xf0527", "tooltip-remove": "0xf1560", "tooltip-remove-outline": "0xf1561", "tooltip-text": "0xf0528", "tooltip-text-outline": "0xf0bd7", "tooth": "0xf08c3", "tooth-outline": "0xf0529", "toothbrush": "0xf1129", "toothbrush-electric": "0xf112c", "toothbrush-paste": "0xf112a", "torch": "0xf1606", "tortoise": "0xf0d3b", "toslink": "0xf12b8", "tournament": "0xf09ae", "tow-truck": "0xf083c", "tower-beach": "0xf0681", "tower-fire": "0xf0682", "toy-brick": "0xf1288", "toy-brick-marker": "0xf1289", "toy-brick-marker-outline": "0xf128a", "toy-brick-minus": "0xf128b", "toy-brick-minus-outline": "0xf128c", "toy-brick-outline": "0xf128d", "toy-brick-plus": "0xf128e", "toy-brick-plus-outline": "0xf128f", "toy-brick-remove": "0xf1290", "toy-brick-remove-outline": "0xf1291", "toy-brick-search": "0xf1292", "toy-brick-search-outline": "0xf1293", "track-light": "0xf0914", "trackpad": "0xf07f8", "trackpad-lock": "0xf0933", "tractor": "0xf0892", "tractor-variant": "0xf14c4", "trademark": "0xf0a78", "traffic-cone": "0xf137c", "traffic-light": "0xf052b", "train": "0xf052c", "train-car": "0xf0bd8", "train-car-passenger": "0xf1733", "train-car-passenger-door": "0xf1734", "train-car-passenger-door-open": "0xf1735", "train-car-passenger-variant": "0xf1736", "train-variant": "0xf08c4", "tram": "0xf052d", "tram-side": "0xf0fe7", "transcribe": "0xf052e", "transcribe-close": "0xf052f", "transfer": "0xf1065", "transfer-down": "0xf0da1", "transfer-left": "0xf0da2", "transfer-right": "0xf0530", "transfer-up": "0xf0da3", "transit-connection": "0xf0d3c", "transit-connection-horizontal": "0xf1546", "transit-connection-variant": "0xf0d3d", "transit-detour": "0xf0f8b", "transit-skip": "0xf1515", "transit-transfer": "0xf06ae", "transition": "0xf0915", "transition-masked": "0xf0916", "translate": "0xf05ca", "translate-off": "0xf0e06", "transmission-tower": "0xf0d3e", "trash-can": "0xf0a79", "trash-can-outline": "0xf0a7a", "tray": "0xf1294", "tray-alert": "0xf1295", "tray-full": "0xf1296", "tray-minus": "0xf1297", "tray-plus": "0xf1298", "tray-remove": "0xf1299", "treasure-chest": "0xf0726", "tree": "0xf0531", "tree-outline": "0xf0e69", "trello": "0xf0532", "trending-down": "0xf0533", "trending-neutral": "0xf0534", "trending-up": "0xf0535", "triangle": "0xf0536", "triangle-outline": "0xf0537", "triangle-wave": "0xf147c", "triforce": "0xf0bd9", "trophy": "0xf0538", "trophy-award": "0xf0539", "trophy-broken": "0xf0da4", "trophy-outline": "0xf053a", "trophy-variant": "0xf053b", "trophy-variant-outline": "0xf053c", "truck": "0xf053d", "truck-check": "0xf0cd4", "truck-check-outline": "0xf129a", "truck-delivery": "0xf053e", "truck-delivery-outline": "0xf129b", "truck-fast": "0xf0788", "truck-fast-outline": "0xf129c", "truck-outline": "0xf129d", "truck-trailer": "0xf0727", "trumpet": "0xf1096", "tshirt-crew": "0xf0a7b", "tshirt-crew-outline": "0xf053f", "tshirt-v": "0xf0a7c", "tshirt-v-outline": "0xf0540", "tumble-dryer": "0xf0917", "tumble-dryer-alert": "0xf11ba", "tumble-dryer-off": "0xf11bb", "tune": "0xf062e", "tune-variant": "0xf1542", "tune-vertical": "0xf066a", "tune-vertical-variant": "0xf1543", "turkey": "0xf171b", "turnstile": "0xf0cd5", "turnstile-outline": "0xf0cd6", "turtle": "0xf0cd7", "twitch": "0xf0543", "twitter": "0xf0544", "twitter-retweet": "0xf0547", "two-factor-authentication": "0xf09af", "typewriter": "0xf0f2d", "ubisoft": "0xf0bda", "ubuntu": "0xf0548", "ufo": "0xf10c4", "ufo-outline": "0xf10c5", "ultra-high-definition": "0xf07f9", "umbraco": "0xf0549", "umbrella": "0xf054a", "umbrella-closed": "0xf09b0", "umbrella-closed-outline": "0xf13e2", "umbrella-closed-variant": "0xf13e1", "umbrella-outline": "0xf054b", "undo": "0xf054c", "undo-variant": "0xf054d", "unfold-less-horizontal": "0xf054e", "unfold-less-vertical": "0xf0760", "unfold-more-horizontal": "0xf054f", "unfold-more-vertical": "0xf0761", "ungroup": "0xf0550", "unicode": "0xf0ed0", "unicorn": "0xf15c2", "unicorn-variant": "0xf15c3", "unicycle": "0xf15e5", "unity": "0xf06af", "unreal": "0xf09b1", "untappd": "0xf0551", "update": "0xf06b0", "upload": "0xf0552", "upload-lock": "0xf1373", "upload-lock-outline": "0xf1374", "upload-multiple": "0xf083d", "upload-network": "0xf06f6", "upload-network-outline": "0xf0cd8", "upload-off": "0xf10c6", "upload-off-outline": "0xf10c7", "upload-outline": "0xf0e07", "usb": "0xf0553", "usb-flash-drive": "0xf129e", "usb-flash-drive-outline": "0xf129f", "usb-port": "0xf11f0", "valve": "0xf1066", "valve-closed": "0xf1067", "valve-open": "0xf1068", "van-passenger": "0xf07fa", "van-utility": "0xf07fb", "vanish": "0xf07fc", "vanish-quarter": "0xf1554", "vanity-light": "0xf11e1", "variable": "0xf0ae7", "variable-box": "0xf1111", "vector-arrange-above": "0xf0554", "vector-arrange-below": "0xf0555", "vector-bezier": "0xf0ae8", "vector-circle": "0xf0556", "vector-circle-variant": "0xf0557", "vector-combine": "0xf0558", "vector-curve": "0xf0559", "vector-difference": "0xf055a", "vector-difference-ab": "0xf055b", "vector-difference-ba": "0xf055c", "vector-ellipse": "0xf0893", "vector-intersection": "0xf055d", "vector-line": "0xf055e", "vector-link": "0xf0fe8", "vector-point": "0xf055f", "vector-polygon": "0xf0560", "vector-polyline": "0xf0561", "vector-polyline-edit": "0xf1225", "vector-polyline-minus": "0xf1226", "vector-polyline-plus": "0xf1227", "vector-polyline-remove": "0xf1228", "vector-radius": "0xf074a", "vector-rectangle": "0xf05c6", "vector-selection": "0xf0562", "vector-square": "0xf0001", "vector-triangle": "0xf0563", "vector-union": "0xf0564", "vhs": "0xf0a1b", "vibrate": "0xf0566", "vibrate-off": "0xf0cd9", "video": "0xf0567", "video-3d": "0xf07fd", "video-3d-off": "0xf13d9", "video-3d-variant": "0xf0ed1", "video-4k-box": "0xf083e", "video-account": "0xf0919", "video-box": "0xf00fd", "video-box-off": "0xf00fe", "video-check": "0xf1069", "video-check-outline": "0xf106a", "video-high-definition": "0xf152e", "video-image": "0xf091a", "video-input-antenna": "0xf083f", "video-input-component": "0xf0840", "video-input-hdmi": "0xf0841", "video-input-scart": "0xf0f8c", "video-input-svideo": "0xf0842", "video-minus": "0xf09b2", "video-minus-outline": "0xf02ba", "video-off": "0xf0568", "video-off-outline": "0xf0bdb", "video-outline": "0xf0bdc", "video-plus": "0xf09b3", "video-plus-outline": "0xf01d3", "video-stabilization": "0xf091b", "video-switch": "0xf0569", "video-switch-outline": "0xf0790", "video-vintage": "0xf0a1c", "video-wireless": "0xf0ed2", "video-wireless-outline": "0xf0ed3", "view-agenda": "0xf056a", "view-agenda-outline": "0xf11d8", "view-array": "0xf056b", "view-array-outline": "0xf1485", "view-carousel": "0xf056c", "view-carousel-outline": "0xf1486", "view-column": "0xf056d", "view-column-outline": "0xf1487", "view-comfy": "0xf0e6a", "view-comfy-outline": "0xf1488", "view-compact": "0xf0e6b", "view-compact-outline": "0xf0e6c", "view-dashboard": "0xf056e", "view-dashboard-outline": "0xf0a1d", "view-dashboard-variant": "0xf0843", "view-dashboard-variant-outline": "0xf1489", "view-day": "0xf056f", "view-day-outline": "0xf148a", "view-grid": "0xf0570", "view-grid-outline": "0xf11d9", "view-grid-plus": "0xf0f8d", "view-grid-plus-outline": "0xf11da", "view-headline": "0xf0571", "view-list": "0xf0572", "view-list-outline": "0xf148b", "view-module": "0xf0573", "view-module-outline": "0xf148c", "view-parallel": "0xf0728", "view-parallel-outline": "0xf148d", "view-quilt": "0xf0574", "view-quilt-outline": "0xf148e", "view-sequential": "0xf0729", "view-sequential-outline": "0xf148f", "view-split-horizontal": "0xf0bcb", "view-split-vertical": "0xf0bcc", "view-stream": "0xf0575", "view-stream-outline": "0xf1490", "view-week": "0xf0576", "view-week-outline": "0xf1491", "vimeo": "0xf0577", "violin": "0xf060f", "virtual-reality": "0xf0894", "virus": "0xf13b6", "virus-outline": "0xf13b7", "vk": "0xf0579", "vlc": "0xf057c", "voice-off": "0xf0ed4", "voicemail": "0xf057d", "volleyball": "0xf09b4", "volume-high": "0xf057e", "volume-low": "0xf057f", "volume-medium": "0xf0580", "volume-minus": "0xf075e", "volume-mute": "0xf075f", "volume-off": "0xf0581", "volume-plus": "0xf075d", "volume-source": "0xf1120", "volume-variant-off": "0xf0e08", "volume-vibrate": "0xf1121", "vote": "0xf0a1f", "vote-outline": "0xf0a20", "vpn": "0xf0582", "vuejs": "0xf0844", "vuetify": "0xf0e6d", "walk": "0xf0583", "wall": "0xf07fe", "wall-sconce": "0xf091c", "wall-sconce-flat": "0xf091d", "wall-sconce-flat-variant": "0xf041c", "wall-sconce-round": "0xf0748", "wall-sconce-round-variant": "0xf091e", "wallet": "0xf0584", "wallet-giftcard": "0xf0585", "wallet-membership": "0xf0586", "wallet-outline": "0xf0bdd", "wallet-plus": "0xf0f8e", "wallet-plus-outline": "0xf0f8f", "wallet-travel": "0xf0587", "wallpaper": "0xf0e09", "wan": "0xf0588", "wardrobe": "0xf0f90", "wardrobe-outline": "0xf0f91", "warehouse": "0xf0f81", "washing-machine": "0xf072a", "washing-machine-alert": "0xf11bc", "washing-machine-off": "0xf11bd", "watch": "0xf0589", "watch-export": "0xf058a", "watch-export-variant": "0xf0895", "watch-import": "0xf058b", "watch-import-variant": "0xf0896", "watch-variant": "0xf0897", "watch-vibrate": "0xf06b1", "watch-vibrate-off": "0xf0cda", "water": "0xf058c", "water-alert": "0xf1502", "water-alert-outline": "0xf1503", "water-boiler": "0xf0f92", "water-boiler-alert": "0xf11b3", "water-boiler-off": "0xf11b4", "water-check": "0xf1504", "water-check-outline": "0xf1505", "water-minus": "0xf1506", "water-minus-outline": "0xf1507", "water-off": "0xf058d", "water-off-outline": "0xf1508", "water-outline": "0xf0e0a", "water-percent": "0xf058e", "water-percent-alert": "0xf1509", "water-plus": "0xf150a", "water-plus-outline": "0xf150b", "water-polo": "0xf12a0", "water-pump": "0xf058f", "water-pump-off": "0xf0f93", "water-remove": "0xf150c", "water-remove-outline": "0xf150d", "water-well": "0xf106b", "water-well-outline": "0xf106c", "watering-can": "0xf1481", "watering-can-outline": "0xf1482", "watermark": "0xf0612", "wave": "0xf0f2e", "waveform": "0xf147d", "waves": "0xf078d", "waze": "0xf0bde", "weather-cloudy": "0xf0590", "weather-cloudy-alert": "0xf0f2f", "weather-cloudy-arrow-right": "0xf0e6e", "weather-fog": "0xf0591", "weather-hail": "0xf0592", "weather-hazy": "0xf0f30", "weather-hurricane": "0xf0898", "weather-lightning": "0xf0593", "weather-lightning-rainy": "0xf067e", "weather-night": "0xf0594", "weather-night-partly-cloudy": "0xf0f31", "weather-partly-cloudy": "0xf0595", "weather-partly-lightning": "0xf0f32", "weather-partly-rainy": "0xf0f33", "weather-partly-snowy": "0xf0f34", "weather-partly-snowy-rainy": "0xf0f35", "weather-pouring": "0xf0596", "weather-rainy": "0xf0597", "weather-snowy": "0xf0598", "weather-snowy-heavy": "0xf0f36", "weather-snowy-rainy": "0xf067f", "weather-sunny": "0xf0599", "weather-sunny-alert": "0xf0f37", "weather-sunny-off": "0xf14e4", "weather-sunset": "0xf059a", "weather-sunset-down": "0xf059b", "weather-sunset-up": "0xf059c", "weather-tornado": "0xf0f38", "weather-windy": "0xf059d", "weather-windy-variant": "0xf059e", "web": "0xf059f", "web-box": "0xf0f94", "web-clock": "0xf124a", "webcam": "0xf05a0", "webcam-off": "0xf1737", "webhook": "0xf062f", "webpack": "0xf072b", "webrtc": "0xf1248", "wechat": "0xf0611", "weight": "0xf05a1", "weight-gram": "0xf0d3f", "weight-kilogram": "0xf05a2", "weight-lifter": "0xf115d", "weight-pound": "0xf09b5", "whatsapp": "0xf05a3", "wheel-barrow": "0xf14f2", "wheelchair-accessibility": "0xf05a4", "whistle": "0xf09b6", "whistle-outline": "0xf12bc", "white-balance-auto": "0xf05a5", "white-balance-incandescent": "0xf05a6", "white-balance-iridescent": "0xf05a7", "white-balance-sunny": "0xf05a8", "widgets": "0xf072c", "widgets-outline": "0xf1355", "wifi": "0xf05a9", "wifi-alert": "0xf16b5", "wifi-arrow-down": "0xf16b6", "wifi-arrow-left": "0xf16b7", "wifi-arrow-left-right": "0xf16b8", "wifi-arrow-right": "0xf16b9", "wifi-arrow-up": "0xf16ba", "wifi-arrow-up-down": "0xf16bb", "wifi-cancel": "0xf16bc", "wifi-check": "0xf16bd", "wifi-cog": "0xf16be", "wifi-lock": "0xf16bf", "wifi-lock-open": "0xf16c0", "wifi-marker": "0xf16c1", "wifi-minus": "0xf16c2", "wifi-off": "0xf05aa", "wifi-plus": "0xf16c3", "wifi-refresh": "0xf16c4", "wifi-remove": "0xf16c5", "wifi-settings": "0xf16c6", "wifi-star": "0xf0e0b", "wifi-strength-1": "0xf091f", "wifi-strength-1-alert": "0xf0920", "wifi-strength-1-lock": "0xf0921", "wifi-strength-1-lock-open": "0xf16cb", "wifi-strength-2": "0xf0922", "wifi-strength-2-alert": "0xf0923", "wifi-strength-2-lock": "0xf0924", "wifi-strength-2-lock-open": "0xf16cc", "wifi-strength-3": "0xf0925", "wifi-strength-3-alert": "0xf0926", "wifi-strength-3-lock": "0xf0927", "wifi-strength-3-lock-open": "0xf16cd", "wifi-strength-4": "0xf0928", "wifi-strength-4-alert": "0xf0929", "wifi-strength-4-lock": "0xf092a", "wifi-strength-4-lock-open": "0xf16ce", "wifi-strength-alert-outline": "0xf092b", "wifi-strength-lock-open-outline": "0xf16cf", "wifi-strength-lock-outline": "0xf092c", "wifi-strength-off": "0xf092d", "wifi-strength-off-outline": "0xf092e", "wifi-strength-outline": "0xf092f", "wifi-sync": "0xf16c7", "wikipedia": "0xf05ac", "wind-turbine": "0xf0da5", "window-close": "0xf05ad", "window-closed": "0xf05ae", "window-closed-variant": "0xf11db", "window-maximize": "0xf05af", "window-minimize": "0xf05b0", "window-open": "0xf05b1", "window-open-variant": "0xf11dc", "window-restore": "0xf05b2", "window-shutter": "0xf111c", "window-shutter-alert": "0xf111d", "window-shutter-open": "0xf111e", "windsock": "0xf15fa", "wiper": "0xf0ae9", "wiper-wash": "0xf0da6", "wizard-hat": "0xf1477", "wordpress": "0xf05b4", "wrap": "0xf05b6", "wrap-disabled": "0xf0bdf", "wrench": "0xf05b7", "wrench-outline": "0xf0be0", "xamarin": "0xf0845", "xamarin-outline": "0xf0846", "xing": "0xf05be", "xml": "0xf05c0", "xmpp": "0xf07ff", "y-combinator": "0xf0624", "yahoo": "0xf0b4f", "yeast": "0xf05c1", "yin-yang": "0xf0680", "yoga": "0xf117c", "youtube": "0xf05c3", "youtube-gaming": "0xf0848", "youtube-studio": "0xf0847", "youtube-subscription": "0xf0d40", "youtube-tv": "0xf0448", "yurt": "0xf1516", "z-wave": "0xf0aea", "zend": "0xf0aeb", "zigbee": "0xf0d41", "zip-box": "0xf05c4", "zip-box-outline": "0xf0ffa", "zip-disk": "0xf0a23", "zodiac-aquarius": "0xf0a7d", "zodiac-aries": "0xf0a7e", "zodiac-cancer": "0xf0a7f", "zodiac-capricorn": "0xf0a80", "zodiac-gemini": "0xf0a81", "zodiac-leo": "0xf0a82", "zodiac-libra": "0xf0a83", "zodiac-pisces": "0xf0a84", "zodiac-sagittarius": "0xf0a85", "zodiac-scorpio": "0xf0a86", "zodiac-taurus": "0xf0a87", "zodiac-virgo": "0xf0a88"}