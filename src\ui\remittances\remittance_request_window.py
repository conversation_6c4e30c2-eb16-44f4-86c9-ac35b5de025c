# -*- coding: utf-8 -*-
"""
شاشة طلب حوالة - نظام إدارة الحوالات
Remittance Request Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QFormLayout, QLabel, QPushButton, QLineEdit,
                               QComboBox, QTextEdit, QDateEdit,
                               QFrame, QGroupBox, QGridLayout, QMessageBox,
                               QProgressBar, QCheckBox, QSpinBox, QTabWidget,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QSplitter, QScrollArea, QToolBar, QStatusBar)
from PySide6.QtCore import Qt, Signal, QDate, QTimer, QThread
from PySide6.QtGui import QFont, QPixmap, QIcon, QAction

import sqlite3
from pathlib import Path
from datetime import datetime
import json
import uuid

class RemittanceRequestWindow(QMainWindow):
    """شاشة طلب حوالة الرئيسية"""
    
    # إشارات للتواصل مع النوافذ الأخرى
    remittance_request_created = Signal(dict)  # إشارة عند إنشاء طلب الحوالة
    send_to_create_remittance = Signal(dict)  # إشارة لإرسال البيانات لنافذة إنشاء الحوالة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("طلب حوالة - نظام إدارة الحوالات المتقدم")
        self.setMinimumSize(1200, 900)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()

        # متغيرات النافذة
        self.current_requests = []
        self.selected_request_id = None
        self.editing_request_id = None  # معرف الطلب قيد التحرير
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل البيانات الأولية
        self.load_requests()
        self.load_filters_data()
        
        # تطبيق الستايل العصري
        self.apply_modern_style()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # العنوان الرئيسي
        header_frame = self.create_header()
        layout.addWidget(header_frame)

        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #3498db;
                border-radius: 10px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 25px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب قائمة الطلبات
        requests_list_tab = self.create_requests_list_tab()
        self.tab_widget.addTab(requests_list_tab, "📋 قائمة طلبات الحوالات")

        # تبويب إنشاء طلب جديد
        new_request_tab = self.create_new_request_tab()
        self.tab_widget.addTab(new_request_tab, "➕ طلب حوالة جديد")

        # تبويب التقارير والإحصائيات
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "📊 التقارير والإحصائيات")

        layout.addWidget(self.tab_widget)

    def create_header(self):
        """إنشاء رأس النافذة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(25, 20, 25, 20)

        # أيقونة
        icon_label = QLabel("📝")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 56px;
                color: white;
                background: transparent;
            }
        """)
        layout.addWidget(icon_label)

        # النص
        text_layout = QVBoxLayout()
        
        title_label = QLabel("طلب حوالة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: bold;
                color: white;
                background: transparent;
            }
        """)
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("نظام إدارة طلبات الحوالات المتقدم")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #ecf0f1;
                background: transparent;
            }
        """)
        text_layout.addWidget(subtitle_label)

        layout.addLayout(text_layout)
        layout.addStretch()

        # إحصائيات سريعة
        stats_layout = QVBoxLayout()
        
        # عدد الطلبات اليوم
        today_requests_label = QLabel("طلبات اليوم: 0")
        today_requests_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: white;
                background: rgba(255,255,255,0.2);
                padding: 8px 15px;
                border-radius: 8px;
                font-weight: bold;
            }
        """)
        stats_layout.addWidget(today_requests_label)

        # الطلبات المعلقة
        pending_requests_label = QLabel("طلبات معلقة: 0")
        pending_requests_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: white;
                background: rgba(255,255,255,0.2);
                padding: 8px 15px;
                border-radius: 8px;
                font-weight: bold;
            }
        """)
        stats_layout.addWidget(pending_requests_label)

        layout.addLayout(stats_layout)

        return frame

    def create_requests_list_tab(self):
        """إنشاء تبويب قائمة الطلبات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم المرشحات
        filters_frame = self.create_filters_section()
        layout.addWidget(filters_frame)

        # جدول الطلبات
        self.requests_table = QTableWidget()
        self.requests_table.setColumnCount(10)
        self.requests_table.setHorizontalHeaderLabels([
            "رقم الطلب", "اسم المرسل", "اسم المستقبل", "المبلغ", 
            "العملة", "البلد المستقبل", "تاريخ الطلب", "الحالة", 
            "الأولوية", "الإجراءات"
        ])
        
        # تنسيق الجدول
        self.requests_table.horizontalHeader().setStretchLastSection(True)
        self.requests_table.setAlternatingRowColors(True)
        self.requests_table.setSelectionBehavior(QTableWidget.SelectRows)

        # زيادة ارتفاع الصفوف
        self.requests_table.verticalHeader().setDefaultSectionSize(50)

        self.requests_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 40px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                min-height: 35px;
            }
        """)
        
        layout.addWidget(self.requests_table)

        # أزرار العمليات
        buttons_frame = self.create_list_buttons_section()
        layout.addWidget(buttons_frame)

        return widget

    def create_filters_section(self):
        """إنشاء قسم المرشحات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)

        # مرشح الحالة
        layout.addWidget(QLabel("🔍 الحالة:"), 0, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "معلق", "مؤكد", "مرفوض", "مكتمل"])
        layout.addWidget(self.status_filter, 0, 1)

        # مرشح التاريخ من
        layout.addWidget(QLabel("📅 من تاريخ:"), 0, 2)
        self.date_from_filter = QDateEdit()
        self.date_from_filter.setDate(QDate.currentDate().addDays(-30))
        self.date_from_filter.setCalendarPopup(True)
        layout.addWidget(self.date_from_filter, 0, 3)

        # مرشح التاريخ إلى
        layout.addWidget(QLabel("📅 إلى تاريخ:"), 0, 4)
        self.date_to_filter = QDateEdit()
        self.date_to_filter.setDate(QDate.currentDate())
        self.date_to_filter.setCalendarPopup(True)
        layout.addWidget(self.date_to_filter, 0, 5)

        # زر البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        search_btn.clicked.connect(self.filter_requests)
        layout.addWidget(search_btn, 0, 6)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        refresh_btn.clicked.connect(self.load_requests)
        layout.addWidget(refresh_btn, 0, 7)

        return frame

    def create_list_buttons_section(self):
        """إنشاء قسم أزرار قائمة الطلبات"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.addStretch()

        # زر عرض التفاصيل
        view_details_btn = QPushButton("👁️ عرض التفاصيل")
        view_details_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        view_details_btn.clicked.connect(self.view_request_details)
        layout.addWidget(view_details_btn)

        # زر إرسال لإنشاء حوالة
        send_to_create_btn = QPushButton("💸 إرسال لإنشاء حوالة")
        send_to_create_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        send_to_create_btn.clicked.connect(self.send_selected_to_create_remittance)
        layout.addWidget(send_to_create_btn)

        # زر تحديث الحالة
        update_status_btn = QPushButton("📝 تحديث الحالة")
        update_status_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        update_status_btn.clicked.connect(self.update_request_status)
        layout.addWidget(update_status_btn)

        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(self.delete_request)
        layout.addWidget(delete_btn)

        return frame

    def create_new_request_tab(self):
        """إنشاء تبويب طلب جديد"""
        widget = QWidget()

        # إنشاء scroll area للتمرير
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(widget)

        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # البيانات الأساسية (قسم جديد)
        basic_data_group = self.create_basic_data_section()
        layout.addWidget(basic_data_group)

        # معلومات المرسل
        sender_group = self.create_sender_info_section()
        layout.addWidget(sender_group)

        # معلومات المستقبل
        receiver_group = self.create_receiver_info_section()
        layout.addWidget(receiver_group)

        # الملاحظات والخيارات
        notes_group = self.create_notes_options_section()
        layout.addWidget(notes_group)

        # أزرار الإجراءات
        actions_frame = self.create_new_request_buttons()
        layout.addWidget(actions_frame)

        layout.addStretch()

        # إنشاء container widget
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.addWidget(scroll)

        return container

    def create_basic_data_section(self):
        """إنشاء قسم البيانات الأساسية"""
        group = QGroupBox("📋 البيانات الأساسية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #27ae60;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # التاريخ
        layout.addWidget(QLabel("📅 التاريخ:"), 0, 0)
        self.request_date_input = QDateEdit()
        self.request_date_input.setDate(QDate.currentDate())
        self.request_date_input.setCalendarPopup(True)
        layout.addWidget(self.request_date_input, 0, 1)

        # رقم الطلب (تلقائي)
        layout.addWidget(QLabel("🔢 رقم الطلب:"), 0, 2)
        self.request_number_input = QLineEdit()
        self.request_number_input.setReadOnly(True)
        self.request_number_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                color: #6c757d;
                font-weight: bold;
            }
        """)
        # إنشاء رقم طلب تلقائي
        auto_number = f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"
        self.request_number_input.setText(auto_number)
        layout.addWidget(self.request_number_input, 0, 3)

        # اسم الصراف (قائمة منسدلة)
        layout.addWidget(QLabel("👤 اسم الصراف:"), 1, 0)
        self.exchanger_combo = QComboBox()
        self.exchanger_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(self.exchanger_combo, 1, 1)

        # العملة (قائمة منسدلة)
        layout.addWidget(QLabel("💱 العملة:"), 1, 2)
        self.currency_combo = QComboBox()
        self.currency_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(self.currency_combo, 1, 3)

        # مبلغ الحوالة
        layout.addWidget(QLabel("💰 مبلغ الحوالة:"), 2, 0)
        self.remittance_amount_input = QLineEdit()
        self.remittance_amount_input.setPlaceholderText("أدخل مبلغ الحوالة...")
        self.remittance_amount_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
        """)
        layout.addWidget(self.remittance_amount_input, 2, 1)

        # الغرض من التحويل
        layout.addWidget(QLabel("📝 الغرض من التحويل:"), 3, 0)
        self.transfer_purpose_input = QLineEdit()
        self.transfer_purpose_input.setPlaceholderText("أدخل الغرض من التحويل...")
        layout.addWidget(self.transfer_purpose_input, 3, 1, 1, 3)

        return group

    def create_sender_info_section(self):
        """إنشاء قسم معلومات المرسل"""
        group = QGroupBox("👤 معلومات المرسل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # اسم المرسل
        layout.addWidget(QLabel("👤 الاسم الكامل:"), 0, 0)
        self.sender_name_input = QLineEdit()
        self.sender_name_input.setPlaceholderText("أدخل الاسم الكامل للمرسل...")
        # تعبئة البيانات الافتراضية
        self.sender_name_input.setText("ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD")
        layout.addWidget(self.sender_name_input, 0, 1)

        # الجهة (بدلاً من رقم الهوية)
        layout.addWidget(QLabel("🏛️ الجهة:"), 0, 2)
        self.sender_entity_input = QLineEdit()
        self.sender_entity_input.setPlaceholderText("أدخل اسم الجهة...")
        # تعبئة البيانات الافتراضية
        self.sender_entity_input.setText("G.M: NASHA'AT RASHAD QASIM ALDUBAEE")
        layout.addWidget(self.sender_entity_input, 0, 3)

        # رقم الهاتف
        layout.addWidget(QLabel("📱 رقم الهاتف:"), 1, 0)
        self.sender_phone_input = QLineEdit()
        self.sender_phone_input.setPlaceholderText("أدخل رقم الهاتف...")
        # تعبئة البيانات الافتراضية
        self.sender_phone_input.setText("+967 1 616109")
        layout.addWidget(self.sender_phone_input, 1, 1)

        # رقم الفاكس
        layout.addWidget(QLabel("📠 رقم الفاكس:"), 1, 2)
        self.sender_fax_input = QLineEdit()
        self.sender_fax_input.setPlaceholderText("أدخل رقم الفاكس...")
        # تعبئة البيانات الافتراضية
        self.sender_fax_input.setText("+967 1 615909")
        layout.addWidget(self.sender_fax_input, 1, 3)

        # رقم الموبايل (نُقل إلى الصف الثاني)
        layout.addWidget(QLabel("📲 رقم الموبايل:"), 2, 0)
        self.sender_mobile_input = QLineEdit()
        self.sender_mobile_input.setPlaceholderText("أدخل رقم الموبايل...")
        # تعبئة البيانات الافتراضية
        self.sender_mobile_input.setText("+967 *********")
        layout.addWidget(self.sender_mobile_input, 2, 1)

        # ص.ب (صندوق بريد) (نُقل إلى الصف الثاني)
        layout.addWidget(QLabel("📮 ص.ب:"), 2, 2)
        self.sender_pobox_input = QLineEdit()
        self.sender_pobox_input.setPlaceholderText("أدخل صندوق البريد...")
        # تعبئة البيانات الافتراضية
        self.sender_pobox_input.setText("1903")
        layout.addWidget(self.sender_pobox_input, 2, 3)

        # البريد الإلكتروني
        layout.addWidget(QLabel("📧 البريد الإلكتروني:"), 3, 0)
        self.sender_email_input = QLineEdit()
        self.sender_email_input.setPlaceholderText("أدخل البريد الإلكتروني...")
        # تعبئة البيانات الافتراضية
        self.sender_email_input.setText("<EMAIL>, <EMAIL>")
        layout.addWidget(self.sender_email_input, 3, 1)

        # العنوان
        layout.addWidget(QLabel("📍 العنوان:"), 3, 2)
        self.sender_address_input = QLineEdit()
        self.sender_address_input.setPlaceholderText("أدخل العنوان...")
        # تعبئة البيانات الافتراضية
        self.sender_address_input.setText("TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN")
        layout.addWidget(self.sender_address_input, 3, 3)

        return group

    def create_receiver_info_section(self):
        """إنشاء قسم معلومات المستقبل"""
        group = QGroupBox("👥 معلومات المستقبل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # اسم المستقبل
        layout.addWidget(QLabel("👤 الاسم الكامل:"), 0, 0)
        self.receiver_name_input = QLineEdit()
        self.receiver_name_input.setPlaceholderText("أدخل الاسم الكامل للمستقبل...")
        layout.addWidget(self.receiver_name_input, 0, 1)

        # رقم الحساب (بدلاً من رقم الهوية)
        layout.addWidget(QLabel("🏦 رقم الحساب:"), 0, 2)
        self.receiver_account_input = QLineEdit()
        self.receiver_account_input.setPlaceholderText("أدخل رقم الحساب...")
        layout.addWidget(self.receiver_account_input, 0, 3)

        # اسم البنك (بدلاً من رقم الهاتف)
        layout.addWidget(QLabel("🏛️ اسم البنك:"), 1, 0)
        self.receiver_bank_input = QLineEdit()
        self.receiver_bank_input.setPlaceholderText("أدخل اسم البنك...")
        layout.addWidget(self.receiver_bank_input, 1, 1)

        # فرع البنك
        layout.addWidget(QLabel("🏢 فرع البنك:"), 1, 2)
        self.receiver_bank_branch_input = QLineEdit()
        self.receiver_bank_branch_input.setPlaceholderText("أدخل فرع البنك...")
        layout.addWidget(self.receiver_bank_branch_input, 1, 3)

        # السويفت
        layout.addWidget(QLabel("🔗 السويفت:"), 2, 0)
        self.receiver_swift_input = QLineEdit()
        self.receiver_swift_input.setPlaceholderText("أدخل رمز السويفت...")
        layout.addWidget(self.receiver_swift_input, 2, 1)

        # البلد
        layout.addWidget(QLabel("🌍 البلد:"), 2, 2)
        self.receiver_country_combo = QComboBox()
        self.receiver_country_combo.setEditable(True)
        layout.addWidget(self.receiver_country_combo, 2, 3)

        # العنوان
        layout.addWidget(QLabel("📍 العنوان:"), 3, 0)
        self.receiver_address_input = QLineEdit()
        self.receiver_address_input.setPlaceholderText("أدخل العنوان...")
        layout.addWidget(self.receiver_address_input, 3, 1, 1, 3)

        return group



    def create_notes_options_section(self):
        """إنشاء قسم الملاحظات والخيارات"""
        group = QGroupBox("📝 ملاحظات وخيارات إضافية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #9b59b6;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # الملاحظات
        notes_label = QLabel("📝 ملاحظات إضافية:")
        layout.addWidget(notes_label)

        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية حول طلب الحوالة...")
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                background-color: #fafafa;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: white;
            }
        """)
        layout.addWidget(self.notes_input)

        # الخيارات
        options_layout = QGridLayout()

        # إشعار SMS
        self.sms_notification_check = QCheckBox("📱 إرسال إشعار SMS للمستقبل")
        self.sms_notification_check.setChecked(True)
        options_layout.addWidget(self.sms_notification_check, 0, 0)

        # إشعار بريد إلكتروني
        self.email_notification_check = QCheckBox("📧 إرسال إشعار بريد إلكتروني")
        options_layout.addWidget(self.email_notification_check, 0, 1)

        # إنشاء حوالة تلقائي
        self.auto_create_remittance_check = QCheckBox("🚀 إنشاء حوالة تلقائياً عند الموافقة")
        self.auto_create_remittance_check.setChecked(True)
        options_layout.addWidget(self.auto_create_remittance_check, 1, 0, 1, 2)

        layout.addLayout(options_layout)

        return group

    def create_new_request_buttons(self):
        """إنشاء أزرار طلب جديد"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.addStretch()

        # زر حفظ الطلب
        save_request_btn = QPushButton("💾 حفظ طلب الحوالة")
        save_request_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 180px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        save_request_btn.clicked.connect(self.save_new_request)
        layout.addWidget(save_request_btn)

        # زر طباعة النموذج
        print_btn = QPushButton("🖨️ طباعة النموذج")
        print_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8e44ad, stop:1 #7d3c98);
            }
        """)
        print_btn.clicked.connect(self.print_request_form)
        layout.addWidget(print_btn)

        # زر إرسال لإنشاء حوالة
        send_to_create_btn = QPushButton("💸 إرسال لإنشاء حوالة")
        send_to_create_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        send_to_create_btn.clicked.connect(self.send_new_request_to_create_remittance)
        layout.addWidget(send_to_create_btn)

        # زر إلغاء التحرير
        self.cancel_edit_btn = QPushButton("❌ إلغاء التحرير")
        self.cancel_edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 180px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d68910);
            }
        """)
        self.cancel_edit_btn.clicked.connect(self.cancel_editing)
        self.cancel_edit_btn.setVisible(False)  # مخفي افتراضياً
        layout.addWidget(self.cancel_edit_btn)

        # زر مسح النموذج
        clear_form_btn = QPushButton("🗑️ مسح النموذج")
        clear_form_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        clear_form_btn.clicked.connect(self.clear_form)
        layout.addWidget(clear_form_btn)

        return frame

    def create_reports_tab(self):
        """إنشاء تبويب التقارير والإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # إحصائيات سريعة
        stats_frame = self.create_quick_stats()
        layout.addWidget(stats_frame)

        # رسم بياني للطلبات
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        chart_layout = QVBoxLayout(chart_frame)

        chart_title = QLabel("📊 إحصائيات طلبات الحوالات")
        chart_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        chart_layout.addWidget(chart_title)

        # هنا يمكن إضافة رسم بياني لاحقاً
        chart_placeholder = QLabel("📈 الرسم البياني قيد التطوير...")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                padding: 50px;
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
            }
        """)
        chart_layout.addWidget(chart_placeholder)

        layout.addWidget(chart_frame)
        layout.addStretch()

        return widget

    def create_quick_stats(self):
        """إنشاء إحصائيات سريعة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 20px;
            }
        """)

        layout = QGridLayout(frame)
        layout.setSpacing(20)

        # إجمالي الطلبات
        total_card = self.create_stat_card("📋", "إجمالي الطلبات", "0", "#3498db")
        layout.addWidget(total_card, 0, 0)

        # الطلبات المعلقة
        pending_card = self.create_stat_card("⏳", "طلبات معلقة", "0", "#f39c12")
        layout.addWidget(pending_card, 0, 1)

        # الطلبات المؤكدة
        confirmed_card = self.create_stat_card("✅", "طلبات مؤكدة", "0", "#27ae60")
        layout.addWidget(confirmed_card, 0, 2)

        # الطلبات المرفوضة
        rejected_card = self.create_stat_card("❌", "طلبات مرفوضة", "0", "#e74c3c")
        layout.addWidget(rejected_card, 0, 3)

        return frame

    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 15px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                color: {color};
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
                margin-bottom: 5px;
            }}
        """)
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)

        return card

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #34495e;
                border: none;
                spacing: 10px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QToolBar QToolButton:hover {
                background-color: #3498db;
            }
        """)

        # إنشاء طلب جديد
        new_request_action = QAction("➕ طلب جديد", self)
        new_request_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        toolbar.addAction(new_request_action)

        toolbar.addSeparator()

        # تحديث البيانات
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.load_requests)
        toolbar.addAction(refresh_action)

        # تصدير البيانات
        export_action = QAction("📤 تصدير", self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # الإعدادات
        settings_action = QAction("⚙️ الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                border-top: 1px solid #2c3e50;
                padding: 5px;
            }
        """)

        # رسالة الحالة
        self.status_bar.showMessage("جاهز - نظام طلب الحوالات")

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # ربط تغيير التبويب بتحديث البيانات
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # ربط اختيار صف في الجدول
        self.requests_table.itemSelectionChanged.connect(self.on_request_selected)

        # ربط النقر المزدوج لفتح الطلب للتعديل
        self.requests_table.itemDoubleClicked.connect(self.edit_selected_request)

    def apply_modern_style(self):
        """تطبيق الستايل العصري"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border-color: #3498db;
            }
            QLabel {
                color: #2c3e50;
                font-weight: 500;
            }
        """)

    def load_requests(self):
        """تحميل قائمة طلبات الحوالات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء الجدول إذا لم يكن موجوداً أو تحديثه
            self.create_or_update_table(cursor)

            # جلب البيانات مع التحقق من وجود الأعمدة
            try:
                cursor.execute("""
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency,
                           CASE WHEN receiver_country IS NOT NULL THEN receiver_country ELSE '' END as receiver_country,
                           created_at, status, priority
                    FROM remittance_requests
                    ORDER BY created_at DESC
                """)
            except sqlite3.OperationalError:
                # إذا فشل الاستعلام، استخدم الأعمدة الأساسية فقط
                cursor.execute("""
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency, '' as receiver_country, created_at, status,
                           CASE WHEN priority IS NOT NULL THEN priority ELSE 'عادي' END as priority
                    FROM remittance_requests
                    ORDER BY created_at DESC
                """)

            requests = cursor.fetchall()
            self.current_requests = requests

            # ملء الجدول
            self.populate_requests_table(requests)

            conn.close()

            # تحديث شريط الحالة
            self.status_bar.showMessage(f"تم تحميل {len(requests)} طلب حوالة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات:\n{str(e)}")

    def create_or_update_table(self, cursor):
        """إنشاء أو تحديث جدول طلبات الحوالة"""
        try:
            # إنشاء الجدول الأساسي إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS remittance_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    request_number TEXT UNIQUE NOT NULL,
                    sender_name TEXT NOT NULL,
                    receiver_name TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    source_currency TEXT NOT NULL,
                    target_currency TEXT NOT NULL,
                    status TEXT DEFAULT 'معلق',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # قائمة الأعمدة الجديدة التي نريد إضافتها
            new_columns = [
                ("sender_phone", "TEXT"),
                ("sender_id", "TEXT"),
                ("sender_email", "TEXT"),
                ("sender_address", "TEXT"),
                ("receiver_phone", "TEXT"),
                ("receiver_id", "TEXT"),
                ("receiver_country", "TEXT"),
                ("receiver_city", "TEXT"),
                ("receiver_address", "TEXT"),
                ("exchange_rate", "DECIMAL(10,4) DEFAULT 1.0000"),
                ("sender_bank", "TEXT"),
                ("receiver_bank", "TEXT"),
                ("transfer_date", "DATE"),
                ("priority", "TEXT DEFAULT 'عادي'"),
                ("notes", "TEXT"),
                ("sms_notification", "BOOLEAN DEFAULT 1"),
                ("email_notification", "BOOLEAN DEFAULT 0"),
                ("auto_create_remittance", "BOOLEAN DEFAULT 1"),
                ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            ]

            # فحص الأعمدة الموجودة
            cursor.execute("PRAGMA table_info(remittance_requests)")
            existing_columns = [column[1] for column in cursor.fetchall()]

            # إضافة الأعمدة المفقودة
            for column_name, column_type in new_columns:
                if column_name not in existing_columns:
                    try:
                        cursor.execute(f"ALTER TABLE remittance_requests ADD COLUMN {column_name} {column_type}")
                        print(f"تم إضافة العمود: {column_name}")
                    except sqlite3.OperationalError as e:
                        print(f"خطأ في إضافة العمود {column_name}: {e}")

        except Exception as e:
            print(f"خطأ في إنشاء/تحديث الجدول: {e}")

    def populate_requests_table(self, requests):
        """ملء جدول الطلبات"""
        self.requests_table.setRowCount(len(requests))

        for row, request in enumerate(requests):
            # رقم الطلب
            item = QTableWidgetItem(str(request[1]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 0, item)

            # اسم المرسل
            item = QTableWidgetItem(str(request[2]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 1, item)

            # اسم المستقبل
            item = QTableWidgetItem(str(request[3]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 2, item)

            # المبلغ
            item = QTableWidgetItem(f"{request[4]:.2f}")
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 3, item)

            # العملة
            item = QTableWidgetItem(str(request[5]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 4, item)

            # البلد المستقبل
            item = QTableWidgetItem(str(request[6] or ""))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 5, item)

            # تاريخ الطلب
            date_str = request[7][:10] if request[7] else ""
            item = QTableWidgetItem(date_str)
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 6, item)

            # الحالة
            status_item = QTableWidgetItem(str(request[8]))
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            if request[8] == "معلق":
                status_item.setBackground(Qt.yellow)
            elif request[8] == "مؤكد":
                status_item.setBackground(Qt.green)
            elif request[8] == "مرفوض":
                status_item.setBackground(Qt.red)
            self.requests_table.setItem(row, 7, status_item)

            # الأولوية
            priority_item = QTableWidgetItem(str(request[9]))
            priority_item.setFlags(priority_item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            if request[9] == "طارئ":
                priority_item.setBackground(Qt.red)
            elif request[9] == "عاجل":
                priority_item.setBackground(Qt.yellow)
            self.requests_table.setItem(row, 8, priority_item)

            # أزرار الإجراءات
            actions_btn = QPushButton("⚙️ إجراءات")
            actions_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            self.requests_table.setCellWidget(row, 9, actions_btn)

    def load_filters_data(self):
        """تحميل بيانات المرشحات من إدارة البنوك وإعدادات النظام"""
        print("📊 تحميل بيانات النموذج من قاعدة البيانات...")
        self.load_currencies_from_system_settings()
        self.load_exchangers_from_bank_management()
        self.load_countries_data()
        print("✅ تم تحميل جميع البيانات بنجاح")

    def load_currencies_from_system_settings(self):
        """تحميل العملات من إعدادات النظام"""
        print("💱 تحميل العملات من إعدادات النظام...")
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # محاولة تحميل العملات من جدول إعدادات النظام
            try:
                # البحث في جدول إعدادات النظام
                cursor.execute("""
                    SELECT currency_code, currency_name, currency_symbol, exchange_rate
                    FROM system_currencies
                    WHERE is_active = 1
                    ORDER BY currency_name
                """)
                currencies = cursor.fetchall()

                if not currencies:
                    # البحث في جدول بديل
                    cursor.execute("""
                        SELECT code, name, symbol, exchange_rate
                        FROM currencies
                        WHERE is_active = 1
                        ORDER BY name
                    """)
                    currencies = cursor.fetchall()

                if not currencies:
                    # إنشاء عملات افتراضية
                    self.create_default_currencies_for_system(cursor)
                    conn.commit()
                    cursor.execute("""
                        SELECT currency_code, currency_name, currency_symbol, exchange_rate
                        FROM system_currencies
                        WHERE is_active = 1
                        ORDER BY currency_name
                    """)
                    currencies = cursor.fetchall()

            except sqlite3.OperationalError:
                # إنشاء الجدول والعملات الافتراضية
                self.create_system_currencies_table(cursor)
                self.create_default_currencies_for_system(cursor)
                conn.commit()
                cursor.execute("""
                    SELECT currency_code, currency_name, currency_symbol, exchange_rate
                    FROM system_currencies
                    WHERE is_active = 1
                    ORDER BY currency_name
                """)
                currencies = cursor.fetchall()

            # تنظيف قائمة العملة
            self.currency_combo.clear()

            # إضافة خيار افتراضي
            self.currency_combo.addItem("اختر العملة...", None)

            # إضافة العملات
            for currency in currencies:
                symbol = currency[2] if len(currency) > 2 and currency[2] else currency[0]
                display_text = f"{currency[1]} ({symbol})"
                self.currency_combo.addItem(display_text, currency[0])

            # تعيين الريال اليمني كافتراضي
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == "YER":
                    self.currency_combo.setCurrentIndex(i)
                    break

            conn.close()
            print(f"✅ تم تحميل {len(currencies)} عملة من إعدادات النظام")

        except Exception as e:
            print(f"❌ خطأ في تحميل العملات: {e}")
            # في حالة الخطأ، إضافة عملات افتراضية
            self.load_default_currencies()

    def create_system_currencies_table(self, cursor):
        """إنشاء جدول العملات في إعدادات النظام"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                currency_code TEXT UNIQUE NOT NULL,
                currency_name TEXT NOT NULL,
                currency_symbol TEXT,
                exchange_rate REAL DEFAULT 1.0,
                country TEXT,
                is_base_currency INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_currencies_table(self, cursor):
        """إنشاء جدول العملات البديل"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                symbol TEXT,
                exchange_rate REAL DEFAULT 1.0,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_default_currencies_for_system(self, cursor):
        """إنشاء العملات الافتراضية لإعدادات النظام"""
        default_currencies = [
            ('YER', 'ريال يمني', 'ر.ي', 1.0, 'اليمن', 1),
            ('SAR', 'ريال سعودي', 'ر.س', 0.15, 'السعودية', 0),
            ('USD', 'دولار أمريكي', '$', 0.004, 'الولايات المتحدة', 0),
            ('EUR', 'يورو', '€', 0.0037, 'الاتحاد الأوروبي', 0),
            ('AED', 'درهم إماراتي', 'د.إ', 0.015, 'الإمارات', 0),
            ('QAR', 'ريال قطري', 'ر.ق', 0.015, 'قطر', 0),
            ('KWD', 'دينار كويتي', 'د.ك', 0.0012, 'الكويت', 0),
            ('BHD', 'دينار بحريني', 'د.ب', 0.0015, 'البحرين', 0),
            ('OMR', 'ريال عماني', 'ر.ع', 0.0015, 'عمان', 0),
            ('JOD', 'دينار أردني', 'د.أ', 0.003, 'الأردن', 0),
            ('EGP', 'جنيه مصري', 'ج.م', 0.12, 'مصر', 0),
            ('GBP', 'جنيه إسترليني', '£', 0.003, 'بريطانيا', 0),
            ('JPY', 'ين ياباني', '¥', 0.6, 'اليابان', 0),
            ('CNY', 'يوان صيني', '¥', 0.029, 'الصين', 0),
            ('TRY', 'ليرة تركية', '₺', 0.12, 'تركيا', 0)
        ]

        for code, name, symbol, rate, country, is_base in default_currencies:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO system_currencies
                    (currency_code, currency_name, currency_symbol, exchange_rate, country, is_base_currency, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, 1)
                """, (code, name, symbol, rate, country, is_base))
            except sqlite3.IntegrityError:
                pass  # العملة موجودة بالفعل

    def create_default_currencies(self, cursor):
        """إنشاء العملات الافتراضية للجدول البديل"""
        default_currencies = [
            ('YER', 'ريال يمني', 'ر.ي', 1.0),
            ('SAR', 'ريال سعودي', 'ر.س', 0.15),
            ('USD', 'دولار أمريكي', '$', 0.004),
            ('EUR', 'يورو', '€', 0.0037),
            ('AED', 'درهم إماراتي', 'د.إ', 0.015),
            ('QAR', 'ريال قطري', 'ر.ق', 0.015),
            ('KWD', 'دينار كويتي', 'د.ك', 0.0012),
            ('BHD', 'دينار بحريني', 'د.ب', 0.0015),
            ('OMR', 'ريال عماني', 'ر.ع', 0.0015),
            ('JOD', 'دينار أردني', 'د.أ', 0.003),
            ('EGP', 'جنيه مصري', 'ج.م', 0.12),
            ('GBP', 'جنيه إسترليني', '£', 0.003)
        ]

        for code, name, symbol, rate in default_currencies:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies (code, name, symbol, exchange_rate, is_active)
                    VALUES (?, ?, ?, ?, 1)
                """, (code, name, symbol, rate))
            except sqlite3.IntegrityError:
                pass  # العملة موجودة بالفعل

    def load_default_currencies(self):
        """تحميل عملات افتراضية في حالة الخطأ"""
        self.currency_combo.clear()
        self.currency_combo.addItem("اختر العملة...", None)

        default_currencies = [
            ("YER", "ريال يمني (ر.ي)"),
            ("SAR", "ريال سعودي (ر.س)"),
            ("USD", "دولار أمريكي ($)"),
            ("EUR", "يورو (€)"),
            ("AED", "درهم إماراتي (د.إ)"),
            ("QAR", "ريال قطري (ر.ق)"),
            ("KWD", "دينار كويتي (د.ك)"),
            ("BHD", "دينار بحريني (د.ب)"),
            ("OMR", "ريال عماني (ر.ع)"),
            ("JOD", "دينار أردني (د.أ)"),
            ("EGP", "جنيه مصري (ج.م)"),
            ("GBP", "جنيه إسترليني (£)")
        ]

        for code, display in default_currencies:
            self.currency_combo.addItem(display, code)

        # تعيين الريال اليمني كافتراضي
        self.currency_combo.setCurrentIndex(1)  # الريال اليمني

    def load_branches_from_bank_management(self):
        """تحميل الفروع من إدارة البنوك والصرافين"""
        print("🏦 تحميل الفروع من إدارة البنوك...")
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الفروع من جدول إدارة البنوك والصرافين
            try:
                # البحث في جدول إدارة البنوك والصرافين
                cursor.execute("""
                    SELECT id, branch_name, address, phone, manager_name
                    FROM bank_branches
                    WHERE is_active = 1
                    ORDER BY branch_name
                """)
                branches = cursor.fetchall()

                if not branches:
                    # البحث في جدول بديل
                    cursor.execute("""
                        SELECT id, name, address, phone, manager_name
                        FROM branches
                        WHERE is_active = 1
                        ORDER BY name
                    """)
                    branches = cursor.fetchall()

                if not branches:
                    # إنشاء فروع افتراضية
                    self.create_default_branches_for_bank_management(cursor)
                    conn.commit()
                    cursor.execute("""
                        SELECT id, branch_name, address, phone, manager_name
                        FROM bank_branches
                        WHERE is_active = 1
                        ORDER BY branch_name
                    """)
                    branches = cursor.fetchall()

            except sqlite3.OperationalError:
                # إنشاء الجدول والبيانات الافتراضية
                self.create_bank_branches_table_for_management(cursor)
                self.create_default_branches_for_bank_management(cursor)
                conn.commit()
                cursor.execute("""
                    SELECT id, branch_name, address, phone, manager_name
                    FROM bank_branches
                    WHERE is_active = 1
                    ORDER BY branch_name
                """)
                branches = cursor.fetchall()

            # تنظيف قائمة الفروع
            self.branch_combo.clear()
            self.branch_combo.addItem("اختر الفرع...", None)

            # إضافة الفروع مع معلومات تفصيلية
            for branch in branches:
                branch_name = branch[1] if len(branch) > 1 else f"فرع {branch[0]}"
                display_text = branch_name

                # إضافة العنوان إذا كان متوفراً
                if len(branch) > 2 and branch[2]:
                    display_text += f" - {branch[2]}"

                # إضافة اسم المدير إذا كان متوفراً
                if len(branch) > 4 and branch[4]:
                    display_text += f" (مدير: {branch[4]})"

                self.branch_combo.addItem(display_text, branch[0])

            conn.close()
            print(f"✅ تم تحميل {len(branches)} فرع من إدارة البنوك")

        except Exception as e:
            print(f"❌ خطأ في تحميل الفروع: {e}")
            # في حالة الخطأ، إضافة فروع افتراضية
            self.load_default_branches()

    def load_exchangers_from_bank_management(self):
        """تحميل الصرافين من إدارة البنوك والصرافين"""
        print("👤 تحميل الصرافين من إدارة البنوك...")

        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الصرافين من جدول إدارة البنوك والصرافين
            try:
                # البحث في جدول إدارة البنوك والصرافين
                cursor.execute("""
                    SELECT e.id, e.exchanger_name, e.phone, e.email, e.license_number,
                           b.branch_name, e.address
                    FROM exchangers e
                    LEFT JOIN bank_branches b ON e.branch_id = b.id
                    WHERE e.is_active = 1
                    ORDER BY e.exchanger_name
                """)
                exchangers = cursor.fetchall()

                if not exchangers:
                    # البحث في جدول بديل
                    cursor.execute("""
                        SELECT e.id, e.name, e.phone, e.email, e.license_number,
                               b.name as branch_name, e.address
                        FROM exchangers e
                        LEFT JOIN branches b ON e.branch_id = b.id
                        WHERE e.is_active = 1
                        ORDER BY e.name
                    """)
                    exchangers = cursor.fetchall()

                if not exchangers:
                    # إنشاء صرافين افتراضيين
                    self.create_default_exchangers_for_bank_management(cursor)
                    conn.commit()
                    cursor.execute("""
                        SELECT e.id, e.exchanger_name, e.phone, e.email, e.license_number,
                               b.branch_name, e.address
                        FROM exchangers e
                        LEFT JOIN bank_branches b ON e.branch_id = b.id
                        WHERE e.is_active = 1
                        ORDER BY e.exchanger_name
                    """)
                    exchangers = cursor.fetchall()

            except sqlite3.OperationalError:
                # إنشاء الجدول والبيانات الافتراضية
                self.create_exchangers_table_for_management(cursor)
                self.create_default_exchangers_for_bank_management(cursor)
                conn.commit()
                cursor.execute("""
                    SELECT e.id, e.exchanger_name, e.phone, e.email, e.license_number,
                           b.branch_name, e.address
                    FROM exchangers e
                    LEFT JOIN bank_branches b ON e.branch_id = b.id
                    WHERE e.is_active = 1
                    ORDER BY e.exchanger_name
                """)
                exchangers = cursor.fetchall()

            # تنظيف قائمة الصرافين
            self.exchanger_combo.clear()
            self.exchanger_combo.addItem("اختر الصراف...", None)

            # إضافة الصرافين مع معلومات تفصيلية
            for exchanger in exchangers:
                exchanger_name = exchanger[1] if len(exchanger) > 1 else f"صراف {exchanger[0]}"
                display_text = exchanger_name

                # إضافة اسم الفرع إذا كان متوفراً
                if len(exchanger) > 5 and exchanger[5]:
                    display_text += f" ({exchanger[5]})"

                # إضافة رقم الترخيص إذا كان متوفراً
                if len(exchanger) > 4 and exchanger[4]:
                    display_text += f" - ترخيص: {exchanger[4]}"

                # إضافة رقم الهاتف إذا كان متوفراً
                if len(exchanger) > 2 and exchanger[2]:
                    display_text += f" - {exchanger[2]}"

                self.exchanger_combo.addItem(display_text, exchanger[0])

            conn.close()
            print(f"✅ تم تحميل {len(exchangers)} صراف من إدارة البنوك")

        except Exception as e:
            print(f"❌ خطأ في تحميل الصرافين: {e}")
            # في حالة الخطأ، إضافة صرافين افتراضيين
            self.load_default_exchangers()

    def create_bank_branches_table_for_management(self, cursor):
        """إنشاء جدول فروع البنوك لإدارة البنوك والصرافين"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                branch_name TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT,
                manager_name TEXT,
                city TEXT,
                region TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_exchangers_table_for_management(self, cursor):
        """إنشاء جدول الصرافين لإدارة البنوك والصرافين"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS exchangers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchanger_name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                branch_id INTEGER,
                license_number TEXT,
                license_date TEXT,
                license_expiry TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES bank_branches (id)
            )
        """)

    def create_default_branches_for_bank_management(self, cursor):
        """إنشاء فروع افتراضية لإدارة البنوك والصرافين"""
        default_branches = [
            ('الفرع الرئيسي - صنعاء', 'شارع الزبيري، حي السبعين، صنعاء', '+967 1 234567', '<EMAIL>', 'أحمد محمد المدير', 'صنعاء', 'أمانة العاصمة'),
            ('فرع صنعاء الشمالي', 'شارع الستين، حي الحصبة، صنعاء', '+967 1 345678', '<EMAIL>', 'علي سالم المدير', 'صنعاء', 'أمانة العاصمة'),
            ('فرع عدن الرئيسي', 'شارع المعلا، كريتر، عدن', '+967 2 456789', '<EMAIL>', 'محمد أحمد المدير', 'عدن', 'محافظة عدن'),
            ('فرع تعز المركزي', 'شارع جمال عبدالناصر، تعز', '+967 4 567890', '<EMAIL>', 'سالم علي المدير', 'تعز', 'محافظة تعز'),
            ('فرع الحديدة التجاري', 'شارع الكورنيش، الحديدة', '+967 3 678901', '<EMAIL>', 'أحمد سالم المدير', 'الحديدة', 'محافظة الحديدة'),
            ('فرع إب الجنوبي', 'شارع الثورة، إب', '+967 4 789012', '<EMAIL>', 'محمد سالم المدير', 'إب', 'محافظة إب')
        ]

        for branch_name, address, phone, email, manager, city, region in default_branches:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO bank_branches
                    (branch_name, address, phone, email, manager_name, city, region, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                """, (branch_name, address, phone, email, manager, city, region))
            except sqlite3.IntegrityError:
                pass  # الفرع موجود بالفعل

    def create_default_exchangers_for_bank_management(self, cursor):
        """إنشاء صرافين افتراضيين لإدارة البنوك والصرافين"""
        default_exchangers = [
            ('أحمد محمد الصراف', '+967 *********', '<EMAIL>', 'شارع الزبيري، صنعاء', 1, 'EX2024001', '2024-01-01', '2025-12-31'),
            ('علي سالم للصرافة', '+967 *********', '<EMAIL>', 'شارع الستين، صنعاء', 2, 'EX2024002', '2024-01-15', '2025-12-31'),
            ('محمد عبدالله للتحويل', '+967 *********', '<EMAIL>', 'شارع المعلا، عدن', 3, 'EX2024003', '2024-02-01', '2025-12-31'),
            ('سالم أحمد الصراف', '+967 *********', '<EMAIL>', 'شارع جمال، تعز', 4, 'EX2024004', '2024-02-15', '2025-12-31'),
            ('عبدالله محمد للصرافة', '+967 *********', '<EMAIL>', 'شارع الكورنيش، الحديدة', 5, 'EX2024005', '2024-03-01', '2025-12-31'),
            ('حسن علي للتحويلات', '+967 777678901', '<EMAIL>', 'شارع الثورة، إب', 6, '*********', '2024-03-15', '2025-12-31'),
            ('فاطمة أحمد للصرافة', '+967 777789012', '<EMAIL>', 'شارع الزبيري، صنعاء', 1, '*********', '2024-04-01', '2025-12-31'),
            ('خالد سالم للتحويلات', '+967 777890123', '<EMAIL>', 'شارع المعلا، عدن', 3, '*********', '2024-04-15', '2025-12-31')
        ]

        for exchanger_name, phone, email, address, branch_id, license_num, license_date, license_expiry in default_exchangers:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO exchangers
                    (exchanger_name, phone, email, address, branch_id, license_number, license_date, license_expiry, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                """, (exchanger_name, phone, email, address, branch_id, license_num, license_date, license_expiry))
            except sqlite3.IntegrityError:
                pass  # الصراف موجود بالفعل

    def load_default_branches(self):
        """تحميل فروع افتراضية في حالة الخطأ"""
        self.branch_combo.clear()
        self.branch_combo.addItem("اختر الفرع...", None)

        default_branches = [
            ("1", "الفرع الرئيسي - صنعاء (أحمد محمد المدير)"),
            ("2", "فرع صنعاء الشمالي (علي سالم المدير)"),
            ("3", "فرع عدن الرئيسي (محمد أحمد المدير)"),
            ("4", "فرع تعز المركزي (سالم علي المدير)"),
            ("5", "فرع الحديدة التجاري (أحمد سالم المدير)"),
            ("6", "فرع إب الجنوبي (محمد سالم المدير)")
        ]

        for branch_id, name in default_branches:
            self.branch_combo.addItem(name, branch_id)

        print("⚠️ تم تحميل فروع افتراضية")

    def load_default_exchangers(self):
        """تحميل صرافين افتراضيين في حالة الخطأ"""
        self.exchanger_combo.clear()
        self.exchanger_combo.addItem("اختر الصراف...", None)

        default_exchangers = [
            ("1", "أحمد محمد الصراف (الفرع الرئيسي) - EX2024001"),
            ("2", "علي سالم للصرافة (فرع الشمال) - EX2024002"),
            ("3", "محمد عبدالله للتحويل (فرع عدن) - EX2024003"),
            ("4", "سالم أحمد الصراف (فرع تعز) - EX2024004"),
            ("5", "عبدالله محمد للصرافة (فرع الحديدة) - EX2024005"),
            ("6", "حسن علي للتحويلات (فرع إب) - *********"),
            ("7", "فاطمة أحمد للصرافة (الفرع الرئيسي) - *********"),
            ("8", "خالد سالم للتحويلات (فرع عدن) - *********")
        ]

        for exchanger_id, name in default_exchangers:
            self.exchanger_combo.addItem(name, exchanger_id)

        print("⚠️ تم تحميل صرافين افتراضيين")





    def load_countries_data(self):
        """تحميل قائمة البلدان"""
        countries = [
            "اليمن", "السعودية", "الإمارات", "قطر", "الكويت", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "السودان", "ليبيا",
            "المغرب", "الجزائر", "تونس", "موريتانيا", "الصومال", "جيبوتي",
            "أمريكا", "بريطانيا", "ألمانيا", "فرنسا", "إيطاليا", "هولندا",
            "السويد", "النرويج", "الدنمارك", "كندا", "أستراليا", "ماليزيا",
            "إندونيسيا", "تركيا", "الهند", "باكستان", "بنغلاديش", "الفلبين"
        ]

        self.receiver_country_combo.clear()
        self.receiver_country_combo.addItem("اختر البلد...")
        self.receiver_country_combo.addItems(countries)

    # وظائف الأحداث والإجراءات
    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        if index == 0:  # تبويب قائمة الطلبات
            self.load_requests()
        elif index == 2:  # تبويب التقارير
            self.update_statistics()

    def on_request_selected(self):
        """معالج اختيار طلب من الجدول"""
        selected_rows = self.requests_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            if row < len(self.current_requests):
                self.selected_request_id = self.current_requests[row][0]
                print(f"📋 تم اختيار الطلب: {self.selected_request_id}")
            else:
                print(f"⚠️ صف غير صالح: {row}, العدد الكلي: {len(self.current_requests)}")
        else:
            print("⚠️ لم يتم اختيار أي صف")

    def filter_requests(self):
        """تطبيق المرشحات على الطلبات"""
        try:
            status_filter = self.status_filter.currentText()
            date_from = self.date_from_filter.date().toString("yyyy-MM-dd")
            date_to = self.date_to_filter.date().toString("yyyy-MM-dd")

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام آمن مع التحقق من وجود الأعمدة
            try:
                query = """
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency,
                           CASE WHEN receiver_country IS NOT NULL THEN receiver_country ELSE '' END as receiver_country,
                           created_at, status,
                           CASE WHEN priority IS NOT NULL THEN priority ELSE 'عادي' END as priority
                    FROM remittance_requests
                    WHERE DATE(created_at) BETWEEN ? AND ?
                """
                params = [date_from, date_to]

                if status_filter != "جميع الحالات":
                    query += " AND status = ?"
                    params.append(status_filter)

                query += " ORDER BY created_at DESC"

                cursor.execute(query, params)
            except sqlite3.OperationalError:
                # استعلام بديل للجداول القديمة
                query = """
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency, '' as receiver_country, created_at, status, 'عادي' as priority
                    FROM remittance_requests
                    WHERE DATE(created_at) BETWEEN ? AND ?
                """
                params = [date_from, date_to]

                if status_filter != "جميع الحالات":
                    query += " AND status = ?"
                    params.append(status_filter)

                query += " ORDER BY created_at DESC"

                cursor.execute(query, params)

            requests = cursor.fetchall()

            self.current_requests = requests
            self.populate_requests_table(requests)

            conn.close()

            self.status_bar.showMessage(f"تم العثور على {len(requests)} طلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث:\n{str(e)}")

    def save_new_request(self):
        """حفظ طلب حوالة جديد أو تحديث طلب موجود"""
        if not self.validate_new_request_form():
            return

        try:
            # جمع البيانات
            request_data = self.collect_new_request_data()

            # التحقق من وضع التحرير
            if self.editing_request_id:
                # تحديث طلب موجود
                success = self.update_request_in_database(request_data, self.editing_request_id)

                if success:
                    QMessageBox.information(self, "نجح التحديث",
                                          f"تم تحديث طلب الحوالة بنجاح!\nرقم الطلب: {request_data['request_number']}")

                    # إعادة تعيين وضع التحرير
                    self.editing_request_id = None

                    # مسح النموذج
                    self.clear_form()

                    # تحديث القائمة
                    self.load_requests()

                    # الانتقال لتبويب القائمة
                    self.tab_widget.setCurrentIndex(1)
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تحديث طلب الحوالة")
            else:
                # حفظ طلب جديد
                request_id = self.save_request_to_database(request_data)

                if request_id:
                    QMessageBox.information(self, "نجح الحفظ",
                                          f"تم حفظ طلب الحوالة بنجاح!\nرقم الطلب: {request_data['request_number']}")

                    # إرسال إشارة
                    request_data['id'] = request_id
                    self.remittance_request_created.emit(request_data)

                    # مسح النموذج
                    self.clear_form()

                    # تحديث القائمة
                    self.load_requests()

                    # الانتقال لتبويب القائمة
                    self.tab_widget.setCurrentIndex(1)
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حفظ طلب الحوالة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الطلب:\n{str(e)}")

    def print_request_form(self):
        """طباعة نموذج طلب الحوالة"""
        try:
            # التحقق من وجود بيانات للطباعة
            if not self.validate_new_request_form():
                QMessageBox.warning(self, "تحذير", "يرجى تعبئة البيانات المطلوبة قبل الطباعة")
                return

            # جمع البيانات للطباعة
            request_data = self.collect_new_request_data()

            # محاولة استخدام النموذج الاحترافي أولاً
            try:
                from .professional_print_template import ProfessionalPrintTemplate
                self.print_window = ProfessionalPrintTemplate(request_data)
                self.print_window.show()
            except ImportError:
                # في حالة فشل الاستيراد، استخدم النموذج المتقدم
                try:
                    from .remittance_print_template import RemittancePrintTemplate
                    self.print_window = RemittancePrintTemplate(request_data)
                    self.print_window.show()
                except ImportError:
                    # في حالة فشل الاستيراد، استخدم النموذج المبسط
                    from .simple_print_template import SimplePrintTemplate
                    self.print_window = SimplePrintTemplate(request_data)
                    self.print_window.show()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نموذج الطباعة:\n{str(e)}")

    def send_new_request_to_create_remittance(self):
        """إرسال طلب جديد لنافذة إنشاء الحوالة"""
        if not self.validate_new_request_form():
            return

        try:
            # جمع البيانات
            request_data = self.collect_new_request_data()

            # إرسال إشارة
            self.send_to_create_remittance.emit(request_data)

            QMessageBox.information(self, "تم الإرسال",
                                  "تم إرسال البيانات لنافذة إنشاء الحوالة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال البيانات:\n{str(e)}")

    def send_selected_to_create_remittance(self):
        """إرسال الطلب المختار لنافذة إنشاء الحوالة"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        try:
            # جلب بيانات الطلب المختار
            request_data = self.get_request_data(self.selected_request_id)

            if request_data:
                # إرسال إشارة
                self.send_to_create_remittance.emit(request_data)

                QMessageBox.information(self, "تم الإرسال",
                                      "تم إرسال الطلب لنافذة إنشاء الحوالة بنجاح!")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في جلب بيانات الطلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال الطلب:\n{str(e)}")

    # وظائف مساعدة
    def validate_new_request_form(self):
        """التحقق من صحة نموذج الطلب الجديد"""
        # التحقق من البيانات الأساسية
        amount_text = self.remittance_amount_input.text().strip()
        if not amount_text:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال مبلغ الحوالة")
            self.remittance_amount_input.setFocus()
            return False

        # التحقق من أن المبلغ رقم صالح
        try:
            amount_value = float(amount_text.replace(',', ''))
            if amount_value <= 0:
                QMessageBox.warning(self, "بيانات غير صحيحة", "يرجى إدخال مبلغ صحيح أكبر من صفر")
                self.remittance_amount_input.setFocus()
                return False
        except ValueError:
            QMessageBox.warning(self, "بيانات غير صحيحة", "يرجى إدخال مبلغ صحيح (أرقام فقط)")
            self.remittance_amount_input.setFocus()
            return False

        if not self.transfer_purpose_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال الغرض من التحويل")
            self.transfer_purpose_input.setFocus()
            return False

        # التحقق من معلومات المرسل
        if not self.sender_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المرسل")
            self.sender_name_input.setFocus()
            return False

        if not self.sender_phone_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رقم هاتف المرسل")
            self.sender_phone_input.setFocus()
            return False

        # التحقق من معلومات المستقبل
        if not self.receiver_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
            self.receiver_name_input.setFocus()
            return False

        if not self.receiver_account_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رقم حساب المستقبل")
            self.receiver_account_input.setFocus()
            return False

        if not self.receiver_bank_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم البنك المستقبل")
            self.receiver_bank_input.setFocus()
            return False

        return True

    def collect_new_request_data(self):
        """جمع بيانات الطلب الجديد"""
        # استخدام رقم الطلب من الحقل (تم إنشاؤه تلقائياً)
        request_number = self.request_number_input.text().strip()

        return {
            'request_number': request_number,
            'request_date': self.request_date_input.date().toString("yyyy-MM-dd"),
            'exchanger': self.exchanger_combo.currentText(),
            'exchanger_id': self.exchanger_combo.currentData(),
            'remittance_amount': self.remittance_amount_input.text().strip(),
            'currency': self.currency_combo.currentText(),
            'currency_code': self.currency_combo.currentData(),
            'transfer_purpose': self.transfer_purpose_input.text().strip(),

            # معلومات المرسل المحدثة
            'sender_name': self.sender_name_input.text().strip(),
            'sender_entity': self.sender_entity_input.text().strip(),
            'sender_phone': self.sender_phone_input.text().strip(),
            'sender_fax': self.sender_fax_input.text().strip(),
            'sender_mobile': self.sender_mobile_input.text().strip(),
            'sender_pobox': self.sender_pobox_input.text().strip(),
            'sender_email': self.sender_email_input.text().strip(),
            'sender_address': self.sender_address_input.text().strip(),

            # معلومات المستقبل المحدثة
            'receiver_name': self.receiver_name_input.text().strip(),
            'receiver_account': self.receiver_account_input.text().strip(),
            'receiver_bank': self.receiver_bank_input.text().strip(),
            'receiver_bank_branch': self.receiver_bank_branch_input.text().strip(),
            'receiver_swift': self.receiver_swift_input.text().strip(),
            'receiver_country': self.receiver_country_combo.currentText(),
            'receiver_address': self.receiver_address_input.text().strip(),

            # ملاحظات وخيارات
            'notes': self.notes_input.toPlainText().strip(),
            'sms_notification': self.sms_notification_check.isChecked(),
            'email_notification': self.email_notification_check.isChecked(),
            'auto_create_remittance': self.auto_create_remittance_check.isChecked(),
            'status': 'معلق',
            'created_at': datetime.now().isoformat()
        }

    def save_request_to_database(self, data):
        """حفظ الطلب في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من تحديث الجدول أولاً
            self.create_or_update_table(cursor)

            # محاولة الإدراج مع الحقول الجديدة والقديمة
            try:
                cursor.execute("""
                    INSERT INTO remittance_requests (
                        request_number, request_date, branch, branch_id, exchanger, exchanger_id,
                        remittance_amount, currency, currency_code, transfer_purpose,
                        sender_name, sender_entity, sender_phone, sender_fax, sender_mobile, sender_pobox,
                        sender_email, sender_address,
                        receiver_name, receiver_account, receiver_bank_name, receiver_bank_branch,
                        receiver_swift, receiver_country, receiver_address,
                        notes, sms_notification, email_notification, auto_create_remittance,
                        status, created_at,
                        amount, source_currency, target_currency
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['request_number'], data['request_date'], data['branch'], data.get('branch_id'),
                    data['exchanger'], data.get('exchanger_id'), data['remittance_amount'],
                    data['currency'], data.get('currency_code'), data['transfer_purpose'],
                    data['sender_name'], data['sender_entity'], data['sender_phone'],
                    data['sender_fax'], data['sender_mobile'], data['sender_pobox'],
                    data['sender_email'], data['sender_address'],
                    data['receiver_name'], data['receiver_account'], data['receiver_bank'],
                    data['receiver_bank_branch'], data['receiver_swift'], data['receiver_country'],
                    data['receiver_address'], data['notes'], data['sms_notification'],
                    data['email_notification'], data['auto_create_remittance'],
                    data['status'], data['created_at'],
                    # الحقول القديمة المطلوبة
                    data['remittance_amount'], data['currency'], data['currency']
                ))
            except sqlite3.OperationalError as e:
                print(f"خطأ في الإدراج الكامل: {e}")
                # إذا فشل، استخدم الأعمدة الأساسية فقط مع الحقول المطلوبة
                cursor.execute("""
                    INSERT INTO remittance_requests (
                        request_number, sender_name, receiver_name, amount,
                        source_currency, target_currency, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['request_number'], data['sender_name'], data['receiver_name'],
                    data['remittance_amount'], data['currency'], data['currency'],
                    data['status'], data['created_at']
                ))

            request_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return request_id

        except Exception as e:
            print(f"خطأ في حفظ الطلب: {e}")
            return None

    def get_request_data(self, request_id):
        """جلب بيانات طلب محدد"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM remittance_requests WHERE id = ?
            """, (request_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                # تحويل النتيجة إلى قاموس
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))

            return None

        except Exception as e:
            print(f"خطأ في جلب بيانات الطلب: {e}")
            return None

    def clear_form(self):
        """مسح نموذج الطلب الجديد"""
        # إعادة تعيين وضع التحرير
        self.editing_request_id = None
        self.cancel_edit_btn.setVisible(False)

        # مسح البيانات الأساسية
        self.request_date_input.setDate(QDate.currentDate())
        # إنشاء رقم طلب جديد
        auto_number = f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"
        self.request_number_input.setText(auto_number)
        self.branch_combo.setCurrentIndex(0)
        self.exchanger_combo.setCurrentIndex(0)
        self.remittance_amount_input.clear()
        self.currency_combo.setCurrentIndex(0)
        self.transfer_purpose_input.clear()

        # إعادة تعبئة معلومات المرسل بالبيانات الافتراضية
        self.sender_name_input.setText("ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD")
        self.sender_entity_input.setText("G.M: NASHA'AT RASHAD QASIM ALDUBAEE")
        self.sender_phone_input.setText("+967 1 616109")
        self.sender_fax_input.setText("+967 1 615909")
        self.sender_mobile_input.setText("+967 *********")
        self.sender_pobox_input.setText("1903")
        self.sender_email_input.setText("<EMAIL>, <EMAIL>")
        self.sender_address_input.setText("TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN")

        # مسح معلومات المستقبل
        self.receiver_name_input.clear()
        self.receiver_account_input.clear()
        self.receiver_bank_input.clear()
        self.receiver_bank_branch_input.clear()
        self.receiver_swift_input.clear()
        self.receiver_country_combo.setCurrentIndex(0)
        self.receiver_address_input.clear()

        # مسح الملاحظات والخيارات
        self.notes_input.clear()
        self.sms_notification_check.setChecked(True)
        self.email_notification_check.setChecked(False)
        self.auto_create_remittance_check.setChecked(True)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        # هذه الدالة يمكن تطويرها لاحقاً لحساب الإحصائيات الفعلية
        pass

    # وظائف أخرى
    def view_request_details(self):
        """عرض تفاصيل الطلب"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        try:
            # استيراد نافذة التفاصيل
            from .remittance_details_window import RemittanceDetailsWindow

            # إنشاء وفتح نافذة التفاصيل
            self.details_window = RemittanceDetailsWindow(self.selected_request_id, self)
            self.details_window.show()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التفاصيل:\n{str(e)}")

    def update_request_status(self):
        """تحديث حالة الطلب"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        # يمكن فتح نافذة تحديث الحالة
        QMessageBox.information(self, "تحديث الحالة", f"تحديث حالة الطلب رقم: {self.selected_request_id}")

    def delete_request(self):
        """حذف الطلب"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا الطلب؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                # تنفيذ الحذف الفعلي من قاعدة البيانات
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("DELETE FROM remittance_requests WHERE id = ?", (self.selected_request_id,))
                conn.commit()
                conn.close()

                # إعادة تحميل قائمة الطلبات
                self.load_requests()

                # إعادة تعيين الطلب المختار
                self.selected_request_id = None

                QMessageBox.information(self, "تم الحذف", "تم حذف الطلب بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الطلب:\n{str(e)}")

    def edit_selected_request(self):
        """تحرير الطلب المختار"""
        print(f"🔧 محاولة تحرير الطلب: {self.selected_request_id}")

        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        try:
            # تحميل بيانات الطلب من قاعدة البيانات
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM remittance_requests WHERE id = ?", (self.selected_request_id,))
            result = cursor.fetchone()

            if result:
                print(f"✅ تم العثور على الطلب في قاعدة البيانات")

                # الحصول على أسماء الأعمدة
                cursor.execute("PRAGMA table_info(remittance_requests)")
                columns = [column[1] for column in cursor.fetchall()]

                # تحويل النتيجة إلى قاموس
                request_data = dict(zip(columns, result))
                print(f"📋 بيانات الطلب: رقم {request_data.get('request_number', 'غير محدد')}")

                # تعبئة النموذج بالبيانات
                print("📝 تعبئة النموذج...")
                self.populate_form_for_editing(request_data)

                # التبديل إلى تبويب الطلب الجديد
                print("🔄 التبديل إلى تبويب الطلب الجديد...")
                self.tab_widget.setCurrentIndex(1)  # تبويب طلب حوالة جديد

                # حفظ معرف الطلب للتحديث
                self.editing_request_id = self.selected_request_id
                print(f"💾 تم حفظ معرف التحرير: {self.editing_request_id}")

                # إظهار زر إلغاء التحرير
                self.cancel_edit_btn.setVisible(True)
                print("🔘 تم إظهار زر إلغاء التحرير")

                # إظهار النافذة وجعلها في المقدمة
                print("🖥️ إظهار النافذة...")

                # إجبار إظهار النافذة بطرق متعددة
                self.setVisible(True)
                self.show()
                self.showNormal()

                # التأكد من أن النافذة مرئية
                if self.isMinimized():
                    print("📏 النافذة مصغرة، استعادة الحجم العادي...")
                    self.showNormal()

                if self.isHidden():
                    print("👁️ النافذة مخفية، إظهارها...")
                    self.show()
                    self.setVisible(True)

                # رفع النافذة للمقدمة وتفعيلها
                self.raise_()
                self.activateWindow()

                # تعيين النافذة كنافذة نشطة
                self.setWindowState(Qt.WindowActive)

                # محاولة إحضار النافذة للمقدمة بقوة
                try:
                    # للويندوز
                    import ctypes
                    from ctypes import wintypes
                    hwnd = int(self.winId())
                    ctypes.windll.user32.SetForegroundWindow(hwnd)
                    ctypes.windll.user32.BringWindowToTop(hwnd)
                    print("🪟 تم إحضار النافذة للمقدمة (Windows)")
                except:
                    print("⚠️ لم يتمكن من استخدام Windows API")

                # التركيز على الحقل الأول
                print("🎯 تركيز على الحقل الأول...")
                self.sender_name_input.setFocus()

                print("✅ تم إظهار النافذة بنجاح!")

                # إظهار رسالة نجاح بعد تأخير قصير
                from PySide6.QtCore import QTimer
                def show_success_message():
                    QMessageBox.information(self, "وضع التحرير",
                        f"تم تحميل الطلب رقم {request_data.get('request_number', self.selected_request_id)} في وضع التحرير.\n\n"
                        "✅ تم تحميل جميع البيانات\n"
                        "📝 يمكنك الآن تعديل البيانات\n"
                        "💾 اضغط 'حفظ التغييرات' عند الانتهاء\n"
                        "❌ أو 'إلغاء التحرير' للعودة")

                # تأخير الرسالة لضمان ظهور النافذة أولاً
                QTimer.singleShot(100, show_success_message)
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب")

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلب للتحرير:\n{str(e)}")

    def populate_form_for_editing(self, request_data):
        """تعبئة النموذج للتحرير"""
        try:
            # البيانات الأساسية
            if 'request_date' in request_data and request_data['request_date']:
                self.request_date_input.setDate(QDate.fromString(request_data['request_date'], "yyyy-MM-dd"))

            if 'request_number' in request_data and request_data['request_number']:
                self.request_number_input.setText(request_data['request_number'])

            if 'exchanger' in request_data and request_data['exchanger']:
                # البحث بالاسم أو المعرف
                exchanger_name = request_data['exchanger']
                for i in range(self.exchanger_combo.count()):
                    if exchanger_name in self.exchanger_combo.itemText(i):
                        self.exchanger_combo.setCurrentIndex(i)
                        break

            # المبلغ والعملة
            amount = request_data.get('remittance_amount') or request_data.get('amount', '')
            if amount:
                self.remittance_amount_input.setText(str(amount))

            if 'currency' in request_data and request_data['currency']:
                index = self.currency_combo.findText(request_data['currency'])
                if index >= 0:
                    self.currency_combo.setCurrentIndex(index)

            if 'transfer_purpose' in request_data and request_data['transfer_purpose']:
                self.transfer_purpose_input.setText(request_data['transfer_purpose'])

            # معلومات المستقبل
            if 'receiver_name' in request_data and request_data['receiver_name']:
                self.receiver_name_input.setText(request_data['receiver_name'])

            if 'receiver_account' in request_data and request_data['receiver_account']:
                self.receiver_account_input.setText(request_data['receiver_account'])

            if 'receiver_bank_name' in request_data and request_data['receiver_bank_name']:
                self.receiver_bank_input.setText(request_data['receiver_bank_name'])

            if 'receiver_bank_branch' in request_data and request_data['receiver_bank_branch']:
                self.receiver_bank_branch_input.setText(request_data['receiver_bank_branch'])

            if 'receiver_swift' in request_data and request_data['receiver_swift']:
                self.receiver_swift_input.setText(request_data['receiver_swift'])

            if 'receiver_country' in request_data and request_data['receiver_country']:
                index = self.receiver_country_combo.findText(request_data['receiver_country'])
                if index >= 0:
                    self.receiver_country_combo.setCurrentIndex(index)

            if 'receiver_address' in request_data and request_data['receiver_address']:
                self.receiver_address_input.setText(request_data['receiver_address'])

            # الملاحظات
            if 'notes' in request_data and request_data['notes']:
                self.notes_input.setPlainText(request_data['notes'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تعبئة النموذج:\n{str(e)}")

    def update_request_in_database(self, request_data, request_id):
        """تحديث طلب في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحديث البيانات
            cursor.execute("""
                UPDATE remittance_requests SET
                    request_number = ?, request_date = ?, branch = ?, exchanger = ?,
                    remittance_amount = ?, currency = ?, transfer_purpose = ?,
                    sender_name = ?, sender_entity = ?, sender_phone = ?, sender_fax = ?,
                    sender_mobile = ?, sender_pobox = ?, sender_email = ?, sender_address = ?,
                    receiver_name = ?, receiver_account = ?, receiver_bank_name = ?,
                    receiver_bank_branch = ?, receiver_swift = ?, receiver_country = ?,
                    receiver_address = ?, notes = ?, sms_notification = ?,
                    email_notification = ?, auto_create_remittance = ?, status = ?,
                    updated_at = ?, amount = ?, source_currency = ?, target_currency = ?
                WHERE id = ?
            """, (
                request_data['request_number'], request_data['request_date'],
                request_data['branch'], request_data['exchanger'],
                request_data['remittance_amount'], request_data['currency'],
                request_data['transfer_purpose'], request_data['sender_name'],
                request_data['sender_entity'], request_data['sender_phone'],
                request_data['sender_fax'], request_data['sender_mobile'],
                request_data['sender_pobox'], request_data['sender_email'],
                request_data['sender_address'], request_data['receiver_name'],
                request_data['receiver_account'], request_data['receiver_bank_name'],
                request_data['receiver_bank_branch'], request_data['receiver_swift'],
                request_data['receiver_country'], request_data['receiver_address'],
                request_data['notes'], request_data['sms_notification'],
                request_data['email_notification'], request_data['auto_create_remittance'],
                request_data['status'], request_data['updated_at'],
                request_data['remittance_amount'], request_data['currency'],
                request_data['currency'], request_id
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في تحديث الطلب: {e}")
            return False

    def cancel_editing(self):
        """إلغاء وضع التحرير"""
        reply = QMessageBox.question(self, "إلغاء التحرير",
                                   "هل أنت متأكد من إلغاء التحرير؟\nسيتم فقدان التغييرات غير المحفوظة.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # إعادة تعيين وضع التحرير
            self.editing_request_id = None
            self.cancel_edit_btn.setVisible(False)

            # مسح النموذج
            self.clear_form()

            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء وضع التحرير")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير البيانات", "ميزة التصدير قيد التطوير...")

    def open_settings(self):
        """فتح الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "نافذة الإعدادات قيد التطوير...")
