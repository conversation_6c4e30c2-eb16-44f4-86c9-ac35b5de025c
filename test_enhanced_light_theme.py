#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الثيم الفاتح المحسن
Test Enhanced Light Theme
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QLabel, QPushButton, QLineEdit, 
                               QTextEdit, QComboBox, QTableWidget, QTableWidgetItem,
                               QGroupBox, QTabWidget, QFormLayout, QProgressBar,
                               QCheckBox, QRadioButton, QSpinBox, QSlider,
                               QFrame, QSplitter, QListWidget, QTreeWidget,
                               QTreeWidgetItem, QScrollArea)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor

from src.utils.arabic_support import setup_arabic_support
from src.ui.styles.light_theme_enhancer import apply_enhanced_light_theme, get_button_style
from src.ui.styles.style_manager import style_manager

class EnhancedLightThemeTestWindow(QMainWindow):
    """نافذة اختبار الثيم الفاتح المحسن"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.setWindowTitle("اختبار الثيم الفاتح المحسن - ProShipment")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("اختبار الثيم الفاتح المحسن والمتطور")
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 700;
                color: #1a1a1a;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f0f8ff, stop:0.5 #e6f3ff, stop:1 #f0f8ff);
                border: 1px solid #0969da;
                border-radius: 12px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء Splitter للتقسيم
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الجانب الأيسر - التبويبات الرئيسية
        self.create_main_tabs(splitter)
        
        # الجانب الأيمن - لوحة التحكم
        self.create_control_panel(splitter)
        
        # تعيين نسب التقسيم
        splitter.setSizes([900, 500])
        
        # شريط الحالة
        self.statusBar().showMessage("الثيم الفاتح المحسن - جاهز للاختبار")
        
    def create_main_tabs(self, parent):
        """إنشاء التبويبات الرئيسية"""
        tab_widget = QTabWidget()
        tab_widget.setObjectName("mainTabs")
        
        # تبويب العناصر الأساسية
        self.create_basic_elements_tab(tab_widget)
        
        # تبويب الأزرار المحسنة
        self.create_enhanced_buttons_tab(tab_widget)
        
        # تبويب الجداول والقوائم
        self.create_data_display_tab(tab_widget)
        
        # تبويب النماذج
        self.create_forms_tab(tab_widget)
        
        parent.addWidget(tab_widget)
        
    def create_basic_elements_tab(self, parent):
        """إنشاء تبويب العناصر الأساسية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # مجموعة النصوص
        text_group = QGroupBox("النصوص والتسميات")
        text_layout = QFormLayout(text_group)
        
        title_label = QLabel("عنوان رئيسي")
        title_label.setObjectName("title")
        text_layout.addRow("عنوان:", title_label)
        
        subtitle_label = QLabel("عنوان فرعي")
        subtitle_label.setObjectName("subtitle")
        text_layout.addRow("عنوان فرعي:", subtitle_label)
        
        caption_label = QLabel("نص توضيحي صغير")
        caption_label.setObjectName("caption")
        text_layout.addRow("توضيح:", caption_label)
        
        layout.addWidget(text_group)
        
        # مجموعة حقول الإدخال
        input_group = QGroupBox("حقول الإدخال المحسنة")
        input_layout = QFormLayout(input_group)
        
        normal_edit = QLineEdit("حقل إدخال عادي")
        input_layout.addRow("عادي:", normal_edit)
        
        important_edit = QLineEdit("حقل مهم")
        important_edit.setObjectName("importantField")
        input_layout.addRow("مهم:", important_edit)
        
        error_edit = QLineEdit("حقل خطأ")
        error_edit.setObjectName("errorField")
        input_layout.addRow("خطأ:", error_edit)
        
        success_edit = QLineEdit("حقل نجح")
        success_edit.setObjectName("successField")
        input_layout.addRow("نجح:", success_edit)
        
        layout.addWidget(input_group)
        
        # مجموعة العناصر التفاعلية
        interactive_group = QGroupBox("العناصر التفاعلية")
        interactive_layout = QVBoxLayout(interactive_group)
        
        # مربعات الاختيار
        check_layout = QHBoxLayout()
        check1 = QCheckBox("خيار أول")
        check1.setChecked(True)
        check2 = QCheckBox("خيار ثاني")
        check3 = QCheckBox("خيار ثالث")
        check_layout.addWidget(check1)
        check_layout.addWidget(check2)
        check_layout.addWidget(check3)
        interactive_layout.addLayout(check_layout)
        
        # أزرار الراديو
        radio_layout = QHBoxLayout()
        radio1 = QRadioButton("اختيار أول")
        radio1.setChecked(True)
        radio2 = QRadioButton("اختيار ثاني")
        radio3 = QRadioButton("اختيار ثالث")
        radio_layout.addWidget(radio1)
        radio_layout.addWidget(radio2)
        radio_layout.addWidget(radio3)
        interactive_layout.addLayout(radio_layout)
        
        # قائمة منسدلة
        combo = QComboBox()
        combo.addItems(["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "الخيار الرابع"])
        interactive_layout.addWidget(combo)
        
        # منزلق القيم
        slider = QSlider(Qt.Horizontal)
        slider.setRange(0, 100)
        slider.setValue(50)
        interactive_layout.addWidget(slider)
        
        # مربع الأرقام
        spin_box = QSpinBox()
        spin_box.setRange(0, 1000)
        spin_box.setValue(100)
        interactive_layout.addWidget(spin_box)
        
        layout.addWidget(interactive_group)
        
        # شريط التقدم
        progress_group = QGroupBox("شريط التقدم")
        progress_layout = QVBoxLayout(progress_group)
        
        progress = QProgressBar()
        progress.setObjectName("mainProgress")
        progress.setRange(0, 100)
        progress.setValue(65)
        progress_layout.addWidget(progress)
        
        layout.addWidget(progress_group)
        
        parent.addTab(widget, "العناصر الأساسية")
        
    def create_enhanced_buttons_tab(self, parent):
        """إنشاء تبويب الأزرار المحسنة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # مجموعة الأزرار العادية
        normal_group = QGroupBox("الأزرار العادية")
        normal_layout = QHBoxLayout(normal_group)
        
        buttons = ["حفظ", "إلغاء", "تعديل", "حذف", "إضافة"]
        for text in buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(40)
            normal_layout.addWidget(btn)
            
        layout.addWidget(normal_group)
        
        # مجموعة الأزرار المحسنة
        enhanced_group = QGroupBox("الأزرار المحسنة")
        enhanced_layout = QVBoxLayout(enhanced_group)
        
        # صف الأزرار الأساسية
        primary_layout = QHBoxLayout()
        
        primary_btn = QPushButton("زر أساسي")
        primary_btn.setObjectName("primaryButton")
        primary_layout.addWidget(primary_btn)
        
        success_btn = QPushButton("زر النجاح")
        success_btn.setObjectName("successButton")
        primary_layout.addWidget(success_btn)
        
        danger_btn = QPushButton("زر الخطر")
        danger_btn.setObjectName("dangerButton")
        primary_layout.addWidget(danger_btn)
        
        warning_btn = QPushButton("زر التحذير")
        warning_btn.setObjectName("warningButton")
        primary_layout.addWidget(warning_btn)
        
        enhanced_layout.addLayout(primary_layout)
        
        # أزرار بستايلات مخصصة
        custom_layout = QHBoxLayout()
        
        custom1 = QPushButton("مخصص 1")
        custom1.setStyleSheet(get_button_style("primary"))
        custom_layout.addWidget(custom1)
        
        custom2 = QPushButton("مخصص 2")
        custom2.setStyleSheet(get_button_style("success"))
        custom_layout.addWidget(custom2)
        
        custom3 = QPushButton("مخصص 3")
        custom3.setStyleSheet(get_button_style("danger"))
        custom_layout.addWidget(custom3)
        
        enhanced_layout.addLayout(custom_layout)
        
        layout.addWidget(enhanced_group)
        
        # مجموعة أزرار التحكم في الثيم
        theme_group = QGroupBox("التحكم في الثيم")
        theme_layout = QHBoxLayout(theme_group)
        
        light_btn = QPushButton("الثيم الفاتح المحسن")
        light_btn.clicked.connect(self.apply_enhanced_light)
        theme_layout.addWidget(light_btn)
        
        modern_btn = QPushButton("الثيم الحديث")
        modern_btn.clicked.connect(lambda: style_manager.load_theme("modern"))
        theme_layout.addWidget(modern_btn)
        
        dark_btn = QPushButton("الثيم المظلم")
        dark_btn.clicked.connect(style_manager.apply_dark_theme)
        theme_layout.addWidget(dark_btn)
        
        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(style_manager.reset_style)
        theme_layout.addWidget(reset_btn)
        
        layout.addWidget(theme_group)
        
        parent.addTab(widget, "الأزرار المحسنة")
        
    def create_data_display_tab(self, parent):
        """إنشاء تبويب عرض البيانات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # جدول البيانات
        table_group = QGroupBox("جدول البيانات المحسن")
        table_layout = QVBoxLayout(table_group)
        
        table = QTableWidget(6, 4)
        table.setObjectName("dataTable")
        table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحالة", "التاريخ"])
        
        # بيانات تجريبية
        sample_data = [
            ["شركة النور", "مورد", "نشط", "2025-01-01"],
            ["شركة الأمل", "عميل", "غير نشط", "2025-01-02"],
            ["شركة التقدم", "مورد", "نشط", "2025-01-03"],
            ["شركة الإبداع", "شحن", "نشط", "2025-01-04"],
            ["شركة المستقبل", "مورد", "غير نشط", "2025-01-05"],
            ["شركة الريادة", "عميل", "نشط", "2025-01-06"]
        ]
        
        for row, row_data in enumerate(sample_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                table.setItem(row, col, item)
                
        table_layout.addWidget(table)
        layout.addWidget(table_group)
        
        # قائمة البيانات
        list_group = QGroupBox("قائمة البيانات")
        list_layout = QVBoxLayout(list_group)
        
        list_widget = QListWidget()
        items = ["عنصر أول", "عنصر ثاني", "عنصر ثالث", "عنصر رابع", "عنصر خامس"]
        for item in items:
            list_widget.addItem(item)
            
        list_layout.addWidget(list_widget)
        layout.addWidget(list_group)
        
        parent.addTab(widget, "عرض البيانات")
        
    def create_forms_tab(self, parent):
        """إنشاء تبويب النماذج"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # نموذج تجريبي
        form_group = QGroupBox("نموذج تجريبي محسن")
        form_group.setObjectName("importantGroup")
        form_layout = QFormLayout(form_group)
        
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("أدخل الاسم")
        form_layout.addRow("الاسم:", name_edit)
        
        email_edit = QLineEdit()
        email_edit.setPlaceholderText("أدخل البريد الإلكتروني")
        form_layout.addRow("البريد:", email_edit)
        
        phone_edit = QLineEdit()
        phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("الهاتف:", phone_edit)
        
        address_edit = QTextEdit()
        address_edit.setPlaceholderText("أدخل العنوان")
        address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", address_edit)
        
        layout.addWidget(form_group)
        
        parent.addTab(widget, "النماذج")
        
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setSpacing(15)
        
        # عنوان لوحة التحكم
        control_title = QLabel("لوحة التحكم")
        control_title.setObjectName("title")
        control_title.setAlignment(Qt.AlignCenter)
        control_layout.addWidget(control_title)
        
        # بطاقة معلومات
        info_card = QFrame()
        info_card.setObjectName("card")
        info_card_layout = QVBoxLayout(info_card)
        
        info_title = QLabel("معلومات الثيم")
        info_title.setObjectName("subtitle")
        info_card_layout.addWidget(info_title)
        
        info_text = QLabel("الثيم الفاتح المحسن يوفر:\n• وضوح ممتاز للنصوص\n• ألوان متناسقة\n• تباين عالي\n• تجربة مستخدم محسنة")
        info_text.setObjectName("caption")
        info_text.setWordWrap(True)
        info_card_layout.addWidget(info_text)
        
        control_layout.addWidget(info_card)
        
        # أزرار التحكم السريع
        quick_group = QGroupBox("التحكم السريع")
        quick_layout = QVBoxLayout(quick_group)
        
        refresh_btn = QPushButton("تحديث الثيم")
        refresh_btn.clicked.connect(self.apply_enhanced_light)
        quick_layout.addWidget(refresh_btn)
        
        test_btn = QPushButton("اختبار العناصر")
        test_btn.clicked.connect(self.test_elements)
        quick_layout.addWidget(test_btn)
        
        control_layout.addWidget(quick_group)
        
        # إضافة مساحة فارغة
        control_layout.addStretch()
        
        parent.addWidget(control_widget)
        
    def apply_enhanced_light(self):
        """تطبيق الثيم الفاتح المحسن"""
        success = apply_enhanced_light_theme()
        if success:
            self.statusBar().showMessage("✅ تم تطبيق الثيم الفاتح المحسن بنجاح")
        else:
            self.statusBar().showMessage("❌ فشل في تطبيق الثيم الفاتح المحسن")
            
    def test_elements(self):
        """اختبار العناصر"""
        self.statusBar().showMessage("🧪 جاري اختبار العناصر...")
        
        # محاكاة اختبار
        QTimer.singleShot(2000, lambda: self.statusBar().showMessage("✅ تم اختبار العناصر بنجاح"))

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الثيم الفاتح المحسن")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد دعم العربية
        setup_arabic_support(app)
        
        # تطبيق الثيم الفاتح المحسن
        apply_enhanced_light_theme()
        
        print("✅ تم إعداد التطبيق والثيم الفاتح المحسن")
        
        # إنشاء النافذة
        window = EnhancedLightThemeTestWindow()
        window.show()
        
        print("✅ تم عرض نافذة الاختبار")
        print("\n🎯 يمكنك الآن اختبار:")
        print("   • الثيم الفاتح المحسن")
        print("   • وضوح النصوص والعناصر")
        print("   • الأزرار المحسنة")
        print("   • الجداول والقوائم")
        print("   • النماذج المحسنة")
        print("   • تبديل الثيمات")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏁 انتهى الاختبار برمز الخروج: {exit_code}")
    sys.exit(exit_code)
