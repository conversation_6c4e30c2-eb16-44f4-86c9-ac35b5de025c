#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح هيكل جدول الفروع لإضافة الأعمدة المفقودة
Fix Branches Table Structure
"""

import sqlite3
from pathlib import Path

def fix_branches_table_structure():
    """إصلاح هيكل جدول الفروع"""
    
    print("🔧 إصلاح هيكل جدول الفروع...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(branches)")
        existing_columns = cursor.fetchall()
        column_names = [col[1] for col in existing_columns]
        
        print("📋 الأعمدة الموجودة حالياً:")
        for col in column_names:
            print(f"   • {col}")
        
        # 2. إضافة الأعمدة المفقودة
        required_columns = {
            'bank_id': 'INTEGER',
            'exchange_id': 'INTEGER'
        }
        
        for column_name, column_type in required_columns.items():
            if column_name not in column_names:
                try:
                    cursor.execute(f"ALTER TABLE branches ADD COLUMN {column_name} {column_type}")
                    print(f"✅ تم إضافة عمود {column_name}")
                except sqlite3.OperationalError as e:
                    print(f"⚠️ خطأ في إضافة عمود {column_name}: {e}")
            else:
                print(f"ℹ️ عمود {column_name} موجود بالفعل")
        
        # 3. تحديث الفروع الموجودة لربطها بالبنوك/الصرافات
        print("\n🔄 تحديث الفروع الموجودة...")
        
        # تحديث الفروع التي لها parent_type = "بنك"
        cursor.execute("""
            UPDATE branches 
            SET bank_id = parent_id 
            WHERE parent_type = 'بنك' AND bank_id IS NULL
        """)
        updated_banks = cursor.rowcount
        if updated_banks > 0:
            print(f"✅ تم تحديث {updated_banks} فرع بنكي")
        
        # تحديث الفروع التي لها parent_type = "صراف"
        cursor.execute("""
            UPDATE branches 
            SET exchange_id = parent_id 
            WHERE parent_type = 'صراف' AND exchange_id IS NULL
        """)
        updated_exchanges = cursor.rowcount
        if updated_exchanges > 0:
            print(f"✅ تم تحديث {updated_exchanges} فرع صرافة")
        
        # 4. التحقق من النتيجة النهائية
        print("\n🔍 التحقق من النتيجة النهائية...")
        cursor.execute("PRAGMA table_info(branches)")
        final_columns = cursor.fetchall()
        final_column_names = [col[1] for col in final_columns]
        
        print("📋 الأعمدة النهائية:")
        for col in final_column_names:
            print(f"   • {col}")
        
        # عرض عينة من البيانات
        cursor.execute("""
            SELECT id, name, parent_type, parent_id, bank_id, exchange_id 
            FROM branches 
            LIMIT 3
        """)
        sample_data = cursor.fetchall()
        
        if sample_data:
            print("\n📊 عينة من البيانات:")
            for row in sample_data:
                print(f"   ID: {row[0]}, الاسم: {row[1]}, النوع: {row[2]}, parent_id: {row[3]}, bank_id: {row[4]}, exchange_id: {row[5]}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح هيكل جدول الفروع بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح هيكل الجدول: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_fixed_structure():
    """اختبار الهيكل المصلح"""
    
    print("\n🧪 اختبار الهيكل المصلح...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = Path("data/proshipment.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على بنك للاختبار
        cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 LIMIT 1")
        bank = cursor.fetchone()
        
        if bank:
            print(f"🏦 اختبار مع البنك: {bank[1]} (ID: {bank[0]})")
            
            # إضافة فرع تجريبي
            cursor.execute("""
                INSERT INTO branches (
                    name, code, type, parent_type, parent_id, bank_id, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                'فرع اختبار الهيكل',
                'STRUCT_TEST',
                'فرع اختبار',
                'بنك',
                bank[0],
                bank[0],
                1
            ))
            
            test_id = cursor.lastrowid
            print(f"✅ تم إضافة فرع اختبار (ID: {test_id})")
            
            # التحقق من البيانات
            cursor.execute("""
                SELECT b.id, b.name, b.bank_id, banks.name as bank_name
                FROM branches b
                LEFT JOIN banks ON b.bank_id = banks.id
                WHERE b.id = ?
            """, (test_id,))
            
            result = cursor.fetchone()
            if result:
                print(f"   ID: {result[0]}")
                print(f"   الاسم: {result[1]}")
                print(f"   bank_id: {result[2]}")
                print(f"   اسم البنك: {result[3]}")
            
            # حذف الفرع التجريبي
            cursor.execute("DELETE FROM branches WHERE id = ?", (test_id,))
            print("🗑️ تم حذف الفرع التجريبي")
            
        else:
            print("❌ لا توجد بنوك للاختبار!")
        
        conn.commit()
        conn.close()
        
        print("🎉 اختبار الهيكل المصلح نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الهيكل: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح هيكل جدول الفروع...")
    print("=" * 60)
    
    # إصلاح الهيكل
    fix_success = fix_branches_table_structure()
    
    if fix_success:
        # اختبار الهيكل المصلح
        test_success = test_fixed_structure()
        
        if test_success:
            print("\n🏆 تم إصلاح واختبار هيكل جدول الفروع بنجاح!")
            print("✅ الآن يمكن إضافة الفروع وتحديث القائمة بشكل صحيح")
        else:
            print("\n⚠️ تم الإصلاح ولكن فشل الاختبار")
    else:
        print("\n❌ فشل في إصلاح الهيكل!")
    
    print("=" * 60)
