#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق من إصلاح مشكلة الاستيراد
Fix Verification Test
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import_fix():
    """اختبار إصلاح مشكلة الاستيراد"""
    
    print("🔧 اختبار إصلاح مشكلة الاستيراد...")
    print("=" * 60)
    
    try:
        # محاولة استيراد شاشة طلب الحوالة
        print("📝 محاولة استيراد RemittanceRequestWindow...")
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        print("   ✅ تم الاستيراد بنجاح!")
        
        # محاولة استيراد النافذة الرئيسية
        print("\n🏠 محاولة استيراد MainWindow...")
        from src.ui.main_window import MainWindow
        print("   ✅ تم الاستيراد بنجاح!")
        
        # التحقق من وجود الدالة الجديدة
        if hasattr(MainWindow, 'open_remittance_request_window'):
            print("   ✅ دالة open_remittance_request_window موجودة")
        else:
            print("   ❌ دالة open_remittance_request_window مفقودة")
            return False
        
        print("\n🎉 جميع الاستيرادات نجحت!")
        return True
        
    except ImportError as e:
        print(f"   ❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ عام: {str(e)}")
        return False

def check_file_content():
    """فحص محتوى الملف للتأكد من الإصلاح"""
    
    print("\n🔍 فحص محتوى الملف...")
    print("=" * 60)
    
    try:
        file_path = "src/ui/remittances/remittance_request_window.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن pyqtSignal
        if 'pyqtSignal' in content:
            print("   ❌ لا يزال يحتوي على 'pyqtSignal'")
            return False
        else:
            print("   ✅ تم إزالة 'pyqtSignal' بنجاح")
        
        # البحث عن Signal
        if 'Signal' in content:
            print("   ✅ يحتوي على 'Signal' الصحيح")
        else:
            print("   ❌ لا يحتوي على 'Signal'")
            return False
        
        # فحص سطر الاستيراد
        import_lines = [line for line in content.split('\n') if 'from PySide6.QtCore import' in line]
        
        if import_lines:
            print(f"   ✅ سطر الاستيراد: {import_lines[0].strip()}")
        else:
            print("   ❌ لم يتم العثور على سطر الاستيراد")
            return False
        
        print("   ✅ محتوى الملف صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الملف: {str(e)}")
        return False

def display_solution():
    """عرض الحل المطبق"""
    
    print("\n" + "=" * 70)
    print("🎯 الحل المطبق لمشكلة الاستيراد")
    print("=" * 70)
    
    print("\n❌ المشكلة الأصلية:")
    print("   cannot import name 'pyqtSignal' from 'PySide6.QtCore'")
    
    print("\n🔍 السبب:")
    print("   - في PyQt5 نستخدم 'pyqtSignal'")
    print("   - في PySide6 نستخدم 'Signal' مباشرة")
    print("   - الكود كان يحاول استيراد 'pyqtSignal' من PySide6")
    
    print("\n✅ الحل:")
    print("   تم تغيير سطر الاستيراد من:")
    print("   from PySide6.QtCore import Qt, Signal, QDate, QTimer, QThread, pyqtSignal")
    print("   إلى:")
    print("   from PySide6.QtCore import Qt, Signal, QDate, QTimer, QThread")
    
    print("\n🚀 النتيجة:")
    print("   ✅ تم حل مشكلة الاستيراد")
    print("   ✅ شاشة طلب الحوالة تعمل الآن")
    print("   ✅ جميع الإشارات تستخدم 'Signal' الصحيح")
    
    print("\n🎯 كيفية الاستخدام:")
    print("   1. شغل البرنامج الرئيسي")
    print("   2. اذهب إلى القائمة الرئيسية")
    print("   3. اختر 'إدارة الحوالات'")
    print("   4. انقر على 'طلب حوالة'")
    print("   5. ستفتح الشاشة الجديدة بنجاح!")

if __name__ == "__main__":
    print("🚀 بدء التحقق من إصلاح مشكلة الاستيراد...")
    print("=" * 80)
    
    # فحص محتوى الملف
    content_ok = check_file_content()
    
    # اختبار الاستيراد
    import_ok = test_import_fix()
    
    # عرض الحل
    display_solution()
    
    # النتيجة النهائية
    if content_ok and import_ok:
        print("\n🏆 تم إصلاح المشكلة بنجاح!")
        print("✅ الملف تم تصحيحه")
        print("✅ الاستيراد يعمل بشكل صحيح")
        print("✅ النظام جاهز للاستخدام")
        
        print("\n🎉 يمكنك الآن استخدام شاشة طلب الحوالة بدون مشاكل!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إلى حل")
    
    print("=" * 80)
