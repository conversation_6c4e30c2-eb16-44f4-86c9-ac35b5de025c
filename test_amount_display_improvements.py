#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات عرض المبلغ في نموذج طباعة طلب الحوالة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_amount_text_addition():
    """اختبار إضافة نص المبلغ في النماذج"""
    print("💰 اختبار إضافة نص المبلغ...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي"),
            ("src/ui/remittances/remittance_pdf_generator.py", "مولد PDF")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن نص الطلب المحسن
            if "يرجى تحويل مبلغ" in content:
                print(f"      ✅ يحتوي على نص الطلب")
                
                # التحقق من وجود المبلغ والعملة
                if "amount_with_symbol" in content or "amount_words" in content:
                    print(f"      ✅ يحتوي على تنسيق المبلغ والعملة")
                else:
                    print(f"      ❌ لا يحتوي على تنسيق المبلغ")
                    return False
                    
                # التحقق من دالة تحويل المبلغ إلى كلمات
                if "convert_amount_to_words" in content:
                    print(f"      ✅ يحتوي على دالة تحويل المبلغ إلى كلمات")
                else:
                    print(f"      ❌ لا يحتوي على دالة تحويل المبلغ")
                    return False
            else:
                print(f"      ❌ لا يحتوي على نص الطلب")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نص المبلغ: {e}")
        return False

def test_currency_formatting():
    """اختبار تنسيق العملات"""
    print("\n💱 اختبار تنسيق العملات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # بيانات تجريبية لعملات مختلفة
        test_cases = [
            {'amount': '5000', 'currency': 'USD', 'expected_symbol': '$5000'},
            {'amount': '1000', 'currency': 'EUR', 'expected_symbol': '€1000'},
            {'amount': '500000', 'currency': 'YER', 'expected_symbol': '500000 ريال يمني'},
            {'amount': '2000', 'currency': 'SAR', 'expected_symbol': '2000 ريال سعودي'},
        ]
        
        # اختبار النموذج الأساسي
        print("   📋 اختبار النموذج الأساسي:")
        try:
            from src.ui.remittances.remittance_print_template import RemittancePrintTemplate
            
            for test_case in test_cases:
                test_data = {
                    'remittance_amount': test_case['amount'],
                    'currency': test_case['currency']
                }
                
                template = RemittancePrintTemplate(test_data)
                formatted = template.format_amount_with_currency(test_case['amount'], test_case['currency'])
                
                if formatted == test_case['expected_symbol']:
                    print(f"      ✅ {test_case['currency']}: {formatted}")
                else:
                    print(f"      ❌ {test_case['currency']}: متوقع {test_case['expected_symbol']}, حصل على {formatted}")
                    template.close()
                    return False
                
                template.close()
                
        except Exception as e:
            print(f"      ❌ خطأ في النموذج الأساسي: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق العملات: {e}")
        return False

def test_amount_to_words():
    """اختبار تحويل المبلغ إلى كلمات"""
    print("\n📝 اختبار تحويل المبلغ إلى كلمات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # حالات اختبار
        test_cases = [
            {'amount': '63500', 'currency': 'USD', 'expected_words': 'ثلاثة وستون ألف وخمسمائة'},
            {'amount': '5000', 'currency': 'USD', 'expected_words': 'خمسة آلاف'},
            {'amount': '1000', 'currency': 'SAR', 'expected_words': 'ألف'},
            {'amount': '500', 'currency': 'EUR', 'expected_words': 'خمسمائة'},
        ]
        
        # اختبار النموذج الأساسي
        print("   📋 اختبار تحويل الأرقام إلى كلمات:")
        try:
            from src.ui.remittances.remittance_print_template import RemittancePrintTemplate
            
            template = RemittancePrintTemplate({})
            
            for test_case in test_cases:
                words = template.convert_amount_to_words(test_case['amount'], test_case['currency'])
                
                if test_case['expected_words'] in words:
                    print(f"      ✅ {test_case['amount']} {test_case['currency']}: {words}")
                else:
                    print(f"      ❌ {test_case['amount']} {test_case['currency']}: لا يحتوي على '{test_case['expected_words']}'")
                    print(f"         النتيجة: {words}")
                    template.close()
                    return False
            
            template.close()
            
        except Exception as e:
            print(f"      ❌ خطأ في تحويل الأرقام: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحويل المبلغ: {e}")
        return False

def test_template_functionality():
    """اختبار وظائف النماذج مع المبلغ"""
    print("\n🧪 اختبار وظائف النماذج مع المبلغ...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # بيانات تجريبية
        test_data = {
            'request_number': '2025-001',
            'request_date': '2024/12/09',
            'remittance_amount': '63500',
            'currency': 'USD',
            'receiver_name': 'MOHAMMED AHMED ALI',
            'transfer_purpose': 'COST OF FOODSTUFF'
        }
        
        templates_to_test = [
            ("src.ui.remittances.remittance_print_template", "RemittancePrintTemplate", "النموذج الأساسي"),
            ("src.ui.remittances.simple_print_template", "SimplePrintTemplate", "النموذج المبسط"),
            ("src.ui.remittances.professional_print_template", "ProfessionalPrintTemplate", "النموذج الاحترافي")
        ]
        
        for module_name, class_name, template_desc in templates_to_test:
            print(f"   📋 اختبار {template_desc}:")
            
            try:
                # استيراد النموذج
                module = __import__(module_name, fromlist=[class_name])
                template_class = getattr(module, class_name)
                
                # إنشاء النموذج
                template = template_class(test_data)
                
                # التحقق من وجود نص الطلب
                if hasattr(template, 'request_text'):
                    request_text = template.request_text.text()
                    
                    if "يرجى تحويل مبلغ" in request_text:
                        print(f"      ✅ يحتوي على نص الطلب")
                        
                        if "$63500" in request_text or "63500" in request_text:
                            print(f"      ✅ يحتوي على المبلغ")
                        else:
                            print(f"      ❌ لا يحتوي على المبلغ")
                            print(f"         النص: {request_text}")
                            template.close()
                            return False
                            
                        if "ثلاثة وستون ألف" in request_text:
                            print(f"      ✅ يحتوي على المبلغ كتابة")
                        else:
                            print(f"      ❌ لا يحتوي على المبلغ كتابة")
                            print(f"         النص: {request_text}")
                            template.close()
                            return False
                    else:
                        print(f"      ❌ لا يحتوي على نص الطلب")
                        template.close()
                        return False
                else:
                    print(f"      ❌ لا يحتوي على عنصر نص الطلب")
                    template.close()
                    return False
                
                template.close()
                
            except Exception as e:
                print(f"      ❌ خطأ في {template_desc}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات عرض المبلغ في نموذج طباعة طلب الحوالة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_amount_text_addition())
    results.append(test_currency_formatting())
    results.append(test_amount_to_words())
    results.append(test_template_functionality())
    
    # عرض النتائج النهائية
    print("\n" + "="*80)
    print("🎯 ملخص اختبار تحسينات عرض المبلغ:")
    print("="*80)
    
    test_names = [
        "إضافة نص المبلغ في النماذج",
        "تنسيق العملات",
        "تحويل المبلغ إلى كلمات",
        "وظائف النماذج مع المبلغ"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع تحسينات عرض المبلغ تعمل بنجاح!")
        print("✅ تم إضافة نص الطلب مع المبلغ في جميع النماذج")
        print("✅ تنسيق العملات يعمل بشكل صحيح")
        print("✅ تحويل المبلغ إلى كلمات يعمل بشكل صحيح")
        print("✅ جميع النماذج تعرض المبلغ رقماً ونصاً")
        
        print("\n🌟 الميزات الجديدة:")
        print("   💰 عرض المبلغ مع رمز العملة")
        print("   📝 تحويل المبلغ إلى كلمات عربية")
        print("   💱 دعم عملات متعددة (USD, EUR, YER, SAR)")
        print("   🎨 تصميم جذاب لنص الطلب")
        print("   📋 تطبيق شامل في جميع النماذج")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم تحسينات عرض المبلغ تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحسينات فشلت. يرجى مراجعة:")
        print("- نص الطلب مع المبلغ")
        print("- تنسيق العملات")
        print("- تحويل المبلغ إلى كلمات")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
