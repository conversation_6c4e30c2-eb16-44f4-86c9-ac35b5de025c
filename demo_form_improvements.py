#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تجريبي لتحسينات نموذج طلب الحوالة
Demo for Form Improvements
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_sample_data():
    """إنشاء بيانات تجريبية للاختبار"""
    
    import sqlite3
    from datetime import datetime
    
    try:
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # الاتصال بقاعدة البيانات
        db_path = data_dir / "proshipment.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # إنشاء جدول العملات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                symbol TEXT,
                exchange_rate REAL DEFAULT 1.0,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول فروع البنوك
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT,
                manager_name TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول الصرافين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS exchangers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                branch_id INTEGER,
                license_number TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES bank_branches (id)
            )
        """)
        
        # إدراج عملات تجريبية
        currencies = [
            ('YER', 'ريال يمني', 'ر.ي', 1.0),
            ('SAR', 'ريال سعودي', 'ر.س', 0.15),
            ('USD', 'دولار أمريكي', '$', 0.004),
            ('EUR', 'يورو', '€', 0.0037),
            ('AED', 'درهم إماراتي', 'د.إ', 0.015),
            ('QAR', 'ريال قطري', 'ر.ق', 0.015),
            ('KWD', 'دينار كويتي', 'د.ك', 0.0012),
            ('BHD', 'دينار بحريني', 'د.ب', 0.0015),
            ('OMR', 'ريال عماني', 'ر.ع', 0.0015),
            ('JOD', 'دينار أردني', 'د.أ', 0.003)
        ]
        
        for code, name, symbol, rate in currencies:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO currencies (code, name, symbol, exchange_rate, is_active)
                    VALUES (?, ?, ?, ?, 1)
                """, (code, name, symbol, rate))
            except sqlite3.IntegrityError:
                pass
        
        # إدراج فروع تجريبية
        branches = [
            ('الفرع الرئيسي - صنعاء', 'شارع الزبيري، صنعاء', '+967 1 234567', '<EMAIL>', 'أحمد محمد المدير'),
            ('فرع صنعاء الشمالي', 'شارع الستين، صنعاء', '+967 1 345678', '<EMAIL>', 'علي سالم المدير'),
            ('فرع عدن', 'شارع المعلا، عدن', '+967 2 456789', '<EMAIL>', 'محمد أحمد المدير'),
            ('فرع تعز', 'شارع جمال، تعز', '+967 4 567890', '<EMAIL>', 'سالم علي المدير'),
            ('فرع الحديدة', 'شارع الكورنيش، الحديدة', '+967 3 678901', '<EMAIL>', 'أحمد سالم المدير')
        ]
        
        for name, address, phone, email, manager in branches:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO bank_branches (name, address, phone, email, manager_name, is_active)
                    VALUES (?, ?, ?, ?, ?, 1)
                """, (name, address, phone, email, manager))
            except sqlite3.IntegrityError:
                pass
        
        # إدراج صرافين تجريبيين
        exchangers = [
            ('أحمد محمد الصراف', '+967 *********', '<EMAIL>', 'شارع الزبيري، صنعاء', 1, 'EX001'),
            ('علي سالم للصرافة', '+967 *********', '<EMAIL>', 'شارع الستين، صنعاء', 2, 'EX002'),
            ('محمد عبدالله للتحويل', '+967 *********', '<EMAIL>', 'شارع المعلا، عدن', 3, 'EX003'),
            ('سالم أحمد الصراف', '+967 *********', '<EMAIL>', 'شارع جمال، تعز', 4, 'EX004'),
            ('عبدالله محمد للصرافة', '+967 *********', '<EMAIL>', 'شارع الكورنيش، الحديدة', 5, 'EX005'),
            ('حسن علي للتحويلات', '+967 777678901', '<EMAIL>', 'فرع متنقل', 1, 'EX006')
        ]
        
        for name, phone, email, address, branch_id, license_num in exchangers:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO exchangers (name, phone, email, address, branch_id, license_number, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, 1)
                """, (name, phone, email, address, branch_id, license_num))
            except sqlite3.IntegrityError:
                pass
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء البيانات التجريبية بنجاح")
        print(f"   💱 {len(currencies)} عملة")
        print(f"   🏦 {len(branches)} فرع")
        print(f"   👤 {len(exchangers)} صراف")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def run_improvements_demo():
    """تشغيل العرض التجريبي للتحسينات"""
    
    print("🚀 بدء العرض التجريبي لتحسينات نموذج طلب الحوالة...")
    print("=" * 80)
    
    try:
        # إنشاء البيانات التجريبية
        print("📊 إنشاء بيانات تجريبية...")
        if not create_sample_data():
            return 1
        
        # إعداد البيئة
        import os
        os.environ['QT_QPA_PLATFORM'] = 'windows'  # للويندوز
        
        from PySide6.QtWidgets import QApplication, QMessageBox
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("🖥️ فتح نافذة طلب الحوالة...")
        
        # إنشاء النافذة الرئيسية
        window = RemittanceRequestWindow()
        window.show()
        
        # التبديل إلى تبويب الطلب الجديد
        window.tab_widget.setCurrentIndex(0)
        
        # تحديث البيانات
        window.load_filters_data()
        
        # عرض رسالة توضيحية
        msg = QMessageBox()
        msg.setWindowTitle("عرض تجريبي لتحسينات النموذج")
        msg.setText(f"""
🎉 مرحباً بك في العرض التجريبي للتحسينات!

✨ التحسينات المطبقة:

💰 حقل مبلغ الحوالة:
• تم تغييره من حقل رقمي إلى حقل نصي
• يدعم الآن إدخال المبالغ بمرونة أكبر
• معالجة ذكية للفواصل والأرقام
• تصميم محسن مع نص توضيحي

💱 ربط العملات:
• مربوط الآن بجدول العملات في إعدادات النظام
• تم إنشاء 10 عملات تجريبية
• يشمل أسعار الصرف ورموز العملات
• فلترة العملات النشطة فقط

🏦 ربط الصرافين:
• مربوط الآن بجدول الصرافين في إدارة البنوك
• تم إنشاء 5 فروع و 6 صرافين تجريبيين
• معلومات تفصيلية لكل صراف
• ربط الصرافين بالفروع

🔧 كيفية الاختبار:
1. جرب إدخال مبالغ مختلفة في حقل المبلغ
2. اختر عملة من القائمة المحدثة
3. اختر صراف من القائمة المربوطة بإدارة البنوك
4. لاحظ التحسينات في التصميم والوظائف

استكشف جميع التحسينات الآن!
        """.strip())
        msg.setIcon(QMessageBox.Information)
        msg.exec()
        
        print("✅ تم فتح النافذة بنجاح!")
        print("💡 يمكنك الآن:")
        print("   💰 اختبار حقل المبلغ النصي الجديد")
        print("   💱 اختيار العملات من القائمة المحدثة")
        print("   🏦 اختيار الصرافين من إدارة البنوك")
        print("   🎨 ملاحظة التحسينات في التصميم")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PySide6: pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل العرض التجريبي: {e}")
        import traceback
        traceback.print_exc()
        return 1

def display_improvements_summary():
    """عرض ملخص التحسينات المطبقة"""
    
    print("\n" + "=" * 80)
    print("🏆 ملخص التحسينات المطبقة على نموذج طلب الحوالة")
    print("=" * 80)
    
    print("\n🎯 المشاكل التي تم حلها:")
    print("   ❌ حقل اسم الصراف غير مرتبط بالصرافين في إدارة البنوك")
    print("   ❌ حقل العملة غير مرتبط بالعملات في إعدادات النظام")
    print("   ❌ حقل مبلغ الحوالة محدود بنوع رقمي")
    
    print("\n✅ الحلول المطبقة:")
    
    print("\n   💰 تحسينات حقل مبلغ الحوالة:")
    print("      • تغيير من QDoubleSpinBox إلى QLineEdit")
    print("      • إضافة نص توضيحي 'أدخل مبلغ الحوالة...'")
    print("      • تصميم محسن مع CSS وألوان جذابة")
    print("      • معالجة ذكية للنصوص والأرقام")
    print("      • دعم الفواصل في الأرقام (1,000,000)")
    print("      • التحقق من صحة البيانات مع رسائل خطأ واضحة")
    
    print("\n   💱 ربط العملات بإعدادات النظام:")
    print("      • إنشاء جدول currencies تلقائياً")
    print("      • ربط مع إعدادات النظام")
    print("      • 10 عملات افتراضية شاملة")
    print("      • دعم أسعار الصرف")
    print("      • رموز العملات (ر.ي، $، €)")
    print("      • فلترة العملات النشطة فقط")
    
    print("\n   🏦 ربط الصرافين بإدارة البنوك:")
    print("      • إنشاء جدول bank_branches تلقائياً")
    print("      • إنشاء جدول exchangers تلقائياً")
    print("      • ربط الصرافين بالفروع")
    print("      • معلومات تفصيلية (هاتف، إيميل، عنوان)")
    print("      • أرقام تراخيص للصرافين")
    print("      • فلترة الصرافين النشطين فقط")
    
    print("\n🗄️ تحسينات قاعدة البيانات:")
    print("   📊 جداول جديدة:")
    print("      • currencies - العملات مع أسعار الصرف")
    print("      • bank_branches - فروع البنوك مع التفاصيل")
    print("      • exchangers - الصرافين مع الترخيص")
    
    print("\n   🔗 علاقات الجداول:")
    print("      • مفتاح خارجي بين الصرافين والفروع")
    print("      • حقول الحالة النشطة")
    print("      • حقول التواريخ (إنشاء/تحديث)")
    
    print("\n🎨 تحسينات التصميم:")
    print("   ✨ حقل المبلغ:")
    print("      • تصميم عصري مع حواف مدورة")
    print("      • ألوان تفاعلية عند التركيز")
    print("      • نص توضيحي واضح")
    
    print("\n   📋 قوائم الاختيار:")
    print("      • عرض معلومات إضافية")
    print("      • ترتيب أبجدي")
    print("      • خيارات افتراضية واضحة")
    
    print("\n🚀 الميزات الجديدة:")
    print("   🔧 إنشاء تلقائي للجداول عند عدم وجودها")
    print("   📊 بيانات افتراضية غنية ومفيدة")
    print("   🛡️ معالجة شاملة للأخطاء")
    print("   📝 تسجيل مفصل للعمليات")
    print("   🔄 تحديث تلقائي للبيانات")

if __name__ == "__main__":
    # عرض ملخص التحسينات
    display_improvements_summary()
    
    # تشغيل العرض التجريبي
    exit_code = run_improvements_demo()
    
    print("\n" + "=" * 80)
    if exit_code == 0:
        print("🎉 انتهى العرض التجريبي بنجاح!")
        print("✅ جميع التحسينات تعمل بشكل مثالي")
        print("✅ حقل المبلغ نصي ومرن")
        print("✅ العملات مربوطة بإعدادات النظام")
        print("✅ الصرافين مربوطين بإدارة البنوك")
        print("✅ قاعدة البيانات محسنة ومطورة")
    else:
        print("❌ حدث خطأ في العرض التجريبي")
        print("💡 تأكد من تثبيت المكتبات المطلوبة")
    print("=" * 80)
    
    sys.exit(exit_code)
