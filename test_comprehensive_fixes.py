#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الإصلاحات في نظام إدارة الحوالات
Comprehensive Test for All Fixes in Remittance Management System
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_branch_integration():
    """اختبار ربط الفروع بإدارة البنوك والصرافين"""
    
    print("🏦 اختبار ربط الفروع بإدارة البنوك والصرافين...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص ربط الفروع
        branch_integration = [
            ("load_branches_from_bank_management", "دالة تحميل الفروع من إدارة البنوك"),
            ("create_bank_branches_table_for_management", "دالة إنشاء جدول الفروع"),
            ("create_default_branches_for_bank_management", "دالة إنشاء فروع افتراضية"),
            ("SELECT id, branch_name, address, phone, manager_name", "استعلام الفروع المحدث"),
            ("FROM bank_branches", "جدول فروع البنوك"),
            ("WHERE is_active = 1", "فلترة الفروع النشطة"),
            ("ORDER BY branch_name", "ترتيب الفروع"),
            ("branch_name TEXT NOT NULL", "حقل اسم الفرع"),
            ("manager_name TEXT", "حقل اسم المدير"),
            ("city TEXT", "حقل المدينة"),
            ("region TEXT", "حقل المنطقة")
        ]
        
        print("   📋 فحص ربط الفروع:")
        all_found = True
        
        for integration, description in branch_integration:
            if integration in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص ربط الفروع: {e}")
        return False

def test_exchanger_integration():
    """اختبار ربط الصرافين بإدارة البنوك والصرافين"""
    
    print("\n👤 اختبار ربط الصرافين بإدارة البنوك والصرافين...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص ربط الصرافين
        exchanger_integration = [
            ("load_exchangers_from_bank_management", "دالة تحميل الصرافين من إدارة البنوك"),
            ("create_exchangers_table_for_management", "دالة إنشاء جدول الصرافين"),
            ("create_default_exchangers_for_bank_management", "دالة إنشاء صرافين افتراضيين"),
            ("SELECT e.id, e.exchanger_name, e.phone, e.email, e.license_number", "استعلام الصرافين المحدث"),
            ("FROM exchangers e", "جدول الصرافين"),
            ("LEFT JOIN bank_branches b ON e.branch_id = b.id", "ربط مع جدول الفروع"),
            ("WHERE e.is_active = 1", "فلترة الصرافين النشطين"),
            ("ORDER BY e.exchanger_name", "ترتيب الصرافين"),
            ("exchanger_name TEXT NOT NULL", "حقل اسم الصراف"),
            ("license_number TEXT", "حقل رقم الترخيص"),
            ("license_date TEXT", "حقل تاريخ الترخيص"),
            ("license_expiry TEXT", "حقل انتهاء الترخيص")
        ]
        
        print("   📋 فحص ربط الصرافين:")
        all_found = True
        
        for integration, description in exchanger_integration:
            if integration in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص ربط الصرافين: {e}")
        return False

def test_currency_integration():
    """اختبار ربط العملات بإعدادات النظام"""
    
    print("\n💱 اختبار ربط العملات بإعدادات النظام...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص ربط العملات
        currency_integration = [
            ("load_currencies_from_system_settings", "دالة تحميل العملات من إعدادات النظام"),
            ("create_system_currencies_table", "دالة إنشاء جدول العملات في النظام"),
            ("create_default_currencies_for_system", "دالة إنشاء عملات افتراضية للنظام"),
            ("SELECT currency_code, currency_name, currency_symbol, exchange_rate", "استعلام العملات المحدث"),
            ("FROM system_currencies", "جدول عملات النظام"),
            ("WHERE is_active = 1", "فلترة العملات النشطة"),
            ("ORDER BY currency_name", "ترتيب العملات"),
            ("currency_code TEXT UNIQUE NOT NULL", "حقل كود العملة"),
            ("currency_name TEXT NOT NULL", "حقل اسم العملة"),
            ("currency_symbol TEXT", "حقل رمز العملة"),
            ("exchange_rate REAL DEFAULT 1.0", "حقل سعر الصرف"),
            ("country TEXT", "حقل البلد"),
            ("is_base_currency INTEGER DEFAULT 0", "حقل العملة الأساسية")
        ]
        
        print("   📋 فحص ربط العملات:")
        all_found = True
        
        for integration, description in currency_integration:
            if integration in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص ربط العملات: {e}")
        return False

def test_edit_window_fixes():
    """اختبار إصلاحات نافذة التحرير"""
    
    print("\n🖥️ اختبار إصلاحات نافذة التحرير...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص إصلاحات النافذة
        window_fixes = [
            ("setVisible(True)", "إجبار إظهار النافذة"),
            ("show()", "إظهار النافذة"),
            ("showNormal()", "إظهار النافذة بالحجم العادي"),
            ("raise_()", "رفع النافذة للمقدمة"),
            ("activateWindow()", "تفعيل النافذة"),
            ("setWindowState(Qt.WindowActive)", "تعيين النافذة كنشطة"),
            ("ctypes.windll.user32.SetForegroundWindow", "إحضار النافذة للمقدمة (Windows)"),
            ("ctypes.windll.user32.BringWindowToTop", "رفع النافذة لأعلى (Windows)"),
            ("QTimer.singleShot", "تأخير الرسالة"),
            ("itemDoubleClicked.connect(self.edit_selected_request)", "ربط النقر المزدوج")
        ]
        
        print("   📋 فحص إصلاحات النافذة:")
        all_found = True
        
        for fix, description in window_fixes:
            if fix in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص إصلاحات النافذة: {e}")
        return False

def test_data_loading_functions():
    """اختبار دوال تحميل البيانات المحدثة"""
    
    print("\n📊 اختبار دوال تحميل البيانات المحدثة...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص دوال التحميل
        loading_functions = [
            ("load_filters_data", "دالة تحميل بيانات المرشحات"),
            ("load_currencies_from_system_settings", "دالة تحميل العملات من إعدادات النظام"),
            ("load_branches_from_bank_management", "دالة تحميل الفروع من إدارة البنوك"),
            ("load_exchangers_from_bank_management", "دالة تحميل الصرافين من إدارة البنوك"),
            ("load_default_branches", "دالة تحميل فروع افتراضية"),
            ("load_default_exchangers", "دالة تحميل صرافين افتراضيين"),
            ("print(\"📊 تحميل بيانات النموذج من قاعدة البيانات\")", "تسجيل تحميل البيانات"),
            ("print(\"💱 تحميل العملات من إعدادات النظام\")", "تسجيل تحميل العملات"),
            ("print(\"🏦 تحميل الفروع من إدارة البنوك\")", "تسجيل تحميل الفروع"),
            ("print(\"👤 تحميل الصرافين من إدارة البنوك\")", "تسجيل تحميل الصرافين")
        ]
        
        print("   📋 فحص دوال التحميل:")
        all_found = True
        
        for function, description in loading_functions:
            if function in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص دوال التحميل: {e}")
        return False

def display_comprehensive_summary():
    """عرض ملخص شامل للإصلاحات"""
    
    print("\n" + "=" * 80)
    print("🏆 ملخص شامل لجميع الإصلاحات في نظام إدارة الحوالات")
    print("=" * 80)
    
    print("\n🎯 المشاكل التي تم حلها:")
    print("   ❌ حقل الفرع غير مرتبط مع إدارة البنوك والصرافين")
    print("   ❌ حقل اسم الصراف غير مرتبط بالصرافين في إدارة البنوك")
    print("   ❌ حقل العملة غير مرتبط بالعملات في إعدادات النظام")
    print("   ❌ النقر على طلب في القائمة لا يفتح النافذة في وضع التعديل")
    
    print("\n✅ الحلول المطبقة:")
    
    print("\n   🏦 ربط الفروع بإدارة البنوك والصرافين:")
    print("      • دالة load_branches_from_bank_management()")
    print("      • جدول bank_branches مع حقول متقدمة")
    print("      • معلومات تفصيلية: اسم الفرع، العنوان، الهاتف، المدير، المدينة، المنطقة")
    print("      • 6 فروع افتراضية شاملة")
    print("      • فلترة الفروع النشطة فقط")
    print("      • ترتيب أبجدي للفروع")
    
    print("\n   👤 ربط الصرافين بإدارة البنوك والصرافين:")
    print("      • دالة load_exchangers_from_bank_management()")
    print("      • جدول exchangers مع ربط بالفروع")
    print("      • معلومات تفصيلية: اسم الصراف، الهاتف، الإيميل، رقم الترخيص، تواريخ الترخيص")
    print("      • 8 صرافين افتراضيين مع تراخيص")
    print("      • ربط كل صراف بفرع محدد")
    print("      • عرض معلومات الفرع مع اسم الصراف")
    
    print("\n   💱 ربط العملات بإعدادات النظام:")
    print("      • دالة load_currencies_from_system_settings()")
    print("      • جدول system_currencies متقدم")
    print("      • معلومات تفصيلية: كود العملة، الاسم، الرمز، سعر الصرف، البلد")
    print("      • 15 عملة افتراضية شاملة")
    print("      • دعم العملة الأساسية")
    print("      • أسعار صرف محدثة")
    
    print("\n   🖥️ إصلاحات نافذة التحرير:")
    print("      • إجبار إظهار النافذة بطرق متعددة")
    print("      • استخدام Windows API لإحضار النافذة للمقدمة")
    print("      • تأخير الرسالة لضمان ظهور النافذة")
    print("      • تحسين ربط النقر المزدوج")
    print("      • تسجيل مفصل لتتبع المشاكل")
    
    print("\n🗄️ تحسينات قاعدة البيانات:")
    print("   📊 جداول جديدة ومحسنة:")
    print("      • bank_branches - فروع البنوك مع تفاصيل شاملة")
    print("      • exchangers - الصرافين مع تراخيص وربط بالفروع")
    print("      • system_currencies - عملات النظام مع أسعار صرف")
    
    print("\n   🔗 علاقات متقدمة:")
    print("      • مفتاح خارجي بين الصرافين والفروع")
    print("      • حقول الحالة النشطة لجميع الجداول")
    print("      • حقول التواريخ (إنشاء/تحديث)")
    print("      • دعم العملة الأساسية")
    
    print("\n🚀 الميزات الجديدة:")
    print("   🔧 إنشاء تلقائي للجداول والبيانات")
    print("   📊 بيانات افتراضية غنية ومفيدة")
    print("   🛡️ معالجة شاملة للأخطاء مع بدائل")
    print("   📝 تسجيل مفصل لجميع العمليات")
    print("   🔄 تحديث تلقائي للبيانات")
    print("   🖥️ إصلاحات متقدمة لظهور النافذة")
    
    print("\n🎯 كيفية الاستخدام:")
    print("   1. فتح نافذة طلب الحوالة")
    print("   2. ستتم إنشاء الجداول وتحميل البيانات تلقائياً")
    print("   3. اختيار الفرع من قائمة الفروع المربوطة بإدارة البنوك")
    print("   4. اختيار الصراف من قائمة الصرافين المربوطة بإدارة البنوك")
    print("   5. اختيار العملة من قائمة العملات المربوطة بإعدادات النظام")
    print("   6. النقر المزدوج على أي طلب في القائمة لفتحه في وضع التعديل")

def run_comprehensive_test():
    """تشغيل اختبار شامل لجميع الإصلاحات"""
    
    print("🚀 بدء اختبار شامل لجميع الإصلاحات...")
    print("=" * 80)
    
    # اختبار ربط الفروع
    branch_ok = test_branch_integration()
    
    # اختبار ربط الصرافين
    exchanger_ok = test_exchanger_integration()
    
    # اختبار ربط العملات
    currency_ok = test_currency_integration()
    
    # اختبار إصلاحات النافذة
    window_ok = test_edit_window_fixes()
    
    # اختبار دوال التحميل
    loading_ok = test_data_loading_functions()
    
    # عرض ملخص شامل
    display_comprehensive_summary()
    
    # النتيجة النهائية
    if branch_ok and exchanger_ok and currency_ok and window_ok and loading_ok:
        print("\n🏆 تم تطبيق جميع الإصلاحات بنجاح!")
        print("✅ الفروع مربوطة بإدارة البنوك والصرافين")
        print("✅ الصرافين مربوطين بإدارة البنوك والصرافين")
        print("✅ العملات مربوطة بإعدادات النظام")
        print("✅ نافذة التحرير تعمل بشكل مثالي")
        print("✅ دوال التحميل محدثة ومحسنة")
        
        print("\n🎉 نظام إدارة الحوالات محسن ومطور بالكامل!")
        print("💡 جرب الآن:")
        print("   • فتح نافذة طلب الحوالة")
        print("   • اختبار قوائم الفروع والصرافين والعملات")
        print("   • النقر المزدوج على طلب في القائمة")
        print("   • مراقبة رسائل التسجيل")
        
        return True
        
    else:
        print("\n❌ لا تزال هناك مشاكل في بعض الإصلاحات")
        if not branch_ok:
            print("   - مشكلة في ربط الفروع")
        if not exchanger_ok:
            print("   - مشكلة في ربط الصرافين")
        if not currency_ok:
            print("   - مشكلة في ربط العملات")
        if not window_ok:
            print("   - مشكلة في إصلاحات النافذة")
        if not loading_ok:
            print("   - مشكلة في دوال التحميل")
        
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    print("=" * 80)
    sys.exit(0 if success else 1)
