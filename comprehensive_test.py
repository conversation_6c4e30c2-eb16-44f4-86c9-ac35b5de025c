#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def comprehensive_test():
    """فحص شامل لنافذة إنشاء الحوالة"""
    print("🔍 فحص شامل لنافذة إنشاء الحوالة...")
    print("=" * 60)
    
    errors_found = []
    warnings_found = []
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication, QLineEdit, QDoubleSpinBox, QComboBox
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة
        print("\n🔄 إنشاء النافذة...")
        dialog = NewRemittanceDialog()
        print("✅ تم إنشاء نافذة الحوالة بنجاح")
        
        # فحص الحقول الأساسية
        print("\n🔄 فحص الحقول الأساسية...")
        
        required_fields = [
            'remittance_date',
            'remittance_number_input', 
            'transfer_entity_combo',
            'transfer_entity_name_combo',
            'reference_number_input',
            'currency_combo',
            'amount_input',
            'status_combo',
            'notes_input',
            'suppliers_table'
        ]
        
        for field_name in required_fields:
            if hasattr(dialog, field_name):
                field = getattr(dialog, field_name)
                field_type = type(field).__name__
                print(f"   ✅ {field_name}: {field_type}")
            else:
                error_msg = f"حقل مفقود: {field_name}"
                errors_found.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # فحص الأزرار
        print("\n🔄 فحص الأزرار...")
        
        required_buttons = [
            'save_draft_btn',
            'confirm_btn', 
            'transfer_to_suppliers_btn',
            'cancel_btn',
            'clear_btn'
        ]
        
        for button_name in required_buttons:
            if hasattr(dialog, button_name):
                print(f"   ✅ {button_name}: موجود")
            else:
                error_msg = f"زر مفقود: {button_name}"
                errors_found.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # فحص الدوال الأساسية
        print("\n🔄 فحص الدوال الأساسية...")
        
        required_functions = [
            'setup_validators',
            'validate_form',
            'collect_form_data',
            'update_transfer_entity_name',
            'add_supplier_to_remittance',
            'setup_suppliers_table',
            'save_as_draft',
            'confirm_remittance',
            'transfer_to_suppliers'
        ]
        
        for func_name in required_functions:
            if hasattr(dialog, func_name):
                print(f"   ✅ {func_name}: موجودة")
            else:
                error_msg = f"دالة مفقودة: {func_name}"
                errors_found.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # اختبار الدوال
        print("\n🔄 اختبار الدوال...")
        
        # اختبار setup_validators
        try:
            dialog.setup_validators()
            print("   ✅ setup_validators: تعمل بدون أخطاء")
        except Exception as e:
            error_msg = f"خطأ في setup_validators: {e}"
            errors_found.append(error_msg)
            print(f"   ❌ {error_msg}")
        
        # اختبار validate_form
        try:
            result = dialog.validate_form()
            print(f"   ✅ validate_form: تعمل بدون أخطاء (النتيجة: {result})")
        except Exception as e:
            error_msg = f"خطأ في validate_form: {e}"
            errors_found.append(error_msg)
            print(f"   ❌ {error_msg}")
        
        # اختبار collect_form_data
        try:
            data = dialog.collect_form_data()
            print("   ✅ collect_form_data: تعمل بدون أخطاء")
        except Exception as e:
            error_msg = f"خطأ في collect_form_data: {e}"
            errors_found.append(error_msg)
            print(f"   ❌ {error_msg}")
        
        # اختبار update_transfer_entity_name
        try:
            dialog.update_transfer_entity_name()
            print("   ✅ update_transfer_entity_name: تعمل بدون أخطاء")
        except Exception as e:
            error_msg = f"خطأ في update_transfer_entity_name: {e}"
            errors_found.append(error_msg)
            print(f"   ❌ {error_msg}")
        
        # فحص نوع حقل المبلغ
        print("\n🔄 فحص نوع حقل المبلغ...")
        if hasattr(dialog, 'amount_input'):
            if isinstance(dialog.amount_input, QLineEdit):
                print("   ✅ حقل المبلغ من نوع QLineEdit (صحيح)")
            elif isinstance(dialog.amount_input, QDoubleSpinBox):
                warning_msg = "حقل المبلغ من نوع QDoubleSpinBox (قد يسبب مشاكل)"
                warnings_found.append(warning_msg)
                print(f"   ⚠️ {warning_msg}")
            else:
                error_msg = f"حقل المبلغ من نوع غير متوقع: {type(dialog.amount_input).__name__}"
                errors_found.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # فحص جدول الموردين
        print("\n🔄 فحص جدول الموردين...")
        if hasattr(dialog, 'suppliers_table'):
            table = dialog.suppliers_table
            expected_columns = ["المورد", "المبلغ", "العملة", "الوصف", "الحالة"]
            actual_columns = []
            
            for i in range(table.columnCount()):
                header_item = table.horizontalHeaderItem(i)
                if header_item:
                    actual_columns.append(header_item.text())
            
            if actual_columns == expected_columns:
                print("   ✅ أعمدة جدول الموردين صحيحة")
            else:
                error_msg = f"أعمدة جدول الموردين غير صحيحة: {actual_columns}"
                errors_found.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # تقرير النتائج
        print("\n" + "=" * 60)
        print("📊 تقرير الفحص الشامل:")
        print(f"   🔴 أخطاء: {len(errors_found)}")
        print(f"   🟡 تحذيرات: {len(warnings_found)}")
        
        if errors_found:
            print("\n❌ الأخطاء المكتشفة:")
            for i, error in enumerate(errors_found, 1):
                print(f"   {i}. {error}")
        
        if warnings_found:
            print("\n⚠️ التحذيرات:")
            for i, warning in enumerate(warnings_found, 1):
                print(f"   {i}. {warning}")
        
        if not errors_found and not warnings_found:
            print("\n🎉 لا توجد أخطاء أو تحذيرات!")
            print("✅ النافذة جاهزة للاستخدام بشكل كامل.")
            return True
        elif not errors_found:
            print("\n✅ لا توجد أخطاء، فقط تحذيرات بسيطة.")
            print("🎯 النافذة تعمل بشكل صحيح.")
            return True
        else:
            print("\n❌ يوجد أخطاء تحتاج إلى إصلاح.")
            return False
        
    except Exception as e:
        print(f"\n💥 خطأ كبير في الفحص: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_test_summary():
    """عرض ملخص الاختبار"""
    print("\n📝 ملخص الفحص الشامل:")
    print("=" * 40)
    print("الهدف:")
    print("   🎯 فحص جميع مكونات النافذة")
    print("   🎯 اختبار جميع الدوال الأساسية")
    print("   🎯 التأكد من عدم وجود أخطاء")
    print()
    print("المكونات المفحوصة:")
    print("   ✓ الحقول الأساسية")
    print("   ✓ الأزرار والإجراءات")
    print("   ✓ الدوال والوظائف")
    print("   ✓ جدول الموردين")
    print("   ✓ أنواع البيانات")

if __name__ == "__main__":
    success = comprehensive_test()
    
    if success:
        show_test_summary()
        print("\n🚀 النافذة جاهزة للاستخدام!")
    else:
        print("\n🔧 يوجد مشاكل تحتاج إلى إصلاح إضافي.")
