#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توحيد أحجام خطوط أسماء الشركة بالعربية والإنجليزية
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_unified_font_sizes():
    """اختبار توحيد أحجام الخطوط"""
    print("🔤 اختبار توحيد أحجام خطوط أسماء الشركة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-UNIFIED-FONTS',
            'request_date': '2024/01/03',
            'remittance_amount': '95,000',
            'currency': 'SAR',
            'transfer_purpose': 'UNIFIED FONT SIZES TEST',
            'exchanger': 'شركة الصرافة الموحدة',
            
            # بيانات المستفيد
            'receiver_name': 'UNIFIED FONTS TEST COMPANY LIMITED',
            'receiver_address': 'TEST ADDRESS FOR UNIFIED FONTS',
            'receiver_city': 'RIYADH',
            'receiver_phone': '+966 11 5555555',
            'receiver_account': '5555555555555555555',
            'receiver_country': 'SAUDI ARABIA',
            
            # بيانات البنك
            'receiver_bank': 'SAUDI NATIONAL BANK',
            'receiver_bank_branch': 'RIYADH MAIN BRANCH',
            'receiver_bank_address': 'KING FAHD ROAD, RIYADH',
            'receiver_swift': 'NCBKSARI',
            
            # بيانات المرسل
            'manager_name': 'مدير الخطوط الموحدة'
        }
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # التحقق من بيانات الشركة
        if pdf_generator.company_data:
            print("✅ بيانات الشركة للاختبار:")
            print(f"   الاسم العربي: {pdf_generator.company_data['name']}")
            print(f"   الاسم الإنجليزي: {pdf_generator.company_data['name_en']}")
            print("   📏 سيتم استخدام حجم خط 12 لكلا الاسمين")
        else:
            print("⚠️ لم يتم تحميل بيانات الشركة")
        
        # إنشاء ملف PDF مع الخطوط الموحدة
        output_path = "نموذج_خطوط_موحدة.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        print(f"✅ تم إنشاء النموذج مع الخطوط الموحدة: {result_path}")
        
        # التحقق من وجود الملف
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"📄 حجم الملف: {file_size} بايت")
            print(f"📁 مسار الملف: {Path(result_path).absolute()}")
            
            # التحقق من نوع الرأس المستخدم
            logo_path = pdf_generator.get_company_logo_path()
            if logo_path:
                print(f"🖼️ تم استخدام الشعار: {logo_path}")
                print("   📏 أسماء الشركة بحجم خط موحد (12) حول الشعار")
            else:
                print("📝 تم استخدام الرأس النصي")
                print("   📏 أسماء الشركة بحجم خط موحد (12) في الرأس النصي")
            
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_header_unified_fonts():
    """اختبار الرأس النصي مع الخطوط الموحدة"""
    print("\n📝 اختبار الرأس النصي مع الخطوط الموحدة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # إخفاء الشعار مؤقتاً لاختبار الرأس النصي
        original_logo_path = None
        if pdf_generator.company_data and pdf_generator.company_data.get('logo_path'):
            original_logo_path = pdf_generator.company_data['logo_path']
            pdf_generator.company_data['logo_path'] = None
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-TEXT-UNIFIED',
            'request_date': '2024/01/03',
            'remittance_amount': '55,000',
            'currency': 'SAR',
            'transfer_purpose': 'TEXT HEADER UNIFIED FONTS TEST',
            'exchanger': 'شركة الصرافة النصية الموحدة',
            'receiver_name': 'TEXT UNIFIED FONTS COMPANY',
            'receiver_country': 'SAUDI ARABIA',
            'manager_name': 'مدير الاختبار النصي الموحد'
        }
        
        # إنشاء ملف PDF مع الرأس النصي
        output_path = "نموذج_رأس_نصي_خطوط_موحدة.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        # استعادة مسار الشعار الأصلي
        if original_logo_path and pdf_generator.company_data:
            pdf_generator.company_data['logo_path'] = original_logo_path
        
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"✅ تم إنشاء النموذج النصي الموحد: {result_path}")
            print(f"📄 حجم الملف: {file_size} بايت")
            print("📝 الرأس النصي يحتوي على:")
            print("   ✅ اسم الشركة العربي (حجم خط 12)")
            print("   ✅ اسم الشركة الإنجليزي (حجم خط 12)")
            print("   ✅ أحجام خطوط موحدة لكلا الاسمين")
            print("   ✅ العناوين ومعلومات الاتصال بأحجام مناسبة")
            return True
        else:
            print("❌ فشل في إنشاء النموذج النصي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الرأس النصي: {e}")
        return False

def test_font_size_consistency():
    """اختبار اتساق أحجام الخطوط"""
    print("\n📊 اختبار اتساق أحجام الخطوط...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        if pdf_generator.company_data:
            print("📏 تحليل أحجام الخطوط المستخدمة:")
            
            # أحجام الخطوط المحددة في الكود
            font_sizes = {
                'اسم الشركة العربي': 12,
                'اسم الشركة الإنجليزي': 12,
                'العنوان العربي': 10,
                'العنوان الإنجليزي': 10,
                'معلومات الاتصال العربية': 9,
                'معلومات الاتصال الإنجليزية': 9,
                'البريد الإلكتروني': 8
            }
            
            print("\n✅ أحجام الخطوط المحددة:")
            for element, size in font_sizes.items():
                print(f"   {element}: {size}pt")
            
            # التحقق من التوحيد
            arabic_name_size = font_sizes['اسم الشركة العربي']
            english_name_size = font_sizes['اسم الشركة الإنجليزي']
            
            if arabic_name_size == english_name_size:
                print(f"\n✅ أسماء الشركة موحدة: {arabic_name_size}pt")
                print("✅ التوحيد محقق بنجاح")
                return True
            else:
                print(f"\n❌ أسماء الشركة غير موحدة:")
                print(f"   العربي: {arabic_name_size}pt")
                print(f"   الإنجليزي: {english_name_size}pt")
                return False
        else:
            print("❌ لم يتم تحميل بيانات الشركة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار اتساق الخطوط: {e}")
        return False

def compare_before_after():
    """مقارنة قبل وبعد التوحيد"""
    print("\n🔄 مقارنة قبل وبعد توحيد الخطوط...")
    
    print("📋 التغييرات المطبقة:")
    
    changes = [
        {
            'element': 'اسم الشركة العربي (حول الشعار)',
            'before': '11pt',
            'after': '12pt',
            'status': 'زيادة'
        },
        {
            'element': 'اسم الشركة الإنجليزي (حول الشعار)', 
            'before': '11pt',
            'after': '12pt',
            'status': 'زيادة'
        },
        {
            'element': 'اسم الشركة العربي (رأس نصي)',
            'before': '12pt',
            'after': '12pt',
            'status': 'بدون تغيير'
        },
        {
            'element': 'اسم الشركة الإنجليزي (رأس نصي)',
            'before': '12pt', 
            'after': '12pt',
            'status': 'بدون تغيير'
        }
    ]
    
    print("\n📊 جدول التغييرات:")
    print("="*80)
    print(f"{'العنصر':<40} {'قبل':<8} {'بعد':<8} {'الحالة':<15}")
    print("="*80)
    
    for change in changes:
        print(f"{change['element']:<40} {change['before']:<8} {change['after']:<8} {change['status']:<15}")
    
    print("="*80)
    print("\n✅ النتيجة: جميع أسماء الشركة الآن بحجم خط موحد (12pt)")
    print("✅ التوازن البصري محقق بين النصوص العربية والإنجليزية")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار توحيد أحجام خطوط أسماء الشركة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_font_size_consistency())
    results.append(compare_before_after())
    results.append(test_unified_font_sizes())
    results.append(test_text_header_unified_fonts())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار توحيد أحجام الخطوط:")
    print("="*60)
    
    test_names = [
        "اتساق أحجام الخطوط",
        "مقارنة قبل وبعد",
        "النموذج مع الخطوط الموحدة",
        "الرأس النصي مع الخطوط الموحدة"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 توحيد أحجام الخطوط تم بنجاح!")
        print("✅ أسماء الشركة بالعربية والإنجليزية بحجم خط موحد (12pt)")
        print("✅ التوازن البصري محقق في جميع أنواع الرؤوس")
        print("✅ الخطوط متناسقة ومقروءة")
        print("✅ التخطيط محسن ومتوازن")
        
        # عرض الملفات المنشأة
        created_files = [
            "نموذج_خطوط_موحدة.pdf",
            "نموذج_رأس_نصي_خطوط_موحدة.pdf"
        ]
        
        existing_files = [f for f in created_files if Path(f).exists()]
        if existing_files:
            print(f"\n📁 الملفات المنشأة:")
            for file in existing_files:
                print(f"   • {file}")
            print("يمكنك فتح الملفات لمراجعة الخطوط الموحدة")
            
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
