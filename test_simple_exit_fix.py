#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لإصلاح مشكلة رسالة الحفظ عند الخروج
Simple Test for Exit Save Message Fix
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🧪 اختبار الوظائف الأساسية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة طلب الحوالة
        window = RemittanceRequestWindow()
        
        print("   ✅ تم إنشاء نافذة طلب الحوالة")
        
        # اختبار 1: تبويب قائمة الطلبات
        window.tab_widget.setCurrentIndex(1)  # تبويب قائمة طلبات الحوالات
        has_changes_list = window.has_unsaved_changes()
        print(f"   📋 تبويب قائمة الطلبات - تغييرات غير محفوظة: {has_changes_list}")
        
        # اختبار 2: تبويب طلب جديد فارغ
        window.tab_widget.setCurrentIndex(0)  # تبويب طلب حوالة جديد
        window.clear_form()
        has_changes_empty = window.has_unsaved_changes()
        print(f"   📝 نموذج فارغ - تغييرات غير محفوظة: {has_changes_empty}")
        
        # اختبار 3: إدخال بيانات
        window.receiver_name_input.setText("اسم تجريبي")
        has_changes_data = window.has_unsaved_changes()
        print(f"   ✏️ نموذج مع بيانات - تغييرات غير محفوظة: {has_changes_data}")
        
        # اختبار 4: وضع التحرير بدون تعديل
        window.editing_request_id = 123
        window.form_modified = False
        has_changes_edit_no_mod = window.has_unsaved_changes()
        print(f"   🔧 وضع التحرير بدون تعديل - تغييرات غير محفوظة: {has_changes_edit_no_mod}")
        
        # اختبار 5: وضع التحرير مع تعديل
        window.form_modified = True
        has_changes_edit_mod = window.has_unsaved_changes()
        print(f"   ✏️ وضع التحرير مع تعديل - تغييرات غير محفوظة: {has_changes_edit_mod}")
        
        # تقييم النتائج
        expected_results = [False, False, True, False, True]
        actual_results = [has_changes_list, has_changes_empty, has_changes_data, has_changes_edit_no_mod, has_changes_edit_mod]
        
        success = expected_results == actual_results
        
        if success:
            print("   ✅ جميع الاختبارات نجحت!")
        else:
            print("   ❌ بعض الاختبارات فشلت")
            print(f"      المتوقع: {expected_results}")
            print(f"      الفعلي:  {actual_results}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مبسط لإصلاح مشكلة رسالة الحفظ")
    print("="*50)
    
    success = test_basic_functionality()
    
    print("\n" + "="*50)
    if success:
        print("🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ رسالة الحفظ ستظهر فقط عند الحاجة")
        print("❌ رسالة الحفظ لن تظهر عند استعراض المرفقات")
    else:
        print("⚠️ المشكلة لم يتم إصلاحها بالكامل")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
