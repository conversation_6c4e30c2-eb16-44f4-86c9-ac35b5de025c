#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات محاذاة التوقيع والمدير العام
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_signature_section_structure():
    """اختبار هيكل قسم التوقيع المحسن"""
    print("📝 اختبار هيكل قسم التوقيع...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي"),
            ("src/ui/remittances/remittance_pdf_generator.py", "مولد PDF")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن التحسينات
            if "manager_section" in content or "signature_x" in content:
                print(f"      ✅ يحتوي على قسم المدير المحسن")
                
                # التحقق من المحاذاة الاحترافية
                if "QVBoxLayout" in content and "المدير العام" in content:
                    print(f"      ✅ يحتوي على محاذاة عمودية احترافية")
                else:
                    print(f"      ❌ لا يحتوي على محاذاة عمودية")
                    return False
                    
                # التحقق من التصميم المحسن
                if "border-radius" in content or "padding" in content:
                    print(f"      ✅ يحتوي على تصميم محسن")
                else:
                    print(f"      ❌ لا يحتوي على تصميم محسن")
                    return False
            else:
                print(f"      ❌ لا يحتوي على قسم المدير المحسن")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل التوقيع: {e}")
        return False

def test_manager_title_positioning():
    """اختبار موضع عنوان المدير العام"""
    print("\n👔 اختبار موضع عنوان المدير العام...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'TEST-001',
            'request_date': '2024/12/09',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        templates_to_test = [
            ("src.ui.remittances.remittance_print_template", "RemittancePrintTemplate", "النموذج الأساسي"),
            ("src.ui.remittances.simple_print_template", "SimplePrintTemplate", "النموذج المبسط"),
            ("src.ui.remittances.professional_print_template", "ProfessionalPrintTemplate", "النموذج الاحترافي")
        ]
        
        for module_name, class_name, template_desc in templates_to_test:
            print(f"   📋 اختبار {template_desc}:")
            
            try:
                # استيراد النموذج
                module = __import__(module_name, fromlist=[class_name])
                template_class = getattr(module, class_name)
                
                # إنشاء النموذج
                template = template_class(test_data)
                
                # البحث عن عناصر التوقيع
                manager_elements = []
                signature_elements = []
                
                # البحث في جميع العناصر الفرعية
                def find_elements(widget):
                    for child in widget.findChildren(template.QLabel):
                        text = child.text()
                        if "المدير العام" in text:
                            manager_elements.append(child)
                        elif "نشأت رشاد قاسم الدبعي" in text:
                            signature_elements.append(child)
                
                find_elements(template)
                
                if manager_elements:
                    print(f"      ✅ وجد عنصر المدير العام")
                    
                    # التحقق من المحاذاة
                    manager_element = manager_elements[0]
                    alignment = manager_element.alignment()
                    
                    if alignment & template.Qt.AlignCenter:
                        print(f"      ✅ المدير العام محاذي في الوسط")
                    else:
                        print(f"      ⚠️ المدير العام غير محاذي في الوسط")
                else:
                    print(f"      ❌ لم يجد عنصر المدير العام")
                    template.close()
                    return False
                
                if signature_elements:
                    print(f"      ✅ وجد عنصر التوقيع")
                    
                    # التحقق من موضع التوقيع
                    signature_element = signature_elements[0]
                    if signature_element.parent():
                        print(f"      ✅ التوقيع في حاوي منفصل")
                    else:
                        print(f"      ⚠️ التوقيع ليس في حاوي منفصل")
                else:
                    print(f"      ❌ لم يجد عنصر التوقيع")
                    template.close()
                    return False
                
                template.close()
                
            except Exception as e:
                print(f"      ❌ خطأ في {template_desc}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موضع المدير: {e}")
        return False

def test_visual_styling():
    """اختبار التصميم المرئي"""
    print("\n🎨 اختبار التصميم المرئي...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.professional_print_template import ProfessionalPrintTemplate
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'STYLE-TEST-001',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        template = ProfessionalPrintTemplate(test_data)
        
        # البحث عن عناصر التصميم
        styled_elements = []
        
        def find_styled_elements(widget):
            for child in widget.findChildren(template.QFrame):
                style = child.styleSheet()
                if "border-radius" in style and "background" in style:
                    styled_elements.append(child)
        
        find_styled_elements(template)
        
        if styled_elements:
            print(f"   ✅ وجد {len(styled_elements)} عنصر مصمم")
            
            # فحص التصميم
            for i, element in enumerate(styled_elements[:3]):  # فحص أول 3 عناصر
                style = element.styleSheet()
                
                if "qlineargradient" in style:
                    print(f"   ✅ العنصر {i+1}: يحتوي على تدرج لوني")
                elif "border-radius" in style:
                    print(f"   ✅ العنصر {i+1}: يحتوي على زوايا مدورة")
                elif "padding" in style:
                    print(f"   ✅ العنصر {i+1}: يحتوي على حشو داخلي")
                else:
                    print(f"   ⚠️ العنصر {i+1}: تصميم أساسي")
        else:
            print(f"   ❌ لم يجد عناصر مصممة")
            template.close()
            return False
        
        template.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التصميم: {e}")
        return False

def test_pdf_signature_layout():
    """اختبار تخطيط التوقيع في PDF"""
    print("\n📄 اختبار تخطيط التوقيع في PDF...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'PDF-TEST-001',
            'manager_name': 'نشأت رشاد قاسم الدبعي',
            'remittance_amount': '5000',
            'currency': 'USD'
        }
        
        generator = RemittancePDFGenerator()
        
        # فحص الكود للتأكد من التحسينات
        import inspect
        source = inspect.getsource(generator.add_signature_section)
        
        if "signature_x" in source and "signature_y" in source:
            print(f"   ✅ يحتوي على إحداثيات التوقيع المحسنة")
            
            if "rect(" in source:
                print(f"   ✅ يحتوي على إطار للتوقيع")
            else:
                print(f"   ❌ لا يحتوي على إطار للتوقيع")
                return False
                
            if "drawCentredText" in source:
                print(f"   ✅ يحتوي على نص محاذي في الوسط")
            else:
                print(f"   ❌ لا يحتوي على نص محاذي في الوسط")
                return False
                
            if "المدير العام" in source:
                print(f"   ✅ يحتوي على عنوان المدير العام")
            else:
                print(f"   ❌ لا يحتوي على عنوان المدير العام")
                return False
        else:
            print(f"   ❌ لا يحتوي على تحسينات التوقيع")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        return False

def test_responsive_layout():
    """اختبار التخطيط المتجاوب"""
    print("\n📱 اختبار التخطيط المتجاوب...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.simple_print_template import SimplePrintTemplate
        
        # بيانات تجريبية
        test_data = {
            'request_number': 'RESPONSIVE-TEST-001',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        template = SimplePrintTemplate(test_data)
        
        # اختبار أحجام مختلفة
        sizes = [(800, 600), (1024, 768), (1200, 900)]
        
        for width, height in sizes:
            template.resize(width, height)
            
            # التحقق من أن العناصر لا تزال مرئية
            manager_elements = template.findChildren(template.QLabel)
            visible_elements = [e for e in manager_elements if e.isVisible() and "المدير العام" in e.text()]
            
            if visible_elements:
                print(f"   ✅ الحجم {width}x{height}: المدير العام مرئي")
            else:
                print(f"   ⚠️ الحجم {width}x{height}: المدير العام قد يكون مخفي")
        
        template.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التخطيط المتجاوب: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات محاذاة التوقيع والمدير العام...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_signature_section_structure())
    results.append(test_manager_title_positioning())
    results.append(test_visual_styling())
    results.append(test_pdf_signature_layout())
    results.append(test_responsive_layout())
    
    # عرض النتائج النهائية
    print("\n" + "="*80)
    print("🎯 ملخص اختبار تحسينات محاذاة التوقيع:")
    print("="*80)
    
    test_names = [
        "هيكل قسم التوقيع",
        "موضع عنوان المدير العام",
        "التصميم المرئي",
        "تخطيط التوقيع في PDF",
        "التخطيط المتجاوب"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع تحسينات محاذاة التوقيع تعمل بنجاح!")
        print("✅ المدير العام في الأعلى والتوقيع في الأسفل")
        print("✅ محاذاة احترافية ومتسقة")
        print("✅ تصميم مرئي محسن")
        print("✅ تخطيط PDF محسن")
        print("✅ تخطيط متجاوب")
        
        print("\n🌟 التحسينات المطبقة:")
        print("   📝 هيكل عمودي للمدير العام والتوقيع")
        print("   🎨 تصميم احترافي مع إطارات وألوان")
        print("   📐 محاذاة مثالية في الوسط")
        print("   📄 تحسين PDF مع إطار التوقيع")
        print("   📱 تخطيط متجاوب لأحجام مختلفة")
        
    elif successful_tests >= len(results) * 0.8:
        print("\n✅ معظم تحسينات محاذاة التوقيع تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحسينات فشلت. يرجى مراجعة:")
        print("- هيكل قسم التوقيع")
        print("- موضع المدير العام")
        print("- التصميم المرئي")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
