#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تجريبي للنموذج الاحترافي لطباعة طلب الحوالة
Demo for Professional Print Template
"""

import sys
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_demo_data():
    """إنشاء بيانات تجريبية احترافية"""
    
    return {
        'request_number': f'ALF-PRO-{datetime.now().strftime("%Y%m%d%H%M")}',
        'request_date': datetime.now().strftime('%Y/%m/%d'),
        'branch': 'صرافة الأمانة للتحويلات المالية - الفرع الرئيسي',
        'exchanger': 'أحمد محمد علي الصراف',
        'remittance_amount': 25000.0,
        'currency': 'ريال يمني',
        'transfer_purpose': 'COST OF FOODSTUFF AND MEDICAL SUPPLIES FOR HUMANITARIAN AID',
        
        # معلومات المرسل (شركة الفقيهي)
        'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
        'sender_entity': 'G.M: NASHA\'AT RASHAD QASIM ALDUBAEE',
        'sender_phone': '+967 1 616109',
        'sender_fax': '+967 1 615909',
        'sender_mobile': '+967 *********',
        'sender_pobox': '1903',
        'sender_email': '<EMAIL>, <EMAIL>',
        'sender_address': 'TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',
        
        # معلومات المستقبل
        'receiver_name': 'MOHAMMED AHMED HASSAN AL-YEMENI TRADING COMPANY',
        'receiver_account': '1234567890123456789',
        'receiver_bank_name': 'AL RAJHI BANK - SAUDI ARABIA',
        'receiver_bank_branch': 'RIYADH MAIN BRANCH - KING FAHD ROAD',
        'receiver_swift': 'RJHISARI',
        'receiver_country': 'KINGDOM OF SAUDI ARABIA',
        'receiver_address': 'KING FAHD ROAD, AL OLAYA DISTRICT, RIYADH 12211, SAUDI ARABIA',
        
        # معلومات إضافية
        'notes': 'طلب تحويل عاجل للمواد الغذائية والطبية - مساعدات إنسانية',
        'status': 'معلق - في انتظار الموافقة',
        'created_at': datetime.now().isoformat(),
        'priority': 'عالي',
        'reference': 'REF-HUMANITARIAN-2024'
    }

def run_professional_demo():
    """تشغيل العرض التجريبي للنموذج الاحترافي"""
    
    print("🚀 بدء العرض التجريبي للنموذج الاحترافي...")
    print("=" * 80)
    
    try:
        # إعداد البيئة
        import os
        os.environ['QT_QPA_PLATFORM'] = 'windows'  # للويندوز
        
        from PySide6.QtWidgets import QApplication, QMessageBox
        from src.ui.remittances.professional_print_template import ProfessionalPrintTemplate
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء البيانات التجريبية
        demo_data = create_demo_data()
        
        print("📋 البيانات التجريبية الاحترافية:")
        print(f"   📄 رقم الطلب: {demo_data['request_number']}")
        print(f"   📅 التاريخ: {demo_data['request_date']}")
        print(f"   🏢 الفرع: {demo_data['branch']}")
        print(f"   👤 المستقبل: {demo_data['receiver_name']}")
        print(f"   🏦 البنك: {demo_data['receiver_bank_name']}")
        print(f"   💰 المبلغ: {demo_data['remittance_amount']:,} {demo_data['currency']}")
        print(f"   🎯 الغرض: {demo_data['transfer_purpose']}")
        print(f"   📊 الحالة: {demo_data['status']}")
        print(f"   ⚡ الأولوية: {demo_data['priority']}")
        
        print("\n🎨 فتح النموذج الاحترافي...")
        
        # إنشاء النموذج الاحترافي
        professional_window = ProfessionalPrintTemplate(demo_data)
        professional_window.show()
        
        # عرض رسالة توضيحية
        msg = QMessageBox()
        msg.setWindowTitle("النموذج الاحترافي لطباعة طلب الحوالة")
        msg.setText(f"""
🎉 مرحباً بك في النموذج الاحترافي!

📋 الطلب المعروض:
• رقم الطلب: {demo_data['request_number']}
• المرسل: شركة الفقيهي للتجارة والتموينات
• المستقبل: {demo_data['receiver_name'][:40]}...
• المبلغ: {demo_data['remittance_amount']:,} {demo_data['currency']}

✨ الميزات الاحترافية المتاحة:

📄 تصدير PDF:
• تصدير مباشر إلى ملف PDF عالي الجودة
• معالجة متعددة الخيوط مع شريط تقدم
• بدائل ذكية في حالة عدم توفر المكتبات

🖼️ حفظ كصورة:
• التقاط عالي الجودة (PNG, JPG)
• جودة 100% للطباعة والأرشفة

🖨️ طباعة احترافية:
• طباعة مباشرة بجودة عالية
• تنسيق A4 مثالي
• حوار طباعة متقدم

🎨 تصميم احترافي:
• رأس مع تدرجات لونية وشعار الشركة
• أقسام ملونة ومنظمة حسب المحتوى
• خطوط فاصلة وحواف مدورة
• تذييل معلوماتي احترافي

🌍 دعم RTL:
• النصوص الإنجليزية في الأقسام المحددة بـ RTL
• تخطيط مناسب للقراء العرب

جرب جميع الميزات واستكشف التصميم الاحترافي!
        """.strip())
        msg.setIcon(QMessageBox.Information)
        msg.exec()
        
        print("✅ تم فتح النموذج الاحترافي بنجاح!")
        print("💡 يمكنك الآن:")
        print("   📄 تجربة تصدير PDF")
        print("   🖼️ حفظ النموذج كصورة عالية الجودة")
        print("   🖨️ طباعة النموذج مباشرة")
        print("   🎨 استكشاف التصميم الاحترافي")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PySide6: pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل العرض التجريبي: {e}")
        import traceback
        traceback.print_exc()
        return 1

def display_professional_summary():
    """عرض ملخص النموذج الاحترافي"""
    
    print("\n" + "=" * 80)
    print("🏆 ملخص النموذج الاحترافي لطباعة طلب الحوالة")
    print("=" * 80)
    
    print("\n🎯 الهدف المحقق:")
    print("   تصميم نموذج طباعة احترافي مع إمكانية تصدير PDF عالي الجودة")
    
    print("\n✨ الميزات الاحترافية:")
    
    print("\n   🎨 التصميم:")
    print("      • رأس احترافي مع تدرجات لونية")
    print("      • شعار الشركة وعنوان مميز")
    print("      • أقسام ملونة حسب المحتوى:")
    print("        - أزرق للمعلومات العامة")
    print("        - أحمر لمعلومات المستفيد")
    print("        - أزرق فاتح لمعلومات البنك")
    print("        - أخضر لمعلومات المرسل")
    print("        - برتقالي للغرض من التحويل")
    print("        - بنفسجي للتوقيع والختم")
    print("      • خطوط فاصلة بتدرجات")
    print("      • حواف مدورة وظلال")
    print("      • تذييل معلوماتي")
    
    print("\n   📄 تصدير PDF:")
    print("      • تصدير مباشر باستخدام QPrinter")
    print("      • معالجة متعددة الخيوط")
    print("      • شريط تقدم تفاعلي")
    print("      • بدائل ذكية (PIL, صور)")
    print("      • معالجة شاملة للأخطاء")
    print("      • دعم أنظمة تشغيل متعددة")
    
    print("\n   🖼️ حفظ الصور:")
    print("      • جودة 100% للطباعة")
    print("      • دعم PNG و JPG")
    print("      • حوار حفظ احترافي")
    print("      • أسماء ملفات ذكية بالتاريخ")
    
    print("\n   🖨️ الطباعة:")
    print("      • طباعة مباشرة عالية الجودة")
    print("      • حوار طباعة متقدم")
    print("      • تنسيق A4 مثالي")
    print("      • بدائل للطباعة المبسطة")
    
    print("\n   🔧 التقنيات المستخدمة:")
    print("      • PySide6 للواجهة")
    print("      • QPrinter لتصدير PDF")
    print("      • QThread للمعالجة المتوازية")
    print("      • CSS متقدم للتصميم")
    print("      • PIL كبديل للصور")
    print("      • معالجة الأخطاء الشاملة")
    
    print("\n   🌍 دعم RTL:")
    print("      • النصوص الإنجليزية في الأقسام المحددة")
    print("      • تخطيط مناسب للقراء العرب")
    print("      • محاذاة صحيحة لجميع العناصر")
    
    print("\n🔗 التكامل:")
    print("   ✅ مدمج مع نافذة طلب الحوالة")
    print("   ✅ مدمج مع نافذة تفاصيل الطلب")
    print("   ✅ أولوية عالية في الاستخدام")
    print("   ✅ بدائل في حالة عدم التوفر")
    
    print("\n🎯 الاستخدام:")
    print("   1. فتح نافذة طلب الحوالة")
    print("   2. تعبئة البيانات")
    print("   3. الضغط على 'طباعة النموذج'")
    print("   4. ستفتح النافذة الاحترافية")
    print("   5. اختيار الميزة المطلوبة:")
    print("      • تصدير PDF للأرشفة")
    print("      • حفظ كصورة للمشاركة")
    print("      • طباعة مباشرة للاستخدام")

if __name__ == "__main__":
    # عرض ملخص النموذج
    display_professional_summary()
    
    # تشغيل العرض التجريبي
    exit_code = run_professional_demo()
    
    print("\n" + "=" * 80)
    if exit_code == 0:
        print("🎉 انتهى العرض التجريبي بنجاح!")
        print("✅ النموذج الاحترافي يعمل بشكل مثالي")
        print("✅ جميع الميزات متاحة وتعمل")
        print("✅ التصميم احترافي وجذاب")
        print("✅ تصدير PDF وحفظ الصور يعمل")
    else:
        print("❌ حدث خطأ في العرض التجريبي")
        print("💡 تأكد من تثبيت المكتبات المطلوبة")
    print("=" * 80)
    
    sys.exit(exit_code)
