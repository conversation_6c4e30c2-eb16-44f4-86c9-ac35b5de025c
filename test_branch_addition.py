#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة فرع جديد وتحديث القائمة
Test Branch Addition and List Update
"""

import sqlite3
from pathlib import Path

def test_branch_addition():
    """اختبار إضافة فرع جديد"""
    
    print("🧪 اختبار إضافة فرع جديد وتحديث القائمة...")
    print("=" * 60)
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. عرض الفروع الموجودة قبل الإضافة
        print("\n📋 الفروع الموجودة قبل الإضافة:")
        cursor.execute("""
            SELECT b.id, b.name, b.type, b.parent_type, b.parent_id,
                   banks.name as bank_name, exchanges.name as exchange_name
            FROM branches b
            LEFT JOIN banks ON b.bank_id = banks.id
            LEFT JOIN exchanges ON b.exchange_id = exchanges.id
            WHERE b.is_active = 1
            ORDER BY b.name
        """)
        existing_branches = cursor.fetchall()
        
        for branch in existing_branches:
            parent_name = branch[5] if branch[5] else branch[6] if branch[6] else "غير محدد"
            print(f"   ID: {branch[0]}, الاسم: {branch[1]}, النوع: {branch[2]}, الجهة الأم: {parent_name}")
        
        print(f"\n📊 إجمالي الفروع الموجودة: {len(existing_branches)}")
        
        # 2. الحصول على بنك أو صراف للربط
        cursor.execute("SELECT id, name FROM banks WHERE is_active = 1 LIMIT 1")
        bank = cursor.fetchone()
        
        cursor.execute("SELECT id, name FROM exchanges WHERE is_active = 1 LIMIT 1")
        exchange = cursor.fetchone()
        
        if not bank and not exchange:
            print("❌ لا توجد بنوك أو صرافات نشطة للربط!")
            return False
        
        # 3. إضافة فرع جديد
        print("\n➕ إضافة فرع جديد...")
        
        if bank:
            parent_type = "بنك"
            parent_id = bank[0]
            parent_name = bank[1]
            bank_id = bank[0]
            exchange_id = None
        else:
            parent_type = "صراف"
            parent_id = exchange[0]
            parent_name = exchange[1]
            bank_id = None
            exchange_id = exchange[0]
        
        # بيانات الفرع الجديد
        branch_data = {
            'name': f'فرع اختبار {parent_name}',
            'name_en': f'Test Branch {parent_name}',
            'code': 'TEST_BR_001',
            'type': 'فرع اختبار',
            'parent_type': parent_type,
            'parent_id': parent_id,
            'bank_id': bank_id,
            'exchange_id': exchange_id,
            'address': 'عنوان اختبار',
            'phone': '011-1234567',
            'fax': '011-1234568',
            'email': '<EMAIL>',
            'manager_name': 'مدير الاختبار',
            'manager_phone': '050-1234567',
            'notes': 'فرع تجريبي للاختبار',
            'is_active': 1
        }
        
        # إدراج الفرع
        cursor.execute("""
            INSERT INTO branches (
                name, name_en, code, type, parent_type, parent_id,
                bank_id, exchange_id, address, phone, fax, email, 
                manager_name, manager_phone, notes, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            branch_data['name'], branch_data['name_en'], branch_data['code'], 
            branch_data['type'], branch_data['parent_type'], branch_data['parent_id'],
            branch_data['bank_id'], branch_data['exchange_id'], branch_data['address'], 
            branch_data['phone'], branch_data['fax'], branch_data['email'], 
            branch_data['manager_name'], branch_data['manager_phone'], 
            branch_data['notes'], branch_data['is_active']
        ))
        
        new_branch_id = cursor.lastrowid
        print(f"✅ تم إضافة فرع جديد بنجاح (ID: {new_branch_id})")
        print(f"   الاسم: {branch_data['name']}")
        print(f"   الرمز: {branch_data['code']}")
        print(f"   الجهة الأم: {parent_name} ({parent_type})")
        
        # 4. عرض الفروع بعد الإضافة
        print("\n📋 الفروع الموجودة بعد الإضافة:")
        cursor.execute("""
            SELECT b.id, b.name, b.type, b.parent_type, b.parent_id,
                   banks.name as bank_name, exchanges.name as exchange_name
            FROM branches b
            LEFT JOIN banks ON b.bank_id = banks.id
            LEFT JOIN exchanges ON b.exchange_id = exchanges.id
            WHERE b.is_active = 1
            ORDER BY b.name
        """)
        updated_branches = cursor.fetchall()
        
        for branch in updated_branches:
            parent_name_display = branch[5] if branch[5] else branch[6] if branch[6] else "غير محدد"
            status = "🆕" if branch[0] == new_branch_id else "   "
            print(f"{status} ID: {branch[0]}, الاسم: {branch[1]}, النوع: {branch[2]}, الجهة الأم: {parent_name_display}")
        
        print(f"\n📊 إجمالي الفروع بعد الإضافة: {len(updated_branches)}")
        print(f"📈 تم إضافة {len(updated_branches) - len(existing_branches)} فرع جديد")
        
        # 5. اختبار استعلام تحديث القائمة
        print("\n🔄 اختبار استعلام تحديث القائمة...")
        cursor.execute("""
            SELECT b.id, b.name, b.name_en, b.code, b.type, b.parent_type, b.parent_id,
                   b.bank_id, b.exchange_id, b.address, b.phone, b.fax, b.email,
                   b.manager_name, b.manager_phone, b.notes, b.is_active, b.created_at,
                   banks.name as bank_name, exchanges.name as exchange_name
            FROM branches b
            LEFT JOIN banks ON b.bank_id = banks.id
            LEFT JOIN exchanges ON b.exchange_id = exchanges.id
            WHERE b.is_active = 1
            ORDER BY b.name
        """)
        full_branches_data = cursor.fetchall()
        
        print(f"✅ استعلام تحديث القائمة يعمل بنجاح")
        print(f"📊 تم استرجاع {len(full_branches_data)} فرع مع جميع التفاصيل")
        
        # 6. حذف الفرع التجريبي
        print(f"\n🗑️ حذف الفرع التجريبي (ID: {new_branch_id})...")
        cursor.execute("DELETE FROM branches WHERE id = ?", (new_branch_id,))
        print("✅ تم حذف الفرع التجريبي")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 اختبار إضافة الفرع وتحديث القائمة نجح بالكامل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_branch_dialog_integration():
    """اختبار تكامل نافذة إضافة الفرع"""
    
    print("\n🔗 اختبار تكامل نافذة إضافة الفرع...")
    print("=" * 60)
    
    try:
        # اختبار استيراد النوافذ
        from src.ui.remittances.add_new_branch_dialog import AddNewBranchDialog
        from src.ui.remittances.banks_management_window import BanksManagementWindow
        
        print("✅ تم استيراد النوافذ بنجاح")
        
        # اختبار إنشاء النوافذ
        banks_window = BanksManagementWindow()
        print("✅ تم إنشاء نافذة إدارة البنوك")
        
        branch_dialog = AddNewBranchDialog()
        print("✅ تم إنشاء نافذة إضافة الفرع")
        
        # اختبار وجود الإشارات والدوال
        if hasattr(branch_dialog, 'branch_added'):
            print("✅ إشارة branch_added موجودة")
        else:
            print("❌ إشارة branch_added مفقودة")
        
        if hasattr(banks_window, 'on_branch_added'):
            print("✅ دالة on_branch_added موجودة")
        else:
            print("❌ دالة on_branch_added مفقودة")
        
        if hasattr(banks_window, 'load_branches_data'):
            print("✅ دالة load_branches_data موجودة")
        else:
            print("❌ دالة load_branches_data مفقودة")
        
        print("🎉 اختبار التكامل نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إضافة الفرع وتحديث القائمة...")
    print("=" * 70)
    
    # اختبار قاعدة البيانات
    db_success = test_branch_addition()
    
    # اختبار التكامل
    integration_success = test_branch_dialog_integration()
    
    if db_success and integration_success:
        print("\n🏆 جميع الاختبارات نجحت!")
        print("✅ إضافة الفرع وتحديث القائمة يعمل بشكل صحيح")
    else:
        print("\n❌ بعض الاختبارات فشلت")
    
    print("=" * 70)
