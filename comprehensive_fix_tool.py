#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة الإصلاح الشاملة للتطبيق
Comprehensive Application Fix Tool
"""

import sys
import os
import sqlite3
import shutil
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ApplicationFixer:
    """مصلح شامل للتطبيق"""
    
    def __init__(self):
        self.project_root = project_root
        self.fixes_applied = []
        self.errors = []
        
    def fix_all_issues(self):
        """إصلاح جميع المشاكل"""
        print("🔧 بدء الإصلاح الشامل للتطبيق...")
        print("="*60)
        
        # 1. إصلاح هيكلية المشروع
        self.fix_project_structure()
        
        # 2. إصلاح قاعدة البيانات
        self.fix_database_issues()
        
        # 3. إصلاح ملفات التكوين
        self.fix_configuration_files()
        
        # 4. إصلاح الأذونات
        self.fix_permissions()
        
        # 5. إصلاح الاستيرادات
        self.fix_import_issues()
        
        # 6. إصلاح واجهة المستخدم
        self.fix_ui_issues()
        
        # 7. تحسين الأداء
        self.optimize_performance()
        
        # 8. إضافة ملفات الأمان
        self.add_security_files()
        
        # عرض التقرير النهائي
        self.generate_fix_report()
        
    def fix_project_structure(self):
        """إصلاح هيكلية المشروع"""
        print("\n📁 إصلاح هيكلية المشروع...")
        
        # إنشاء المجلدات المطلوبة
        required_dirs = [
            "data",
            "attachments",
            "attachments/shipments",
            "attachments/suppliers",
            "attachments/items",
            "logs",
            "backups",
            "temp",
            "exports",
            "imports"
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                try:
                    full_path.mkdir(parents=True, exist_ok=True)
                    self.fixes_applied.append(f"تم إنشاء مجلد: {dir_path}")
                    print(f"   ✅ تم إنشاء مجلد: {dir_path}")
                except Exception as e:
                    self.errors.append(f"فشل في إنشاء مجلد {dir_path}: {e}")
                    print(f"   ❌ فشل في إنشاء مجلد {dir_path}: {e}")
            else:
                print(f"   ✅ مجلد موجود: {dir_path}")
        
        # إنشاء ملفات __init__.py المفقودة
        init_dirs = [
            "src",
            "src/database",
            "src/ui",
            "src/ui/dialogs",
            "src/ui/widgets",
            "src/utils",
            "src/reports"
        ]
        
        for dir_path in init_dirs:
            init_file = self.project_root / dir_path / "__init__.py"
            if not init_file.exists():
                try:
                    init_file.parent.mkdir(parents=True, exist_ok=True)
                    init_file.write_text("# -*- coding: utf-8 -*-\n", encoding='utf-8')
                    self.fixes_applied.append(f"تم إنشاء __init__.py في: {dir_path}")
                    print(f"   ✅ تم إنشاء __init__.py في: {dir_path}")
                except Exception as e:
                    self.errors.append(f"فشل في إنشاء __init__.py في {dir_path}: {e}")
    
    def fix_database_issues(self):
        """إصلاح مشاكل قاعدة البيانات"""
        print("\n🗄️ إصلاح مشاكل قاعدة البيانات...")
        
        db_path = self.project_root / "data" / "proshipment.db"
        
        if not db_path.exists():
            print("   ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها عند أول تشغيل")
            return
        
        try:
            # إنشاء نسخة احتياطية
            backup_path = self.project_root / "backups" / f"proshipment_backup_{self.get_timestamp()}.db"
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(db_path, backup_path)
            self.fixes_applied.append(f"تم إنشاء نسخة احتياطية: {backup_path.name}")
            print(f"   ✅ تم إنشاء نسخة احتياطية")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # فحص وإصلاح الفهارس المفقودة
            indexes_to_create = [
                ("idx_suppliers_code", "suppliers", "code"),
                ("idx_items_code", "items", "code"),
                ("idx_shipments_number", "shipments", "shipment_number"),
                ("idx_shipments_date", "shipments", "shipment_date"),
                ("idx_shipment_items_shipment", "shipment_items", "shipment_id"),
                ("idx_shipment_items_item", "shipment_items", "item_id")
            ]
            
            for index_name, table_name, column_name in indexes_to_create:
                try:
                    cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name})")
                    self.fixes_applied.append(f"تم إنشاء فهرس: {index_name}")
                    print(f"   ✅ تم إنشاء فهرس: {index_name}")
                except Exception as e:
                    print(f"   ⚠️ فهرس {index_name} موجود مسبقاً أو خطأ: {e}")
            
            # تحسين قاعدة البيانات
            cursor.execute("VACUUM")
            cursor.execute("ANALYZE")
            self.fixes_applied.append("تم تحسين قاعدة البيانات (VACUUM & ANALYZE)")
            print("   ✅ تم تحسين قاعدة البيانات")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.errors.append(f"خطأ في إصلاح قاعدة البيانات: {e}")
            print(f"   ❌ خطأ في إصلاح قاعدة البيانات: {e}")
    
    def fix_configuration_files(self):
        """إصلاح ملفات التكوين"""
        print("\n⚙️ إصلاح ملفات التكوين...")
        
        # إنشاء ملف requirements.txt محدث
        requirements_content = """PySide6>=6.5.0
SQLAlchemy>=2.0.0
reportlab>=4.0.0
requests>=2.31.0
beautifulsoup4>=4.12.0
openpyxl>=3.1.0
Pillow>=10.0.0
python-bidi>=0.4.2
arabic-reshaper>=3.0.0
num2words>=0.5.14
"""
        
        req_file = self.project_root / "requirements.txt"
        try:
            req_file.write_text(requirements_content, encoding='utf-8')
            self.fixes_applied.append("تم تحديث requirements.txt")
            print("   ✅ تم تحديث requirements.txt")
        except Exception as e:
            self.errors.append(f"فشل في تحديث requirements.txt: {e}")
        
        # إنشاء ملف .gitignore
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Temporary files
temp/
*.tmp
*.temp

# Backups
backups/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Application specific
attachments/
exports/
imports/
"""
        
        gitignore_file = self.project_root / ".gitignore"
        try:
            gitignore_file.write_text(gitignore_content, encoding='utf-8')
            self.fixes_applied.append("تم إنشاء .gitignore")
            print("   ✅ تم إنشاء .gitignore")
        except Exception as e:
            self.errors.append(f"فشل في إنشاء .gitignore: {e}")
    
    def fix_permissions(self):
        """إصلاح الأذونات"""
        print("\n🔒 إصلاح الأذونات...")
        
        # فحص وإصلاح أذونات قاعدة البيانات
        db_path = self.project_root / "data" / "proshipment.db"
        if db_path.exists():
            try:
                # محاولة الكتابة
                with open(db_path, 'r+b') as f:
                    pass
                print("   ✅ قاعدة البيانات قابلة للكتابة")
            except PermissionError:
                self.errors.append("قاعدة البيانات غير قابلة للكتابة")
                print("   ❌ قاعدة البيانات غير قابلة للكتابة")
        
        # فحص مجلدات الكتابة
        writable_dirs = ["attachments", "logs", "backups", "temp", "exports"]
        for dir_name in writable_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                try:
                    test_file = dir_path / "test_write.tmp"
                    test_file.write_text("test", encoding='utf-8')
                    test_file.unlink()
                    print(f"   ✅ {dir_name} قابل للكتابة")
                except PermissionError:
                    self.errors.append(f"مجلد {dir_name} غير قابل للكتابة")
                    print(f"   ❌ مجلد {dir_name} غير قابل للكتابة")
    
    def fix_import_issues(self):
        """إصلاح مشاكل الاستيراد"""
        print("\n📦 إصلاح مشاكل الاستيراد...")
        
        # التحقق من المكتبات المطلوبة
        required_packages = [
            ("PySide6", "PySide6"),
            ("SQLAlchemy", "sqlalchemy"),
            ("ReportLab", "reportlab"),
            ("Requests", "requests"),
            ("BeautifulSoup4", "bs4"),
            ("OpenPyXL", "openpyxl"),
            ("Pillow", "PIL"),
            ("num2words", "num2words")
        ]
        
        missing_packages = []
        for name, module in required_packages:
            try:
                __import__(module)
                print(f"   ✅ {name} متاح")
            except ImportError:
                missing_packages.append(name)
                print(f"   ❌ {name} مفقود")
        
        if missing_packages:
            print(f"\n   📦 المكتبات المفقودة: {', '.join(missing_packages)}")
            print("   💡 لتثبيت المكتبات المفقودة، شغل: pip install -r requirements.txt")
            self.errors.append(f"مكتبات مفقودة: {', '.join(missing_packages)}")
    
    def fix_ui_issues(self):
        """إصلاح مشاكل واجهة المستخدم"""
        print("\n🖥️ إصلاح مشاكل واجهة المستخدم...")
        
        # التحقق من النوافذ الرئيسية
        ui_path = self.project_root / "src" / "ui"
        main_windows = [
            "main_window.py",
            "shipments/shipments_window.py",
            "suppliers/suppliers_window.py",
            "items/items_window.py"
        ]
        
        for window_file in main_windows:
            window_path = ui_path / window_file
            if window_path.exists():
                print(f"   ✅ نافذة موجودة: {window_file}")
            else:
                print(f"   ⚠️ نافذة مفقودة: {window_file}")
        
        # التحقق من مجلد الحوارات
        dialogs_path = ui_path / "dialogs"
        if dialogs_path.exists():
            dialog_files = list(dialogs_path.glob("*.py"))
            print(f"   ✅ {len(dialog_files)} حوار موجود")
        else:
            print("   ⚠️ مجلد الحوارات مفقود")
    
    def optimize_performance(self):
        """تحسين الأداء"""
        print("\n⚡ تحسين الأداء...")
        
        # إنشاء ملف تكوين للأداء
        performance_config = """# إعدادات الأداء
DATABASE_POOL_SIZE = 10
DATABASE_TIMEOUT = 30
UI_UPDATE_INTERVAL = 100
CACHE_SIZE = 1000
AUTO_BACKUP_INTERVAL = 3600  # ساعة واحدة
"""
        
        config_file = self.project_root / "performance_config.py"
        try:
            config_file.write_text(performance_config, encoding='utf-8')
            self.fixes_applied.append("تم إنشاء ملف تكوين الأداء")
            print("   ✅ تم إنشاء ملف تكوين الأداء")
        except Exception as e:
            self.errors.append(f"فشل في إنشاء ملف تكوين الأداء: {e}")
    
    def add_security_files(self):
        """إضافة ملفات الأمان"""
        print("\n🔐 إضافة ملفات الأمان...")
        
        # إنشاء ملف الأمان
        security_content = """# إعدادات الأمان
ENABLE_LOGGING = True
LOG_LEVEL = "INFO"
MAX_LOGIN_ATTEMPTS = 3
SESSION_TIMEOUT = 3600  # ساعة واحدة
BACKUP_ENCRYPTION = False
"""
        
        security_file = self.project_root / "security_config.py"
        try:
            security_file.write_text(security_content, encoding='utf-8')
            self.fixes_applied.append("تم إنشاء ملف إعدادات الأمان")
            print("   ✅ تم إنشاء ملف إعدادات الأمان")
        except Exception as e:
            self.errors.append(f"فشل في إنشاء ملف الأمان: {e}")
    
    def get_timestamp(self):
        """الحصول على الطابع الزمني"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def generate_fix_report(self):
        """إنشاء تقرير الإصلاح"""
        print("\n" + "="*60)
        print("📊 تقرير الإصلاح الشامل")
        print("="*60)
        
        print(f"\n✅ الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   ✅ {fix}")
        
        print(f"\n❌ الأخطاء: {len(self.errors)}")
        for error in self.errors:
            print(f"   ❌ {error}")
        
        # تقييم الحالة النهائية
        if len(self.errors) == 0:
            print("\n🎉 تم الإصلاح بنجاح! التطبيق جاهز للاستخدام")
            status = "ممتاز"
        elif len(self.errors) <= 2:
            print("\n✅ تم معظم الإصلاحات مع بعض التحذيرات")
            status = "جيد"
        else:
            print("\n⚠️ تم بعض الإصلاحات لكن توجد مشاكل تحتاج انتباه")
            status = "يحتاج مراجعة"
        
        print(f"\nالحالة النهائية: {status}")
        
        return len(self.errors) == 0

def main():
    """الدالة الرئيسية"""
    fixer = ApplicationFixer()
    success = fixer.fix_all_issues()
    
    print("\n" + "="*60)
    if success:
        print("🎉 تم الإصلاح الشامل بنجاح!")
        print("✅ التطبيق جاهز للاستخدام النهائي")
    else:
        print("⚠️ تم تطبيق معظم الإصلاحات")
        print("🔍 راجع الأخطاء المتبقية أعلاه")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
