{"account": "0xeb99", "activate-breakpoints": "0xea97", "add": "0xea60", "alert": "0xea6c", "archive": "0xea98", "array": "0xea8a", "arrow-both": "0xea99", "arrow-circle-down": "0xebfc", "arrow-circle-left": "0xebfd", "arrow-circle-right": "0xebfe", "arrow-circle-up": "0xebff", "arrow-down": "0xea9a", "arrow-left": "0xea9b", "arrow-right": "0xea9c", "arrow-small-down": "0xea9d", "arrow-small-left": "0xea9e", "arrow-small-right": "0xea9f", "arrow-small-up": "0xeaa0", "arrow-swap": "0xebcb", "arrow-up": "0xeaa1", "azure": "0xebd8", "azure-devops": "0xebe8", "beaker": "0xea79", "beaker-stop": "0xebe1", "bell": "0xeaa2", "bell-dot": "0xeb9a", "bell-slash": "0xec08", "bell-slash-dot": "0xec09", "blank": "0xec03", "bold": "0xeaa3", "book": "0xeaa4", "bookmark": "0xeaa5", "bracket": "0xeb0f", "bracket-dot": "0xebe5", "bracket-error": "0xebe6", "briefcase": "0xeaac", "broadcast": "0xeaad", "browser": "0xeaae", "bug": "0xeaaf", "calendar": "0xeab0", "call-incoming": "0xeb92", "call-outgoing": "0xeb93", "case-sensitive": "0xeab1", "check": "0xeab2", "check-all": "0xebb1", "checklist": "0xeab3", "chevron-down": "0xeab4", "chevron-left": "0xeab5", "chevron-right": "0xeab6", "chevron-up": "0xeab7", "chip": "0xec19", "chrome-close": "0xeab8", "chrome-maximize": "0xeab9", "chrome-minimize": "0xeaba", "chrome-restore": "0xeabb", "circle": "0xeabc", "circle-filled": "0xea71", "circle-large": "0xebb5", "circle-large-filled": "0xebb4", "circle-large-outline": "0xebb5", "circle-outline": "0xeabc", "circle-slash": "0xeabd", "circle-small": "0xec07", "circle-small-filled": "0xeb8a", "circuit-board": "0<PERSON>abe", "clear-all": "0xeabf", "clippy": "0xeac0", "clock": "0xea82", "clone": "0xea78", "close": "0xea76", "close-all": "0xeac1", "close-dirty": "0xea71", "cloud": "0xebaa", "cloud-download": "0xeac2", "cloud-upload": "0xeac3", "code": "0xeac4", "code-oss": "0xec2b", "coffee": "0xec15", "collapse-all": "0xeac5", "color-mode": "0xeac6", "combine": "0xebb6", "comment": "0xea6b", "comment-add": "0xea6b", "comment-discussion": "0xeac7", "comment-draft": "0xec0e", "comment-unresolved": "0xec0a", "compare-changes": "0xeafd", "compass": "0xebd5", "compass-active": "0xebd7", "compass-dot": "0xebd6", "console": "0xea85", "copilot": "0xec1e", "copy": "0xebcc", "coverage": "0xec2e", "credit-card": "0xeac9", "dash": "0xeacc", "dashboard": "0xeacd", "database": "0xeace", "debug": "0xead8", "debug-all": "0xebdc", "debug-alt": "0xeb91", "debug-alt-small": "0xeba8", "debug-breakpoint": "0xea71", "debug-breakpoint-conditional": "0xeaa7", "debug-breakpoint-conditional-disabled": "0xeaa7", "debug-breakpoint-conditional-unverified": "0xeaa6", "debug-breakpoint-data": "0xeaa9", "debug-breakpoint-data-disabled": "0xeaa9", "debug-breakpoint-data-unverified": "0xeaa8", "debug-breakpoint-disabled": "0xea71", "debug-breakpoint-function": "0xeb88", "debug-breakpoint-function-disabled": "0xeb88", "debug-breakpoint-function-unverified": "0xeb87", "debug-breakpoint-log": "0xeaab", "debug-breakpoint-log-disabled": "0xeaab", "debug-breakpoint-log-unverified": "0xeaaa", "debug-breakpoint-unsupported": "0xeb8c", "debug-breakpoint-unverified": "0xeabc", "debug-console": "0xeb9b", "debug-continue": "0xeacf", "debug-continue-small": "0xebe0", "debug-coverage": "0xebdd", "debug-disconnect": "0xead0", "debug-hint": "0xea71", "debug-line-by-line": "0xebd0", "debug-pause": "0xead1", "debug-rerun": "0xebc0", "debug-restart": "0xead2", "debug-restart-frame": "0xeb90", "debug-reverse-continue": "0xeb8e", "debug-stackframe": "0xeb8b", "debug-stackframe-active": "0xeb89", "debug-stackframe-dot": "0xeb8a", "debug-stackframe-focused": "0xeb8b", "debug-start": "0xead3", "debug-step-back": "0xeb8f", "debug-step-into": "0xead4", "debug-step-out": "0xead5", "debug-step-over": "0xead6", "debug-stop": "0xead7", "desktop-download": "0xea78", "device-camera": "0xeada", "device-camera-video": "0xead9", "device-desktop": "0xea7a", "device-mobile": "0xeadb", "diff": "0xeae1", "diff-added": "0xeadc", "diff-ignored": "0xeadd", "diff-modified": "0xeade", "diff-multiple": "0xec23", "diff-removed": "0xeadf", "diff-renamed": "0xeae0", "diff-sidebyside": "0xeae1", "diff-single": "0xec22", "discard": "0xeae2", "edit": "0xea73", "editor-layout": "0xeae3", "ellipsis": "0xea7c", "empty-window": "0xeae4", "error": "0xea87", "error-small": "0xebfb", "exclude": "0xeae5", "expand-all": "0xeb95", "export": "0xebac", "extensions": "0xeae6", "eye": "0xea70", "eye-closed": "0xeae7", "eye-unwatch": "0xea70", "eye-watch": "0xea70", "feedback": "0xeb96", "file": "0xea7b", "file-add": "0xea7f", "file-binary": "0xeae8", "file-code": "0xeae9", "file-directory": "0xea83", "file-directory-create": "0xea80", "file-media": "0xeaea", "file-pdf": "0xeaeb", "file-submodule": "0xeaec", "file-symlink-directory": "0xeaed", "file-symlink-file": "0xeaee", "file-text": "0xea7b", "file-zip": "0xeaef", "files": "0xeaf0", "filter": "0xeaf1", "filter-filled": "0xebce", "flame": "0xeaf2", "fold": "0xeaf5", "fold-down": "0xeaf3", "fold-horizontal": "0xec05", "fold-horizontal-filled": "0xec06", "fold-up": "0xeaf4", "fold-vertical": "0xec30", "fold-vertical-filled": "0xec31", "folder": "0xea83", "folder-active": "0xeaf6", "folder-library": "0xebdf", "folder-opened": "0xeaf7", "game": "0xec17", "gather": "0xebb6", "gear": "0xeaf8", "gift": "0xeaf9", "gist": "0xeafb", "gist-fork": "0xea63", "gist-new": "0xea60", "gist-private": "0xea75", "gist-secret": "0xeafa", "git-branch": "0xea68", "git-branch-create": "0xea68", "git-branch-delete": "0xea68", "git-commit": "0xeafc", "git-compare": "0xeafd", "git-fork-private": "0xea75", "git-merge": "0xeafe", "git-pull-request": "0xea64", "git-pull-request-abandoned": "0xea64", "git-pull-request-assignee": "0xeb99", "git-pull-request-closed": "0xebda", "git-pull-request-create": "0xebbc", "git-pull-request-draft": "0xebdb", "git-pull-request-go-to-changes": "0xec0b", "git-pull-request-label": "0xea66", "git-pull-request-milestone": "0xeb20", "git-pull-request-new-changes": "0xec0c", "git-pull-request-reviewer": "0xeb96", "git-stash": "0xec26", "git-stash-apply": "0xec27", "git-stash-pop": "0xec28", "github": "0xea84", "github-action": "0xeaff", "github-alt": "0xeb00", "github-inverted": "0xeba1", "github-project": "0xec2f", "globe": "0xeb01", "go-to-file": "0xea94", "go-to-search": "0xec32", "grabber": "0xeb02", "graph": "0xeb03", "graph-left": "0xebad", "graph-line": "0xebe2", "graph-scatter": "0xebe3", "gripper": "0xeb04", "group-by-ref-type": "0xeb97", "heart": "0xeb05", "heart-filled": "0xec04", "history": "0xea82", "home": "0xeb06", "horizontal-rule": "0xeb07", "hubot": "0xeb08", "inbox": "0xeb09", "indent": "0xebf9", "info": "0xea74", "insert": "0xec11", "inspect": "0xebd1", "issue-closed": "0xeba4", "issue-draft": "0xebd9", "issue-opened": "0xea74", "issue-reopened": "0xeb0b", "issues": "0xeb0c", "italic": "0xeb0d", "jersey": "0xeb0e", "json": "0xeb0f", "kebab-horizontal": "0xea7c", "kebab-vertical": "0xeb10", "key": "0xeb11", "keyboard": "0xea65", "law": "0xeb12", "layers": "0xebd2", "layers-active": "0xebd4", "layers-dot": "0xebd3", "layout": "0xebeb", "layout-activitybar-left": "0xebec", "layout-activitybar-right": "0xebed", "layout-centered": "0xebf7", "layout-menubar": "0xebf6", "layout-panel": "0xebf2", "layout-panel-center": "0xebef", "layout-panel-justify": "0xebf0", "layout-panel-left": "0xebee", "layout-panel-off": "0xec01", "layout-panel-right": "0xebf1", "layout-sidebar-left": "0xebf3", "layout-sidebar-left-off": "0xec02", "layout-sidebar-right": "0xebf4", "layout-sidebar-right-off": "0xec00", "layout-statusbar": "0xebf5", "library": "0xeb9c", "light-bulb": "0xea61", "lightbulb": "0xea61", "lightbulb-autofix": "0xeb13", "lightbulb-sparkle": "0xec1f", "link": "0xeb15", "link-external": "0xeb14", "list-filter": "0xeb83", "list-flat": "0xeb84", "list-ordered": "0xeb16", "list-selection": "0xeb85", "list-tree": "0xeb86", "list-unordered": "0xeb17", "live-share": "0xeb18", "loading": "0xeb19", "location": "0xeb1a", "lock": "0xea75", "lock-small": "0xebe7", "log-in": "0xea6f", "log-out": "0xea6e", "logo-github": "0xea84", "magnet": "0xebae", "mail": "0xeb1c", "mail-read": "0xeb1b", "mail-reply": "0xea7d", "map": "0xec05", "map-filled": "0xec06", "map-horizontal": "0xec05", "map-horizontal-filled": "0xec06", "map-vertical": "0xec30", "map-vertical-filled": "0xec31", "mark-github": "0xea84", "markdown": "0xeb1d", "megaphone": "0xeb1e", "mention": "0xeb1f", "menu": "0xeb94", "merge": "0xebab", "mic": "0xec12", "mic-filled": "0xec1c", "microscope": "0xea79", "milestone": "0xeb20", "mirror": "0xea69", "mirror-private": "0xea75", "mirror-public": "0xea69", "more": "0xea7c", "mortar-board": "0xeb21", "move": "0xeb22", "multiple-windows": "0xeb23", "music": "0xec1b", "mute": "0xeb24", "new-file": "0xea7f", "new-folder": "0xea80", "newline": "0xebea", "no-newline": "0xeb25", "note": "0xeb26", "notebook": "0xebaf", "notebook-template": "0xebbf", "octoface": "0xeb27", "open-preview": "0xeb28", "organization": "0xea7e", "organization-filled": "0xea7e", "organization-outline": "0xea7e", "output": "0xeb9d", "package": "0xeb29", "paintcan": "0xeb2a", "pass": "0xeba4", "pass-filled": "0xebb3", "pencil": "0xea73", "percentage": "0xec33", "person": "0xea67", "person-add": "0xebcd", "person-filled": "0xea67", "person-follow": "0xea67", "person-outline": "0xea67", "piano": "0xec1a", "pie-chart": "0xebe4", "pin": "0xeb2b", "pinned": "0xeba0", "pinned-dirty": "0xebb2", "play": "0xeb2c", "play-circle": "0xeba6", "plug": "0xeb2d", "plus": "0xea60", "preserve-case": "0xeb2e", "preview": "0xeb2f", "primitive-dot": "0xea71", "primitive-square": "0xea72", "project": "0xeb30", "pulse": "0xeb31", "question": "0xeb32", "quote": "0xeb33", "radio-tower": "0xeb34", "reactions": "0xeb35", "record": "0xeba7", "record-keys": "0xea65", "record-small": "0xebfa", "redo": "0xebb0", "references": "0xeb36", "refresh": "0xeb37", "regex": "0xeb38", "remote": "0xeb3a", "remote-explorer": "0xeb39", "remove": "0xeb3b", "remove-close": "0xea76", "repl": "0xea85", "replace": "0xeb3d", "replace-all": "0xeb3c", "reply": "0xea7d", "repo": "0xea62", "repo-clone": "0xeb3e", "repo-create": "0xea60", "repo-delete": "0xea62", "repo-fetch": "0xec1d", "repo-force-push": "0xeb3f", "repo-forked": "0xea63", "repo-pull": "0xeb40", "repo-push": "0xeb41", "repo-sync": "0xea77", "report": "0xeb42", "request-changes": "0xeb43", "robot": "0xec20", "rocket": "0xeb44", "root-folder": "0xeb46", "root-folder-opened": "0xeb45", "rss": "0xeb47", "ruby": "0xeb48", "run": "0xeb2c", "run-above": "0xebbd", "run-all": "0xeb9e", "run-all-coverage": "0xec2d", "run-below": "0xebbe", "run-coverage": "0xec2c", "run-errors": "0xebde", "save": "0xeb4b", "save-all": "0xeb49", "save-as": "0xeb4a", "screen-full": "0xeb4c", "screen-normal": "0xeb4d", "search": "0xea6d", "search-fuzzy": "0xec0d", "search-save": "0xea6d", "search-stop": "0xeb4e", "selection": "0xeb85", "send": "0xec0f", "server": "0xeb50", "server-environment": "0xeba3", "server-process": "0xeba2", "settings": "0xeb52", "settings-gear": "0xeb51", "share": "0xec25", "shield": "0xeb53", "sign-in": "0xea6f", "sign-out": "0xea6e", "smiley": "0xeb54", "snake": "0xec16", "sort-percentage": "0xec33", "sort-precedence": "0xeb55", "source-control": "0xea68", "sparkle": "0xec10", "sparkle-filled": "0xec21", "split-horizontal": "0xeb56", "split-vertical": "0xeb57", "squirrel": "0xeb58", "star": "0xea6a", "star-add": "0xea6a", "star-delete": "0xea6a", "star-empty": "0xea6a", "star-full": "0xeb59", "star-half": "0xeb5a", "stop": "0xea87", "stop-circle": "0xeba5", "surround-with": "0xec24", "symbol-array": "0xea8a", "symbol-boolean": "0xea8f", "symbol-class": "0xeb5b", "symbol-color": "0xeb5c", "symbol-constant": "0xeb5d", "symbol-constructor": "0xea8c", "symbol-enum": "0xea95", "symbol-enum-member": "0xeb5e", "symbol-event": "0xea86", "symbol-field": "0xeb5f", "symbol-file": "0xeb60", "symbol-folder": "0xea83", "symbol-function": "0xea8c", "symbol-interface": "0xeb61", "symbol-key": "0xea93", "symbol-keyword": "0xeb62", "symbol-method": "0xea8c", "symbol-misc": "0xeb63", "symbol-module": "0xea8b", "symbol-namespace": "0xea8b", "symbol-null": "0xea8f", "symbol-number": "0xea90", "symbol-numeric": "0xea90", "symbol-object": "0xea8b", "symbol-operator": "0xeb64", "symbol-package": "0xea8b", "symbol-parameter": "0xea92", "symbol-property": "0xeb65", "symbol-reference": "0xea94", "symbol-ruler": "0xea96", "symbol-snippet": "0xeb66", "symbol-string": "0xeb8d", "symbol-struct": "0xea91", "symbol-structure": "0xea91", "symbol-text": "0xea93", "symbol-type-parameter": "0xea92", "symbol-unit": "0xea96", "symbol-value": "0xea95", "symbol-variable": "0xea88", "sync": "0xea77", "sync-ignored": "0xeb9f", "table": "0xebb7", "tag": "0xea66", "tag-add": "0xea66", "tag-remove": "0xea66", "target": "0xebf8", "tasklist": "0xeb67", "telescope": "0xeb68", "terminal": "0xea85", "terminal-bash": "0xebca", "terminal-cmd": "0xebc4", "terminal-debian": "0xebc5", "terminal-decoration-error": "0xebfb", "terminal-decoration-incomplete": "0xeabc", "terminal-decoration-mark": "0xeb8a", "terminal-decoration-success": "0xea71", "terminal-linux": "0xebc6", "terminal-powershell": "0xebc7", "terminal-tmux": "0xebc8", "terminal-ubuntu": "0xebc9", "text-size": "0xeb69", "three-bars": "0xeb6a", "thumbsdown": "0xeb6b", "thumbsdown-filled": "0xec13", "thumbsup": "0xeb6c", "thumbsup-filled": "0xec14", "tools": "0xeb6d", "trash": "0xea81", "trashcan": "0xea81", "triangle-down": "0xeb6e", "triangle-left": "0xeb6f", "triangle-right": "0xeb70", "triangle-up": "0xeb71", "twitter": "0xeb72", "type-hierarchy": "0xebb9", "type-hierarchy-sub": "0xebba", "type-hierarchy-super": "0xebbb", "unfold": "0xeb73", "ungroup-by-ref-type": "0xeb98", "unlock": "0xeb74", "unmute": "0xeb75", "unverified": "0xeb76", "variable": "0xea88", "variable-group": "0xebb8", "verified": "0xeb77", "verified-filled": "0xebe9", "versions": "0xeb78", "vm": "0xea7a", "vm-active": "0xeb79", "vm-connect": "0xeba9", "vm-outline": "0xeb7a", "vm-running": "0xeb7b", "vr": "0xec18", "vscode": "0xec29", "vscode-insiders": "0xec2a", "wand": "0xebcf", "warning": "0xea6c", "watch": "0xeb7c", "whitespace": "0xeb7d", "whole-word": "0xeb7e", "window": "0xeb7f", "word-wrap": "0xeb80", "workspace-trusted": "0xebc1", "workspace-unknown": "0xebc3", "workspace-untrusted": "0xebc2", "wrench": "0xeb65", "wrench-subaction": "0xeb65", "x": "0xea76", "zap": "0xea86", "zoom-in": "0xeb81", "zoom-out": "0xeb82"}