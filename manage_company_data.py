#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إدارة بيانات الشركة في قاعدة البيانات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def view_current_company():
    """عرض بيانات الشركة الحالية"""
    print("👁️ عرض بيانات الشركة الحالية...")
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import Company
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            companies = session.query(Company).filter(Company.is_active == True).all()
            
            if companies:
                print(f"\n📋 تم العثور على {len(companies)} شركة نشطة:")
                print("="*60)
                
                for i, company in enumerate(companies, 1):
                    print(f"\n{i}. الشركة رقم {company.id}:")
                    print(f"   الاسم العربي: {company.name}")
                    print(f"   الاسم الإنجليزي: {company.name_en}")
                    print(f"   العنوان: {company.address}")
                    print(f"   الهاتف: {company.phone}")
                    print(f"   البريد الإلكتروني: {company.email}")
                    print(f"   الرقم الضريبي: {company.tax_number or 'غير محدد'}")
                    print(f"   السجل التجاري: {company.commercial_register or 'غير محدد'}")
                    
                    if company.logo_path:
                        logo_exists = "✅ موجود" if os.path.exists(company.logo_path) else "❌ غير موجود"
                        print(f"   مسار الشعار: {company.logo_path} ({logo_exists})")
                    else:
                        print(f"   مسار الشعار: غير محدد")
                    
                    print(f"   تاريخ الإنشاء: {company.created_at}")
                    print(f"   آخر تحديث: {company.updated_at}")
                
                return companies
            else:
                print("⚠️ لا توجد شركات نشطة في قاعدة البيانات")
                return []
                
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ في عرض بيانات الشركة: {e}")
        return []

def update_company_data():
    """تحديث بيانات الشركة"""
    print("\n✏️ تحديث بيانات الشركة...")
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import Company
        
        # عرض الشركات الحالية
        companies = view_current_company()
        
        if not companies:
            print("لا توجد شركات لتحديثها")
            return False
        
        # اختيار الشركة للتحديث
        if len(companies) == 1:
            company_to_update = companies[0]
            print(f"\nسيتم تحديث الشركة: {company_to_update.name}")
        else:
            print(f"\nاختر الشركة للتحديث (1-{len(companies)}):")
            try:
                choice = int(input("رقم الشركة: ")) - 1
                if 0 <= choice < len(companies):
                    company_to_update = companies[choice]
                else:
                    print("اختيار غير صحيح")
                    return False
            except ValueError:
                print("يرجى إدخال رقم صحيح")
                return False
        
        # تحديث البيانات
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            # الحصول على الشركة من الجلسة الحالية
            company = session.query(Company).get(company_to_update.id)
            
            print(f"\nتحديث بيانات الشركة: {company.name}")
            print("(اتركه فارغاً للاحتفاظ بالقيمة الحالية)")
            
            # تحديث الاسم العربي
            new_name = input(f"الاسم العربي [{company.name}]: ").strip()
            if new_name:
                company.name = new_name
            
            # تحديث الاسم الإنجليزي
            new_name_en = input(f"الاسم الإنجليزي [{company.name_en}]: ").strip()
            if new_name_en:
                company.name_en = new_name_en
            
            # تحديث العنوان
            new_address = input(f"العنوان [{company.address}]: ").strip()
            if new_address:
                company.address = new_address
            
            # تحديث الهاتف
            new_phone = input(f"الهاتف [{company.phone}]: ").strip()
            if new_phone:
                company.phone = new_phone
            
            # تحديث البريد الإلكتروني
            new_email = input(f"البريد الإلكتروني [{company.email}]: ").strip()
            if new_email:
                company.email = new_email
            
            # تحديث الرقم الضريبي
            new_tax = input(f"الرقم الضريبي [{company.tax_number or ''}]: ").strip()
            if new_tax:
                company.tax_number = new_tax
            
            # تحديث السجل التجاري
            new_register = input(f"السجل التجاري [{company.commercial_register or ''}]: ").strip()
            if new_register:
                company.commercial_register = new_register
            
            # تحديث مسار الشعار
            new_logo = input(f"مسار الشعار [{company.logo_path or ''}]: ").strip()
            if new_logo:
                if os.path.exists(new_logo):
                    company.logo_path = new_logo
                    print(f"✅ تم تحديد مسار الشعار: {new_logo}")
                else:
                    print(f"⚠️ الملف غير موجود: {new_logo}")
                    confirm = input("هل تريد حفظ المسار رغم ذلك؟ (y/n): ")
                    if confirm.lower() == 'y':
                        company.logo_path = new_logo
            
            # حفظ التغييرات
            session.commit()
            print("\n✅ تم تحديث بيانات الشركة بنجاح!")
            
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في تحديث البيانات: {e}")
            return False
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ في تحديث بيانات الشركة: {e}")
        return False

def set_logo_path():
    """تحديد مسار الشعار"""
    print("\n🖼️ تحديد مسار الشعار...")
    
    # البحث عن ملفات الشعار المحتملة
    possible_logos = []
    
    # البحث في المجلد الحالي
    for ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp']:
        for pattern in ['logo', 'LOGO', 'Logo', 'شعار']:
            files = list(Path('.').glob(f'*{pattern}*.{ext}'))
            possible_logos.extend(files)
    
    # البحث في مجلد assets
    if Path('assets').exists():
        for ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp']:
            files = list(Path('assets').glob(f'*.{ext}'))
            possible_logos.extend(files)
    
    if possible_logos:
        print("📁 ملفات الشعار المحتملة:")
        for i, logo in enumerate(possible_logos, 1):
            file_size = logo.stat().st_size if logo.exists() else 0
            print(f"   {i}. {logo} ({file_size} بايت)")
        
        print(f"   {len(possible_logos) + 1}. إدخال مسار مخصص")
        
        try:
            choice = int(input(f"اختر الشعار (1-{len(possible_logos) + 1}): "))
            
            if 1 <= choice <= len(possible_logos):
                selected_logo = str(possible_logos[choice - 1].absolute())
                print(f"تم اختيار: {selected_logo}")
                return selected_logo
            elif choice == len(possible_logos) + 1:
                custom_path = input("أدخل مسار الشعار: ").strip()
                return custom_path if custom_path else None
            else:
                print("اختيار غير صحيح")
                return None
                
        except ValueError:
            print("يرجى إدخال رقم صحيح")
            return None
    else:
        print("لم يتم العثور على ملفات شعار محتملة")
        custom_path = input("أدخل مسار الشعار (أو اتركه فارغاً): ").strip()
        return custom_path if custom_path else None

def test_pdf_with_updated_data():
    """اختبار مولد PDF مع البيانات المحدثة"""
    print("\n📄 اختبار مولد PDF مع البيانات المحدثة...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # إنشاء مولد PDF جديد (سيحمل البيانات المحدثة)
        pdf_generator = RemittancePDFGenerator()
        
        # بيانات اختبار
        test_data = {
            'request_number': '2025-UPDATED',
            'request_date': '2024/01/03',
            'remittance_amount': '75,000',
            'currency': 'SAR',
            'transfer_purpose': 'UPDATED COMPANY DATA TEST',
            'exchanger': 'شركة الصرافة المحدثة',
            
            'receiver_name': 'UPDATED TEST COMPANY',
            'receiver_country': 'SAUDI ARABIA',
            'manager_name': 'مدير محدث'
        }
        
        # إنشاء ملف PDF
        output_path = "نموذج_بيانات_الشركة_المحدثة.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"✅ تم إنشاء النموذج المحدث: {result_path}")
            print(f"📄 حجم الملف: {file_size} بايت")
            return True
        else:
            print("❌ فشل في إنشاء النموذج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏢 أداة إدارة بيانات الشركة في قاعدة البيانات")
    print("="*60)
    
    while True:
        print("\nاختر العملية:")
        print("1. عرض بيانات الشركة الحالية")
        print("2. تحديث بيانات الشركة")
        print("3. تحديد مسار الشعار")
        print("4. اختبار مولد PDF")
        print("5. خروج")
        
        try:
            choice = input("\nاختيارك (1-5): ").strip()
            
            if choice == '1':
                view_current_company()
            elif choice == '2':
                update_company_data()
            elif choice == '3':
                logo_path = set_logo_path()
                if logo_path:
                    print(f"مسار الشعار المحدد: {logo_path}")
                    # يمكن هنا تحديث قاعدة البيانات مباشرة
            elif choice == '4':
                test_pdf_with_updated_data()
            elif choice == '5':
                print("👋 وداعاً!")
                break
            else:
                print("اختيار غير صحيح، يرجى المحاولة مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
