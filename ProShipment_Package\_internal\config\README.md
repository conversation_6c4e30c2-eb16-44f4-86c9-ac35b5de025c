# ملفات إعدادات Oracle - ProShipment V2.0.0

## الملفات المتاحة:

### 1. database_development.json
- إعدادات التطوير مع Oracle XE المحلي
- مناسب للتطوير والاختبار المحلي
- pool_size صغير (5 اتصالات)

### 2. database_testing.json
- إعدادات بيئة الاختبار
- يتضمن إعدادات SSL
- pool_size متوسط (10 اتصالات)

### 3. database_production.json
- إعدادات بيئة الإنتاج
- أمان عالي مع SSL و Wallet
- pool_size كبير (50 اتصال)
- كلمة المرور فارغة (يجب تعيينها من متغيرات البيئة)

### 4. database_cloud.json
- إعدادات Oracle Cloud
- منفذ 1522 (Oracle Cloud الافتراضي)
- يتطلب Oracle Wallet

### 5. database_enterprise_sid.json
- إعدادات Oracle Enterprise مع SID
- يستخدم SID بدلاً من Service Name

### 6. database_sqlite.json
- إعدادات SQLite للمقارنة
- يمكن استخدامها للعودة إلى SQLite

## كيفية الاستخدام:

### 1. نسخ الملف المناسب:
```bash
cp config/database_development.json config/database.json
```

### 2. تعديل الإعدادات:
- عدل عنوان الخادم
- عدل اسم المستخدم وكلمة المرور
- عدل اسم الخدمة أو SID

### 3. تعيين كلمة المرور من متغيرات البيئة (للإنتاج):
```bash
export ORACLE_PASSWORD="your_secure_password"
```

### 4. اختبار الإعدادات:
```bash
python tools/oracle_setup_wizard.py
```

## متغيرات البيئة المدعومة:

- `DATABASE_TYPE`: نوع قاعدة البيانات (sqlite/oracle)
- `ORACLE_HOST`: عنوان خادم Oracle
- `ORACLE_PORT`: منفذ Oracle
- `ORACLE_SERVICE_NAME`: اسم خدمة Oracle
- `ORACLE_SID`: SID لـ Oracle
- `ORACLE_USERNAME`: اسم مستخدم Oracle
- `ORACLE_PASSWORD`: كلمة مرور Oracle
- `ORACLE_CONNECTION_TYPE`: نوع الاتصال (service_name/sid/tns)
- `ORACLE_USE_SSL`: استخدام SSL (true/false)
- `ORACLE_WALLET_LOCATION`: مسار Oracle Wallet

## أمثلة على الاستخدام:

### تطوير محلي:
```bash
export DATABASE_TYPE=oracle
export ORACLE_HOST=localhost
export ORACLE_SERVICE_NAME=XE
export ORACLE_USERNAME=proshipment
export ORACLE_PASSWORD=dev123
```

### إنتاج:
```bash
export DATABASE_TYPE=oracle
export ORACLE_HOST=prod-oracle.company.com
export ORACLE_SERVICE_NAME=PROSHIP_PROD
export ORACLE_USERNAME=proshipment_prod
export ORACLE_PASSWORD=secure_prod_password
export ORACLE_USE_SSL=true
export ORACLE_WALLET_LOCATION=/etc/oracle/wallet
```
