#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_remittances_window():
    """اختبار نافذة الحوالات المحسنة"""
    print("🔍 اختبار نافذة الحوالات المحسنة...")
    print("=" * 50)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt, QTimer
        from src.ui.main_window import MainWindow
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        print("✅ تم إنشاء وعرض النافذة الرئيسية")
        
        # اختبار فتح نافذة الحوالات
        print("\n🔄 اختبار فتح نافذة الحوالات...")
        main_window.open_remittances_window()
        
        # التحقق من أن النافذة تم إنشاؤها
        if hasattr(main_window, 'remittances_window') and main_window.remittances_window:
            print("✅ تم إنشاء نافذة الحوالات")
            
            # التحقق من أن النافذة ظاهرة
            if main_window.remittances_window.isVisible():
                print("✅ نافذة الحوالات ظاهرة")
            else:
                print("❌ نافذة الحوالات غير ظاهرة")
                
            # التحقق من أن النافذة في المقدمة
            if main_window.remittances_window.isActiveWindow():
                print("✅ نافذة الحوالات نشطة في المقدمة")
            else:
                print("⚠️ نافذة الحوالات ليست نشطة")
                
        else:
            print("❌ فشل في إنشاء نافذة الحوالات")
        
        print("\n📋 التحسينات المطبقة:")
        print("   • استخدام WindowStaysOnTopHint مؤقتاً")
        print("   • إغلاق النوافذ السابقة قبل فتح جديدة")
        print("   • توسيط النوافذ وسط الشاشة")
        print("   • إحضار النوافذ للمقدمة بطرق متعددة")
        print("   • إزالة خاصية البقاء في المقدمة بعد ثانية")
        
        print("\n🎉 اختبار نافذة الحوالات مكتمل!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_remittances_window()
    
    if success:
        print("\n✅ نافذة الحوالات تعمل بشكل صحيح!")
        print("\n📝 للاختبار الكامل:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. انقر على 'إدارة الحوالات' في القائمة الرئيسية")
        print("3. ستظهر النافذة في المقدمة ومتوسطة")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
