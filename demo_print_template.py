#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تجريبي لنموذج طباعة طلب الحوالة
Demo for Remittance Print Template
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    from datetime import datetime
    
    return {
        'request_number': 'ALF-2025-001',
        'request_date': datetime.now().strftime('%Y/%m/%d'),
        'branch': 'صرافة الأمانة للتحويلات المالية',
        'exchanger': 'أحمد محمد الصراف',
        'remittance_amount': 5000.0,
        'currency': 'ريال يمني',
        'transfer_purpose': 'COST OF FOODSTUFF AND MEDICAL SUPPLIES',
        
        # معلومات المرسل (شركة الفقيهي)
        'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
        'sender_entity': 'G.M: NASHA\'AT RASHAD QASIM ALDUBAEE',
        'sender_phone': '+967 1 616109',
        'sender_fax': '+967 1 615909',
        'sender_mobile': '+967 *********',
        'sender_pobox': '1903',
        'sender_email': '<EMAIL>, <EMAIL>',
        'sender_address': 'TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',
        
        # معلومات المستقبل
        'receiver_name': 'MOHAMMED AHMED HASSAN AL-YEMENI',
        'receiver_account': '****************',
        'receiver_bank_name': 'AL RAJHI BANK',
        'receiver_bank_branch': 'RIYADH MAIN BRANCH',
        'receiver_swift': 'RJHISARI',
        'receiver_country': 'SAUDI ARABIA',
        'receiver_address': 'KING FAHD ROAD, RIYADH, SAUDI ARABIA',
        
        # معلومات إضافية
        'notes': 'طلب تحويل عاجل للمواد الغذائية والطبية',
        'sms_notification': True,
        'email_notification': True,
        'auto_create_remittance': False,
        'status': 'معلق',
        'created_at': datetime.now().isoformat()
    }

def run_demo():
    """تشغيل العرض التجريبي"""
    
    print("🚀 بدء العرض التجريبي لنموذج طباعة طلب الحوالة...")
    print("=" * 80)
    
    try:
        # إعداد البيئة
        import os
        os.environ['QT_QPA_PLATFORM'] = 'windows'  # للويندوز
        
        from PySide6.QtWidgets import QApplication, QMessageBox
        from src.ui.remittances.remittance_print_template import RemittancePrintTemplate
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء البيانات التجريبية
        sample_data = create_sample_data()
        
        print("📋 البيانات التجريبية:")
        print(f"   📄 رقم الطلب: {sample_data['request_number']}")
        print(f"   📅 التاريخ: {sample_data['request_date']}")
        print(f"   🏢 الفرع: {sample_data['branch']}")
        print(f"   👤 المستقبل: {sample_data['receiver_name']}")
        print(f"   🏦 البنك: {sample_data['receiver_bank_name']}")
        print(f"   💰 المبلغ: {sample_data['remittance_amount']} {sample_data['currency']}")
        print(f"   🎯 الغرض: {sample_data['transfer_purpose']}")
        
        print("\n🖨️ فتح نموذج الطباعة...")
        
        # إنشاء نافذة الطباعة
        print_window = RemittancePrintTemplate(sample_data)
        print_window.show()
        
        # عرض رسالة توضيحية
        msg = QMessageBox()
        msg.setWindowTitle("نموذج طباعة طلب الحوالة")
        msg.setText("""
🎉 مرحباً بك في نموذج طباعة طلب الحوالة!

📋 الميزات المتاحة:
• معاينة النموذج قبل الطباعة
• طباعة مباشرة بجودة عالية
• تنسيق احترافي مطابق للنموذج الأصلي
• تعبئة تلقائية للبيانات

🖨️ للطباعة:
1. انقر على "معاينة الطباعة" لرؤية النموذج
2. انقر على "طباعة" للطباعة المباشرة
3. انقر على "إغلاق" للخروج

✨ النموذج يحتوي على جميع عناصر النموذج الأصلي:
• رأس الشركة بالعربية والإنجليزية
• معلومات المستفيد والبنك
• معلومات شركة الفقيهي كاملة
• التوقيع والختم
        """.strip())
        msg.setIcon(QMessageBox.Information)
        msg.exec()
        
        print("✅ تم فتح نموذج الطباعة بنجاح!")
        print("💡 يمكنك الآن استخدام أزرار الطباعة والمعاينة")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PySide6: pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل العرض التجريبي: {e}")
        import traceback
        traceback.print_exc()
        return 1

def display_template_features():
    """عرض ميزات النموذج"""
    
    print("\n" + "=" * 80)
    print("🎨 ميزات نموذج طباعة طلب الحوالة")
    print("=" * 80)
    
    print("\n📋 التصميم:")
    print("   ✅ مطابق تماماً للنموذج الأصلي المرفق")
    print("   ✅ تخطيط احترافي ومنظم")
    print("   ✅ نصوص عربية وإنجليزية")
    print("   ✅ شعار الشركة في المنتصف")
    print("   ✅ ألوان وخطوط مناسبة للطباعة")
    
    print("\n🏢 معلومات الشركة:")
    print("   ✅ اسم الشركة بالعربية والإنجليزية")
    print("   ✅ العنوان الكامل")
    print("   ✅ أرقام الهاتف والفاكس والموبايل")
    print("   ✅ البريد الإلكتروني")
    print("   ✅ صندوق البريد")
    print("   ✅ اسم المدير العام")
    
    print("\n📄 محتوى النموذج:")
    print("   ✅ رقم المستند والتاريخ")
    print("   ✅ معلومات الأطراف (الأخوة، للصرافة، المحترمون)")
    print("   ✅ تحية رسمية")
    print("   ✅ طلب التحويل")
    print("   ✅ معلومات المستفيد كاملة")
    print("   ✅ معلومات البنك والسويفت")
    print("   ✅ الغرض من التحويل")
    print("   ✅ التوقيع والختم")
    
    print("\n🖨️ ميزات الطباعة:")
    print("   ✅ معاينة قبل الطباعة")
    print("   ✅ طباعة مباشرة")
    print("   ✅ تنسيق A4 قياسي")
    print("   ✅ جودة طباعة عالية")
    print("   ✅ تكبير تلقائي للصفحة")
    print("   ✅ هوامش مناسبة")
    
    print("\n🔗 التكامل:")
    print("   ✅ مدمج مع شاشة طلب الحوالة")
    print("   ✅ تعبئة تلقائية للبيانات")
    print("   ✅ زر طباعة في الشاشة الرئيسية")
    print("   ✅ التحقق من البيانات قبل الطباعة")
    
    print("\n🎯 الاستخدام:")
    print("   1. فتح شاشة طلب الحوالة")
    print("   2. تعبئة البيانات المطلوبة")
    print("   3. الضغط على زر 'طباعة النموذج'")
    print("   4. اختيار معاينة أو طباعة مباشرة")
    print("   5. الحصول على نموذج احترافي جاهز")

if __name__ == "__main__":
    # عرض ميزات النموذج
    display_template_features()
    
    # تشغيل العرض التجريبي
    exit_code = run_demo()
    
    print("\n" + "=" * 80)
    if exit_code == 0:
        print("🎉 انتهى العرض التجريبي بنجاح!")
        print("✅ نموذج الطباعة جاهز للاستخدام")
    else:
        print("❌ حدث خطأ في العرض التجريبي")
    print("=" * 80)
    
    sys.exit(exit_code)
