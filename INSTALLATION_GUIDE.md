# دليل التثبيت والتوزيع - ProShipment V2.0.0

## 🎯 نظرة عامة
هذا الدليل يوضح كيفية إنشاء ملف تثبيت تنفيذي (.exe) لنظام ProShipment ليصبح جاهزاً للعمل على أي جهاز ويندوز مع الاحتفاظ بجميع البيانات الموجودة.

## 📋 متطلبات البناء

### متطلبات النظام:
- **نظام التشغيل:** Windows 10/11
- **Python:** 3.8 أو أحدث
- **الذاكرة:** 8 GB RAM (للبناء)
- **التخزين:** 5 GB مساحة فارغة
- **الإنترنت:** لتحميل التبعيات

### البرامج المطلوبة:
```bash
# تثبيت Python (إذا لم يكن مثبتاً)
# تحميل من: https://www.python.org/downloads/

# تحديث pip
python -m pip install --upgrade pip
```

## 🚀 خطوات إنشاء ملف التثبيت

### الطريقة الأولى: استخدام الملف التلقائي (الأسهل)

1. **تشغيل ملف البناء التلقائي:**
   ```cmd
   build_exe.bat
   ```

2. **انتظار اكتمال العملية:**
   - سيتم تثبيت المتطلبات تلقائياً
   - سيتم بناء الملف التنفيذي
   - سيتم إنشاء حزمة التثبيت

### الطريقة الثانية: البناء اليدوي

1. **تثبيت متطلبات البناء:**
   ```cmd
   pip install -r requirements_build.txt
   ```

2. **تشغيل أداة البناء:**
   ```cmd
   python build_installer.py
   ```

## 📦 محتويات الحزمة المنشأة

بعد اكتمال البناء، ستجد في مجلد `installer/`:

### الملفات المضغوطة:
- `ProShipment-V2.0.0-YYYY-MM-DD.zip` - حزمة ZIP
- `ProShipment-V2.0.0-YYYY-MM-DD.tar.gz` - حزمة TAR.GZ

### ملفات إضافية:
- `checksums.json` - للتحقق من سلامة الملفات
- `RELEASE_NOTES_V2.0.0.md` - ملاحظات الإصدار

### محتويات الحزمة المفكوكة:
```
ProShipment/
├── ProShipment.exe              # الملف التنفيذي الرئيسي
├── data/                        # البيانات وقاعدة البيانات
│   ├── proshipment.db          # قاعدة البيانات الرئيسية
│   └── attachments_backup/     # نسخة احتياطية من المرفقات
├── config/                      # ملفات الإعدادات
├── الوثائق/                    # دليل المستخدم والوثائق
├── تشغيل_التطبيق.bat           # ملف تشغيل سريع
├── دليل_التشغيل.md             # دليل التشغيل
├── سجل_التغييرات.md            # سجل التغييرات
├── متطلبات_النظام.txt          # متطلبات النظام
└── معلومات_التثبيت.json       # معلومات التثبيت
```

## 💾 الاحتفاظ بالبيانات

### البيانات المحفوظة تلقائياً:
- ✅ **قاعدة البيانات:** `data/proshipment.db`
- ✅ **المرفقات:** `attachments/` → `data/attachments_backup/`
- ✅ **الإعدادات:** `config/`
- ✅ **السجلات:** `logs/`

### النسخ الاحتياطية:
- يتم إنشاء نسخة احتياطية تلقائياً قبل البناء
- النسخ الاحتياطية محفوظة في `data/` مع طابع زمني

## 📤 توزيع التطبيق

### للمستخدم النهائي:

1. **تحميل الحزمة:**
   - إرسال ملف ZIP أو TAR.GZ للمستخدم

2. **فك الضغط:**
   - فك ضغط الملف في أي مكان على الجهاز

3. **التشغيل:**
   ```
   # الطريقة الأولى
   تشغيل ProShipment.exe مباشرة
   
   # الطريقة الثانية
   تشغيل تشغيل_التطبيق.bat
   ```

### متطلبات جهاز المستخدم:
- **نظام التشغيل:** Windows 10/11
- **الذاكرة:** 4 GB RAM
- **التخزين:** 2 GB مساحة فارغة
- **الشاشة:** 1024x768 أو أعلى
- **لا يحتاج Python أو أي برامج إضافية**

## 🔧 استكشاف الأخطاء

### مشاكل البناء الشائعة:

#### خطأ: "PyInstaller not found"
```cmd
pip install pyinstaller
```

#### خطأ: "Missing module"
```cmd
pip install -r requirements.txt
pip install -r requirements_build.txt
```

#### خطأ: "Permission denied"
- تشغيل Command Prompt كمدير
- إغلاق برامج مكافحة الفيروسات مؤقتاً

#### خطأ: "Out of memory"
- إغلاق البرامج الأخرى
- زيادة الذاكرة الافتراضية

### مشاكل التشغيل الشائعة:

#### "Application failed to start"
- التأكد من وجود جميع ملفات DLL
- تشغيل كمدير إذا لزم الأمر

#### "Database error"
- التأكد من وجود مجلد `data/`
- التأكد من صلاحيات الكتابة

## 📊 معلومات الأداء

### حجم الملفات المتوقع:
- **الملف التنفيذي:** ~200-300 MB
- **الحزمة الكاملة:** ~400-500 MB
- **بعد الفك:** ~800 MB - 1 GB

### وقت البناء المتوقع:
- **جهاز سريع:** 5-10 دقائق
- **جهاز متوسط:** 10-20 دقيقة
- **جهاز بطيء:** 20-30 دقيقة

## 🔒 الأمان

### التحقق من سلامة الملفات:
```cmd
# استخدام checksums.json للتحقق
python -c "
import json, hashlib
with open('checksums.json') as f:
    checksums = json.load(f)
# التحقق من كل ملف...
"
```

### التوقيع الرقمي (اختياري):
- يمكن إضافة توقيع رقمي للملف التنفيذي
- استخدام أدوات مثل SignTool من Microsoft

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:

1. **مراجعة هذا الدليل**
2. **فحص ملفات السجلات**
3. **التأكد من متطلبات النظام**
4. **التواصل مع الدعم الفني**

### معلومات مفيدة للدعم:
- إصدار Windows
- إصدار Python المستخدم
- رسائل الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## 🎉 الخلاصة

باتباع هذا الدليل، ستحصل على:
- ✅ ملف تثبيت تنفيذي جاهز للتوزيع
- ✅ احتفاظ كامل بجميع البيانات الموجودة
- ✅ سهولة التثبيت على أي جهاز ويندوز
- ✅ عدم الحاجة لتثبيت Python أو تبعيات إضافية

---

**ملاحظة:** هذا الدليل خاص بالإصدار V2.0.0 من ProShipment. للإصدارات الأخرى، قد تختلف بعض التفاصيل.
