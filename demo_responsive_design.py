#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لنظام التصميم المتجاوب - ProShipment
Demo for Responsive Design System
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_responsive_window():
    """عرض توضيحي للنافذة المتجاوبة"""
    try:
        from PySide6.QtWidgets import (
            QApplication, QVBoxLayout, QHBoxLayout, QGridLayout,
            QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
            QTableWidget, QTableWidgetItem, QGroupBox, QTabWidget,
            QWidget, QFrame, QCheckBox, QSlider, QSpinBox
        )
        from src.ui.base.base_window import BaseWindow
        from src.ui.responsive.responsive_manager import responsive_manager
        from src.ui.themes.theme_manager import theme_manager
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = BaseWindow(window_title="عرض توضيحي - نظام التصميم المتجاوب")
        
        # إنشاء التبويبات
        tab_widget = QTabWidget()
        window.main_layout.addWidget(tab_widget)
        
        # تبويب العناصر الأساسية
        basic_tab = create_basic_elements_tab()
        tab_widget.addTab(basic_tab, "العناصر الأساسية")
        
        # تبويب الجداول
        table_tab = create_table_tab()
        tab_widget.addTab(table_tab, "الجداول")
        
        # تبويب النماذج
        form_tab = create_form_tab()
        tab_widget.addTab(form_tab, "النماذج")
        
        # تبويب الألوان
        colors_tab = create_colors_tab()
        tab_widget.addTab(colors_tab, "الألوان")
        
        # إضافة شريط أدوات
        toolbar = window.create_toolbar("أدوات التصميم")
        
        # إضافة أدوات للشريط
        toolbar.addAction("🎨 تغيير الثيم", lambda: change_theme(window))
        toolbar.addAction("🌈 تغيير الألوان", lambda: change_colors(window))
        toolbar.addAction("📱 معلومات الشاشة", lambda: show_screen_info(window))
        toolbar.addAction("⚙️ إعدادات التصميم", lambda: open_design_settings(window))
        
        # عرض النافذة
        window.show()
        
        print("🎬 عرض توضيحي لنظام التصميم المتجاوب")
        print("📋 الميزات المتاحة:")
        print("   📱 تصميم متجاوب يتكيف مع حجم الشاشة")
        print("   🎨 ثيمات متعددة (فاتح، مظلم)")
        print("   🌈 مخططات ألوان متنوعة")
        print("   🔤 خطوط قابلة للتخصيص")
        print("   📐 عناصر واجهة موحدة")
        print("\n💡 جرب تغيير حجم النافذة لرؤية التصميم المتجاوب!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")

def create_basic_elements_tab():
    """إنشاء تبويب العناصر الأساسية"""
    from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGroupBox
    from src.ui.themes.theme_manager import theme_manager
    
    widget = QWidget()
    layout = QVBoxLayout(widget)
    
    # مجموعة الأزرار
    buttons_group = QGroupBox("🔘 الأزرار")
    buttons_layout = QHBoxLayout(buttons_group)
    
    button_styles = [
        ("أساسي", "primary"),
        ("ثانوي", "secondary"),
        ("نجاح", "success"),
        ("تحذير", "warning"),
        ("خطر", "danger"),
        ("معلومات", "info")
    ]
    
    for text, style in button_styles:
        button = QPushButton(text)
        theme_manager.apply_button_style(button, style)
        buttons_layout.addWidget(button)
    
    layout.addWidget(buttons_group)
    
    # مجموعة النصوص
    text_group = QGroupBox("📝 النصوص")
    text_layout = QVBoxLayout(text_group)
    
    text_layout.addWidget(QLabel("نص عادي"))
    
    title_label = QLabel("عنوان رئيسي")
    title_font = title_label.font()
    title_font.setPointSize(16)
    title_font.setBold(True)
    title_label.setFont(title_font)
    text_layout.addWidget(title_label)
    
    subtitle_label = QLabel("عنوان فرعي")
    subtitle_font = subtitle_label.font()
    subtitle_font.setPointSize(14)
    subtitle_label.setFont(subtitle_font)
    text_layout.addWidget(subtitle_label)
    
    layout.addWidget(text_group)
    layout.addStretch()
    
    return widget

def create_table_tab():
    """إنشاء تبويب الجداول"""
    from PySide6.QtWidgets import QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView
    
    widget = QWidget()
    layout = QVBoxLayout(widget)
    
    # إنشاء جدول تجريبي
    table = QTableWidget(5, 4)
    table.setHorizontalHeaderLabels(["الاسم", "العمر", "المدينة", "الوظيفة"])
    
    # بيانات تجريبية
    data = [
        ["أحمد محمد", "30", "الرياض", "مهندس"],
        ["فاطمة علي", "25", "جدة", "طبيبة"],
        ["محمد أحمد", "35", "الدمام", "معلم"],
        ["نورا سالم", "28", "مكة", "محاسبة"],
        ["خالد عبدالله", "32", "المدينة", "مبرمج"]
    ]
    
    for row, row_data in enumerate(data):
        for col, cell_data in enumerate(row_data):
            table.setItem(row, col, QTableWidgetItem(cell_data))
    
    # تنسيق الجدول
    table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
    table.setAlternatingRowColors(True)
    
    layout.addWidget(table)
    
    return widget

def create_form_tab():
    """إنشاء تبويب النماذج"""
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QFormLayout, QLineEdit, 
        QTextEdit, QComboBox, QCheckBox, QGroupBox
    )
    
    widget = QWidget()
    layout = QVBoxLayout(widget)
    
    # نموذج معلومات شخصية
    personal_group = QGroupBox("📋 معلومات شخصية")
    personal_layout = QFormLayout(personal_group)
    
    personal_layout.addRow("الاسم الكامل:", QLineEdit())
    personal_layout.addRow("البريد الإلكتروني:", QLineEdit())
    personal_layout.addRow("رقم الهاتف:", QLineEdit())
    
    city_combo = QComboBox()
    city_combo.addItems(["الرياض", "جدة", "الدمام", "مكة", "المدينة"])
    personal_layout.addRow("المدينة:", city_combo)
    
    personal_layout.addRow("العنوان:", QTextEdit())
    personal_layout.addRow("اشتراك النشرة:", QCheckBox("أريد تلقي النشرة الإخبارية"))
    
    layout.addWidget(personal_group)
    layout.addStretch()
    
    return widget

def create_colors_tab():
    """إنشاء تبويب الألوان"""
    from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QGroupBox
    from src.ui.themes.theme_manager import theme_manager
    
    widget = QWidget()
    layout = QVBoxLayout(widget)
    
    # مجموعة الألوان الأساسية
    colors_group = QGroupBox("🎨 مخطط الألوان الحالي")
    colors_layout = QVBoxLayout(colors_group)
    
    # الألوان الأساسية
    primary_colors = ["primary", "secondary", "success", "warning", "danger", "info"]
    primary_layout = QHBoxLayout()
    
    for color_name in primary_colors:
        color_frame = create_color_sample(color_name, theme_manager.get_color(color_name))
        primary_layout.addWidget(color_frame)
    
    colors_layout.addLayout(primary_layout)
    
    # ألوان النظام
    system_colors = ["background", "surface", "text", "text_secondary"]
    system_layout = QHBoxLayout()
    
    for color_name in system_colors:
        color_frame = create_color_sample(color_name, theme_manager.get_color(color_name))
        system_layout.addWidget(color_frame)
    
    colors_layout.addLayout(system_layout)
    
    layout.addWidget(colors_group)
    layout.addStretch()
    
    return widget

def create_color_sample(name, color):
    """إنشاء عينة لون"""
    from PySide6.QtWidgets import QFrame, QVBoxLayout, QLabel
    
    frame = QFrame()
    frame.setFixedSize(100, 80)
    frame.setStyleSheet(f"""
        QFrame {{
            background-color: {color};
            border: 2px solid #ddd;
            border-radius: 8px;
        }}
    """)
    
    layout = QVBoxLayout(frame)
    
    name_label = QLabel(name)
    name_label.setStyleSheet("color: white; font-weight: bold; background: rgba(0,0,0,0.5); padding: 2px; border-radius: 3px;")
    layout.addWidget(name_label)
    
    color_label = QLabel(color)
    color_label.setStyleSheet("color: white; font-size: 10px; background: rgba(0,0,0,0.5); padding: 2px; border-radius: 3px;")
    layout.addWidget(color_label)
    
    layout.addStretch()
    
    return frame

def change_theme(window):
    """تغيير الثيم"""
    from src.ui.themes.theme_manager import theme_manager, ThemeType
    
    current = theme_manager.current_theme
    if current == ThemeType.LIGHT:
        theme_manager.set_theme(ThemeType.DARK)
        print("🌙 تم التبديل إلى الثيم المظلم")
    else:
        theme_manager.set_theme(ThemeType.LIGHT)
        print("☀️ تم التبديل إلى الثيم الفاتح")

def change_colors(window):
    """تغيير مخطط الألوان"""
    from src.ui.themes.theme_manager import theme_manager, ColorScheme
    
    schemes = list(ColorScheme)
    current_index = schemes.index(theme_manager.current_color_scheme)
    next_index = (current_index + 1) % len(schemes)
    next_scheme = schemes[next_index]
    
    theme_manager.set_color_scheme(next_scheme)
    print(f"🌈 تم التبديل إلى مخطط الألوان: {next_scheme.value}")

def show_screen_info(window):
    """عرض معلومات الشاشة"""
    from PySide6.QtWidgets import QMessageBox
    from src.ui.responsive.responsive_manager import responsive_manager
    
    info = responsive_manager.get_screen_info()
    
    message = "📱 معلومات الشاشة:\n\n"
    for key, value in info.items():
        message += f"{key}: {value}\n"
    
    QMessageBox.information(window, "معلومات الشاشة", message)

def open_design_settings(window):
    """فتح نافذة إعدادات التصميم"""
    try:
        from src.ui.dialogs.design_settings_dialog import DesignSettingsDialog
        
        dialog = DesignSettingsDialog(window)
        dialog.exec()
        
    except Exception as e:
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(window, "خطأ", f"فشل في فتح نافذة الإعدادات:\n{str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🎬 عرض توضيحي لنظام التصميم المتجاوب")
    print("="*50)
    
    print("اختر العرض التوضيحي:")
    print("1. النافذة المتجاوبة الشاملة")
    print("2. معلومات عن النظام")
    
    choice = input("\nاختر رقم (1-2): ").strip()
    
    if choice == "1":
        demo_responsive_window()
    elif choice == "2":
        show_system_info()
    else:
        print("❌ خيار غير صحيح")

def show_system_info():
    """عرض معلومات عن النظام"""
    print("\n📱 نظام التصميم المتجاوب - ProShipment")
    print("="*50)
    
    print("\n🆕 الميزات الجديدة:")
    print("   ✅ مدير التصميم المتجاوب")
    print("   ✅ مدير الثيمات والألوان")
    print("   ✅ نوافذ أساسية موحدة")
    print("   ✅ نافذة إعدادات التصميم")
    print("   ✅ دعم أحجام شاشات متعددة")
    print("   ✅ ثيمات فاتحة ومظلمة")
    print("   ✅ مخططات ألوان متنوعة")
    
    print("\n🎯 الهدف:")
    print("   📐 توحيد تصميم جميع نوافذ التطبيق")
    print("   📱 تكيف تلقائي مع أحجام الشاشات المختلفة")
    print("   🎨 إمكانية تخصيص المظهر والألوان")
    print("   🔤 خطوط قابلة للتخصيص")
    
    print("\n📊 أحجام الشاشات المدعومة:")
    print("   📱 صغيرة: أقل من 1366x768")
    print("   💻 متوسطة: 1366x768 إلى 1920x1080")
    print("   🖥️ كبيرة: 1920x1080 إلى 2560x1440")
    print("   📺 كبيرة جداً: أكبر من 2560x1440")
    
    print("\n🎨 الثيمات المتاحة:")
    print("   ☀️ فاتح - للاستخدام النهاري")
    print("   🌙 مظلم - للاستخدام الليلي")
    print("   🔄 تلقائي - حسب إعدادات النظام")
    
    print("\n🌈 مخططات الألوان:")
    print("   🔵 أزرق - الافتراضي")
    print("   🟢 أخضر - للطبيعة")
    print("   🟣 بنفسجي - للإبداع")
    print("   🟠 برتقالي - للحيوية")
    print("   🔴 أحمر - للقوة")
    print("   🔷 تركوازي - للهدوء")
    
    print("\n🛠️ المكتبات المستخدمة:")
    print("   📦 PySide6 - واجهة المستخدم")
    print("   🎨 qdarkstyle - الثيم المظلم")
    print("   🎯 qtawesome - الأيقونات")
    print("   ✨ qtstylish - التنسيق المتقدم")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. استخدم BaseWindow للنوافذ الرئيسية")
    print("   2. استخدم BaseDialog للنوافذ الحوارية")
    print("   3. طبق responsive_manager للتصميم المتجاوب")
    print("   4. استخدم theme_manager للثيمات والألوان")
    print("   5. افتح نافذة إعدادات التصميم للتخصيص")

if __name__ == "__main__":
    main()
