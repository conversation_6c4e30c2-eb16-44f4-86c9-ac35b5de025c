#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الأساسية الموحدة - ProShipment
Base Window Class for Unified Design
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any

from PySide6.QtWidgets import (
    QMainWindow, QDialog, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QStatusBar, QToolBar,
    QMenuBar, QApplication, QMessageBox
)
from PySide6.QtCore import Qt, QTimer, Signal, QSize
from PySide6.QtGui import QIcon, QFont, QPixmap, QCloseEvent

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# استيراد مديري التصميم
try:
    from src.ui.responsive.responsive_manager import responsive_manager, WindowType
    from src.ui.themes.theme_manager import theme_manager
    MANAGERS_AVAILABLE = True
except ImportError:
    MANAGERS_AVAILABLE = False
    # تعريف WindowType بديل
    class WindowType:
        MAIN_WINDOW = "main_window"
        DIALOG = "dialog"
        POPUP = "popup"
        FULLSCREEN = "fullscreen"
    print("تحذير: مديري التصميم غير متاحين")

class BaseWindow(QMainWindow):
    """النافذة الأساسية الموحدة"""
    
    # إشارات
    window_closing = Signal()
    window_resized = Signal(QSize)
    
    def __init__(self, parent=None, window_title="ProShipment", window_type=None):
        super().__init__(parent)
        
        # خصائص النافذة
        self.window_title = window_title
        self.window_type = window_type or WindowType.MAIN_WINDOW
        self.is_responsive_enabled = True
        self.auto_center = True
        
        # إعداد النافذة
        self.setup_window()
        self.setup_ui()
        self.apply_responsive_design()
        self.connect_signals()
        
        # تطبيق الثيم
        if MANAGERS_AVAILABLE:
            self.apply_theme()
    
    def setup_window(self):
        """إعداد خصائص النافذة الأساسية"""
        self.setWindowTitle(self.window_title)
        
        # أيقونة النافذة
        icon_path = Path("assets/icons/app_icon.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        
        # خصائص النافذة
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        
        # حجم افتراضي
        if MANAGERS_AVAILABLE:
            config = responsive_manager.get_config("window_default_size")
            if config:
                self.resize(*config)
        else:
            self.resize(1200, 800)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        # Widget مركزي
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # التخطيط الرئيسي
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز")
        
        # شريط الأدوات (اختياري)
        self.toolbar = None
        
        # شريط القوائم (اختياري)
        self.menu_bar = None
    
    def apply_responsive_design(self):
        """تطبيق التصميم المتجاوب"""
        if not MANAGERS_AVAILABLE or not self.is_responsive_enabled:
            return
        
        # تطبيق التصميم المتجاوب
        responsive_manager.apply_responsive_layout(self, self.window_type)
        
        # توسيط النافذة إذا مطلوب
        if self.auto_center:
            responsive_manager.center_window(self)
    
    def apply_theme(self):
        """تطبيق الثيم"""
        if not MANAGERS_AVAILABLE:
            return
        
        # تطبيق stylesheet
        stylesheet = theme_manager.generate_stylesheet()
        if MANAGERS_AVAILABLE:
            responsive_stylesheet = responsive_manager.get_responsive_stylesheet()
            stylesheet += "\n" + responsive_stylesheet
        
        self.setStyleSheet(stylesheet)
    
    def connect_signals(self):
        """ربط الإشارات"""
        if MANAGERS_AVAILABLE:
            # ربط إشارات تغيير الثيم
            theme_manager.theme_changed.connect(self.on_theme_changed)
            theme_manager.color_scheme_changed.connect(self.on_color_scheme_changed)
            
            # ربط إشارات تغيير حجم الشاشة
            responsive_manager.screen_size_changed.connect(self.on_screen_size_changed)
    
    def on_theme_changed(self, theme_name: str):
        """معالج تغيير الثيم"""
        self.apply_theme()
        print(f"تم تغيير الثيم إلى: {theme_name}")
    
    def on_color_scheme_changed(self, color_scheme: str):
        """معالج تغيير مخطط الألوان"""
        self.apply_theme()
        print(f"تم تغيير مخطط الألوان إلى: {color_scheme}")
    
    def on_screen_size_changed(self, screen_size):
        """معالج تغيير حجم الشاشة"""
        if self.is_responsive_enabled:
            self.apply_responsive_design()
            print(f"تم تحديث التصميم لحجم الشاشة: {screen_size.value}")
    
    def create_toolbar(self, title="أدوات") -> QToolBar:
        """إنشاء شريط أدوات"""
        if self.toolbar is None:
            self.toolbar = QToolBar(title)
            self.addToolBar(self.toolbar)
        return self.toolbar
    
    def create_menu_bar(self) -> QMenuBar:
        """إنشاء شريط القوائم"""
        if self.menu_bar is None:
            self.menu_bar = self.menuBar()
        return self.menu_bar
    
    def add_status_widget(self, widget: QWidget, permanent=False):
        """إضافة widget إلى شريط الحالة"""
        if permanent:
            self.status_bar.addPermanentWidget(widget)
        else:
            self.status_bar.addWidget(widget)
    
    def show_status_message(self, message: str, timeout=0):
        """عرض رسالة في شريط الحالة"""
        self.status_bar.showMessage(message, timeout)
    
    def set_responsive_enabled(self, enabled: bool):
        """تفعيل/تعطيل التصميم المتجاوب"""
        self.is_responsive_enabled = enabled
        if enabled:
            self.apply_responsive_design()
    
    def set_auto_center(self, enabled: bool):
        """تفعيل/تعطيل التوسيط التلقائي"""
        self.auto_center = enabled
    
    def closeEvent(self, event: QCloseEvent):
        """معالج إغلاق النافذة"""
        self.window_closing.emit()
        super().closeEvent(event)
    
    def resizeEvent(self, event):
        """معالج تغيير حجم النافذة"""
        super().resizeEvent(event)
        self.window_resized.emit(event.size())

class BaseDialog(QDialog):
    """النافذة الحوارية الأساسية الموحدة"""
    
    def __init__(self, parent=None, title="ProShipment", modal=True):
        super().__init__(parent)
        
        # خصائص النافذة
        self.dialog_title = title
        self.is_responsive_enabled = True
        self.auto_center = True
        
        # إعداد النافذة
        self.setup_dialog()
        self.setup_ui()
        self.apply_responsive_design()
        
        # تطبيق الثيم
        if MANAGERS_AVAILABLE:
            self.apply_theme()
        
        # تعيين النمط
        self.setModal(modal)
    
    def setup_dialog(self):
        """إعداد خصائص النافذة الحوارية"""
        self.setWindowTitle(self.dialog_title)
        
        # أيقونة النافذة
        icon_path = Path("assets/icons/app_icon.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        
        # خصائص النافذة
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # حجم افتراضي
        if MANAGERS_AVAILABLE:
            config = responsive_manager.get_config("window_default_size")
            if config:
                width = int(config[0] * 0.8)  # أصغر من النافذة الرئيسية
                height = int(config[1] * 0.8)
                self.resize(width, height)
        else:
            self.resize(800, 600)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        # التخطيط الرئيسي
        self.main_layout = QVBoxLayout(self)
        
        # منطقة المحتوى
        self.content_frame = QFrame()
        self.content_layout = QVBoxLayout(self.content_frame)
        self.main_layout.addWidget(self.content_frame)
        
        # منطقة الأزرار
        self.buttons_frame = QFrame()
        self.buttons_layout = QHBoxLayout(self.buttons_frame)
        self.buttons_layout.addStretch()
        self.main_layout.addWidget(self.buttons_frame)
        
        # أزرار افتراضية
        self.create_default_buttons()
    
    def create_default_buttons(self):
        """إنشاء الأزرار الافتراضية"""
        # زر موافق
        self.ok_button = QPushButton("موافق")
        self.ok_button.clicked.connect(self.accept)
        self.buttons_layout.addWidget(self.ok_button)
        
        # زر إلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.buttons_layout.addWidget(self.cancel_button)
        
        # تطبيق الأنماط
        if MANAGERS_AVAILABLE:
            theme_manager.apply_button_style(self.ok_button, "primary")
            theme_manager.apply_button_style(self.cancel_button, "secondary")
    
    def apply_responsive_design(self):
        """تطبيق التصميم المتجاوب"""
        if not MANAGERS_AVAILABLE or not self.is_responsive_enabled:
            return
        
        # تطبيق التصميم المتجاوب
        responsive_manager.apply_responsive_layout(self, WindowType.DIALOG)
        
        # توسيط النافذة إذا مطلوب
        if self.auto_center:
            responsive_manager.center_window(self)
    
    def apply_theme(self):
        """تطبيق الثيم"""
        if not MANAGERS_AVAILABLE:
            return
        
        # تطبيق stylesheet
        stylesheet = theme_manager.generate_stylesheet()
        if MANAGERS_AVAILABLE:
            responsive_stylesheet = responsive_manager.get_responsive_stylesheet()
            stylesheet += "\n" + responsive_stylesheet
        
        self.setStyleSheet(stylesheet)
    
    def add_content_widget(self, widget: QWidget):
        """إضافة widget إلى منطقة المحتوى"""
        self.content_layout.addWidget(widget)
    
    def add_button(self, text: str, callback=None, style_class="primary") -> QPushButton:
        """إضافة زر مخصص"""
        button = QPushButton(text)
        if callback:
            button.clicked.connect(callback)
        
        # إدراج قبل زر الإلغاء
        self.buttons_layout.insertWidget(self.buttons_layout.count() - 1, button)
        
        # تطبيق النمط
        if MANAGERS_AVAILABLE:
            theme_manager.apply_button_style(button, style_class)
        
        return button
    
    def set_responsive_enabled(self, enabled: bool):
        """تفعيل/تعطيل التصميم المتجاوب"""
        self.is_responsive_enabled = enabled
        if enabled:
            self.apply_responsive_design()

def create_message_box(parent=None, title="رسالة", message="", icon_type="information"):
    """إنشاء صندوق رسالة موحد"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    # تعيين نوع الأيقونة
    icon_map = {
        "information": QMessageBox.Information,
        "warning": QMessageBox.Warning,
        "critical": QMessageBox.Critical,
        "question": QMessageBox.Question
    }
    
    msg_box.setIcon(icon_map.get(icon_type, QMessageBox.Information))
    
    # تطبيق الثيم
    if MANAGERS_AVAILABLE:
        stylesheet = theme_manager.generate_stylesheet()
        msg_box.setStyleSheet(stylesheet)
    
    return msg_box
