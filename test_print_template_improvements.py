#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات نموذج طباعة طلب الحوالة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_margin_reduction():
    """اختبار تقليل الهوامش في النماذج"""
    print("📏 اختبار تقليل الهوامش...")
    
    try:
        # اختبار النموذج الأساسي
        print("   📋 فحص النموذج الأساسي:")
        with open("src/ui/remittances/remittance_print_template.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "setContentsMargins(15, 30, 15, 30)" in content:
            print("      ✅ تم تقليل الهوامش من 30 إلى 15")
        else:
            print("      ❌ لم يتم تقليل الهوامش")
            return False
        
        # اختبار النموذج المبسط
        print("   📋 فحص النموذج المبسط:")
        with open("src/ui/remittances/simple_print_template.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "setContentsMargins(15, 40, 15, 40)" in content:
            print("      ✅ تم تقليل الهوامش من 40 إلى 15")
        else:
            print("      ❌ لم يتم تقليل الهوامش")
            return False
        
        # اختبار النموذج الاحترافي
        print("   📋 فحص النموذج الاحترافي:")
        with open("src/ui/remittances/professional_print_template.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "setContentsMargins(15, 40, 15, 40)" in content:
            print("      ✅ تم تقليل الهوامش من 40 إلى 15")
        else:
            print("      ❌ لم يتم تقليل الهوامش")
            return False
        
        # اختبار مولد PDF
        print("   📋 فحص مولد PDF:")
        with open("src/ui/remittances/remittance_pdf_generator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "self.margin = 10 * mm" in content:
            print("      ✅ تم تقليل الهوامش من 20 إلى 10")
        else:
            print("      ❌ لم يتم تقليل الهوامش")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الهوامش: {e}")
        return False

def test_bank_country_field():
    """اختبار استخدام حقل بلد البنك"""
    print("\n🏦 اختبار حقل بلد البنك...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي"),
            ("src/ui/remittances/remittance_pdf_generator.py", "مولد PDF")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن استخدام حقل بلد البنك
            if "receiver_bank_country" in content:
                print(f"      ✅ يستخدم حقل بلد البنك")
                
                # التحقق من الكود الصحيح
                if "bank_country_field or receiver_country" in content or "bank_country = " in content:
                    print(f"      ✅ يستخدم منطق الاختيار الصحيح")
                else:
                    print(f"      ⚠️ قد يحتاج تحسين في منطق الاختيار")
            else:
                print(f"      ❌ لا يستخدم حقل بلد البنك")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حقل بلد البنك: {e}")
        return False

def test_signature_section_changes():
    """اختبار تغييرات قسم التوقيع"""
    print("\n✍️ اختبار تغييرات قسم التوقيع...")
    
    try:
        files_to_check = [
            ("src/ui/remittances/remittance_print_template.py", "النموذج الأساسي"),
            ("src/ui/remittances/simple_print_template.py", "النموذج المبسط"),
            ("src/ui/remittances/professional_print_template.py", "النموذج الاحترافي")
        ]
        
        for file_path, file_desc in files_to_check:
            print(f"   📋 فحص {file_desc}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من حذف الختم
            if "تم حذف الختم" in content or "مساحة للتوقيع ]" in content:
                print(f"      ✅ تم حذف قسم الختم")
            else:
                print(f"      ⚠️ قد لا يكون تم حذف الختم بالكامل")
            
            # التحقق من نقل التوقيع
            if "AlignCenter" in content or "نقل التوقيع" in content:
                print(f"      ✅ تم نقل التوقيع للوسط")
            else:
                print(f"      ⚠️ قد لا يكون تم نقل التوقيع")
        
        # فحص مولد PDF
        print("   📋 فحص مولد PDF:")
        with open("src/ui/remittances/remittance_pdf_generator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "تم حذف قسم الختم" in content:
            print("      ✅ تم حذف قسم الختم من PDF")
        else:
            print("      ❌ لم يتم حذف قسم الختم من PDF")
            return False
        
        if "center_x" in content:
            print("      ✅ تم نقل التوقيع للوسط في PDF")
        else:
            print("      ❌ لم يتم نقل التوقيع للوسط في PDF")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قسم التوقيع: {e}")
        return False

def test_template_functionality():
    """اختبار وظائف النماذج"""
    print("\n🧪 اختبار وظائف النماذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # بيانات تجريبية
        test_data = {
            'request_number': '2025-001',
            'request_date': '2024/12/09',
            'branch': 'صرافة الأمانة',
            'receiver_name': 'MOHAMMED AHMED ALI',
            'receiver_address': 'RIYADH, SAUDI ARABIA',
            'receiver_account': '**********',
            'receiver_bank_name': 'AL RAJHI BANK',
            'receiver_bank_branch': 'RIYADH BRANCH',
            'receiver_swift': 'RJHISARI',
            'receiver_country': 'SAUDI ARABIA',
            'receiver_bank_country': 'KINGDOM OF SAUDI ARABIA',  # حقل بلد البنك
            'transfer_purpose': 'COST OF FOODSTUFF'
        }
        
        # اختبار النموذج الأساسي
        print("   📋 اختبار النموذج الأساسي:")
        try:
            from src.ui.remittances.remittance_print_template import RemittancePrintTemplate
            template = RemittancePrintTemplate(test_data)
            print("      ✅ تم إنشاء النموذج الأساسي بنجاح")
            template.close()
        except Exception as e:
            print(f"      ❌ خطأ في النموذج الأساسي: {e}")
            return False
        
        # اختبار النموذج المبسط
        print("   📋 اختبار النموذج المبسط:")
        try:
            from src.ui.remittances.simple_print_template import SimplePrintTemplate
            template = SimplePrintTemplate(test_data)
            print("      ✅ تم إنشاء النموذج المبسط بنجاح")
            template.close()
        except Exception as e:
            print(f"      ❌ خطأ في النموذج المبسط: {e}")
            return False
        
        # اختبار النموذج الاحترافي
        print("   📋 اختبار النموذج الاحترافي:")
        try:
            from src.ui.remittances.professional_print_template import ProfessionalPrintTemplate
            template = ProfessionalPrintTemplate(test_data)
            print("      ✅ تم إنشاء النموذج الاحترافي بنجاح")
            template.close()
        except Exception as e:
            print(f"      ❌ خطأ في النموذج الاحترافي: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات نموذج طباعة طلب الحوالة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_margin_reduction())
    results.append(test_bank_country_field())
    results.append(test_signature_section_changes())
    results.append(test_template_functionality())
    
    # عرض النتائج النهائية
    print("\n" + "="*70)
    print("🎯 ملخص اختبار تحسينات نموذج الطباعة:")
    print("="*70)
    
    test_names = [
        "تقليل الهوامش يمين ويسار",
        "استخدام حقل بلد البنك",
        "حذف الختم ونقل التوقيع",
        "وظائف النماذج"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع التحسينات تعمل بنجاح!")
        print("✅ تم تقليل الهوامش في جميع النماذج")
        print("✅ يتم استخدام حقل بلد البنك بدلاً من مربعات")
        print("✅ تم حذف قسم الختم ونقل التوقيع")
        print("✅ جميع النماذج تعمل بشكل صحيح")
        
        print("\n🌟 التحسينات المطبقة:")
        print("   📏 هوامش مقللة لاستغلال أفضل للمساحة")
        print("   🏦 عرض صحيح لبلد البنك")
        print("   ✍️ تخطيط محسن لقسم التوقيع")
        print("   🖨️ نماذج طباعة محسنة ومحدثة")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم التحسينات تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة تحسينات فشلت. يرجى مراجعة:")
        print("- إعدادات الهوامش")
        print("- حقل بلد البنك")
        print("- قسم التوقيع والختم")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
