#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات نموذج طلب الحوالة
Test Form Improvements for Remittance Request
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_amount_field_changes():
    """اختبار تغييرات حقل المبلغ"""
    
    print("💰 اختبار تغييرات حقل مبلغ الحوالة...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص تغييرات حقل المبلغ
        amount_changes = [
            ("QLineEdit()", "تغيير نوع الحقل إلى نصي"),
            ("setPlaceholderText", "إضافة نص توضيحي"),
            ("text().strip()", "قراءة النص بدلاً من القيمة"),
            ("clear()", "مسح النص بدلاً من تعيين صفر"),
            ("setText(str(amount))", "تعيين النص بدلاً من القيمة"),
            ("float(amount_text.replace", "تحويل النص إلى رقم مع معالجة الفواصل"),
            ("ValueError", "معالجة خطأ التحويل الرقمي")
        ]
        
        print("   📋 فحص تغييرات حقل المبلغ:")
        all_found = True
        
        for change, description in amount_changes:
            if change in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        # فحص إزالة QDoubleSpinBox
        if "QDoubleSpinBox" not in code:
            print("      ✅ تم إزالة QDoubleSpinBox")
        else:
            print("      ❌ QDoubleSpinBox لا يزال موجود")
            all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص تغييرات حقل المبلغ: {e}")
        return False

def test_currency_integration():
    """اختبار ربط العملات بإعدادات النظام"""
    
    print("\n💱 اختبار ربط العملات بإعدادات النظام...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص ربط العملات
        currency_integration = [
            ("load_currencies_data", "دالة تحميل العملات"),
            ("create_currencies_table", "دالة إنشاء جدول العملات"),
            ("create_default_currencies", "دالة إنشاء عملات افتراضية"),
            ("load_default_currencies", "دالة تحميل عملات افتراضية"),
            ("SELECT code, name, symbol FROM currencies", "استعلام العملات من الجدول"),
            ("WHERE is_active = 1", "فلترة العملات النشطة"),
            ("exchange_rate REAL", "حقل سعر الصرف"),
            ("YER", "الريال اليمني"),
            ("SAR", "الريال السعودي"),
            ("USD", "الدولار الأمريكي")
        ]
        
        print("   📋 فحص ربط العملات:")
        all_found = True
        
        for integration, description in currency_integration:
            if integration in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص ربط العملات: {e}")
        return False

def test_exchanger_integration():
    """اختبار ربط الصرافين بإدارة البنوك"""
    
    print("\n🏦 اختبار ربط الصرافين بإدارة البنوك...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص ربط الصرافين
        exchanger_integration = [
            ("load_branches_and_exchangers_data", "دالة تحميل الفروع والصرافين"),
            ("create_bank_branches_table", "دالة إنشاء جدول الفروع"),
            ("create_exchangers_table", "دالة إنشاء جدول الصرافين"),
            ("create_default_branches", "دالة إنشاء فروع افتراضية"),
            ("create_default_exchangers", "دالة إنشاء صرافين افتراضيين"),
            ("SELECT e.id, e.name, e.phone, e.email, b.name as branch_name", "استعلام الصرافين مع الفروع"),
            ("FROM exchangers e", "جدول الصرافين"),
            ("LEFT JOIN bank_branches b", "ربط مع جدول الفروع"),
            ("WHERE e.is_active = 1", "فلترة الصرافين النشطين"),
            ("license_number TEXT", "حقل رقم الترخيص")
        ]
        
        print("   📋 فحص ربط الصرافين:")
        all_found = True
        
        for integration, description in exchanger_integration:
            if integration in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص ربط الصرافين: {e}")
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات الجديد"""
    
    print("\n🗄️ اختبار هيكل قاعدة البيانات الجديد...")
    print("=" * 60)
    
    try:
        # قراءة ملف النافذة الرئيسية
        window_path = "src/ui/remittances/remittance_request_window.py"
        with open(window_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # فحص هيكل قاعدة البيانات
        database_structure = [
            ("CREATE TABLE IF NOT EXISTS currencies", "جدول العملات"),
            ("CREATE TABLE IF NOT EXISTS bank_branches", "جدول فروع البنوك"),
            ("CREATE TABLE IF NOT EXISTS exchangers", "جدول الصرافين"),
            ("FOREIGN KEY (branch_id) REFERENCES bank_branches", "مفتاح خارجي للفرع"),
            ("is_active INTEGER DEFAULT 1", "حقل الحالة النشطة"),
            ("created_at TEXT DEFAULT CURRENT_TIMESTAMP", "حقل تاريخ الإنشاء"),
            ("updated_at TEXT DEFAULT CURRENT_TIMESTAMP", "حقل تاريخ التحديث"),
            ("exchange_rate REAL DEFAULT 1.0", "حقل سعر الصرف"),
            ("manager_name TEXT", "حقل اسم المدير"),
            ("license_number TEXT", "حقل رقم الترخيص")
        ]
        
        print("   📋 فحص هيكل قاعدة البيانات:")
        all_found = True
        
        for structure, description in database_structure:
            if structure in code:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} - مفقود")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص هيكل قاعدة البيانات: {e}")
        return False

def display_improvements_summary():
    """عرض ملخص التحسينات"""
    
    print("\n" + "=" * 80)
    print("🏆 ملخص تحسينات نموذج طلب الحوالة")
    print("=" * 80)
    
    print("\n🎯 التحسينات المطبقة:")
    
    print("\n   💰 حقل مبلغ الحوالة:")
    print("      • تغيير من QDoubleSpinBox إلى QLineEdit")
    print("      • إضافة نص توضيحي للمستخدم")
    print("      • تحسين التصميم مع CSS")
    print("      • معالجة شاملة للأخطاء")
    print("      • دعم الفواصل في الأرقام")
    print("      • التحقق من صحة البيانات")
    
    print("\n   💱 ربط العملات:")
    print("      • ربط مع جدول العملات في إعدادات النظام")
    print("      • إنشاء جدول العملات تلقائياً")
    print("      • عملات افتراضية شاملة")
    print("      • دعم أسعار الصرف")
    print("      • رموز العملات")
    print("      • فلترة العملات النشطة")
    
    print("\n   🏦 ربط الصرافين:")
    print("      • ربط مع جدول الصرافين في إدارة البنوك")
    print("      • ربط مع جدول فروع البنوك")
    print("      • إنشاء الجداول تلقائياً")
    print("      • بيانات افتراضية شاملة")
    print("      • معلومات تفصيلية للصرافين")
    print("      • ربط الصرافين بالفروع")
    
    print("\n   🗄️ هيكل قاعدة البيانات:")
    print("      • جدول العملات مع أسعار الصرف")
    print("      • جدول فروع البنوك مع التفاصيل")
    print("      • جدول الصرافين مع الترخيص")
    print("      • مفاتيح خارجية للربط")
    print("      • حقول الحالة والتواريخ")
    print("      • بيانات افتراضية غنية")
    
    print("\n✨ الميزات الجديدة:")
    print("   🔧 إنشاء تلقائي للجداول")
    print("   📊 بيانات افتراضية شاملة")
    print("   🛡️ معالجة شاملة للأخطاء")
    print("   🔗 ربط متقدم بين الجداول")
    print("   📝 تسجيل مفصل للعمليات")
    print("   🎨 تحسينات في التصميم")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. فتح نافذة طلب الحوالة")
    print("   2. ستتم إنشاء الجداول تلقائياً")
    print("   3. تحميل البيانات من إعدادات النظام")
    print("   4. اختيار العملة من القائمة المحدثة")
    print("   5. اختيار الصراف من إدارة البنوك")
    print("   6. إدخال المبلغ كنص مرن")

def run_improvements_test():
    """تشغيل اختبار شامل للتحسينات"""
    
    print("🚀 بدء اختبار تحسينات نموذج طلب الحوالة...")
    print("=" * 80)
    
    # اختبار تغييرات حقل المبلغ
    amount_ok = test_amount_field_changes()
    
    # اختبار ربط العملات
    currency_ok = test_currency_integration()
    
    # اختبار ربط الصرافين
    exchanger_ok = test_exchanger_integration()
    
    # اختبار هيكل قاعدة البيانات
    database_ok = test_database_structure()
    
    # عرض ملخص التحسينات
    display_improvements_summary()
    
    # النتيجة النهائية
    if amount_ok and currency_ok and exchanger_ok and database_ok:
        print("\n🏆 تم تطبيق جميع التحسينات بنجاح!")
        print("✅ حقل المبلغ محول إلى نصي")
        print("✅ العملات مربوطة بإعدادات النظام")
        print("✅ الصرافين مربوطين بإدارة البنوك")
        print("✅ هيكل قاعدة البيانات محسن")
        
        print("\n🎉 نموذج طلب الحوالة محسن ومطور!")
        print("💡 جرب الآن:")
        print("   • فتح نافذة طلب الحوالة")
        print("   • اختبار حقل المبلغ النصي")
        print("   • اختيار العملات من القائمة")
        print("   • اختيار الصرافين من القائمة")
        
        return True
        
    else:
        print("\n❌ لا تزال هناك مشاكل في التحسينات")
        if not amount_ok:
            print("   - مشكلة في تغييرات حقل المبلغ")
        if not currency_ok:
            print("   - مشكلة في ربط العملات")
        if not exchanger_ok:
            print("   - مشكلة في ربط الصرافين")
        if not database_ok:
            print("   - مشكلة في هيكل قاعدة البيانات")
        
        return False

if __name__ == "__main__":
    success = run_improvements_test()
    print("=" * 80)
    sys.exit(0 if success else 1)
