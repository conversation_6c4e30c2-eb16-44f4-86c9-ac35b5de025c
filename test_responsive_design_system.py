#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التصميم المتجاوب - ProShipment
Test Responsive Design System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_managers_import():
    """اختبار استيراد مديري التصميم"""
    print("🧪 اختبار استيراد مديري التصميم...")
    
    try:
        from src.ui.responsive.responsive_manager import responsive_manager, ScreenSize, WindowType
        print("   ✅ تم استيراد مدير التصميم المتجاوب")
        
        from src.ui.themes.theme_manager import theme_manager, ThemeType, ColorScheme
        print("   ✅ تم استيراد مدير الثيمات")
        
        from src.ui.base.base_window import BaseWindow, BaseDialog
        print("   ✅ تم استيراد النوافذ الأساسية")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False

def test_responsive_manager():
    """اختبار مدير التصميم المتجاوب"""
    print("\n🧪 اختبار مدير التصميم المتجاوب...")
    
    try:
        from src.ui.responsive.responsive_manager import responsive_manager
        
        # اختبار تحديد حجم الشاشة
        small_size = responsive_manager.detect_screen_size(1024, 768)
        medium_size = responsive_manager.detect_screen_size(1366, 768)
        large_size = responsive_manager.detect_screen_size(1920, 1080)
        xlarge_size = responsive_manager.detect_screen_size(2560, 1440)
        
        print(f"   📱 1024x768 → {small_size.value}")
        print(f"   📱 1366x768 → {medium_size.value}")
        print(f"   📱 1920x1080 → {large_size.value}")
        print(f"   📱 2560x1440 → {xlarge_size.value}")
        
        # اختبار الحصول على الإعدادات
        responsive_manager.current_screen_size = medium_size
        font_size = responsive_manager.get_font_size("base")
        button_height = responsive_manager.get_config("button_height")
        
        print(f"   🔤 حجم الخط الأساسي: {font_size}")
        print(f"   🔘 ارتفاع الأزرار: {button_height}")
        
        # اختبار معلومات الشاشة
        screen_info = responsive_manager.get_screen_info()
        print(f"   📊 معلومات الشاشة: {screen_info}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مدير التصميم المتجاوب: {e}")
        return False

def test_theme_manager():
    """اختبار مدير الثيمات"""
    print("\n🧪 اختبار مدير الثيمات...")
    
    try:
        from src.ui.themes.theme_manager import theme_manager, ThemeType, ColorScheme
        
        # اختبار الثيمات المتاحة
        themes = theme_manager.get_available_themes()
        print(f"   🎨 الثيمات المتاحة: {list(themes.keys())}")
        
        # اختبار مخططات الألوان
        color_schemes = theme_manager.get_available_color_schemes()
        print(f"   🌈 مخططات الألوان: {list(color_schemes.keys())}")
        
        # اختبار الحصول على الألوان
        primary_color = theme_manager.get_color("primary")
        success_color = theme_manager.get_color("success")
        
        print(f"   🔵 اللون الأساسي: {primary_color}")
        print(f"   🟢 لون النجاح: {success_color}")
        
        # اختبار تغيير الثيم
        original_theme = theme_manager.current_theme
        theme_manager.set_color_scheme(ColorScheme.GREEN)
        new_primary = theme_manager.get_color("primary")
        print(f"   🔄 اللون الأساسي بعد التغيير: {new_primary}")
        
        # إعادة الثيم الأصلي
        theme_manager.current_color_scheme = ColorScheme.BLUE
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مدير الثيمات: {e}")
        return False

def test_base_windows():
    """اختبار النوافذ الأساسية"""
    print("\n🧪 اختبار النوافذ الأساسية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.base.base_window import BaseWindow, BaseDialog
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار النافذة الأساسية
        main_window = BaseWindow(window_title="اختبار النافذة الرئيسية")
        print("   ✅ تم إنشاء النافذة الأساسية")
        
        # اختبار النافذة الحوارية
        dialog = BaseDialog(title="اختبار النافذة الحوارية")
        print("   ✅ تم إنشاء النافذة الحوارية")
        
        # اختبار الخصائص
        print(f"   📏 حجم النافذة الرئيسية: {main_window.size().width()}x{main_window.size().height()}")
        print(f"   📏 حجم النافذة الحوارية: {dialog.size().width()}x{dialog.size().height()}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النوافذ الأساسية: {e}")
        return False

def test_design_settings_dialog():
    """اختبار نافذة إعدادات التصميم"""
    print("\n🧪 اختبار نافذة إعدادات التصميم...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.dialogs.design_settings_dialog import DesignSettingsDialog
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة الإعدادات
        settings_dialog = DesignSettingsDialog()
        print("   ✅ تم إنشاء نافذة إعدادات التصميم")
        
        # اختبار التبويبات
        tab_count = settings_dialog.tab_widget.count()
        print(f"   📋 عدد التبويبات: {tab_count}")
        
        for i in range(tab_count):
            tab_text = settings_dialog.tab_widget.tabText(i)
            print(f"      {i+1}. {tab_text}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نافذة إعدادات التصميم: {e}")
        return False

def test_stylesheet_generation():
    """اختبار إنشاء stylesheet"""
    print("\n🧪 اختبار إنشاء stylesheet...")
    
    try:
        from src.ui.themes.theme_manager import theme_manager
        from src.ui.responsive.responsive_manager import responsive_manager
        
        # إنشاء stylesheet للثيم
        theme_stylesheet = theme_manager.generate_stylesheet()
        print(f"   🎨 طول stylesheet الثيم: {len(theme_stylesheet)} حرف")
        
        # إنشاء stylesheet للتصميم المتجاوب
        responsive_stylesheet = responsive_manager.get_responsive_stylesheet()
        print(f"   📱 طول stylesheet المتجاوب: {len(responsive_stylesheet)} حرف")
        
        # دمج الـ stylesheets
        combined_stylesheet = theme_stylesheet + "\n" + responsive_stylesheet
        print(f"   🔗 طول stylesheet المدمج: {len(combined_stylesheet)} حرف")
        
        # التحقق من وجود عناصر مهمة
        important_elements = ["QPushButton", "QLineEdit", "QTableWidget", "QGroupBox"]
        for element in important_elements:
            if element in combined_stylesheet:
                print(f"   ✅ {element} موجود في stylesheet")
            else:
                print(f"   ❌ {element} غير موجود في stylesheet")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إنشاء stylesheet: {e}")
        return False

def test_libraries_availability():
    """اختبار توفر المكتبات المطلوبة"""
    print("\n🧪 اختبار توفر المكتبات...")
    
    libraries = {
        "qdarkstyle": "مكتبة الثيم المظلم",
        "qtawesome": "مكتبة الأيقونات",
        "qtstylish": "مكتبة التنسيق المتقدم"
    }
    
    available_count = 0
    
    for lib_name, description in libraries.items():
        try:
            __import__(lib_name)
            print(f"   ✅ {description} ({lib_name}) متاحة")
            available_count += 1
        except ImportError:
            print(f"   ❌ {description} ({lib_name}) غير متاحة")
    
    print(f"   📊 المكتبات المتاحة: {available_count}/{len(libraries)}")
    
    return available_count == len(libraries)

def test_integration():
    """اختبار التكامل الشامل"""
    print("\n🧪 اختبار التكامل الشامل...")
    
    try:
        from PySide6.QtWidgets import QApplication, QLabel, QPushButton, QVBoxLayout, QWidget
        from src.ui.base.base_window import BaseWindow
        from src.ui.responsive.responsive_manager import responsive_manager
        from src.ui.themes.theme_manager import theme_manager
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة اختبار
        test_window = BaseWindow(window_title="اختبار التكامل")
        
        # إضافة عناصر للاختبار
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # تسمية
        label = QLabel("هذا نص تجريبي لاختبار التصميم المتجاوب")
        layout.addWidget(label)
        
        # أزرار بألوان مختلفة
        buttons = [
            ("زر أساسي", "primary"),
            ("زر نجاح", "success"),
            ("زر تحذير", "warning"),
            ("زر خطر", "danger")
        ]
        
        for text, style in buttons:
            button = QPushButton(text)
            theme_manager.apply_button_style(button, style)
            layout.addWidget(button)
        
        test_window.main_layout.addWidget(content_widget)
        
        # تطبيق التصميم المتجاوب
        responsive_manager.apply_responsive_layout(test_window)
        
        print("   ✅ تم إنشاء نافذة اختبار التكامل")
        print("   ✅ تم تطبيق التصميم المتجاوب")
        print("   ✅ تم تطبيق الثيم")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام التصميم المتجاوب والثيمات")
    print("="*60)
    
    tests = [
        ("استيراد المديرين", test_managers_import),
        ("مدير التصميم المتجاوب", test_responsive_manager),
        ("مدير الثيمات", test_theme_manager),
        ("النوافذ الأساسية", test_base_windows),
        ("نافذة إعدادات التصميم", test_design_settings_dialog),
        ("إنشاء stylesheet", test_stylesheet_generation),
        ("توفر المكتبات", test_libraries_availability),
        ("التكامل الشامل", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج اختبار نظام التصميم المتجاوب")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.9:
        print("\n🎉 نظام التصميم المتجاوب يعمل بشكل ممتاز!")
        print("✅ جاهز للاستخدام")
        status = "ممتاز"
    elif success_rate >= 0.7:
        print("\n✅ نظام التصميم المتجاوب يعمل بشكل جيد")
        print("⚠️ بعض الميزات قد تحتاج مراجعة")
        status = "جيد"
    else:
        print("\n⚠️ نظام التصميم المتجاوب يحتاج إصلاحات")
        print("🔧 راجع الأخطاء أعلاه")
        status = "يحتاج إصلاح"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    # معلومات إضافية
    print(f"\n📋 ملخص النظام الجديد:")
    print(f"   📱 مدير التصميم المتجاوب")
    print(f"   🎨 مدير الثيمات والألوان")
    print(f"   🏗️ نوافذ أساسية موحدة")
    print(f"   ⚙️ نافذة إعدادات التصميم")
    print(f"   📐 تكيف تلقائي مع أحجام الشاشات")
    print(f"   🌈 دعم ثيمات وألوان متعددة")
    
    return success_rate >= 0.7

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
