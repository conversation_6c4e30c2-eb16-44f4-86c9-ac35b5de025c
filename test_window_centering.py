#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_window_centering():
    """اختبار توسيط النوافذ"""
    print("🔄 اختبار توسيط النوافذ...")
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication, QMainWindow
        from src.ui.main_window import MainWindow
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        print("✅ تم إنشاء MainWindow بنجاح")
        
        # اختبار دالة التوسيط
        test_window = QMainWindow()
        test_window.setWindowTitle("نافذة اختبار")
        test_window.resize(400, 300)
        
        main_window.center_window(test_window)
        print("✅ تم اختبار دالة center_window")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("📋 التحسينات المطبقة:")
        print("   • توسيط النوافذ وسط الشاشة")
        print("   • إحضار النوافذ للمقدمة")
        print("   • تفعيل النوافذ")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_window_centering()
    
    if success:
        print("\n✅ النظام جاهز! النوافذ ستظهر في المقدمة ومتوسطة.")
        print("\n📝 النوافذ المحسنة:")
        print("   1. قائمة الحوالات")
        print("   2. إدارة البنوك")
        print("   3. إدارة حسابات الموردين")
        print("   4. إنشاء حوالة جديدة")
        print("   5. إعدادات قاعدة البيانات")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
