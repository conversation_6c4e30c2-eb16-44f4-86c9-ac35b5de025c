#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_windows():
    """اختبار جميع النوافذ الجديدة"""
    print("🚀 اختبار جميع النوافذ الجديدة...")
    print("=" * 50)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        print()
        
        # اختبار نافذة المعاملة الجديدة
        print("1️⃣ اختبار نافذة المعاملة الجديدة...")
        try:
            from src.ui.remittances.new_transaction_dialog import NewTransactionDialog
            dialog1 = NewTransactionDialog()
            print("   ✅ NewTransactionDialog - تم الإنشاء بنجاح")
        except Exception as e:
            print(f"   ❌ NewTransactionDialog - خطأ: {e}")
            
        # اختبار نافذة التحويل الجماعي
        print("\n2️⃣ اختبار نافذة التحويل الجماعي...")
        try:
            from src.ui.remittances.bulk_transfer_dialog import BulkTransferDialog
            dialog2 = BulkTransferDialog()
            print("   ✅ BulkTransferDialog - تم الإنشاء بنجاح")
            
            # اختبار بعض الوظائف
            dialog2.add_transfer_row()
            dialog2.update_statistics()
            print("   ✅ BulkTransferDialog - تم اختبار الوظائف الأساسية")
            
        except Exception as e:
            print(f"   ❌ BulkTransferDialog - خطأ: {e}")
            
        # اختبار نافذة تسوية الحسابات
        print("\n3️⃣ اختبار نافذة تسوية الحسابات...")
        try:
            from src.ui.remittances.account_reconciliation_dialog import AccountReconciliationDialog
            dialog3 = AccountReconciliationDialog()
            print("   ✅ AccountReconciliationDialog - تم الإنشاء بنجاح")
        except Exception as e:
            print(f"   ❌ AccountReconciliationDialog - خطأ: {e}")
            
        print("\n" + "=" * 50)
        print("🎉 تم الانتهاء من اختبار جميع النوافذ!")
        print("✅ جميع النوافذ جاهزة للاستخدام في التطبيق الرئيسي.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📋 تعليمات الاستخدام:")
    print("=" * 30)
    print("1. تشغيل التطبيق الرئيسي:")
    print("   python main.py")
    print()
    print("2. الوصول للنوافذ الجديدة:")
    print("   - اذهب إلى: إدارة الحوالات ← حسابات الموردين")
    print("   - في تبويب المعاملات:")
    print("     • 💰 معاملة جديدة")
    print("     • 🔄 تحويل جماعي") 
    print("     • ⚖️ تسوية الحسابات")
    print()
    print("3. الميزات المتاحة:")
    print("   - واجهات احترافية مع تبويبات متعددة")
    print("   - تكامل كامل مع قاعدة البيانات")
    print("   - التحقق من صحة البيانات")
    print("   - معالجة الأخطاء المتقدمة")
    print("   - تقارير وتصدير البيانات")

if __name__ == "__main__":
    success = test_all_windows()
    
    if success:
        show_usage_instructions()
        print("\n🚀 النظام جاهز للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
