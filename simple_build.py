#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة بناء مبسطة لملف التثبيت التنفيذي
Simple Build Tool for Executable
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def create_simple_spec():
    """إنشاء ملف spec مبسط"""
    print("📝 إنشاء ملف spec مبسط...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('data', 'data'),
        ('config', 'config'),
        ('assets', 'assets'),
        ('docs', 'docs'),
        ('LOGO_FOGEHI.png', '.'),
        ('LOGO_FOGEHI.jpg', '.'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtGui', 
        'PySide6.QtWidgets',
        'PySide6.QtPrintSupport',
        'SQLAlchemy',
        'sqlite3',
        'reportlab',
        'reportlab.pdfgen',
        'reportlab.lib',
        'reportlab.platypus',
        'arabic_reshaper',
        'bidi',
        'openpyxl',
        'requests',
        'beautifulsoup4',
        'PIL',
        'num2words',
        'qdarkstyle',
        'qtawesome',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ProShipment',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ProShipment',
)
'''
    
    spec_file = Path("ProShipment_simple.spec")
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"   ✅ تم إنشاء {spec_file}")
    return spec_file

def run_pyinstaller():
    """تشغيل PyInstaller"""
    print("🔨 تشغيل PyInstaller...")
    
    try:
        # إنشاء ملف spec
        spec_file = create_simple_spec()
        
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            str(spec_file)
        ]
        
        print(f"   🔄 تشغيل: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ تم بناء الملف التنفيذي بنجاح")
            print("   📁 الملفات متاحة في مجلد dist/ProShipment/")
            return True
        else:
            print(f"   ❌ فشل في البناء:")
            print(f"   خطأ: {result.stderr}")
            print(f"   مخرجات: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في البناء: {e}")
        return False

def create_simple_package():
    """إنشاء حزمة بسيطة"""
    print("📦 إنشاء حزمة بسيطة...")
    
    try:
        dist_dir = Path("dist/ProShipment")
        if not dist_dir.exists():
            print("   ❌ مجلد التطبيق غير موجود")
            return False
        
        # إنشاء مجلد التوزيع
        package_dir = Path("ProShipment_Package")
        if package_dir.exists():
            shutil.rmtree(package_dir)
        
        # نسخ التطبيق
        shutil.copytree(dist_dir, package_dir)
        
        # إضافة ملفات إضافية
        additional_files = [
            ("README.md", "دليل_التشغيل.md"),
            ("CHANGELOG.md", "سجل_التغييرات.md"),
        ]
        
        for src, dst in additional_files:
            src_file = Path(src)
            if src_file.exists():
                shutil.copy2(src_file, package_dir / dst)
        
        # إنشاء ملف تشغيل
        run_script = package_dir / "تشغيل_التطبيق.bat"
        with open(run_script, 'w', encoding='utf-8') as f:
            f.write('''@echo off
chcp 65001 > nul
title ProShipment V2.0.0
echo.
echo ===================================
echo   ProShipment V2.0.0
echo   نظام إدارة الشحنات والحوالات
echo ===================================
echo.
echo جاري تشغيل التطبيق...
start "" "ProShipment.exe"
''')
        
        # ضغط الحزمة
        from datetime import datetime
        archive_name = f"ProShipment-V2.0.0-{datetime.now().strftime('%Y-%m-%d')}"
        
        shutil.make_archive(archive_name, 'zip', '.', 'ProShipment_Package')
        
        print(f"   ✅ تم إنشاء: {archive_name}.zip")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء الحزمة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة البناء المبسطة - ProShipment")
    print("="*50)
    
    # تنظيف الملفات السابقة
    print("🧹 تنظيف الملفات السابقة...")
    for dir_name in ["build", "dist"]:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"   🗑️ تم حذف {dir_name}")
    
    # بناء الملف التنفيذي
    if not run_pyinstaller():
        print("❌ فشل في بناء الملف التنفيذي")
        return False
    
    # إنشاء الحزمة
    if not create_simple_package():
        print("❌ فشل في إنشاء الحزمة")
        return False
    
    print("\n🎉 تم إنشاء ملف التثبيت بنجاح!")
    print("📦 الحزمة جاهزة للتوزيع")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
