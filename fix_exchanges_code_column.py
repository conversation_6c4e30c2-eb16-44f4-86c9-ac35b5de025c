#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح عمود code في جدول الصرافات
Fix code column in exchanges table
"""

import sqlite3
import os
from pathlib import Path

def fix_exchanges_code_column():
    """إصلاح عمود code في جدول الصرافات"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إصلاح عمود code في جدول الصرافات...")
        
        # 1. التحقق من وجود عمود code
        cursor.execute("PRAGMA table_info(exchanges)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'code' not in column_names:
            print("📋 إضافة عمود code لجدول الصرافات...")
            try:
                cursor.execute("ALTER TABLE exchanges ADD COLUMN code TEXT")
                print("✅ تم إضافة عمود code لجدول الصرافات")
            except sqlite3.OperationalError as e:
                print(f"⚠️ خطأ في إضافة عمود code: {e}")
        else:
            print("ℹ️ عمود code موجود بالفعل في جدول الصرافات")
        
        # 2. إضافة بيانات تجريبية للصرافات بدون عمود code في البداية
        cursor.execute("SELECT COUNT(*) FROM exchanges")
        exchanges_count = cursor.fetchone()[0]
        
        if exchanges_count == 0:
            print("📋 إضافة صرافات تجريبية...")
            sample_exchanges = [
                ('الراجحي للصرافة', 'Al Rajhi Exchange', 'صرافة', 'SR-EX-001', '011-2116000', '0551234567', 
                 '<EMAIL>', 'الرياض، شارع العليا', 'www.alrajhi-exchange.com', 15.0, 0.5, 'صرافة رائدة في المملكة', 1),
                ('الأهلي للصرافة', 'Al Ahli Exchange', 'صرافة', 'SR-EX-002', '011-4021000', '0551234568', 
                 '<EMAIL>', 'الرياض، شارع الملك عبدالعزيز', 'www.alahli-exchange.com', 12.0, 0.4, 'خدمات صرافة متميزة', 1),
                ('صرافة الإمارات', 'Emirates Exchange', 'صرافة', 'SR-EX-003', '011-5551234', '0551234569', 
                 '<EMAIL>', 'الرياض، حي الملز', 'www.emirates-exchange.com', 10.0, 0.3, 'صرافة دولية', 1),
                ('الخليج للصرافة', 'Gulf Exchange', 'صرافة', 'SR-EX-004', '011-5551235', '**********', 
                 '<EMAIL>', 'جدة، شارع التحلية', 'www.gulf-exchange.com', 18.0, 0.6, 'خدمات سريعة وموثوقة', 1)
            ]
            
            for i, exchange_data in enumerate(sample_exchanges):
                cursor.execute("""
                    INSERT INTO exchanges (name, name_en, category, license_number, phone, mobile, email, address, website, transfer_fee, commission_rate, notes, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, exchange_data)
                
                # إضافة كود للصرافة
                exchange_id = cursor.lastrowid
                code = f"EX{exchange_id:03d}"
                cursor.execute("UPDATE exchanges SET code = ? WHERE id = ?", (code, exchange_id))
            
            print(f"✅ تم إضافة {len(sample_exchanges)} صرافة تجريبية")
        else:
            print(f"ℹ️ يوجد {exchanges_count} صرافة في النظام")
            
            # تحديث الأكواد للصرافات الموجودة إذا لم تكن لديها أكواد
            cursor.execute("SELECT id, name FROM exchanges WHERE code IS NULL OR code = ''")
            exchanges_without_codes = cursor.fetchall()
            
            if exchanges_without_codes:
                print(f"📋 تحديث أكواد {len(exchanges_without_codes)} صرافة...")
                for exchange_id, name in exchanges_without_codes:
                    code = f"EX{exchange_id:03d}"
                    cursor.execute("UPDATE exchanges SET code = ? WHERE id = ?", (code, exchange_id))
                print("✅ تم تحديث أكواد الصرافات")
        
        # 3. التحقق النهائي من الجدول
        print("\n🔍 التحقق النهائي من جدول الصرافات...")
        cursor.execute("PRAGMA table_info(exchanges)")
        columns = cursor.fetchall()
        print(f"📊 جدول الصرافات يحتوي على {len(columns)} عمود:")
        
        required_columns = ['id', 'name', 'name_en', 'code', 'category', 'license_number', 
                           'phone', 'mobile', 'email', 'address', 'website', 'transfer_fee', 
                           'commission_rate', 'notes', 'is_active']
        
        column_names = [col[1] for col in columns]
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"⚠️ أعمدة مفقودة: {missing_columns}")
        else:
            print("✅ جدول الصرافات يحتوي على جميع الأعمدة المطلوبة")
        
        # عرض عينة من البيانات
        cursor.execute("SELECT id, name, name_en, code FROM exchanges LIMIT 3")
        sample_data = cursor.fetchall()
        if sample_data:
            print("\n📋 عينة من بيانات الصرافات:")
            for row in sample_data:
                print(f"   ID: {row[0]}, الاسم: {row[1]}, الاسم الإنجليزي: {row[2]}, الكود: {row[3]}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح جدول الصرافات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول الصرافات: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح عمود code في جدول الصرافات...")
    print("=" * 60)
    
    success = fix_exchanges_code_column()
    
    if success:
        print("\n✅ تم إنجاز الإصلاح بنجاح!")
        print("📊 الآن يمكنك:")
        print("   • إضافة صرافات جديدة بدون أخطاء")
        print("   • استخدام جميع الحقول المطلوبة")
        print("   • عرض أكواد الصرافات بشكل صحيح")
    else:
        print("\n❌ فشل في إنجاز الإصلاح!")
    
    print("=" * 60)
