#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def final_test():
    """اختبار نهائي للنافذة"""
    print("🔍 اختبار نهائي لنافذة إنشاء الحوالة...")
    print("=" * 50)
    
    try:
        # استيراد المكتبات
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # استيراد النافذة
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        print("✅ تم استيراد NewRemittanceDialog")
        
        # إنشاء النافذة
        dialog = NewRemittanceDialog()
        print("✅ تم إنشاء النافذة بنجاح!")
        
        # اختبار الدوال الأساسية
        print("\n🔄 اختبار الدوال الأساسية...")
        
        # اختبار setup_validators
        try:
            dialog.setup_validators()
            print("   ✅ setup_validators")
        except Exception as e:
            print(f"   ❌ setup_validators: {e}")
            return False
        
        # اختبار validate_form
        try:
            result = dialog.validate_form()
            print(f"   ✅ validate_form (النتيجة: {result})")
        except Exception as e:
            print(f"   ❌ validate_form: {e}")
            return False
        
        # اختبار collect_form_data
        try:
            data = dialog.collect_form_data()
            print("   ✅ collect_form_data")
        except Exception as e:
            print(f"   ❌ collect_form_data: {e}")
            return False
        
        # اختبار update_transfer_entity_name
        try:
            dialog.update_transfer_entity_name()
            print("   ✅ update_transfer_entity_name")
        except Exception as e:
            print(f"   ❌ update_transfer_entity_name: {e}")
            return False
        
        # اختبار update_calculations
        try:
            dialog.update_calculations()
            print("   ✅ update_calculations")
        except Exception as e:
            print(f"   ❌ update_calculations: {e}")
            return False
        
        # اختبار الحقول الأساسية
        print("\n🔄 فحص الحقول الأساسية...")
        
        essential_fields = [
            'remittance_date',
            'remittance_number_input',
            'amount_input',
            'currency_combo',
            'suppliers_table'
        ]
        
        for field in essential_fields:
            if hasattr(dialog, field):
                print(f"   ✅ {field}")
            else:
                print(f"   ❌ {field} مفقود")
                return False
        
        # اختبار الأزرار الأساسية
        print("\n🔄 فحص الأزرار الأساسية...")
        
        essential_buttons = [
            'save_draft_btn',
            'confirm_btn',
            'transfer_to_suppliers_btn'
        ]
        
        for button in essential_buttons:
            if hasattr(dialog, button):
                print(f"   ✅ {button}")
            else:
                print(f"   ❌ {button} مفقود")
                return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النافذة جاهزة للاستخدام بشكل كامل.")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = final_test()
    if success:
        print("\n🚀 النافذة تعمل بشكل مثالي!")
        print("يمكنك الآن فتح التطبيق واستخدام النافذة.")
    else:
        print("\n❌ يوجد مشاكل تحتاج إصلاح.")
