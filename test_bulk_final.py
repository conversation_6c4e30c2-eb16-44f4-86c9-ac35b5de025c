#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_bulk_transfer_dialog():
    """اختبار نافذة التحويل الجماعي"""
    print("🔄 اختبار نافذة التحويل الجماعي...")
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.bulk_transfer_dialog import BulkTransferDialog
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # إنشاء النافذة
        dialog = BulkTransferDialog()
        print("✅ تم إنشاء BulkTransferDialog بنجاح")
        
        # اختبار الوظائف الأساسية
        dialog.add_transfer_row()
        print("✅ تم اختبار add_transfer_row")
        
        dialog.update_statistics()
        print("✅ تم اختبار update_statistics")
        
        print("\n🎉 جميع الاختبارات نجحت! النافذة جاهزة للاستخدام.")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bulk_transfer_dialog()
    if success:
        print("\n✅ النافذة جاهزة للاستخدام في التطبيق الرئيسي!")
    else:
        print("\n❌ يوجد مشاكل تحتاج إلى إصلاح.")
