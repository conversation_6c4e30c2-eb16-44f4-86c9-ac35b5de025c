#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مولد PDF مع الصور
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pdf_with_header_image():
    """اختبار مولد PDF مع صورة الرأس"""
    print("🖼️ اختبار مولد PDF مع صورة الرأس...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        # بيانات اختبار شاملة
        test_data = {
            'request_number': '2025-01',
            'request_date': '2024/01/03',
            'remittance_amount': '63,500',
            'currency': 'USD',
            'transfer_purpose': 'COST OF FOODSTUFF',
            'exchanger': 'شركة الحجري للصرافة والتحويلات المحدودة',
            
            # بيانات المستفيد
            'receiver_name': 'CHINA INTERNATIONAL TRADING COMPANY LIMITED',
            'receiver_address': 'NO. 123 MAIN STREET, BUSINESS DISTRICT, CHAOYANG',
            'receiver_city': 'BEIJING',
            'receiver_phone': '+86 10 ********',
            'receiver_account': '********90********9',
            'receiver_country': 'CHINA',
            
            # بيانات البنك
            'receiver_bank': 'BANK OF CHINA LIMITED',
            'receiver_bank_branch': 'BEIJING MAIN BRANCH',
            'receiver_bank_address': 'NO. 1 FUXINGMEN NEI DAJIE, XICHENG DISTRICT, BEIJING',
            'receiver_swift': 'BKCHCNBJ110',
            
            # بيانات المرسل
            'sender_name': 'ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD',
            'sender_address': 'TAIZ STREET, ORPHAN BUILDING, NEAR ALBRKA STORES – SANA\'A, YEMEN',
            'sender_entity': 'G.M: - NASHA\'AT RASHAD QASIM ALDUBAEE',
            'sender_phone': '+967 1 616109',
            'sender_fax': '+967 1 615909',
            'sender_mobile': '+967 *********',
            'sender_email': '<EMAIL>, <EMAIL>',
            'sender_box': '1903',
            'manager_name': 'نشأت رشاد قاسم الدبعي'
        }
        
        # إنشاء مولد PDF
        pdf_generator = RemittancePDFGenerator()
        
        # إنشاء ملف PDF مع صورة الرأس
        output_path = "نموذج_مع_صورة_الرأس.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        print(f"✅ تم إنشاء النموذج مع صورة الرأس: {result_path}")
        
        # التحقق من وجود الملف
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"📄 حجم الملف: {file_size} بايت")
            print(f"📁 مسار الملف: {Path(result_path).absolute()}")
            
            # التحقق من استخدام الصورة
            header_image_path = pdf_generator.get_header_image_path()
            if header_image_path:
                print(f"🖼️ تم استخدام صورة الرأس: {header_image_path}")
            else:
                print("📝 تم استخدام الرأس النصي (لا توجد صورة)")
            
            return True
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_detection():
    """اختبار اكتشاف صور الرأس"""
    print("\n🔍 اختبار اكتشاف صور الرأس...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        # اختبار دالة البحث عن الصور
        header_image_path = pdf_generator.get_header_image_path()
        
        if header_image_path:
            print(f"✅ تم العثور على صورة الرأس: {header_image_path}")
            
            # التحقق من خصائص الصورة
            if os.path.exists(header_image_path):
                file_size = os.path.getsize(header_image_path)
                print(f"   📄 حجم الملف: {file_size} بايت")
                
                # محاولة فتح الصورة
                try:
                    from PIL import Image
                    with Image.open(header_image_path) as img:
                        width, height = img.size
                        print(f"   📏 الأبعاد: {width}x{height}")
                        print(f"   🎨 النمط: {img.mode}")
                        print(f"   📋 التنسيق: {img.format}")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في فتح الصورة: {e}")
                    
            return True
        else:
            print("❌ لم يتم العثور على صورة الرأس")
            print("💡 تأكد من وجود صورة في مجلد assets/")
            
            # عرض المسارات المحتملة
            possible_paths = [
                "assets/header.png",
                "assets/header.jpg", 
                "assets/header.jpeg",
                "assets/company_header.png",
                "assets/company_header.jpg",
                "assets/logo_header.png",
                "assets/logo_header.jpg"
            ]
            
            print("   المسارات المحتملة:")
            for path in possible_paths:
                exists = "✅" if os.path.exists(path) else "❌"
                print(f"     {exists} {path}")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار اكتشاف الصور: {e}")
        return False

def test_image_functions():
    """اختبار دوال معالجة الصور"""
    print("\n🧪 اختبار دوال معالجة الصور...")
    
    try:
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        pdf_generator = RemittancePDFGenerator()
        
        # التحقق من وجود الدوال الجديدة
        functions_to_check = [
            'get_header_image_path',
            'draw_header_image', 
            'draw_text_header'
        ]
        
        missing_functions = []
        for func_name in functions_to_check:
            if hasattr(pdf_generator, func_name):
                print(f"✅ دالة {func_name} موجودة")
            else:
                print(f"❌ دالة {func_name} غير موجودة")
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"⚠️ دوال مفقودة: {', '.join(missing_functions)}")
            return False
        else:
            print("✅ جميع دوال معالجة الصور موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الصور: {e}")
        return False

def test_fallback_to_text():
    """اختبار الرجوع للرأس النصي عند عدم وجود صورة"""
    print("\n🔄 اختبار الرجوع للرأس النصي...")
    
    try:
        # إخفاء الصور مؤقتاً
        image_files = []
        for file in os.listdir("assets"):
            if file.endswith(('.png', '.jpg', '.jpeg')):
                old_path = f"assets/{file}"
                new_path = f"assets/{file}.backup"
                os.rename(old_path, new_path)
                image_files.append((old_path, new_path))
                print(f"🔄 تم إخفاء {old_path}")
        
        # اختبار مولد PDF بدون صور
        from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator
        
        test_data = {
            'request_number': '2025-TEST',
            'request_date': '2024/01/03',
            'remittance_amount': '1,000',
            'currency': 'USD',
            'exchanger': 'اختبار الصراف'
        }
        
        pdf_generator = RemittancePDFGenerator()
        output_path = "نموذج_رأس_نصي.pdf"
        result_path = pdf_generator.generate_pdf(test_data, output_path)
        
        success = Path(result_path).exists()
        if success:
            print(f"✅ تم إنشاء النموذج بالرأس النصي: {result_path}")
        else:
            print("❌ فشل في إنشاء النموذج بالرأس النصي")
        
        # استعادة الصور
        for old_path, new_path in image_files:
            os.rename(new_path, old_path)
            print(f"🔄 تم استعادة {old_path}")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الرجوع للنص: {e}")
        
        # استعادة الصور في حالة الخطأ
        try:
            for old_path, new_path in image_files:
                if os.path.exists(new_path):
                    os.rename(new_path, old_path)
        except:
            pass
            
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار مولد PDF مع الصور...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_image_detection())
    results.append(test_image_functions())
    results.append(test_pdf_with_header_image())
    results.append(test_fallback_to_text())
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎯 ملخص اختبار مولد PDF مع الصور:")
    print("="*60)
    
    test_names = [
        "اكتشاف صور الرأس",
        "دوال معالجة الصور",
        "PDF مع صورة الرأس",
        "الرجوع للرأس النصي"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 مولد PDF مع الصور يعمل بنجاح!")
        print("✅ اكتشاف الصور يعمل بشكل صحيح")
        print("✅ إدراج الصور في PDF يعمل")
        print("✅ الرجوع للرأس النصي يعمل كبديل")
        print("✅ جميع الدوال متوفرة ومتكاملة")
        
        # عرض الملفات المنشأة
        created_files = []
        for filename in ["نموذج_مع_صورة_الرأس.pdf", "نموذج_رأس_نصي.pdf"]:
            if Path(filename).exists():
                created_files.append(filename)
        
        if created_files:
            print(f"\n📁 الملفات المنشأة:")
            for file in created_files:
                print(f"   • {file}")
            print("يمكنك فتح الملفات لمراجعة النتائج")
            
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم الاختبارات نجحت!")
        print("مولد PDF مع الصور يعمل مع بعض القيود")
    else:
        print("\n⚠️ عدة اختبارات فشلت. يرجى مراجعة:")
        print("- تثبيت Pillow: pip install Pillow")
        print("- وجود صور في مجلد assets/")
        print("- صحة مسارات الصور")
    
    return successful_tests >= len(results) * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
