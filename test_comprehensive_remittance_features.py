#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الميزات الجديدة في نافذة طلب الحوالة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_window_creation():
    """اختبار إنشاء النافذة مع جميع الميزات الجديدة"""
    print("🪟 اختبار إنشاء نافذة طلب الحوالة مع الميزات الجديدة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ تم إنشاء تطبيق Qt")
        
        # استيراد نافذة طلب الحوالة
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        print("✅ تم استيراد نافذة طلب الحوالة")
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        print("✅ تم إنشاء نافذة طلب الحوالة بنجاح")
        
        # التحقق من وجود التبويبات
        tab_count = window.tab_widget.count()
        print(f"📊 عدد التبويبات: {tab_count}")
        
        expected_tabs = ["قائمة طلبات الحوالات", "طلب حوالة جديد", "التقارير والإحصائيات", "دفتر العناوين"]
        
        for i in range(tab_count):
            tab_text = window.tab_widget.tabText(i)
            print(f"   {i+1}. {tab_text}")
        
        # التحقق من وجود تبويب دفتر العناوين
        address_book_found = False
        for i in range(tab_count):
            if "دفتر العناوين" in window.tab_widget.tabText(i):
                address_book_found = True
                break
        
        if address_book_found:
            print("✅ تبويب دفتر العناوين موجود")
        else:
            print("❌ تبويب دفتر العناوين غير موجود")
            return False
        
        # التحقق من الحقول الجديدة
        if hasattr(window, 'receiver_bank_country_input'):
            print("✅ حقل بلد البنك موجود")
        else:
            print("❌ حقل بلد البنك غير موجود")
            return False
        
        if hasattr(window, 'receiver_country_input'):
            print("✅ حقل البلد النصي موجود")
        else:
            print("❌ حقل البلد النصي غير موجود")
            return False
        
        # التحقق من البحث التلقائي
        if hasattr(window, 'receiver_name_completer'):
            print("✅ البحث التلقائي لاسم المستقبل موجود")
        else:
            print("❌ البحث التلقائي لاسم المستقبل غير موجود")
            return False
        
        # التحقق من جدول دفتر العناوين
        if hasattr(window, 'address_book_table'):
            print("✅ جدول دفتر العناوين موجود")
        else:
            print("❌ جدول دفتر العناوين غير موجود")
            return False
        
        # إغلاق النافذة
        window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_address_book_functionality():
    """اختبار وظائف دفتر العناوين"""
    print("\n📇 اختبار وظائف دفتر العناوين...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # اختبار ملء نموذج دفتر العناوين
        test_data = {
            'name': 'شركة الاختبار المحدودة',
            'account': '****************',
            'bank': 'البنك الأهلي السعودي',
            'branch': 'فرع الرياض الرئيسي',
            'swift': 'NCBKSARI',
            'country': 'المملكة العربية السعودية',
            'bank_country': 'المملكة العربية السعودية',
            'address': 'الرياض، طريق الملك فهد'
        }
        
        # ملء النموذج
        window.ab_receiver_name_input.setText(test_data['name'])
        window.ab_receiver_account_input.setText(test_data['account'])
        window.ab_receiver_bank_input.setText(test_data['bank'])
        window.ab_receiver_bank_branch_input.setText(test_data['branch'])
        window.ab_receiver_swift_input.setText(test_data['swift'])
        window.ab_receiver_country_input.setText(test_data['country'])
        window.ab_receiver_bank_country_input.setText(test_data['bank_country'])
        window.ab_receiver_address_input.setText(test_data['address'])
        
        # التحقق من القيم
        if window.ab_receiver_name_input.text() == test_data['name']:
            print("✅ ملء نموذج دفتر العناوين يعمل")
        else:
            print("❌ ملء نموذج دفتر العناوين لا يعمل")
            return False
        
        # اختبار مسح النموذج
        window.clear_address_book_form()
        
        if not window.ab_receiver_name_input.text():
            print("✅ مسح نموذج دفتر العناوين يعمل")
        else:
            print("❌ مسح نموذج دفتر العناوين لا يعمل")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دفتر العناوين: {e}")
        return False

def test_receiver_form_fields():
    """اختبار حقول معلومات المستقبل الجديدة"""
    print("\n👥 اختبار حقول معلومات المستقبل الجديدة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء النافذة
        window = RemittanceRequestWindow()
        
        # اختبار الحقول الجديدة
        test_country = "الإمارات العربية المتحدة"
        test_bank_country = "المملكة العربية السعودية"
        
        # ملء الحقول
        window.receiver_country_input.setText(test_country)
        window.receiver_bank_country_input.setText(test_bank_country)
        
        # التحقق من القيم
        if window.receiver_country_input.text() == test_country:
            print("✅ حقل البلد النصي يعمل بشكل صحيح")
        else:
            print("❌ حقل البلد النصي لا يعمل")
            return False
            
        if window.receiver_bank_country_input.text() == test_bank_country:
            print("✅ حقل بلد البنك يعمل بشكل صحيح")
        else:
            print("❌ حقل بلد البنك لا يعمل")
            return False
        
        # اختبار جمع البيانات
        try:
            data = window.collect_new_request_data()
            
            if data.get('receiver_country') == test_country:
                print("✅ جمع بيانات البلد يعمل")
            else:
                print("❌ جمع بيانات البلد لا يعمل")
                return False
                
            if data.get('receiver_bank_country') == test_bank_country:
                print("✅ جمع بيانات بلد البنك يعمل")
            else:
                print("❌ جمع بيانات بلد البنك لا يعمل")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في جمع البيانات: {e}")
            return False
        
        window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حقول المستقبل: {e}")
        return False

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    print("\n🗄️ اختبار تكامل قاعدة البيانات...")
    
    try:
        import sqlite3
        from pathlib import Path
        
        db_path = Path("data/proshipment.db")
        if not db_path.exists():
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # التحقق من جدول دفتر العناوين
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='address_book'
        """)
        
        if cursor.fetchone():
            print("✅ جدول دفتر العناوين موجود")
        else:
            print("ℹ️ جدول دفتر العناوين سيتم إنشاؤه عند الحاجة")
        
        # التحقق من عمود receiver_bank_country في جدول remittance_requests
        cursor.execute("PRAGMA table_info(remittance_requests)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'receiver_bank_country' in columns:
            print("✅ عمود receiver_bank_country موجود في جدول طلبات الحوالة")
        else:
            print("❌ عمود receiver_bank_country غير موجود")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار الشامل لميزات نافذة طلب الحوالة الجديدة...\n")
    
    results = []
    
    # تشغيل الاختبارات
    results.append(test_database_integration())
    results.append(test_window_creation())
    results.append(test_receiver_form_fields())
    results.append(test_address_book_functionality())
    
    # عرض النتائج النهائية
    print("\n" + "="*70)
    print("🎯 ملخص الاختبار الشامل لميزات نافذة طلب الحوالة:")
    print("="*70)
    
    test_names = [
        "تكامل قاعدة البيانات",
        "إنشاء النافذة مع الميزات الجديدة",
        "حقول معلومات المستقبل الجديدة",
        "وظائف دفتر العناوين"
    ]
    
    successful_tests = 0
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {successful_tests}/{len(results)} اختبارات نجحت")
    
    if successful_tests == len(results):
        print("\n🎉 جميع الميزات الجديدة تعمل بنجاح!")
        print("✅ إصلاح مشكلة تحديث طلب الحوالة")
        print("✅ إضافة حقل Bank Country")
        print("✅ تحويل حقل البلد إلى نصي")
        print("✅ إنشاء تبويب دفتر العناوين")
        print("✅ إضافة زر دفتر العناوين")
        print("✅ رسالة حفظ في دفتر العناوين")
        print("✅ تطوير البحث التلقائي")
        
        print("\n🌟 الميزات الجديدة:")
        print("   📇 دفتر العناوين مع إدارة كاملة للعناوين")
        print("   🔍 البحث التلقائي في أسماء المستقبلين")
        print("   💾 حفظ تلقائي للمستقبلين الجدد")
        print("   🌍 حقول مرنة للبلدان")
        print("   🏦 تفصيل أكثر لمعلومات البنوك")
        
    elif successful_tests >= len(results) * 0.75:
        print("\n✅ معظم الميزات تعمل بنجاح!")
        print("بعض التحسينات قد تحتاج مراجعة إضافية")
    else:
        print("\n⚠️ عدة ميزات فشلت. يرجى مراجعة:")
        print("- تهيئة قاعدة البيانات")
        print("- إعدادات النافذة")
        print("- ملفات واجهة المستخدم")
    
    return successful_tests >= len(results) * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
