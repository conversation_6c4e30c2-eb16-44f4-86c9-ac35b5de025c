#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بسيط لترتيب الحقول
Simple Field Order Check
"""

def check_field_order():
    """فحص ترتيب الحقول"""
    
    print("🔍 فحص ترتيب الحقول في معلومات المرسل...")
    print("=" * 60)
    
    try:
        # قراءة الملف
        with open("src/ui/remittances/remittance_request_window.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الأنماط
        mobile_pattern = "رقم الموبايل"
        pobox_pattern = "ص.ب"
        
        # العثور على المواضع
        mobile_found = mobile_pattern in content
        pobox_found = pobox_pattern in content
        
        print(f"   📲 رقم الموبايل: {'✅ موجود' if mobile_found else '❌ غير موجود'}")
        print(f"   📮 ص.ب: {'✅ موجود' if pobox_found else '❌ غير موجود'}")
        
        # فحص الترتيب في الكود
        lines = content.split('\n')
        mobile_line = None
        pobox_line = None
        fax_line = None
        
        for i, line in enumerate(lines):
            if "رقم الفاكس" in line and "2, 0" in line:
                fax_line = i
            elif "رقم الموبايل" in line and "2, 0" in line:
                mobile_line = i
            elif "ص.ب" in line and "2, 2" in line:
                pobox_line = i
        
        print(f"\n   📍 مواضع الحقول:")
        if fax_line:
            print(f"      📠 رقم الفاكس: السطر {fax_line + 1}")
        if mobile_line:
            print(f"      📲 رقم الموبايل: السطر {mobile_line + 1}")
        if pobox_line:
            print(f"      📮 ص.ب: السطر {pobox_line + 1}")
        
        # التحقق من الترتيب
        if mobile_line and pobox_line:
            if mobile_line > fax_line and pobox_line > fax_line:
                print(f"\n   ✅ الترتيب صحيح: الموبايل وص.ب يأتيان بعد الفاكس")
                return True
            else:
                print(f"\n   ❌ الترتيب غير صحيح")
                return False
        else:
            print(f"\n   ❌ لم يتم العثور على الحقول في المواضع المطلوبة")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False

def display_summary():
    """عرض الملخص"""
    
    print("\n" + "=" * 70)
    print("📋 ملخص التعديل المطلوب")
    print("=" * 70)
    
    print("\n📝 المطلوب:")
    print("   نقل الحقل رقم الموبايل و الحقل ص.ب الى الصف الثاني بعد رقم الفاكس")
    
    print("\n✅ التنفيذ:")
    print("   📲 رقم الموبايل → نُقل إلى الصف الثالث (بعد الفاكس)")
    print("   📮 ص.ب → نُقل إلى الصف الثالث (بعد الفاكس)")
    
    print("\n🎯 الترتيب الجديد:")
    print("   الصف 1: الاسم الكامل + الجهة")
    print("   الصف 2: رقم الهاتف + رقم الفاكس")
    print("   الصف 3: رقم الموبايل + ص.ب ← الجديد")
    print("   الصف 4: البريد الإلكتروني + العنوان")
    
    print("\n🚀 الفوائد:")
    print("   ✅ تجميع حقول الاتصال معاً")
    print("   ✅ ترتيب منطقي ومتسلسل")
    print("   ✅ تحسين تجربة المستخدم")
    print("   ✅ واجهة أكثر تنظيماً")

if __name__ == "__main__":
    print("🚀 بدء فحص ترتيب الحقول...")
    print("=" * 80)
    
    # فحص ترتيب الحقول
    order_correct = check_field_order()
    
    # عرض الملخص
    display_summary()
    
    # النتيجة النهائية
    if order_correct:
        print("\n🏆 التعديل المطلوب تم تطبيقه بنجاح!")
        print("✅ رقم الموبايل وص.ب في الموضع الصحيح")
        print("✅ الترتيب منطقي ومحسن")
        print("✅ شاشة طلب الحوالة جاهزة للاستخدام")
        
    else:
        print("\n❌ هناك مشكلة في ترتيب الحقول")
    
    print("=" * 80)
