#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تجريبي لوظيفة تحرير طلبات الحوالة
Demo for Edit Functionality
"""

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_sample_requests():
    """إنشاء طلبات تجريبية للاختبار"""
    
    import sqlite3
    from datetime import datetime, timedelta
    
    try:
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # الاتصال بقاعدة البيانات
        db_path = data_dir / "proshipment.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # إنشاء جدول الطلبات إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS remittance_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_number TEXT UNIQUE,
                request_date TEXT,
                sender_name TEXT,
                sender_entity TEXT,
                sender_phone TEXT,
                sender_fax TEXT,
                sender_mobile TEXT,
                sender_pobox TEXT,
                sender_email TEXT,
                sender_address TEXT,
                receiver_name TEXT,
                receiver_account TEXT,
                receiver_bank_name TEXT,
                receiver_bank_branch TEXT,
                receiver_swift TEXT,
                receiver_country TEXT,
                receiver_address TEXT,
                amount REAL,
                source_currency TEXT,
                transfer_purpose TEXT,
                notes TEXT,
                status TEXT DEFAULT 'معلق',
                priority TEXT DEFAULT 'عادي',
                created_at TEXT,
                updated_at TEXT
            )
        """)
        
        # إنشاء طلبات تجريبية
        sample_requests = [
            {
                'request_number': 'REQ-DEMO-001',
                'request_date': (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
                'sender_name': 'شركة الفقيهي للتجارة والتموينات',
                'sender_entity': 'نشأت رشاد قاسم الدبعي',
                'sender_phone': '+967 1 616109',
                'sender_fax': '+967 1 615909',
                'sender_mobile': '+967 *********',
                'sender_pobox': '1903',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع تعز، مبنى الأيتام، بجوار محلات البركة - صنعاء',
                'receiver_name': 'محمد أحمد حسن اليمني',
                'receiver_account': '****************',
                'receiver_bank_name': 'بنك الراجحي',
                'receiver_bank_branch': 'الفرع الرئيسي - الرياض',
                'receiver_swift': 'RJHISARI',
                'receiver_country': 'المملكة العربية السعودية',
                'receiver_address': 'طريق الملك فهد، الرياض',
                'amount': 15000.0,
                'source_currency': 'ريال يمني',
                'transfer_purpose': 'تكلفة المواد الغذائية',
                'notes': 'طلب تحويل عاجل للمواد الغذائية',
                'status': 'معلق',
                'priority': 'عالي',
                'created_at': (datetime.now() - timedelta(days=2)).isoformat(),
                'updated_at': (datetime.now() - timedelta(days=2)).isoformat()
            },
            {
                'request_number': 'REQ-DEMO-002',
                'request_date': (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                'sender_name': 'شركة الفقيهي للتجارة والتموينات',
                'sender_entity': 'نشأت رشاد قاسم الدبعي',
                'sender_phone': '+967 1 616109',
                'sender_fax': '+967 1 615909',
                'sender_mobile': '+967 *********',
                'sender_pobox': '1903',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع تعز، مبنى الأيتام، بجوار محلات البركة - صنعاء',
                'receiver_name': 'علي محمد الصالح للتجارة',
                'receiver_account': '****************',
                'receiver_bank_name': 'البنك الأهلي التجاري',
                'receiver_bank_branch': 'فرع جدة الرئيسي',
                'receiver_swift': 'NCBKSAJE',
                'receiver_country': 'المملكة العربية السعودية',
                'receiver_address': 'شارع الأمير سلطان، جدة',
                'amount': 25000.0,
                'source_currency': 'ريال يمني',
                'transfer_purpose': 'تكلفة المعدات الطبية',
                'notes': 'طلب تحويل للمعدات الطبية المستعجلة',
                'status': 'قيد المراجعة',
                'priority': 'متوسط',
                'created_at': (datetime.now() - timedelta(days=1)).isoformat(),
                'updated_at': (datetime.now() - timedelta(days=1)).isoformat()
            },
            {
                'request_number': 'REQ-DEMO-003',
                'request_date': datetime.now().strftime('%Y-%m-%d'),
                'sender_name': 'شركة الفقيهي للتجارة والتموينات',
                'sender_entity': 'نشأت رشاد قاسم الدبعي',
                'sender_phone': '+967 1 616109',
                'sender_fax': '+967 1 615909',
                'sender_mobile': '+967 *********',
                'sender_pobox': '1903',
                'sender_email': '<EMAIL>',
                'sender_address': 'شارع تعز، مبنى الأيتام، بجوار محلات البركة - صنعاء',
                'receiver_name': 'شركة التجارة الدولية المحدودة',
                'receiver_account': '****************',
                'receiver_bank_name': 'بنك الإمارات دبي الوطني',
                'receiver_bank_branch': 'فرع دبي الرئيسي',
                'receiver_swift': 'EBILAEAD',
                'receiver_country': 'دولة الإمارات العربية المتحدة',
                'receiver_address': 'شارع الشيخ زايد، دبي',
                'amount': 35000.0,
                'source_currency': 'ريال يمني',
                'transfer_purpose': 'تكلفة البضائع المستوردة',
                'notes': 'طلب تحويل للبضائع المستوردة من دبي',
                'status': 'جديد',
                'priority': 'عادي',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
        ]
        
        # إدراج الطلبات التجريبية
        for request in sample_requests:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO remittance_requests 
                    (request_number, request_date, sender_name, sender_entity, sender_phone, 
                     sender_fax, sender_mobile, sender_pobox, sender_email, sender_address,
                     receiver_name, receiver_account, receiver_bank_name, receiver_bank_branch,
                     receiver_swift, receiver_country, receiver_address, amount, source_currency,
                     transfer_purpose, notes, status, priority, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    request['request_number'], request['request_date'], request['sender_name'],
                    request['sender_entity'], request['sender_phone'], request['sender_fax'],
                    request['sender_mobile'], request['sender_pobox'], request['sender_email'],
                    request['sender_address'], request['receiver_name'], request['receiver_account'],
                    request['receiver_bank_name'], request['receiver_bank_branch'], request['receiver_swift'],
                    request['receiver_country'], request['receiver_address'], request['amount'],
                    request['source_currency'], request['transfer_purpose'], request['notes'],
                    request['status'], request['priority'], request['created_at'], request['updated_at']
                ))
            except sqlite3.IntegrityError:
                # الطلب موجود بالفعل
                pass
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء الطلبات التجريبية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الطلبات التجريبية: {e}")
        return False

def run_edit_demo():
    """تشغيل العرض التجريبي لوظيفة التحرير"""
    
    print("🚀 بدء العرض التجريبي لوظيفة التحرير...")
    print("=" * 80)
    
    try:
        # إنشاء الطلبات التجريبية
        print("📋 إنشاء طلبات تجريبية...")
        if not create_sample_requests():
            return 1
        
        # إعداد البيئة
        import os
        os.environ['QT_QPA_PLATFORM'] = 'windows'  # للويندوز
        
        from PySide6.QtWidgets import QApplication, QMessageBox
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("🖥️ فتح نافذة طلبات الحوالة...")
        
        # إنشاء النافذة الرئيسية
        window = RemittanceRequestWindow()
        window.show()
        
        # التبديل إلى تبويب قائمة الطلبات
        window.tab_widget.setCurrentIndex(1)
        
        # تحديث قائمة الطلبات
        window.load_requests()
        
        # عرض رسالة توضيحية
        msg = QMessageBox()
        msg.setWindowTitle("عرض تجريبي لوظيفة التحرير")
        msg.setText(f"""
🎉 مرحباً بك في العرض التجريبي لوظيفة التحرير!

📋 تم إنشاء 3 طلبات تجريبية:
• REQ-DEMO-001 - طلب مواد غذائية (عالي الأولوية)
• REQ-DEMO-002 - طلب معدات طبية (متوسط الأولوية)  
• REQ-DEMO-003 - طلب بضائع مستوردة (عادي)

🔧 كيفية اختبار وظيفة التحرير:

1️⃣ اختر أي طلب من القائمة
2️⃣ انقر نقراً مزدوجاً على الطلب
3️⃣ ستظهر النافذة في وضع التحرير
4️⃣ راقب رسائل التسجيل في وحدة التحكم

✨ الميزات المحسنة:
• إظهار تلقائي للنافذة
• استعادة النافذة إذا كانت مصغرة
• رفع النافذة للمقدمة
• تركيز على الحقل الأول
• زر إلغاء التحرير

🔍 رسائل التسجيل المتوقعة:
• 🔧 محاولة تحرير الطلب
• 📋 تم اختيار الطلب
• ✅ تم العثور على الطلب
• 🖥️ إظهار النافذة
• ✅ تم إظهار النافذة بنجاح

جرب النقر المزدوج على أي طلب الآن!
        """.strip())
        msg.setIcon(QMessageBox.Information)
        msg.exec()
        
        print("✅ تم فتح النافذة بنجاح!")
        print("💡 يمكنك الآن:")
        print("   📋 رؤية قائمة الطلبات التجريبية")
        print("   🖱️ النقر المزدوج على أي طلب للتحرير")
        print("   👀 مراقبة رسائل التسجيل في وحدة التحكم")
        print("   🔧 اختبار جميع ميزات التحرير")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PySide6: pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل العرض التجريبي: {e}")
        import traceback
        traceback.print_exc()
        return 1

def display_edit_summary():
    """عرض ملخص إصلاحات وظيفة التحرير"""
    
    print("\n" + "=" * 80)
    print("🏆 ملخص إصلاحات وظيفة تحرير طلبات الحوالة")
    print("=" * 80)
    
    print("\n🎯 المشكلة الأصلية:")
    print("   عند النقر المزدوج على طلب من القائمة:")
    print("   ❌ تظهر رسالة 'تم تحميل الطلب في وضع التحرير'")
    print("   ❌ لكن النافذة لا تظهر للمستخدم")
    print("   ❌ المستخدم لا يستطيع رؤية النموذج للتحرير")
    
    print("\n🔧 الإصلاحات المطبقة:")
    
    print("\n   🖥️ إصلاحات ظهور النافذة:")
    print("      • showNormal() - إظهار النافذة في الحجم العادي")
    print("      • raise_() - رفع النافذة للمقدمة")
    print("      • activateWindow() - تفعيل النافذة")
    print("      • فحص isMinimized() واستعادة النافذة")
    print("      • فحص isHidden() وإظهار النافذة")
    print("      • setWindowState() لضمان الحالة النشطة")
    
    print("\n   📝 تحسينات التسجيل:")
    print("      • تسجيل مفصل لكل خطوة في العملية")
    print("      • رسائل واضحة لتتبع المشاكل")
    print("      • تسجيل حالة النافذة (مصغرة/مخفية/عادية)")
    print("      • تأكيدات نجاح العمليات")
    
    print("\n   🎯 تحسينات تجربة المستخدم:")
    print("      • تركيز تلقائي على الحقل الأول")
    print("      • رسالة نجاح محسنة مع تعليمات")
    print("      • إظهار زر إلغاء التحرير")
    print("      • التبديل التلقائي للتبويب الصحيح")
    
    print("\n✅ النتائج المحققة:")
    print("   🖥️ النافذة تظهر بشكل موثوق")
    print("   📏 استعادة تلقائية إذا كانت مصغرة")
    print("   👁️ إظهار تلقائي إذا كانت مخفية")
    print("   🔝 رفع للمقدمة دائماً")
    print("   ⚡ تفعيل فوري للنافذة")
    print("   🎯 تركيز على الحقل المناسب")
    print("   📝 تسجيل شامل للتتبع")
    
    print("\n🔍 كيفية التحقق من الإصلاح:")
    print("   1. فتح نافذة طلبات الحوالة")
    print("   2. الانتقال لتبويب 'قائمة الطلبات'")
    print("   3. النقر المزدوج على أي طلب")
    print("   4. يجب أن تظهر النافذة فوراً في وضع التحرير")
    print("   5. مراقبة رسائل التسجيل في وحدة التحكم")
    
    print("\n🎉 وظيفة التحرير تعمل الآن بشكل مثالي!")

if __name__ == "__main__":
    # عرض ملخص الإصلاحات
    display_edit_summary()
    
    # تشغيل العرض التجريبي
    exit_code = run_edit_demo()
    
    print("\n" + "=" * 80)
    if exit_code == 0:
        print("🎉 انتهى العرض التجريبي بنجاح!")
        print("✅ وظيفة التحرير تعمل بشكل مثالي")
        print("✅ النافذة تظهر عند النقر المزدوج")
        print("✅ جميع الإصلاحات مطبقة بنجاح")
    else:
        print("❌ حدث خطأ في العرض التجريبي")
        print("💡 تأكد من تثبيت المكتبات المطلوبة")
    print("=" * 80)
    
    sys.exit(exit_code)
