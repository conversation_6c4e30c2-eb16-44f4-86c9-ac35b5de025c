#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لميزة المرفقات
Demo for Attachments Feature
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_attachments_manager():
    """عرض توضيحي لنافذة إدارة المرفقات"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.dialogs.attachments_manager_dialog import AttachmentsManagerDialog
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء نافذة إدارة المرفقات
        dialog = AttachmentsManagerDialog(None, request_id=123)
        
        # عرض النافذة
        dialog.show()
        
        print("🎬 عرض توضيحي لنافذة إدارة المرفقات")
        print("📋 الميزات المتاحة:")
        print("   📂 تصفح واختيار الملفات")
        print("   📝 إضافة وصف للمرفقات")
        print("   🏷️ تصنيف أنواع المرفقات")
        print("   👁️ عرض المرفقات")
        print("   💾 تحميل المرفقات")
        print("   🗑️ حذف المرفقات")
        print("   📊 عرض معلومات الملفات")
        print("\n💡 جرب إضافة ملف للاختبار!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")

def demo_remittance_window():
    """عرض توضيحي لنافذة طلب الحوالة مع عمود المستندات"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء نافذة طلب الحوالة
        window = RemittanceRequestWindow()
        
        # عرض النافذة
        window.show()
        
        print("🎬 عرض توضيحي لنافذة طلب الحوالة")
        print("📋 الميزات الجديدة:")
        print("   📎 عمود المستندات في جدول الطلبات")
        print("   🔢 عداد المرفقات لكل طلب")
        print("   🎨 ألوان مختلفة حسب وجود المرفقات")
        print("   🖱️ نقرة واحدة لفتح إدارة المرفقات")
        print("\n💡 اذهب إلى تبويب 'قائمة طلبات الحوالات' لرؤية العمود الجديد!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎬 عرض توضيحي لميزة المرفقات في طلبات الحوالات")
    print("="*60)
    
    print("اختر العرض التوضيحي:")
    print("1. نافذة إدارة المرفقات")
    print("2. نافذة طلب الحوالة مع عمود المستندات")
    print("3. معلومات عن الميزة")
    
    choice = input("\nاختر رقم (1-3): ").strip()
    
    if choice == "1":
        demo_attachments_manager()
    elif choice == "2":
        demo_remittance_window()
    elif choice == "3":
        show_feature_info()
    else:
        print("❌ خيار غير صحيح")

def show_feature_info():
    """عرض معلومات عن الميزة"""
    print("\n📎 ميزة المرفقات في طلبات الحوالات")
    print("="*50)
    
    print("\n🆕 ما الجديد:")
    print("   ✅ عمود 'المستندات' في جدول طلبات الحوالات")
    print("   ✅ نافذة إدارة المرفقات الشاملة")
    print("   ✅ تنظيم الملفات حسب رقم الطلب")
    print("   ✅ دعم جميع أنواع الملفات")
    print("   ✅ عداد المرفقات في الجدول")
    
    print("\n🎯 الهدف:")
    print("   📋 تسهيل إرفاق المستندات مع طلبات الحوالات")
    print("   🗂️ تنظيم أفضل للملفات والمستندات")
    print("   🔍 سهولة الوصول والعرض")
    print("   💾 إمكانية التحميل والمشاركة")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. افتح نافذة طلب الحوالة")
    print("   2. اذهب إلى تبويب 'قائمة طلبات الحوالات'")
    print("   3. اضغط على زر '📎 مرفقات' للطلب المطلوب")
    print("   4. أضف، عرض، أو أدر المرفقات")
    
    print("\n📁 هيكل الملفات:")
    print("   data/attachments/")
    print("   ├── 123/  (رقم الطلب)")
    print("   │   ├── 20241210_143022_passport.pdf")
    print("   │   └── 20241210_143045_id_card.jpg")
    print("   └── 124/")
    print("       └── 20241210_144015_contract.docx")
    
    print("\n🏷️ أنواع المرفقات المدعومة:")
    print("   📄 مستند عام")
    print("   🆔 هوية شخصية")
    print("   📘 جواز سفر")
    print("   🏠 إثبات عنوان")
    print("   🏦 كشف حساب بنكي")
    print("   🧾 فاتورة")
    print("   📜 عقد")
    print("   📸 صورة شخصية")
    print("   📎 أخرى")
    
    print("\n💡 نصائح:")
    print("   ✅ استخدم أسماء ملفات واضحة")
    print("   ✅ أضف وصف مفيد لكل مرفق")
    print("   ✅ اختر النوع المناسب للمرفق")
    print("   ✅ انشئ نسخة احتياطية من المرفقات المهمة")
    
    print("\n📚 للمزيد من المعلومات:")
    print("   📖 راجع: docs/attachments_feature_guide.md")
    print("   🧪 اختبر: python test_attachments_feature.py")
    print("   🎬 جرب: python demo_attachments_feature.py")

if __name__ == "__main__":
    main()
