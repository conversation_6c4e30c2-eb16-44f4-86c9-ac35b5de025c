#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة رسالة الحفظ عند الخروج من نافذة طلب الحوالة
Test Fix for Save Message on Exit from Remittance Request Window
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_has_unsaved_changes_logic():
    """اختبار منطق التحقق من التغييرات غير المحفوظة"""
    print("🧪 اختبار منطق التحقق من التغييرات غير المحفوظة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة طلب الحوالة
        window = RemittanceRequestWindow()
        
        print("   ✅ تم إنشاء نافذة طلب الحوالة")
        
        # اختبار الحالة الأولى: تبويب قائمة الطلبات (لا يجب أن تظهر رسالة حفظ)
        window.tab_widget.setCurrentIndex(1)  # تبويب قائمة طلبات الحوالات
        has_changes_in_list_tab = window.has_unsaved_changes()
        
        if not has_changes_in_list_tab:
            print("   ✅ تبويب قائمة الطلبات: لا توجد تغييرات غير محفوظة")
        else:
            print("   ❌ تبويب قائمة الطلبات: يظهر تغييرات غير محفوظة خطأً")
            return False
        
        # اختبار الحالة الثانية: تبويب طلب جديد بدون بيانات
        window.tab_widget.setCurrentIndex(0)  # تبويب طلب حوالة جديد
        window.clear_form()  # مسح النموذج
        has_changes_empty_form = window.has_unsaved_changes()
        
        if not has_changes_empty_form:
            print("   ✅ نموذج فارغ: لا توجد تغييرات غير محفوظة")
        else:
            print("   ❌ نموذج فارغ: يظهر تغييرات غير محفوظة خطأً")
            return False
        
        # اختبار الحالة الثالثة: إدخال بيانات في طلب جديد
        window.sender_name_input.setText("اسم تجريبي")
        has_changes_with_data = window.has_unsaved_changes()
        
        if has_changes_with_data:
            print("   ✅ نموذج مع بيانات: توجد تغييرات غير محفوظة")
        else:
            print("   ❌ نموذج مع بيانات: لا يظهر تغييرات غير محفوظة")
            return False
        
        # اختبار الحالة الرابعة: وضع التحرير بدون تعديل
        window.editing_request_id = 123
        window.form_modified = False
        has_changes_edit_no_modify = window.has_unsaved_changes()
        
        if not has_changes_edit_no_modify:
            print("   ✅ وضع التحرير بدون تعديل: لا توجد تغييرات غير محفوظة")
        else:
            print("   ❌ وضع التحرير بدون تعديل: يظهر تغييرات غير محفوظة خطأً")
            return False
        
        # اختبار الحالة الخامسة: وضع التحرير مع تعديل
        window.form_modified = True
        has_changes_edit_with_modify = window.has_unsaved_changes()
        
        if has_changes_edit_with_modify:
            print("   ✅ وضع التحرير مع تعديل: توجد تغييرات غير محفوظة")
        else:
            print("   ❌ وضع التحرير مع تعديل: لا يظهر تغييرات غير محفوظة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار منطق التحقق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_change_tracking():
    """اختبار تتبع تغييرات النموذج"""
    print("\n🧪 اختبار تتبع تغييرات النموذج...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة طلب الحوالة
        window = RemittanceRequestWindow()
        
        # التحقق من وجود دالة تتبع التغييرات
        if hasattr(window, 'setup_form_change_tracking'):
            print("   ✅ دالة إعداد تتبع التغييرات موجودة")
        else:
            print("   ❌ دالة إعداد تتبع التغييرات غير موجودة")
            return False
        
        if hasattr(window, 'on_form_field_changed'):
            print("   ✅ دالة معالج تغيير الحقول موجودة")
        else:
            print("   ❌ دالة معالج تغيير الحقول غير موجودة")
            return False
        
        if hasattr(window, 'reset_form_modification_flag'):
            print("   ✅ دالة إعادة تعيين علامة التعديل موجودة")
        else:
            print("   ❌ دالة إعادة تعيين علامة التعديل غير موجودة")
            return False
        
        # اختبار المتغير
        if hasattr(window, 'form_modified'):
            print("   ✅ متغير تتبع التعديل موجود")
            
            # اختبار إعادة التعيين
            window.form_modified = True
            window.reset_form_modification_flag()
            
            if not window.form_modified:
                print("   ✅ إعادة تعيين علامة التعديل تعمل بشكل صحيح")
            else:
                print("   ❌ إعادة تعيين علامة التعديل لا تعمل")
                return False
        else:
            print("   ❌ متغير تتبع التعديل غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تتبع التغييرات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_attachments_manager_integration():
    """اختبار دمج نافذة إدارة المرفقات"""
    print("\n🧪 اختبار دمج نافذة إدارة المرفقات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة طلب الحوالة
        window = RemittanceRequestWindow()
        
        # التحقق من وجود دالة فتح نافذة المرفقات
        if hasattr(window, 'open_attachments_manager'):
            print("   ✅ دالة فتح نافذة المرفقات موجودة")
        else:
            print("   ❌ دالة فتح نافذة المرفقات غير موجودة")
            return False
        
        # التحقق من وجود دالة حساب عدد المرفقات
        if hasattr(window, 'get_attachments_count'):
            print("   ✅ دالة حساب عدد المرفقات موجودة")
            
            # اختبار الدالة
            count = window.get_attachments_count(123)
            if isinstance(count, int):
                print(f"   ✅ دالة حساب المرفقات تعمل: {count} مرفقات")
            else:
                print("   ❌ دالة حساب المرفقات لا تعيد رقم صحيح")
                return False
        else:
            print("   ❌ دالة حساب عدد المرفقات غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار دمج المرفقات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scenario_simulation():
    """محاكاة السيناريو المشكل"""
    print("\n🧪 محاكاة السيناريو المشكل...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة طلب الحوالة
        window = RemittanceRequestWindow()
        
        print("   📋 محاكاة السيناريو:")
        print("      1. المستخدم في تبويب قائمة الطلبات")
        print("      2. يفتح نافذة المرفقات لطلب معين")
        print("      3. يضيف أو يستعرض مرفقات")
        print("      4. يغلق نافذة المرفقات")
        print("      5. يضغط زر الخروج")
        
        # الخطوة 1: الانتقال إلى تبويب قائمة الطلبات
        window.tab_widget.setCurrentIndex(1)
        print("   ✅ الخطوة 1: في تبويب قائمة الطلبات")
        
        # الخطوة 2-4: محاكاة فتح وإغلاق نافذة المرفقات
        # (لا نفتح النافذة فعلياً لتجنب التعقيد)
        print("   ✅ الخطوة 2-4: تم محاكاة فتح وإغلاق نافذة المرفقات")
        
        # الخطوة 5: التحقق من حالة التغييرات غير المحفوظة
        has_unsaved = window.has_unsaved_changes()
        
        if not has_unsaved:
            print("   ✅ النتيجة: لا توجد تغييرات غير محفوظة (صحيح)")
            print("   ✅ لن تظهر رسالة الحفظ عند الخروج")
            return True
        else:
            print("   ❌ النتيجة: توجد تغييرات غير محفوظة (خطأ)")
            print("   ❌ ستظهر رسالة الحفظ عند الخروج")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في محاكاة السيناريو: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاح مشكلة رسالة الحفظ عند الخروج")
    print("="*60)
    
    tests = [
        ("منطق التحقق من التغييرات", test_has_unsaved_changes_logic),
        ("تتبع تغييرات النموذج", test_form_change_tracking),
        ("دمج نافذة المرفقات", test_attachments_manager_integration),
        ("محاكاة السيناريو المشكل", test_scenario_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج اختبار إصلاح مشكلة رسالة الحفظ")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.8:
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ رسالة الحفظ ستظهر فقط عند:")
        print("   📝 إدخال بيانات في طلب جديد والخروج دون حفظ")
        print("   ✏️ تعديل طلب موجود والخروج دون حفظ")
        print("❌ رسالة الحفظ لن تظهر عند:")
        print("   📋 استعراض قائمة الطلبات والخروج")
        print("   📎 إدارة المرفقات والخروج")
        status = "تم الإصلاح"
    elif success_rate >= 0.6:
        print("\n✅ تم إصلاح معظم المشكلة")
        print("⚠️ بعض الجوانب قد تحتاج مراجعة")
        status = "إصلاح جزئي"
    else:
        print("\n⚠️ المشكلة لم يتم إصلاحها بالكامل")
        print("🔧 راجع الأخطاء أعلاه")
        status = "يحتاج إصلاح"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    # ملخص الإصلاحات
    if success_rate >= 0.6:
        print(f"\n📋 ملخص الإصلاحات المطبقة:")
        print(f"   🔧 تحسين دالة has_unsaved_changes()")
        print(f"   📊 إضافة متغير form_modified لتتبع التعديل الفعلي")
        print(f"   🎯 التحقق من التبويب الحالي قبل إظهار رسالة الحفظ")
        print(f"   📎 تحسين دمج نافذة المرفقات لتجنب التحديث غير الضروري")
        print(f"   🔄 إعادة تعيين علامة التعديل عند الحفظ أو التحميل")
    
    return success_rate >= 0.6

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
