#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جاهزية المشروع لبناء ملف التثبيت
Test Project Readiness for Build
"""

import sys
import os
from pathlib import Path
import importlib.util

def test_python_version():
    """اختبار إصدار Python"""
    print("🐍 اختبار إصدار Python...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (مطلوب 3.8+)")
        return False

def test_required_packages():
    """اختبار الحزم المطلوبة"""
    print("\n📦 اختبار الحزم المطلوبة...")
    
    required_packages = [
        ("PySide6", "PySide6"),
        ("SQLAlchemy", "sqlalchemy"),
        ("reportlab", "reportlab"),
        ("openpyxl", "openpyxl"),
        ("Pillow", "PIL"),
        ("requests", "requests"),
        ("beautifulsoup4", "bs4")
    ]
    
    missing_packages = []
    
    for display_name, import_name in required_packages:
        try:
            importlib.import_module(import_name)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ❌ {display_name}")
            missing_packages.append(display_name)
    
    if missing_packages:
        print(f"\n⚠️ حزم مفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيتها: pip install -r requirements.txt")
        return False
    
    return True

def test_build_packages():
    """اختبار حزم البناء"""
    print("\n🔨 اختبار حزم البناء...")
    
    build_packages = [
        ("PyInstaller", "PyInstaller")
    ]
    
    missing_packages = []
    
    for display_name, import_name in build_packages:
        try:
            importlib.import_module(import_name)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ❌ {display_name}")
            missing_packages.append(display_name)
    
    if missing_packages:
        print(f"\n⚠️ حزم بناء مفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيتها: pip install pyinstaller")
        return False
    
    return True

def test_project_structure():
    """اختبار هيكل المشروع"""
    print("\n📁 اختبار هيكل المشروع...")
    
    project_root = Path(__file__).parent
    
    required_files = [
        "main.py",
        "requirements.txt",
        "build_installer.py",
        "prepare_for_build.py"
    ]
    
    required_dirs = [
        "src",
        "src/ui",
        "src/database",
        "data"
    ]
    
    missing_items = []
    
    # فحص الملفات
    for file_name in required_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name}")
            missing_items.append(file_name)
    
    # فحص المجلدات
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ❌ {dir_name}/")
            missing_items.append(f"{dir_name}/")
    
    return len(missing_items) == 0

def test_main_import():
    """اختبار استيراد الملف الرئيسي"""
    print("\n🔍 اختبار استيراد الملف الرئيسي...")
    
    try:
        # محاولة استيراد الوحدات الأساسية
        from src.ui.main_window import MainWindow
        print("   ✅ MainWindow")
        
        from src.database.database_manager import DatabaseManager
        print("   ✅ DatabaseManager")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n💾 اختبار قاعدة البيانات...")
    
    try:
        db_file = Path("data/proshipment.db")
        
        if db_file.exists():
            print(f"   ✅ قاعدة البيانات موجودة: {db_file}")
            
            # اختبار الاتصال
            import sqlite3
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            
            if tables:
                print(f"   ✅ عدد الجداول: {len(tables)}")
                return True
            else:
                print("   ⚠️ قاعدة البيانات فارغة")
                return True  # لا بأس، سيتم إنشاء الجداول
        else:
            print("   ⚠️ قاعدة البيانات غير موجودة (سيتم إنشاؤها)")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_disk_space():
    """اختبار المساحة المتاحة"""
    print("\n💽 اختبار المساحة المتاحة...")
    
    try:
        import shutil
        
        # فحص المساحة المتاحة
        total, used, free = shutil.disk_usage(".")
        
        free_gb = free / (1024**3)
        
        if free_gb >= 5:
            print(f"   ✅ المساحة المتاحة: {free_gb:.1f} GB")
            return True
        else:
            print(f"   ❌ المساحة المتاحة: {free_gb:.1f} GB (مطلوب 5+ GB)")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص المساحة: {e}")
        return False

def test_permissions():
    """اختبار الصلاحيات"""
    print("\n🔐 اختبار الصلاحيات...")
    
    try:
        # اختبار الكتابة في المجلد الحالي
        test_file = Path("test_write_permission.tmp")
        
        with open(test_file, 'w') as f:
            f.write("test")
        
        test_file.unlink()
        
        print("   ✅ صلاحيات الكتابة متاحة")
        return True
        
    except Exception as e:
        print(f"   ❌ مشكلة في الصلاحيات: {e}")
        print("   💡 جرب تشغيل Command Prompt كمدير")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار جاهزية المشروع لبناء ملف التثبيت")
    print("="*60)
    
    tests = [
        ("إصدار Python", test_python_version),
        ("الحزم المطلوبة", test_required_packages),
        ("حزم البناء", test_build_packages),
        ("هيكل المشروع", test_project_structure),
        ("استيراد الملف الرئيسي", test_main_import),
        ("قاعدة البيانات", test_database),
        ("المساحة المتاحة", test_disk_space),
        ("الصلاحيات", test_permissions)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج اختبار الجاهزية")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    # تقييم الحالة النهائية
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.9:
        print("\n🎉 المشروع جاهز تماماً لبناء ملف التثبيت!")
        print("🚀 يمكنك الآن تشغيل:")
        print("   create_installer.bat")
        print("   أو: python build_installer.py")
        status = "جاهز تماماً"
    elif success_rate >= 0.7:
        print("\n✅ المشروع جاهز تقريباً لبناء ملف التثبيت")
        print("⚠️ بعض المشاكل البسيطة قد تحتاج إصلاح")
        print("🔧 راجع الأخطاء أعلاه")
        status = "جاهز تقريباً"
    else:
        print("\n❌ المشروع غير جاهز لبناء ملف التثبيت")
        print("🔧 يجب إصلاح المشاكل أعلاه أولاً")
        status = "غير جاهز"
    
    print(f"\nالحالة النهائية: {status}")
    print(f"معدل النجاح: {success_rate*100:.1f}%")
    
    # نصائح إضافية
    if success_rate < 0.9:
        print(f"\n💡 نصائح للإصلاح:")
        if passed_tests < total_tests:
            print(f"   🔧 راجع الأخطاء المذكورة أعلاه")
            print(f"   📦 تأكد من تثبيت جميع المتطلبات")
            print(f"   🐍 تأكد من إصدار Python المناسب")
            print(f"   💽 تأكد من وجود مساحة كافية")
    
    return success_rate >= 0.7

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
