#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بسيط للتطبيق
Simple Application Audit
"""

import sys
import os
import sqlite3
from pathlib import Path

def main():
    """فحص بسيط للتطبيق"""
    print("🔍 فحص بسيط للتطبيق...")
    print("="*50)
    
    project_root = Path(__file__).parent
    errors = []
    warnings = []
    
    # 1. فحص الملفات الأساسية
    print("\n📁 الملفات الأساسية:")
    
    files = [
        "main.py",
        "src/database/models.py",
        "src/database/database_manager.py",
        "src/ui/main_window.py"
    ]
    
    for file_path in files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            errors.append(f"ملف مفقود: {file_path}")
    
    # 2. فحص قاعدة البيانات
    print("\n🗄️ قاعدة البيانات:")
    
    db_path = project_root / "data" / "proshipment.db"
    if db_path.exists():
        print(f"   ✅ قاعدة البيانات موجودة")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   📊 عدد الجداول: {len(tables)}")
            
            # فحص الجداول المهمة
            important_tables = ['companies', 'suppliers', 'items', 'shipments']
            for table in important_tables:
                if table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table}: {count} سجل")
                else:
                    print(f"   ❌ {table}: مفقود")
                    errors.append(f"جدول مفقود: {table}")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ خطأ في قاعدة البيانات: {e}")
            errors.append(f"خطأ قاعدة البيانات: {e}")
    else:
        print("   ❌ قاعدة البيانات مفقودة")
        errors.append("قاعدة البيانات مفقودة")
    
    # 3. فحص المكتبات
    print("\n📦 المكتبات:")
    
    libraries = [
        ("PySide6", "PySide6"),
        ("SQLAlchemy", "sqlalchemy"),
        ("ReportLab", "reportlab")
    ]
    
    for name, module in libraries:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name}")
            if name in ["PySide6", "SQLAlchemy"]:
                errors.append(f"مكتبة مطلوبة مفقودة: {name}")
            else:
                warnings.append(f"مكتبة اختيارية مفقودة: {name}")
    
    # 4. فحص المجلدات
    print("\n📂 المجلدات:")
    
    dirs = [
        "src",
        "src/database",
        "src/ui",
        "data",
        "attachments"
    ]
    
    for dir_path in dirs:
        full_path = project_root / dir_path
        if full_path.exists():
            print(f"   ✅ {dir_path}")
        else:
            print(f"   ❌ {dir_path}")
            if dir_path in ["src", "src/database", "src/ui", "data"]:
                errors.append(f"مجلد مطلوب مفقود: {dir_path}")
            else:
                warnings.append(f"مجلد اختياري مفقود: {dir_path}")
    
    # النتيجة النهائية
    print("\n" + "="*50)
    print("📊 النتيجة النهائية:")
    print("="*50)
    
    print(f"\n🔴 أخطاء حرجة: {len(errors)}")
    for error in errors:
        print(f"   ❌ {error}")
    
    print(f"\n🟡 تحذيرات: {len(warnings)}")
    for warning in warnings:
        print(f"   ⚠️ {warning}")
    
    if len(errors) == 0:
        if len(warnings) == 0:
            print("\n🎉 التطبيق في حالة ممتازة!")
            status = "ممتاز"
        else:
            print("\n✅ التطبيق في حالة جيدة مع بعض التحذيرات")
            status = "جيد"
    else:
        if len(errors) <= 2:
            print("\n🔴 التطبيق يحتاج إصلاحات")
            status = "يحتاج إصلاح"
        else:
            print("\n🚨 التطبيق في حالة حرجة")
            status = "حرج"
    
    print(f"\nالحالة العامة: {status}")
    
    return len(errors) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
