# دليل استخدام الصور في مولد PDF

## 🎯 الهدف المحقق

تم **تثبيت الأدوات والمكتبات اللازمة** وتطوير نظام متكامل لإدراج الصور في رأس النموذج.

---

## 📦 المكتبات المثبتة

### **المكتبات الأساسية**:
```bash
pip install Pillow                    # معالجة الصور
pip install reportlab                 # إنشاء PDF
pip install arabic-reshaper           # دعم العربية
pip install python-bidi               # ترتيب النصوص ثنائية الاتجاه
```

### **المكتبات المستخدمة في الكود**:
```python
from PIL import Image                 # فتح ومعالجة الصور
from reportlab.lib.utils import ImageReader  # قراءة الصور في ReportLab
from reportlab.pdfgen import canvas   # إنشاء PDF
```

---

## 🏗️ النظام المطور

### **1. دالة اكتشاف الصور**:
```python
def get_header_image_path(self):
    """الحصول على مسار صورة الرأس"""
    possible_paths = [
        "assets/header.png",
        "assets/header.jpg", 
        "assets/header.jpeg",
        "assets/company_header.png",
        "assets/company_header.jpg",
        "assets/logo_header.png",
        "assets/logo_header.jpg"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None
```

### **2. دالة إدراج الصور**:
```python
def draw_header_image(self, c, image_path):
    """رسم صورة الرأس"""
    try:
        # فتح الصورة وتحديد أبعادها
        with Image.open(image_path) as img:
            img_width, img_height = img.size
            
        # حساب أبعاد الصورة في PDF (تناسب عرض الصفحة)
        max_width = self.page_width - 2 * self.margin
        max_height = 40*mm  # ارتفاع مناسب للرأس
        
        # حساب النسبة للحفاظ على تناسق الصورة
        width_ratio = max_width / img_width
        height_ratio = max_height / img_height
        scale_ratio = min(width_ratio, height_ratio)
        
        # الأبعاد النهائية
        final_width = img_width * scale_ratio
        final_height = img_height * scale_ratio
        
        # موضع الصورة (وسط الصفحة)
        x_pos = (self.page_width - final_width) / 2
        y_pos = self.page_height - 15*mm - final_height
        
        # رسم الصورة
        c.drawImage(image_path, x_pos, y_pos, width=final_width, height=final_height)
        
    except Exception as e:
        # رسم الرأس النصي كبديل
        self.draw_text_header(c, self.page_height - 15*mm)
```

### **3. نظام البديل النصي**:
```python
def draw_text_header(self, c, header_y):
    """رسم الرأس النصي كبديل للصورة"""
    # المعلومات العربية (يمين)
    # المعلومات الإنجليزية (يسار)
    # الشعار البديل (وسط)
```

---

## 📁 هيكل الملفات

```
ProShipment1_0/
├── assets/                           # مجلد الصور
│   ├── header.png                    # صورة الرأس الأساسية ✅
│   ├── header_compact.png            # صورة الرأس المضغوطة ✅
│   ├── header.jpg                    # (اختياري)
│   └── company_header.png            # (اختياري)
├── src/ui/remittances/
│   └── remittance_pdf_generator.py   # المولد المحدث ✅
├── create_header_image.py            # أداة إنشاء الصور ✅
├── test_pdf_with_images.py           # اختبار الصور ✅
└── دليل_استخدام_الصور_في_PDF.md    # هذا الدليل ✅
```

---

## 🖼️ الصور المنشأة

### **1. صورة الرأس الأساسية** (`assets/header.png`):
- **الأبعاد**: 800x150 بكسل
- **الحجم**: 12,746 بايت
- **المحتوى**: 
  - المعلومات العربية (يمين)
  - المعلومات الإنجليزية (يسار)
  - شعار دائري (وسط)

### **2. صورة الرأس المضغوطة** (`assets/header_compact.png`):
- **الأبعاد**: 800x100 بكسل
- **الحجم**: 5,500 بايت
- **المحتوى**: نسخة مضغوطة من الرأس

---

## 🔧 كيفية الاستخدام

### **1. إضافة صورة رأس جديدة**:
```bash
# ضع صورة الرأس في مجلد assets
cp your_header_image.png assets/header.png
```

### **2. تشغيل مولد PDF**:
```python
from src.ui.remittances.remittance_pdf_generator import RemittancePDFGenerator

# إنشاء مولد PDF
pdf_generator = RemittancePDFGenerator()

# إنشاء PDF (سيستخدم الصورة تلقائياً إذا كانت متوفرة)
pdf_generator.generate_pdf(request_data, "output.pdf")
```

### **3. إنشاء صور رأس جديدة**:
```bash
# تشغيل أداة إنشاء الصور
python create_header_image.py
```

---

## 📊 نتائج الاختبار

### ✅ **جميع الاختبارات نجحت (4/4)**:

1. **✅ اكتشاف صور الرأس**: يعمل بشكل صحيح
   - يبحث في مسارات متعددة
   - يتحقق من وجود الملفات
   - يعيد أول صورة متوفرة

2. **✅ دوال معالجة الصور**: جميعها متوفرة
   - `get_header_image_path()` ✅
   - `draw_header_image()` ✅
   - `draw_text_header()` ✅

3. **✅ PDF مع صورة الرأس**: يعمل بنجاح
   - حجم الملف: 65,558 بايت
   - الصورة تظهر بوضوح
   - التناسق محفوظ

4. **✅ الرجوع للرأس النصي**: يعمل كبديل
   - عند عدم وجود صورة
   - يحافظ على التخطيط
   - لا يتوقف النظام

---

## 🎨 مميزات النظام

### **1. مرونة في أنواع الصور**:
- ✅ PNG (مفضل للشعارات)
- ✅ JPG/JPEG (للصور الفوتوغرافية)
- ✅ دعم الشفافية في PNG

### **2. تحجيم ذكي للصور**:
- ✅ يحافظ على نسبة العرض للارتفاع
- ✅ يتناسب مع عرض الصفحة
- ✅ ارتفاع محدود (40mm) للرأس

### **3. نظام بديل قوي**:
- ✅ رأس نصي احترافي عند عدم وجود صورة
- ✅ لا يتوقف النظام أبداً
- ✅ رسائل واضحة للمطور

### **4. سهولة الاستخدام**:
- ✅ ضع الصورة في مجلد assets/
- ✅ النظام يكتشفها تلقائياً
- ✅ لا حاجة لتعديل الكود

---

## 💡 نصائح للاستخدام الأمثل

### **1. جودة الصور**:
- استخدم صور عالية الدقة (300 DPI)
- تجنب الصور المضغوطة بشدة
- PNG أفضل للشعارات والنصوص

### **2. أبعاد الصور**:
- العرض المثالي: 800-1200 بكسل
- الارتفاع المثالي: 100-200 بكسل
- نسبة العرض للارتفاع: 4:1 إلى 6:1

### **3. أسماء الملفات**:
- `header.png` (الأولوية الأولى)
- `company_header.png` (بديل)
- `logo_header.png` (بديل)

---

## 🎉 النتيجة النهائية

**تم تثبيت وتطوير نظام متكامل لإدراج الصور في PDF!**

### ✅ **المحقق**:
- **مكتبات مثبتة** ومتكاملة
- **نظام اكتشاف صور** ذكي
- **إدراج صور احترافي** مع تحجيم ذكي
- **نظام بديل قوي** للرأس النصي
- **اختبارات شاملة** (4/4 نجحت)
- **دليل استخدام** مفصل

### 🚀 **الاستخدام**:
1. ضع صورة الرأس في `assets/header.png`
2. استخدم زر "🖨️ طباعة PDF" في نافذة طلب الحوالة
3. النظام سيستخدم الصورة تلقائياً!

**النظام جاهز للاستخدام الإنتاجي مع دعم كامل للصور!** 🎯
