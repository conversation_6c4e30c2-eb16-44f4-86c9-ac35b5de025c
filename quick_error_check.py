#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_error_check():
    """فحص سريع للأخطاء الشائعة"""
    print("🔍 فحص سريع للأخطاء...")
    
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم إنشاء QApplication")
        
        # محاولة إنشاء النافذة
        from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
        print("✅ تم استيراد NewRemittanceDialog")
        
        dialog = NewRemittanceDialog()
        print("✅ تم إنشاء النافذة بنجاح!")
        
        # اختبار سريع للدوال
        dialog.setup_validators()
        print("✅ setup_validators تعمل")
        
        dialog.validate_form()
        print("✅ validate_form تعمل")
        
        dialog.collect_form_data()
        print("✅ collect_form_data تعمل")
        
        print("\n🎉 جميع الاختبارات السريعة نجحت!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_error_check()
    if success:
        print("\n✅ النافذة تعمل بدون أخطاء!")
    else:
        print("\n❌ يوجد أخطاء تحتاج إصلاح.")
