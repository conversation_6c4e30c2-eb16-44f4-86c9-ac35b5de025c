# دليل نظام التصميم المتجاوب - ProShipment

## 📱 نظرة عامة

تم تطوير نظام تصميم متجاوب شامل لجعل جميع نوافذ التطبيق موحدة ومتجاوبة مع أحجام الشاشات المختلفة. هذا النظام يوفر:

- **تصميم متجاوب** يتكيف تلقائياً مع حجم الشاشة
- **ثيمات متعددة** (فاتح، مظلم، تلقائي)
- **مخططات ألوان متنوعة** (6 مخططات مختلفة)
- **نوافذ أساسية موحدة** لجميع أجزاء التطبيق
- **إعدادات قابلة للتخصيص** للمظهر والألوان

## 🏗️ هيكل النظام

### المكونات الأساسية:

```
src/ui/
├── responsive/
│   └── responsive_manager.py     # مدير التصميم المتجاوب
├── themes/
│   └── theme_manager.py          # مدير الثيمات والألوان
├── base/
│   └── base_window.py            # النوافذ الأساسية الموحدة
└── dialogs/
    └── design_settings_dialog.py # نافذة إعدادات التصميم
```

## 📐 مدير التصميم المتجاوب

### الميزات:
- **كشف تلقائي لحجم الشاشة** (صغيرة، متوسطة، كبيرة، كبيرة جداً)
- **تكييف العناصر** حسب حجم الشاشة
- **إدارة الخطوط والأحجام** بشكل ديناميكي
- **توسيط النوافذ** تلقائياً

### أحجام الشاشات المدعومة:

| الحجم | الدقة | الوصف |
|-------|--------|--------|
| صغيرة | < 1366x768 | شاشات صغيرة ولابتوب قديم |
| متوسطة | 1366x768 - 1920x1080 | شاشات عادية ولابتوب حديث |
| كبيرة | 1920x1080 - 2560x1440 | شاشات كبيرة وأجهزة مكتبية |
| كبيرة جداً | > 2560x1440 | شاشات 4K وأكبر |

### الاستخدام:

```python
from src.ui.responsive.responsive_manager import responsive_manager, WindowType

# تطبيق التصميم المتجاوب على نافذة
responsive_manager.apply_responsive_layout(widget, WindowType.DIALOG)

# الحصول على حجم خط متجاوب
font_size = responsive_manager.get_font_size("base")

# الحصول على إعدادات للشاشة الحالية
button_height = responsive_manager.get_config("button_height")
```

## 🎨 مدير الثيمات والألوان

### الثيمات المتاحة:
- **فاتح** - للاستخدام النهاري
- **مظلم** - للاستخدام الليلي (يتطلب qdarkstyle)
- **تلقائي** - حسب إعدادات النظام

### مخططات الألوان:

| المخطط | اللون الأساسي | الوصف |
|---------|---------------|--------|
| أزرق | #3498db | الافتراضي - مهني وهادئ |
| أخضر | #27ae60 | للطبيعة والنمو |
| بنفسجي | #9b59b6 | للإبداع والفخامة |
| برتقالي | #e67e22 | للحيوية والطاقة |
| أحمر | #e74c3c | للقوة والتحذير |
| تركوازي | #1abc9c | للهدوء والانتعاش |

### الاستخدام:

```python
from src.ui.themes.theme_manager import theme_manager, ThemeType, ColorScheme

# تغيير الثيم
theme_manager.set_theme(ThemeType.DARK)

# تغيير مخطط الألوان
theme_manager.set_color_scheme(ColorScheme.GREEN)

# الحصول على لون
primary_color = theme_manager.get_color("primary")

# تطبيق نمط على زر
theme_manager.apply_button_style(button, "success")
```

## 🏗️ النوافذ الأساسية الموحدة

### BaseWindow - للنوافذ الرئيسية:

```python
from src.ui.base.base_window import BaseWindow, WindowType

class MyMainWindow(BaseWindow):
    def __init__(self):
        super().__init__(
            window_title="نافذتي الرئيسية",
            window_type=WindowType.MAIN_WINDOW
        )
        self.setup_my_ui()
    
    def setup_my_ui(self):
        # إضافة المحتوى إلى self.main_layout
        pass
```

### BaseDialog - للنوافذ الحوارية:

```python
from src.ui.base.base_window import BaseDialog

class MyDialog(BaseDialog):
    def __init__(self, parent=None):
        super().__init__(parent, "نافذتي الحوارية")
        self.setup_my_ui()
    
    def setup_my_ui(self):
        # إضافة المحتوى
        self.add_content_widget(my_widget)
        
        # إضافة أزرار مخصصة
        self.add_button("حفظ", self.save_data, "success")
```

## ⚙️ نافذة إعدادات التصميم

### الميزات:
- **تبويب الثيم والألوان** - اختيار الثيم ومخطط الألوان
- **تبويب التصميم المتجاوب** - إعدادات التكيف
- **تبويب الخطوط** - تخصيص أحجام الخطوط
- **تبويب الألوان المخصصة** - تخصيص ألوان فردية

### الاستخدام:

```python
from src.ui.dialogs.design_settings_dialog import DesignSettingsDialog

# فتح نافذة الإعدادات
dialog = DesignSettingsDialog(parent_window)
dialog.exec()
```

## 📦 المكتبات المطلوبة

### المكتبات الأساسية:
```bash
pip install PySide6>=6.5.0
```

### مكتبات التصميم المتجاوب:
```bash
pip install qdarkstyle>=3.2.0      # الثيم المظلم
pip install qtawesome>=1.2.3       # مكتبة الأيقونات
pip install qtstylish>=0.1.5       # التنسيق المتقدم
```

## 🚀 البدء السريع

### 1. تحديث requirements.txt:
```bash
# تم إضافة المكتبات الجديدة تلقائياً
pip install -r requirements.txt
```

### 2. إنشاء نافذة متجاوبة:
```python
from src.ui.base.base_window import BaseWindow

class MyWindow(BaseWindow):
    def __init__(self):
        super().__init__(window_title="نافذة متجاوبة")
        # النافذة ستكون متجاوبة تلقائياً
```

### 3. تطبيق ثيم مخصص:
```python
from src.ui.themes.theme_manager import theme_manager, ColorScheme

# تطبيق مخطط ألوان أخضر
theme_manager.set_color_scheme(ColorScheme.GREEN)
```

### 4. تشغيل العرض التوضيحي:
```bash
python demo_responsive_design.py
```

## 🧪 الاختبار

### تشغيل اختبارات النظام:
```bash
python test_responsive_design_system.py
```

### النتائج المتوقعة:
- ✅ استيراد المديرين
- ✅ مدير التصميم المتجاوب
- ✅ مدير الثيمات
- ✅ النوافذ الأساسية
- ✅ إنشاء stylesheet
- ✅ توفر المكتبات
- ✅ التكامل الشامل

## 📐 إرشادات التصميم

### أحجام العناصر حسب الشاشة:

| العنصر | صغيرة | متوسطة | كبيرة | كبيرة جداً |
|---------|--------|---------|--------|------------|
| الخط الأساسي | 9px | 10px | 11px | 12px |
| ارتفاع الأزرار | 32px | 36px | 40px | 44px |
| ارتفاع الإدخال | 28px | 32px | 36px | 40px |
| التباعد | 8px | 10px | 12px | 15px |
| الهوامش | 10px | 15px | 20px | 25px |

### ألوان النظام:

```css
/* الألوان الأساسية */
primary: #3498db      /* الأزرق الأساسي */
secondary: #2980b9    /* الأزرق الثانوي */
success: #27ae60      /* الأخضر للنجاح */
warning: #f39c12      /* البرتقالي للتحذير */
danger: #e74c3c       /* الأحمر للخطر */
info: #17a2b8         /* التركوازي للمعلومات */

/* ألوان الخلفية */
background: #ffffff   /* خلفية رئيسية */
surface: #f5f5f5      /* خلفية ثانوية */
text: #212529         /* نص أساسي */
text_secondary: #6c757d /* نص ثانوي */
```

## 🔧 التخصيص المتقدم

### إنشاء مخطط ألوان مخصص:
```python
# في theme_manager.py
custom_scheme = {
    "primary": "#your_color",
    "secondary": "#your_secondary",
    # ... باقي الألوان
}

theme_manager.color_schemes[ColorScheme.CUSTOM] = custom_scheme
```

### تخصيص أحجام الشاشة:
```python
# في responsive_manager.py
custom_config = {
    "window_default_size": (1400, 900),
    "font_base_size": 12,
    # ... باقي الإعدادات
}

responsive_manager.size_configs[ScreenSize.CUSTOM] = custom_config
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. "cannot import name 'QDesktopWidget'"
**السبب**: إصدار PySide6 حديث
**الحل**: تم إصلاحه في الكود - استخدام QGuiApplication بدلاً منه

#### 2. "مديري التصميم غير متاحين"
**السبب**: مشكلة في الاستيراد
**الحل**: تأكد من وجود الملفات في المسارات الصحيحة

#### 3. الثيم المظلم لا يعمل
**السبب**: مكتبة qdarkstyle غير مثبتة
**الحل**: `pip install qdarkstyle`

#### 4. الأيقونات لا تظهر
**السبب**: مكتبة qtawesome غير مثبتة
**الحل**: `pip install qtawesome`

## 📊 الأداء

### تحسينات الأداء:
- **تحميل كسول للثيمات** - تحميل عند الحاجة فقط
- **تخزين مؤقت للـ stylesheets** - تجنب إعادة الإنشاء
- **تحديث انتقائي** - تحديث العناصر المتأثرة فقط
- **مراقبة ذكية للشاشة** - كشف التغييرات بكفاءة

### استخدام الذاكرة:
- **مديري مفردين** - مثيل واحد لكل مدير
- **تنظيف تلقائي** - إزالة المراجع غير المستخدمة
- **إعدادات مضغوطة** - تخزين فعال للإعدادات

## 🔮 التطويرات المستقبلية

### ميزات مخططة:
- **دعم RTL/LTR** تلقائي
- **ثيمات مخصصة** قابلة للحفظ
- **انتقالات متحركة** بين الثيمات
- **دعم الشاشات اللمسية**
- **تكامل مع إعدادات النظام**

### تحسينات تقنية:
- **دعم CSS متقدم**
- **نظام مكونات موحد**
- **أدوات تطوير مرئية**
- **اختبارات تلقائية شاملة**

## 📞 الدعم

### للحصول على المساعدة:
- **الوثائق**: راجع هذا الدليل
- **الاختبار**: شغل `test_responsive_design_system.py`
- **العرض التوضيحي**: شغل `demo_responsive_design.py`
- **الأمثلة**: راجع مجلد `examples/`

### الإبلاغ عن مشاكل:
- **وصف المشكلة** بالتفصيل
- **حجم الشاشة** ودقتها
- **نظام التشغيل** والإصدار
- **إصدار PySide6** المستخدم

---

*تم إنشاء هذا الدليل مع نظام التصميم المتجاوب في ProShipment V2.0.0*
*آخر تحديث: ديسمبر 2024*
